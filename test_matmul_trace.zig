const std = @import("std");
const graph_mod = @import("src/graph.zig");
const shape_mod = @import("src/shape.zig");
const autograd_mod = @import("src/training/autograd.zig");
const types = @import("src/types.zig");

test "trace matmul gradient shapes" {
    const allocator = std.testing.allocator;
    
    // Initialize engines
    var graph = try graph_mod.Graph.init(allocator);
    defer graph.deinit();
    
    var shape_engine = try shape_mod.ShapeEngine.init(allocator);
    defer shape_engine.deinit();
    
    var autograd = try autograd_mod.AutogradEngine.init(allocator, &graph, &shape_engine);
    defer autograd.deinit();
    
    // Create matmul decomposition manually
    const a = try graph.constant(allocator, &[_]i64{ 2, 3 }, types.DataType.f32);
    const b = try graph.constant(allocator, &[_]i64{ 3, 4 }, types.DataType.f32);
    
    // Step 1: Expand dimensions
    const a_expanded = try graph.reshape(allocator, a, &[_]i64{ 2, 1, 3 });
    const b_expanded = try graph.reshape(allocator, b, &[_]i64{ 1, 3, 4 });
    
    // Step 2: Multiply with broadcasting
    const multiplied = try graph.multiply(a_expanded, b_expanded);
    
    // Step 3: Reduce sum
    const result = try graph.reduceSum(multiplied, &[_]i64{1}, false);
    
    // Initialize gradient
    const grad_output = try graph.constant(allocator, &[_]i64{ 2, 4 }, types.DataType.f32);
    try autograd.setGradient(result, grad_output);
    
    // Run backward
    std.debug.print("\n=== MATMUL GRADIENT TRACE TEST ===\n", .{});
    std.debug.print("Forward shapes:\n", .{});
    std.debug.print("  a: [2,3], a_expanded: [2,1,3]\n", .{});
    std.debug.print("  b: [3,4], b_expanded: [1,3,4]\n", .{});
    std.debug.print("  multiplied: [2,3,4]\n", .{});
    std.debug.print("  result: [2,4]\n", .{});
    std.debug.print("\nRunning backward pass...\n", .{});
    
    autograd.backward(&.{result}) catch |err| {
        std.debug.print("Backward failed: {}\n", .{err});
        return err;
    };
    
    std.debug.print("Backward succeeded!\n", .{});
    
    // Check gradients
    if (autograd.getGradient(a)) |grad_a| {
        std.debug.print("Gradient for a: node {d}\n", .{grad_a});
    }
    if (autograd.getGradient(b)) |grad_b| {
        std.debug.print("Gradient for b: node {d}\n", .{grad_b});
    }
}