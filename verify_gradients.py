#!/usr/bin/env python3
"""
Gradient verification using PyTorch as gold standard.

This script computes gradients for primitive operations using PyTorch's
autograd and outputs them in a format that can be compared with <PERSON><PERSON>'s
gradient implementation.
"""

import torch
import numpy as np
import json
import math

def verify_add_gradient():
    """Verify add operation: f(x,y) = x + y, df/dx = 1"""
    test_cases = [
        (1.0, 2.0),
        (-3.5, 1.5),
        (0.0, 5.0),
        (100.0, -50.0),
    ]
    
    results = []
    for x_val, y_val in test_cases:
        x = torch.tensor(x_val, requires_grad=True)
        y = torch.tensor(y_val, requires_grad=False)
        
        # Forward pass
        z = x + y
        
        # Backward pass
        z.backward()
        
        pytorch_grad = x.grad.item()
        
        results.append({
            "x": x_val,
            "y": y_val,
            "pytorch_grad": pytorch_grad,
            "expected": 1.0,
            "operation": "add"
        })
    
    return results

def verify_mul_gradient():
    """Verify mul operation: f(x,y) = x * y, df/dx = y"""
    test_cases = [
        (2.0, 3.0),
        (-1.5, 4.0),
        (0.5, -2.0),
        (10.0, 0.1),
    ]
    
    results = []
    for x_val, y_val in test_cases:
        x = torch.tensor(x_val, requires_grad=True)
        y = torch.tensor(y_val, requires_grad=False)
        
        # Forward pass
        z = x * y
        
        # Backward pass
        z.backward()
        
        pytorch_grad = x.grad.item()
        
        results.append({
            "x": x_val,
            "y": y_val,
            "pytorch_grad": pytorch_grad,
            "expected": y_val,
            "operation": "mul"
        })
    
    return results

def verify_recip_gradient():
    """Verify recip operation: f(x) = 1/x, df/dx = -1/x²"""
    test_cases = [0.5, 1.0, 2.0, -1.0, -3.0, 10.0]
    
    results = []
    for x_val in test_cases:
        x = torch.tensor(x_val, requires_grad=True)
        
        # Forward pass
        z = 1.0 / x
        
        # Backward pass
        z.backward()
        
        pytorch_grad = x.grad.item()
        expected = -1.0 / (x_val * x_val)
        
        results.append({
            "x": x_val,
            "pytorch_grad": pytorch_grad,
            "expected": expected,
            "operation": "recip"
        })
    
    return results

def verify_sqrt_gradient():
    """Verify sqrt operation: f(x) = √x, df/dx = 1/(2√x)"""
    test_cases = [0.25, 1.0, 4.0, 9.0, 16.0, 100.0]
    
    results = []
    for x_val in test_cases:
        x = torch.tensor(x_val, requires_grad=True)
        
        # Forward pass
        z = torch.sqrt(x)
        
        # Backward pass
        z.backward()
        
        pytorch_grad = x.grad.item()
        expected = 1.0 / (2.0 * math.sqrt(x_val))
        
        results.append({
            "x": x_val,
            "pytorch_grad": pytorch_grad,
            "expected": expected,
            "operation": "sqrt"
        })
    
    return results

def verify_sin_gradient():
    """Verify sin operation: f(x) = sin(x), df/dx = cos(x)"""
    test_cases = [0.0, math.pi/6, math.pi/4, math.pi/2, math.pi]
    
    results = []
    for x_val in test_cases:
        x = torch.tensor(x_val, requires_grad=True)
        
        # Forward pass
        z = torch.sin(x)
        
        # Backward pass
        z.backward()
        
        pytorch_grad = x.grad.item()
        expected = math.cos(x_val)
        
        results.append({
            "x": x_val,
            "pytorch_grad": pytorch_grad,
            "expected": expected,
            "operation": "sin"
        })
    
    return results

def verify_exp2_gradient():
    """Verify exp2 operation: f(x) = 2^x, df/dx = 2^x * ln(2)"""
    test_cases = [-2.0, -1.0, 0.0, 1.0, 2.0, 3.0]
    
    results = []
    for x_val in test_cases:
        x = torch.tensor(x_val, requires_grad=True)
        
        # Forward pass
        z = torch.pow(2.0, x)
        
        # Backward pass
        z.backward()
        
        pytorch_grad = x.grad.item()
        expected = (2.0 ** x_val) * math.log(2.0)
        
        results.append({
            "x": x_val,
            "pytorch_grad": pytorch_grad,
            "expected": expected,
            "operation": "exp2"
        })
    
    return results

def verify_log2_gradient():
    """Verify log2 operation: f(x) = log₂(x), df/dx = 1/(x*ln(2))"""
    test_cases = [0.5, 1.0, 2.0, 4.0, 8.0, 16.0]
    
    results = []
    for x_val in test_cases:
        x = torch.tensor(x_val, requires_grad=True)
        
        # Forward pass
        z = torch.log2(x)
        
        # Backward pass
        z.backward()
        
        pytorch_grad = x.grad.item()
        expected = 1.0 / (x_val * math.log(2.0))
        
        results.append({
            "x": x_val,
            "pytorch_grad": pytorch_grad,
            "expected": expected,
            "operation": "log2"
        })
    
    return results

def main():
    """Run all gradient verifications and output results"""
    print("=== PyTorch Gradient Verification ===")
    
    all_results = []
    
    print("\n--- Add Operation ---")
    add_results = verify_add_gradient()
    all_results.extend(add_results)
    for r in add_results:
        print(f"  x={r['x']:6.3f}, y={r['y']:6.3f}: pytorch={r['pytorch_grad']:8.6f}, expected={r['expected']:8.6f}")
    
    print("\n--- Mul Operation ---")
    mul_results = verify_mul_gradient()
    all_results.extend(mul_results)
    for r in mul_results:
        print(f"  x={r['x']:6.3f}, y={r['y']:6.3f}: pytorch={r['pytorch_grad']:8.6f}, expected={r['expected']:8.6f}")
    
    print("\n--- Recip Operation ---")
    recip_results = verify_recip_gradient()
    all_results.extend(recip_results)
    for r in recip_results:
        print(f"  x={r['x']:6.3f}: pytorch={r['pytorch_grad']:8.6f}, expected={r['expected']:8.6f}")
    
    print("\n--- Sqrt Operation ---")
    sqrt_results = verify_sqrt_gradient()
    all_results.extend(sqrt_results)
    for r in sqrt_results:
        print(f"  x={r['x']:6.3f}: pytorch={r['pytorch_grad']:8.6f}, expected={r['expected']:8.6f}")
    
    print("\n--- Sin Operation ---")
    sin_results = verify_sin_gradient()
    all_results.extend(sin_results)
    for r in sin_results:
        print(f"  x={r['x']:6.3f}: pytorch={r['pytorch_grad']:8.6f}, expected={r['expected']:8.6f}")
    
    print("\n--- Exp2 Operation ---")
    exp2_results = verify_exp2_gradient()
    all_results.extend(exp2_results)
    for r in exp2_results:
        print(f"  x={r['x']:6.3f}: pytorch={r['pytorch_grad']:8.6f}, expected={r['expected']:8.6f}")
    
    print("\n--- Log2 Operation ---")
    log2_results = verify_log2_gradient()
    all_results.extend(log2_results)
    for r in log2_results:
        print(f"  x={r['x']:6.3f}: pytorch={r['pytorch_grad']:8.6f}, expected={r['expected']:8.6f}")
    
    # Save results to JSON for comparison with Zing
    with open('pytorch_gradients.json', 'w') as f:
        json.dump(all_results, f, indent=2)
    
    print(f"\n✓ Results saved to pytorch_gradients.json ({len(all_results)} test cases)")

if __name__ == "__main__":
    main()