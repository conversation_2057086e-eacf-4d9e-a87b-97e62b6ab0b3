# Zing: Future Features & Advanced Capabilities

This document outlines planned and potential future features for the Zing framework. These features are designed to extend Zing's capabilities for enterprise-grade, high-performance, and specialized use cases.

## 1. ONNX-to-Zing Converter (Core Engine)

### What is it?
This is the core, foundational engine of the entire Zing ecosystem. It's a command-line tool and underlying library that takes a standard `.onnx` model file and compiles it into a standalone, dependency-free Zing binary (either native or WebAssembly). This tool is the critical bridge that allows developers to use models trained in any major framework (PyTorch, TensorFlow, etc.) with Zing.

### How will it be implemented?
The implementation will be a multi-stage process that directly maps the concepts from the ONNX specification to Zing's internal data structures. The core of an ONNX file is a serialized `ModelProto` buffer, which we will parse and traverse.

A new Zig executable will be created, for example `tools/onnx-to-zing.zig`. The logic will be as follows:

1.  **Parse the ONNX `ModelProto`**:
    *   The first step is to parse the `.onnx` file using a Zig Protobuf library (like `zig-protobuf`). This will deserialize the file into a tree of Zig structs representing the ONNX IR.
    *   From the top-level `ModelProto`, we will extract two critical pieces of information:
        *   The `opset_import` version (e.g., `ai.onnx` version 15). This is crucial for correctly mapping ONNX operators to the specific version of their implementation, as operator specifications can change between versions.
        *   The `graph` field, which is a `GraphProto`. This is the main container for the computation graph and will be the focus of the conversion process.

2.  **Translate the `GraphProto` to a `zing.Graph`**:
    *   **Initialize a Symbol Table**: A `std.StringHashMap(*graph.Node)` will be created. This hash map will serve as a symbol table, mapping the string-based names of ONNX tensors (e.g., `"conv1.output"`, `"fc2.weights"`) to the corresponding `*zing.graph.Node` pointers in the live Zing graph.
    *   **Process Initializers (`GraphProto.initializer`)**: We will iterate over the `initializer` list, which contains `TensorProto` messages representing the model's pre-trained weights and biases. For each `TensorProto`:
        1.  Extract its `name` (string), `data_type` (e.g., `FLOAT`, `INT64`), `dims` (the tensor's shape), and its data (`raw_data` or typed fields like `float_data`).
        2.  Use this information to create a `zing.Storage` instance to hold the tensor data.
        3.  Create a constant `zing.graph.Node` that points to this new storage object.
        4.  Populate the symbol table by inserting the new `*graph.Node` with the `TensorProto.name` as the key.
    *   **Process Graph Inputs (`GraphProto.input`)**: We will iterate over the `input` list, which contains `ValueInfoProto` messages. For each input that is *not* already in the symbol table (i.e., it's a true model input, not an initializer), we will create a placeholder "input" node in the Zing graph and add it to the symbol table.
    *   **Process Operator Nodes (`GraphProto.node`)**: This is the core of the conversion. ONNX graphs are topologically sorted, so we can process the nodes in the order they appear in the `node` list. For each `NodeProto`:
        1.  **Look up Input Nodes**: The `NodeProto.input` field is a list of strings. We will use these strings to look up the corresponding `*zing.graph.Node` pointers in our symbol table. These are the inputs for the current operation.
        2.  **Dispatch on `op_type`**: A `switch` statement on the `NodeProto.op_type` string (e.g., `"Conv"`, `"Relu"`, `"Add"`, `"MatMul"`) will dispatch to an operator-specific handler function.
        3.  **Handle Attributes**: Inside the handler (e.g., for `Conv`), we will parse the `NodeProto.attribute` list. These `AttributeProto` messages contain operator-specific parameters. For a convolution, this involves finding attributes named `"strides"`, `"pads"`, etc., and extracting their values.
        4.  **Create Zing Node**: The handler will call the appropriate function from Zing's `tensor_ops.zig` (e.g., `graph.conv2d(...)`), passing the retrieved input nodes and parsed attributes.
        5.  **Register Output Node**: The `tensor_ops` function will return a new `*graph.Node` representing the output of the operation. We will then take the string name from the `NodeProto.output` field and insert this new node into our symbol table, making it available for subsequent nodes.

3.  **Finalize and Compile**:
    *   After iterating through all the nodes, the Zing `Graph` object will be fully constructed.
    *   The final output nodes of the graph can be identified by looking up the names in the `GraphProto.output` list in our symbol table.
    *   This fully-formed `zing.Graph` can then be passed directly to the existing `zing.Compiler` to be compiled into an executable model, requiring no changes to Zing's backend infrastructure.

### Benefits
*   **Enables the Entire Platform:** This converter is the key that unlocks the entire business strategy, making Zing accessible to all ML developers without requiring them to learn Zig.
*   **Frictionless Workflow:** Provides a simple, one-step process to go from a Python-trained model to a high-performance production artifact.
*   **Foundation for Future Features:** All other advanced capabilities, like the web UI and specialized optimizations, will be built on top of this core engine.

### Feasibility
This is the **highest-priority development task**. While a significant undertaking, it is a tractable engineering problem of parsing, mapping, and incremental implementation. The architecture of Zing is already perfectly suited for this compiler-based approach.

## 2. Chunked Weight Loading (Weight Streaming)

### What is it?
For very large models (e.g., LLMs), loading all weights into memory at once during startup can be slow and memory-intensive, making deployment on resource-constrained devices or in serverless environments impractical. Chunked weight loading, or "weight streaming," is a technique where only the parts of the model needed for the current computation are loaded into memory, with other weights being fetched on demand as inference proceeds.

### How could Zing implement this?
This would be an extension of the core compilation pipeline and runtime.
1.  **Custom Model Format:** The `zing-converter` would be extended to produce a special artifact format where weights are stored in separate, indexed chunks or segments rather than being embedded directly in the binary.
2.  **Runtime Support:** The Zing runtime would be modified to support this new format. Instead of having weights in memory, it would contain references to these chunks and load them from disk, a network location, or browser storage (like IndexedDB) as required.
3.  **Memory Mapping:** For native targets, the runtime could use OS-level memory mapping (`mmap`) to efficiently map weight chunks into memory on demand. For WebAssembly, it could use streaming APIs to fetch chunks without blocking the main thread.

### Benefits
*   **Faster Cold Starts:** The initial startup time would be near-instant, as only the model graph and a small fraction of the weights would be loaded.
*   **Dramatically Lower Memory Usage:** A device with 1GB of RAM could run a 10GB model by only ever having a fraction of the weights in memory at any given time.
*   **Enables Multi-Model Serving:** A single server with limited RAM could serve hundreds of different models simultaneously, loading weights only when a specific model is invoked.

### Feasibility
Yes, this is a feasible but **advanced feature**. It would be a significant undertaking best suited for a "Zing Pro" or "Enterprise" offering. The foundational strategy should be to first perfect the deployment of models with fully-loaded weights, then introduce streaming as a powerful, value-added feature for large-model and high-density serving use cases. 