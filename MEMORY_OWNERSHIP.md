# Memory Ownership Patterns in Zing

## Overview

This document describes the simplified memory ownership patterns used in Zing to avoid complex ownership chains and prevent memory leaks.

## Core Principles

1. **Clear Ownership**: Every piece of memory has a single, clear owner
2. **Simple Cleanup**: Cleanup follows a predictable pattern (reverse order of allocation)
3. **Arena for Ephemeral**: Use arena allocators for temporary data with shared lifetimes
4. **Stack for Small**: Use stack allocation for small, common data structures

## Ownership Patterns

### 1. Graph → Arena Pattern
- **Owner**: Graph owns the arena
- **Lifetime**: Arena freed when Graph is destroyed
- **Contents**: Node contents, metadata, consumer lists
- **Cleanup**: Arena deinit automatically frees all contents

```zig
// In graph.zig deinit():
self.consumer_lists.deinit(self.arena.allocator()); // Just hash map structure
self.arena.deinit(); // Frees all arena contents
```

### 2. CompiledGraph → ExecutionStep Pattern  
- **Owner**: CompiledGraph owns ExecutionStep arrays
- **Lifetime**: ExecutionSteps freed when CompiledGraph is destroyed
- **Contents**: Buffer IDs, shapes, kernel metadata
- **Cleanup**: Each ExecutionStep owns its arrays, CompiledGraph frees them

```zig
// In types.zig CompiledGraph.deinit():
for (self.execution_order) |step| {
    allocator.free(step.input_buffers);  // ExecutionStep owns these
    allocator.free(step.output_buffers);
    // ... free shape arrays
}
```

### 3. DataStorage → Metadata Pattern
- **Owner**: DataStorage owns metadata shape arrays
- **Lifetime**: Metadata freed before DataStorage is destroyed
- **Contents**: Shape arrays, stride arrays
- **Cleanup**: Free metadata arrays before hash map

```zig
// In storage.zig DataStorage.deinit():
var iter = self.metadata.iterator();
while (iter.next()) |entry| {
    self.allocator.free(entry.value_ptr.shape);  // Free before hash map
    if (entry.value_ptr.strides) |strides| {
        self.allocator.free(strides);
    }
}
self.metadata.deinit(self.allocator);
```

## Simplified Patterns

### 1. Stack Allocation for Small Data
- Use stack buffers for common small arrays (≤8 elements)
- Copy to heap only when needed for ownership transfer
- Reduces allocation complexity

```zig
// Before (complex):
const input_buffers = try allocator.alloc(BufferId, node.inputs.len);

// After (simple):
var input_buffers_buf: [8]BufferId = undefined;
const input_buffers = try allocator.dupe(BufferId, input_buffers_buf[0..len]);
```

### 2. Consistent dupe() Pattern
- Use `allocator.dupe()` instead of manual allocation + copy
- Creates clear ownership (caller always frees)
- Reduces errdefer complexity

```zig
// Consistent pattern:
const dims = try allocator.dupe(i64, dims_buf[0..shape_len]);
// Caller responsibility to free is clear
```

### 3. Arena for Related Data
- Group related allocations in same arena
- Single cleanup point eliminates complex dependency tracking
- No need for individual frees

## Cleanup Order Rules

1. **Executor → CompiledGraph → Graph** (dependency order)
2. **Arrays before HashMaps** (content before containers)
3. **Metadata before Buffers** (auxiliary before primary)
4. **Children before Parents** (contained before container)

## Anti-Patterns Avoided

❌ **Complex Ownership Chains**: A→B→C→D ownership transfers  
✅ **Simple Ownership**: Clear single owner per allocation

❌ **Manual errdefer Chains**: Multiple errdefer for complex allocations  
✅ **Stack + dupe()**: Simple copy pattern with clear ownership

❌ **Circular References**: Objects that reference each other  
✅ **Unidirectional References**: Clear parent→child relationships

❌ **Scattered Cleanup**: Cleanup code in multiple places  
✅ **Centralized Cleanup**: Single deinit() method per object

## Testing Memory Ownership

- Use `std.testing.allocator` to detect leaks
- Test cleanup order explicitly
- Verify no double-frees or use-after-free
- Check arena allocations are properly scoped

## Result

These patterns eliminate the complex ownership chains while maintaining:
- ✅ Zero memory leaks
- ✅ Clear responsibility boundaries  
- ✅ Predictable cleanup behavior
- ✅ Simple debugging and maintenance