const std = @import("std");
const zing = @import("src/root.zig");

pub fn main() !void {
    const allocator = std.heap.page_allocator;
    
    // Use Session API which is the user-facing API
    var session = try zing.Session.init(allocator);
    defer session.deinit();
    
    std.debug.print("\n=== MATMUL GRADIENT TRACE ===\n", .{});
    std.debug.print("Creating tensors A[2,3] and B[3,4]\n\n", .{});
    
    // Create input tensors using Session API
    const a = try session.tensor(&[_]i64{ 2, 3 }, .{ .requires_grad = true });
    const b = try session.tensor(&[_]i64{ 3, 4 }, .{ .requires_grad = true });
    
    // Get internal graph state before matmul
    const graph = &session.graph;
    const nodes_before = graph.nodes.items.len;
    std.debug.print("Nodes before matmul: {d}\n", .{nodes_before});
    
    // Perform matmul
    std.debug.print("\nPerforming matmul...\n", .{});
    const result = try a.matmul(b);
    
    // Record nodes created
    const nodes_after = graph.nodes.items.len;
    std.debug.print("Nodes after matmul: {d}\n", .{nodes_after});
    std.debug.print("New nodes created: {d}\n\n", .{nodes_after - nodes_before});
    
    // Trace the decomposition
    std.debug.print("=== FORWARD PASS TRACE ===\n", .{});
    for (nodes_before..nodes_after) |i| {
        const node = graph.nodes.items[i];
        const node_id = @as(zing.NodeId, @intCast(i));
        
        std.debug.print("Node {d}: {s}\n", .{ node_id, @tagName(node.op) });
        
        // Get shape if available
        if (node.output_tracker) |tracker_id| {
            if (session.shape_engine.getTracker(tracker_id)) |tracker| {
                std.debug.print("  Shape: [", .{});
                for (tracker.shape, 0..) |dim, j| {
                    if (j > 0) std.debug.print(", ", .{});
                    std.debug.print("{d}", .{dim});
                }
                std.debug.print("]\n", .{});
            }
        }
        
        // Print inputs based on op type
        switch (node.op) {
            .add, .multiply => {
                std.debug.print("  Inputs: {d}, {d}\n", .{ node.data.binary.lhs, node.data.binary.rhs });
            },
            .reduce_sum => {
                std.debug.print("  Input: {d}\n", .{node.data.reduce.input});
                std.debug.print("  Axes: [", .{});
                for (node.data.reduce.axes, 0..) |axis, j| {
                    if (j > 0) std.debug.print(", ", .{});
                    std.debug.print("{d}", .{axis});
                }
                std.debug.print("]\n", .{});
                std.debug.print("  Keep dims: {}\n", .{node.data.reduce.keep_dims});
            },
            .reshape => {
                std.debug.print("  Input: {d}\n", .{node.data.reshape.input});
                std.debug.print("  New shape: [", .{});
                for (node.data.reshape.new_shape, 0..) |dim, j| {
                    if (j > 0) std.debug.print(", ", .{});
                    std.debug.print("{d}", .{dim});
                }
                std.debug.print("]\n", .{});
            },
            else => {},
        }
        std.debug.print("\n", .{});
    }
    
    // Try backward pass using Session API
    std.debug.print("\n=== BACKWARD PASS ===\n", .{});
    
    // Create gradient for output (ones_like)
    const grad_output = try session.onesLike(result);
    std.debug.print("Created gradient output (ones) with shape: ", .{});
    if (grad_output.shape) |shape| {
        std.debug.print("[", .{});
        for (shape, 0..) |dim, i| {
            if (i > 0) std.debug.print(", ", .{});
            std.debug.print("{d}", .{dim});
        }
        std.debug.print("]\n", .{});
    }
    
    // Note: Session API doesn't expose autograd directly, so we can't trace the backward pass
    std.debug.print("\n[Session API doesn't expose autograd directly for tracing]\n", .{});
    
    // Try to show what the decomposition looks like
    std.debug.print("\n=== MATMUL DECOMPOSITION SUMMARY ===\n", .{});
    std.debug.print("1. Input A: [2,3]\n", .{});
    std.debug.print("2. Input B: [3,4]\n", .{});
    std.debug.print("3. Decomposition creates {d} new nodes\n", .{nodes_after - nodes_before});
    std.debug.print("4. Result shape: ", .{});
    if (result.shape) |shape| {
        std.debug.print("[", .{});
        for (shape, 0..) |dim, i| {
            if (i > 0) std.debug.print(", ", .{});
            std.debug.print("{d}", .{dim});
        }
        std.debug.print("]\n", .{});
    }
}