# Topological Sort Memory Management Fix

## Problem
The `graph.zig` module was leaking memory because:
1. `computeTopology()` allocates memory for the topological sort
2. `topologicalSort()` caches this allocated memory but never frees it
3. Tests and autodiff were incorrectly trying to free the cached topology

## Solution
1. **Added cleanup in Graph.deinit()**: Now properly frees cached_topology
2. **Updated invalidateCaches()**: Frees old cached topology before nulling
3. **Simplified autodiff**: Removed duplicate `computeReverseTopoOrder` function, now just iterates backwards through cached topology
4. **Fixed tests**: Removed incorrect `defer free()` calls on cached topology

## Changes Made

### src/graph.zig
- Added cached topology cleanup in `deinit()`
- Updated `invalidateCaches()` to free old cache
- Fixed all tests that were incorrectly freeing cached topology
- Changed `validateIntegrity` to take mutable Graph (needed for topologicalSort)

### src/compiler/passes/autodiff.zig
- Removed `topo_order` field from AutodiffContext
- Removed `computeReverseTopoOrder()` function
- Now directly uses graph's cached topology and iterates backwards
- No more memory allocation for reverse order

## Benefits
1. **No memory leaks**: Proper ownership and cleanup of cached topology
2. **Simpler code**: Removed duplicate functionality in autodiff
3. **Better performance**: No need to allocate reverse topology array
4. **Clearer ownership**: Graph owns and manages its cached topology