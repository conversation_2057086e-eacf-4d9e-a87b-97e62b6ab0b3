[{"x": 1.0, "y": 2.0, "pytorch_grad": 1.0, "expected": 1.0, "operation": "add"}, {"x": -3.5, "y": 1.5, "pytorch_grad": 1.0, "expected": 1.0, "operation": "add"}, {"x": 0.0, "y": 5.0, "pytorch_grad": 1.0, "expected": 1.0, "operation": "add"}, {"x": 100.0, "y": -50.0, "pytorch_grad": 1.0, "expected": 1.0, "operation": "add"}, {"x": 2.0, "y": 3.0, "pytorch_grad": 3.0, "expected": 3.0, "operation": "mul"}, {"x": -1.5, "y": 4.0, "pytorch_grad": 4.0, "expected": 4.0, "operation": "mul"}, {"x": 0.5, "y": -2.0, "pytorch_grad": -2.0, "expected": -2.0, "operation": "mul"}, {"x": 10.0, "y": 0.1, "pytorch_grad": 0.10000000149011612, "expected": 0.1, "operation": "mul"}, {"x": 0.5, "pytorch_grad": -4.0, "expected": -4.0, "operation": "recip"}, {"x": 1.0, "pytorch_grad": -1.0, "expected": -1.0, "operation": "recip"}, {"x": 2.0, "pytorch_grad": -0.25, "expected": -0.25, "operation": "recip"}, {"x": -1.0, "pytorch_grad": -1.0, "expected": -1.0, "operation": "recip"}, {"x": -3.0, "pytorch_grad": -0.111111119389534, "expected": -0.1111111111111111, "operation": "recip"}, {"x": 10.0, "pytorch_grad": -0.010000000707805157, "expected": -0.01, "operation": "recip"}, {"x": 0.25, "pytorch_grad": 1.0, "expected": 1.0, "operation": "sqrt"}, {"x": 1.0, "pytorch_grad": 0.5, "expected": 0.5, "operation": "sqrt"}, {"x": 4.0, "pytorch_grad": 0.25, "expected": 0.25, "operation": "sqrt"}, {"x": 9.0, "pytorch_grad": 0.1666666716337204, "expected": 0.16666666666666666, "operation": "sqrt"}, {"x": 16.0, "pytorch_grad": 0.125, "expected": 0.125, "operation": "sqrt"}, {"x": 100.0, "pytorch_grad": 0.05000000074505806, "expected": 0.05, "operation": "sqrt"}, {"x": 0.0, "pytorch_grad": 1.0, "expected": 1.0, "operation": "sin"}, {"x": 0.5235987755982988, "pytorch_grad": 0.8660253882408142, "expected": 0.8660254037844387, "operation": "sin"}, {"x": 0.7853981633974483, "pytorch_grad": 0.7071067690849304, "expected": 0.7071067811865476, "operation": "sin"}, {"x": 1.5707963267948966, "pytorch_grad": -4.371138828673793e-08, "expected": 6.123233995736766e-17, "operation": "sin"}, {"x": 3.141592653589793, "pytorch_grad": -1.0, "expected": -1.0, "operation": "sin"}, {"x": -2.0, "pytorch_grad": 0.1732867956161499, "expected": 0.17328679513998632, "operation": "exp2"}, {"x": -1.0, "pytorch_grad": 0.3465735912322998, "expected": 0.34657359027997264, "operation": "exp2"}, {"x": 0.0, "pytorch_grad": 0.6931471824645996, "expected": 0.6931471805599453, "operation": "exp2"}, {"x": 1.0, "pytorch_grad": 1.3862943649291992, "expected": 1.3862943611198906, "operation": "exp2"}, {"x": 2.0, "pytorch_grad": 2.7725887298583984, "expected": 2.772588722239781, "operation": "exp2"}, {"x": 3.0, "pytorch_grad": 5.545177459716797, "expected": 5.545177444479562, "operation": "exp2"}, {"x": 0.5, "pytorch_grad": 2.885390043258667, "expected": 2.8853900817779268, "operation": "log2"}, {"x": 1.0, "pytorch_grad": 1.4426950216293335, "expected": 1.4426950408889634, "operation": "log2"}, {"x": 2.0, "pytorch_grad": 0.7213475108146667, "expected": 0.7213475204444817, "operation": "log2"}, {"x": 4.0, "pytorch_grad": 0.3606737554073334, "expected": 0.36067376022224085, "operation": "log2"}, {"x": 8.0, "pytorch_grad": 0.1803368777036667, "expected": 0.18033688011112042, "operation": "log2"}, {"x": 16.0, "pytorch_grad": 0.09016843885183334, "expected": 0.09016844005556021, "operation": "log2"}]