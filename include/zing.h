/**
 * @file zing.h
 * @brief Zing Tensor Library C API
 * 
 * This header provides a C interface to the Zing tensor computation library.
 * The API uses session-owned memory management where all tensors are automatically
 * cleaned up when the session is destroyed.
 * 
 * Example usage:
 * @code
 * #include "zing.h"
 * 
 * int main() {
 *     // Create session
 *     zing_session_t* session = zing_session_create();
 *     
 *     // Create tensors and perform operations
 *     int64_t shape[] = {2, 3};
 *     zing_tensor_t* a = zing_ones(session, shape, 2, ZING_DTYPE_F32);
 *     zing_tensor_t* b = zing_zeros(session, shape, 2, ZING_DTYPE_F32);
 *     zing_tensor_t* result = zing_add(a, b);
 *     
 *     // Get computed data
 *     float data[6];
 *     zing_tensor_get_data_f32(result, data, 6);
 *     
 *     // Cleanup (automatically frees all tensors)
 *     zing_session_destroy(session);
 *     return 0;
 * }
 * @endcode
 */

#ifndef ZING_H
#define ZING_H

#include <stdint.h>
#include <stddef.h>

#ifdef __cplusplus
extern "C" {
#endif

/**
 * @brief Version information
 */
#define ZING_VERSION_MAJOR 0
#define ZING_VERSION_MINOR 2
#define ZING_VERSION_PATCH 0

/**
 * @brief Opaque session handle
 * 
 * A session manages the computation graph lifecycle and owns all tensors
 * created within it. When the session is destroyed, all associated tensors
 * are automatically cleaned up.
 */
typedef struct zing_session_t zing_session_t;

/**
 * @brief Opaque tensor handle
 * 
 * Represents a tensor within a session context. Tensors are automatically
 * managed by their parent session and should not be manually freed.
 */
typedef struct zing_tensor_t zing_tensor_t;

/**
 * @brief Supported data types
 */
typedef enum {
    ZING_DTYPE_F32 = 0,  /**< 32-bit floating point */
    ZING_DTYPE_F64 = 1,  /**< 64-bit floating point */
    ZING_DTYPE_I32 = 2,  /**< 32-bit signed integer */
    ZING_DTYPE_I64 = 3   /**< 64-bit signed integer */
} zing_dtype_t;

/**
 * @brief Backend device types
 */
typedef enum {
    ZING_DEVICE_CPU = 0,   /**< CPU backend */
    ZING_DEVICE_CUDA = 1,  /**< CUDA GPU backend */
    ZING_DEVICE_METAL = 2  /**< Metal GPU backend */
} zing_device_t;

/**
 * @brief Execution modes
 */
typedef enum {
    ZING_MODE_EAGER = 0,  /**< Execute operations immediately when data is requested */
    ZING_MODE_LAZY = 1    /**< Build graph, execute on explicit run() call */
} zing_execution_mode_t;

/**
 * @brief Error codes
 */
typedef enum {
    ZING_OK = 0,                        /**< Success */
    ZING_ERROR_INVALID_SHAPE = 1,       /**< Invalid tensor shape */
    ZING_ERROR_INCOMPATIBLE_TENSORS = 2,/**< Tensors from different sessions or incompatible shapes */
    ZING_ERROR_BACKEND_NOT_AVAILABLE = 3,/**< Requested backend not available */
    ZING_ERROR_OUT_OF_MEMORY = 4,       /**< Memory allocation failed */
    ZING_ERROR_INVALID_DTYPE = 5,       /**< Invalid data type */
    ZING_ERROR_SESSION_NULL = 6,        /**< Session pointer is null */
    ZING_ERROR_TENSOR_NULL = 7,         /**< Tensor pointer is null */
    ZING_ERROR_BUFFER_TOO_SMALL = 8     /**< Output buffer too small */
} zing_error_t;

/* =============================================================================
 * Session Management
 * =============================================================================*/

/**
 * @brief Create a new session with default CPU backend
 * @return New session handle, or NULL on failure
 */
zing_session_t* zing_session_create(void);

/**
 * @brief Create a new session with specified backend
 * @param device Backend device to use
 * @return New session handle, or NULL on failure
 */
zing_session_t* zing_session_create_with_backend(zing_device_t device);

/**
 * @brief Destroy a session and all associated tensors
 * @param session Session to destroy (can be NULL)
 */
void zing_session_destroy(zing_session_t* session);

/**
 * @brief Set the execution mode for a session
 * @param session Session handle
 * @param mode Execution mode to set
 * @return ZING_OK on success, error code on failure
 */
zing_error_t zing_session_set_mode(zing_session_t* session, zing_execution_mode_t mode);

/**
 * @brief Get the current execution mode for a session
 * @param session Session handle
 * @return Current execution mode, or -1 on error
 */
int zing_session_get_mode(zing_session_t* session);

/**
 * @brief Execute the computation graph (for lazy mode)
 * @param session Session handle
 * @return ZING_OK on success, error code on failure
 */
zing_error_t zing_session_run(zing_session_t* session);

/* =============================================================================
 * Tensor Creation
 * =============================================================================*/

/**
 * @brief Create a tensor filled with zeros
 * @param session Session handle
 * @param shape Array of dimension sizes
 * @param ndim Number of dimensions
 * @param dtype Data type
 * @return New tensor handle, or NULL on failure
 */
zing_tensor_t* zing_zeros(zing_session_t* session, const int64_t* shape, size_t ndim, zing_dtype_t dtype);

/**
 * @brief Create a tensor filled with ones
 * @param session Session handle
 * @param shape Array of dimension sizes
 * @param ndim Number of dimensions
 * @param dtype Data type
 * @return New tensor handle, or NULL on failure
 */
zing_tensor_t* zing_ones(zing_session_t* session, const int64_t* shape, size_t ndim, zing_dtype_t dtype);

/**
 * @brief Create a placeholder tensor for later data binding
 * @param session Session handle
 * @param shape Array of dimension sizes
 * @param ndim Number of dimensions
 * @param dtype Data type
 * @return New tensor handle, or NULL on failure
 */
zing_tensor_t* zing_placeholder(zing_session_t* session, const int64_t* shape, size_t ndim, zing_dtype_t dtype);

/* =============================================================================
 * Tensor Operations - Arithmetic
 * =============================================================================*/

/**
 * @brief Element-wise addition
 * @param a First tensor
 * @param b Second tensor
 * @return Result tensor, or NULL on failure
 */
zing_tensor_t* zing_add(zing_tensor_t* a, zing_tensor_t* b);

/**
 * @brief Element-wise multiplication
 * @param a First tensor
 * @param b Second tensor
 * @return Result tensor, or NULL on failure
 */
zing_tensor_t* zing_multiply(zing_tensor_t* a, zing_tensor_t* b);

/**
 * @brief Element-wise subtraction
 * @param a First tensor
 * @param b Second tensor
 * @return Result tensor, or NULL on failure
 */
zing_tensor_t* zing_subtract(zing_tensor_t* a, zing_tensor_t* b);

/**
 * @brief Element-wise division
 * @param a First tensor
 * @param b Second tensor
 * @return Result tensor, or NULL on failure
 */
zing_tensor_t* zing_divide(zing_tensor_t* a, zing_tensor_t* b);

/* =============================================================================
 * Tensor Operations - Linear Algebra
 * =============================================================================*/

/**
 * @brief Matrix multiplication
 * @param a First tensor (matrix)
 * @param b Second tensor (matrix)
 * @return Result tensor, or NULL on failure
 */
zing_tensor_t* zing_matmul(zing_tensor_t* a, zing_tensor_t* b);

/* =============================================================================
 * Tensor Operations - Activations
 * =============================================================================*/

/**
 * @brief ReLU activation function
 * @param input Input tensor
 * @return Result tensor, or NULL on failure
 */
zing_tensor_t* zing_relu(zing_tensor_t* input);

/* =============================================================================
 * Tensor Operations - Shape
 * =============================================================================*/

/**
 * @brief Reshape tensor to new dimensions
 * @param input Input tensor
 * @param new_shape Array of new dimension sizes
 * @param ndim Number of dimensions in new shape
 * @return Result tensor, or NULL on failure
 */
zing_tensor_t* zing_reshape(zing_tensor_t* input, const int64_t* new_shape, size_t ndim);

/* =============================================================================
 * Tensor Information
 * =============================================================================*/

/**
 * @brief Get the number of dimensions (rank) of a tensor
 * @param tensor Tensor handle
 * @return Number of dimensions, or 0 on error
 */
size_t zing_tensor_rank(zing_tensor_t* tensor);

/**
 * @brief Get the total number of elements in a tensor
 * @param tensor Tensor handle
 * @return Total number of elements, or 0 on error
 */
int64_t zing_tensor_size(zing_tensor_t* tensor);

/**
 * @brief Get the shape of a tensor
 * @param tensor Tensor handle
 * @param shape_buffer Buffer to store shape dimensions
 * @param buffer_size Size of the shape buffer
 * @return ZING_OK on success, error code on failure
 */
zing_error_t zing_tensor_shape(zing_tensor_t* tensor, int64_t* shape_buffer, size_t buffer_size);

/**
 * @brief Get the data type of a tensor
 * @param tensor Tensor handle
 * @return Data type, or -1 on error
 */
int zing_tensor_dtype(zing_tensor_t* tensor);

/* =============================================================================
 * Data Access
 * =============================================================================*/

/**
 * @brief Get tensor data as 32-bit floats
 * @param tensor Tensor handle
 * @param buffer Buffer to store data
 * @param buffer_size Size of buffer in number of elements
 * @return ZING_OK on success, error code on failure
 */
zing_error_t zing_tensor_get_data_f32(zing_tensor_t* tensor, float* buffer, size_t buffer_size);

/**
 * @brief Get tensor data as 64-bit floats
 * @param tensor Tensor handle
 * @param buffer Buffer to store data
 * @param buffer_size Size of buffer in number of elements
 * @return ZING_OK on success, error code on failure
 */
zing_error_t zing_tensor_get_data_f64(zing_tensor_t* tensor, double* buffer, size_t buffer_size);

/**
 * @brief Get tensor data as 32-bit integers
 * @param tensor Tensor handle
 * @param buffer Buffer to store data
 * @param buffer_size Size of buffer in number of elements
 * @return ZING_OK on success, error code on failure
 */
zing_error_t zing_tensor_get_data_i32(zing_tensor_t* tensor, int32_t* buffer, size_t buffer_size);

/**
 * @brief Get tensor data as 64-bit integers
 * @param tensor Tensor handle
 * @param buffer Buffer to store data
 * @param buffer_size Size of buffer in number of elements
 * @return ZING_OK on success, error code on failure
 */
zing_error_t zing_tensor_get_data_i64(zing_tensor_t* tensor, int64_t* buffer, size_t buffer_size);

/* =============================================================================
 * Error Handling
 * =============================================================================*/

/**
 * @brief Get the last error code for the current thread
 * @return Error code
 */
zing_error_t zing_get_last_error(void);

/**
 * @brief Get the last error message for the current thread
 * @return Error message string (valid until next error or zing_clear_error)
 */
const char* zing_get_error_message(void);

/**
 * @brief Clear the error state for the current thread
 */
void zing_clear_error(void);

/* =============================================================================
 * Utility Functions
 * =============================================================================*/

/**
 * @brief Get library version string
 * @return Version string (e.g., "0.2.0")
 */
const char* zing_get_version(void);

/**
 * @brief Check if a backend is available
 * @param device Backend device to check
 * @return 1 if available, 0 if not available
 */
int zing_is_backend_available(zing_device_t device);

#ifdef __cplusplus
}
#endif

#endif /* ZING_H */