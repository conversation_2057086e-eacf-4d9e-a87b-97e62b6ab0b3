# Luminal High-Level Operations Summary

This document provides a comprehensive list of high-level operations implemented in Luminal's `hl_ops` directory.

## Binary Operations (`binary.rs`)

### Arithmetic Operations
- **Addition**: `Add` trait implementation for `GraphTensor + GraphTensor`, `GraphTensor + f32`, `f32 + GraphTensor`
- **Subtraction**: `Sub` trait implementation (implemented as `self + -rhs`)
- **Multiplication**: `Mul` trait implementation for element-wise multiplication
- **Division**: `Div` trait implementation (implemented as `self * rhs.recip()`)
- **Remainder/Modulo**: `Rem` trait implementation using `Mod` primitive op
- **Power**: `pow()` method - raises tensor to a power (approximate implementation using `abs().ln().mul(e).exp()`)

### Comparison Operations
- `less_than()` - element-wise less than comparison
- `greater_than()` - element-wise greater than (implemented as `rhs.less_than(self)`)
- `less_than_equal()` - less than or equal (derived from other ops)
- `greater_than_equal()` - greater than or equal (derived from other ops)
- `not_equals()` - not equal comparison
- `equals()` - equality comparison

### Clipping Operations
- `max()` - element-wise maximum between two tensors
- `max_f32()` - element-wise maximum with a scalar
- `min()` - element-wise minimum between two tensors
- `min_f32()` - element-wise minimum with a scalar
- `clip()` - clamp tensor values to range [min, max]

## Unary Operations (`unary.rs`)

### Mathematical Functions
- `neg()` - negation (implemented as `self * -1.0`)
- `log2()` - base 2 logarithm (primitive op)
- `exp2()` - base 2 exponential (primitive op)
- `exp()` - natural exponential (derived from `exp2`)
- `ln()` - natural logarithm (derived from `log2`)
- `recip()` - reciprocal (1/x) (primitive op)
- `sin()` - sine function (primitive op)
- `cos()` - cosine function (implemented as `sin((PI/2) - x)`)
- `square()` - square each element (implemented as `self * self`)
- `sqrt()` - square root (primitive op)
- `abs()` - absolute value (implemented using `relu`)
- `sign()` - sign of each element (-1 or 1)

### Activation Functions
- `relu()` - Rectified Linear Unit (max(0, x))
- `sigmoid()` - sigmoid activation (1 / (1 + exp(-x)))
- `swish()` / `silu()` - Swish/SiLU activation (x * sigmoid(x))
- `tanh()` - hyperbolic tangent
- `leaky_relu()` - Leaky ReLU with configurable negative slope
- `gelu()` - Gaussian Error Linear Unit

### Normalization Operations
- `std_norm()` - normalize to standard deviation of 1.0
- `mean_norm()` - center to mean of 0.0
- `layer_norm()` - layer normalization (combines mean and std normalization)
- `softmax()` - softmax along specified axes
- `log_softmax()` - log softmax along specified axes

### Other Unary Operations
- `argmax()` - get indices of maximum elements along last axis

## Matrix Multiplication (`matmul.rs`)

- `matmul()` - general matrix multiplication supporting:
  - Vector-matrix multiplication (1D × 2D)
  - Matrix-matrix multiplication (2D × 2D)
  - Batch matrix multiplication (3D × 2D, 3D × 3D)
  - 4D tensor multiplication (4D × 2D, 4D × 4D)
  - 5D tensor multiplication (5D × 5D)
- `dot()` - simple dot product of two vectors

## Movement Operations (`movement.rs`)

### Shape Operations
- `permute()` - transpose/swap dimensions
- `expand()` - broadcast tensor along new dimensions
- `expand_to()` - broadcast to specific shape
- `reshape()` - convert to new shape with same number of elements
- `contiguous()` - make tensor memory contiguous

### Slicing and Padding
- `slice()` - take a slice of the tensor
- `slice_along()` - slice along a specific axis
- `excise()` - cut out elements at regular intervals
- `pad()` - pad tensor with zeros
- `pad_along()` - pad along a specific axis

### Advanced Operations
- `pool_last_dim()` - pooling operation along last dimension with kernel, stride, dilation
- `concat_along()` - concatenate tensors along an axis

## Reduction Operations (`reduction.rs`)

- `sum_reduce()` - sum reduction along specified axes
- `max_reduce()` - maximum reduction along specified axes
- `mean_reduce()` - mean reduction along specified axes
- `prod_reduce()` - product reduction along specified axes (implemented as `exp(sum(ln))`)

## Other Operations (`other.rs`)

### Cumulative Operations
- `cumsum_last_dim()` - cumulative sum along last dimension
- `cummax_last_dim()` - cumulative maximum along last dimension
- `cumprod_last_dim()` - cumulative product along last dimension

### Tensor Creation (Graph methods)
- `constant()` - create scalar constant tensor
- `arange()` - create range tensor from 0 to N
- `tril()` - lower triangular matrix of 1s
- `triu()` - upper triangular matrix of 1s

### Special Operations
- `gather()` - gather vectors from matrix using indices
- `print()` - debug print tensor values during execution
- `diff()` - compare tensor values against binary file for testing

## Key Implementation Patterns

1. **Primitive vs Derived Operations**: Many operations are implemented in terms of primitives:
   - Primitives: `Add`, `Mul`, `Mod`, `LessThan`, `Log2`, `Exp2`, `Recip`, `Sin`, `Sqrt`, `SumReduce`, `MaxReduce`, `Contiguous`
   - Derived: Most other operations are built from these primitives

2. **Broadcasting**: Operations automatically handle broadcasting through `expand()` and `expand_to()`

3. **Shape Tracking**: All operations maintain shape information through `ShapeTracker`

4. **Lazy Evaluation**: Operations build a computation graph rather than executing immediately

5. **Operator Overloading**: Standard Rust operators (`+`, `-`, `*`, `/`, `%`) are implemented for ergonomic tensor operations