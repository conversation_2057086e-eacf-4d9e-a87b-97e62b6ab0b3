# Luminal Backend Primitive Operations Analysis

## Overview

This document analyzes how Luminal's backends (CPU, CUDA, Metal) handle primitive operations like add, multiply, etc. The analysis is based on examining the actual implementation code from the Luminal repository.

## Key Findings

### 1. Hybrid Execution Model

Luminal uses a **hybrid approach** for executing primitive operations:

- **Some operations have custom kernels**: Specific operations like matrix multiplication, reductions, and certain optimized patterns have hand-written implementations
- **Most elementwise operations use generic fusion**: Basic operations like add, multiply, sin, exp, etc. are handled through a generic elementwise fusion mechanism

### 2. Elementwise Fusion System

The core innovation in Luminal is its **elementwise fusion compiler** that:

1. **Identifies elementwise operations** marked with the `"elementwise"` custom property
2. **Merges adjacent operations** into single kernels at compile time
3. **Generates backend-specific code** dynamically

#### CUDA Backend Example

For the expression `a + b * c`, the CUDA backend generates:

```cuda
extern "C" __global__ void kernel(
    const float* input0,  // a
    const float* input1,  // b
    const float* input2,  // c
    float* out,
    const int n_elements
) {
    int idx = blockIdx.x * blockDim.x + threadIdx.x;
    if (idx < n_elements) {
        out[idx] = input0[idx] + input1[idx] * input2[idx];
    }
}
```

#### Metal Backend Example

The Metal backend generates similar code:

```metal
kernel void mkernel(
    device float *input0 [[buffer(0)]],  // a
    device float *input1 [[buffer(1)]],  // b
    device float *input2 [[buffer(2)]],  // c
    device float *out [[buffer(3)]],
    device uint& n_elements [[buffer(4)]],
    uint idx [[thread_position_in_grid]]
) {
    if (idx < n_elements) {
        out[idx] = input0[idx] + input1[idx] * input2[idx];
    }
}
```

### 3. CPU Backend Implementation

The CPU backend is **different** from GPU backends:

- **Pattern-based optimization**: Instead of generic fusion, it uses pattern matching (e.g., recognizing `a - b` as `a + (-1 * b)`)
- **Direct execution**: Operations are implemented as regular Rust code with loops
- **Specialized kernels**: Only certain operations like matrix multiplication have optimized implementations

Example CPU execution for subtraction:

```rust
for i in 0..data.len() {
    let lhs = a_data[a_ind.exec_single_var(i)];
    let rhs = b_data[b_ind.exec_single_var(i)];
    data[i] = lhs - rhs;
}
```

### 4. Operation Definition

At the graph level, operations are defined as simple structs:

```rust
// High-level API
impl Add for GraphTensor {
    fn add(self, rhs: GraphTensor) -> Self::Output {
        self.graph()
            .add_op(op::Add)  // Creates Add node in graph
            .input(self.id, 0, self.shape)
            .input(rhs.id, 0, rhs.shape)
            .finish()
    }
}
```

The actual `op::Add` is just a marker - the real implementation happens in the backends.

### 5. Primitive Operations Set

Based on the code analysis, Luminal's core primitive operations include:

**Unary Operations:**
- Log2, Exp2
- Sin, Sqrt, Recip
- Contiguous (memory layout operation)

**Binary Operations:**
- Add, Mul, Mod
- LessThan
- Sub (often decomposed to Add + Mul(-1))

**Reduction Operations:**
- SumReduce
- MaxReduce

**Special Operations:**
- Gather (indexing)
- MatMul (optimized separately)

### 6. Execution Flow

1. **Graph Construction**: High-level operations create nodes in computation graph
2. **Compilation**: Backend-specific compilers process the graph
3. **Fusion Pass**: Elementwise operations are identified and merged
4. **Code Generation**: Fused kernels are generated as strings
5. **JIT Compilation**: GPU backends compile kernels at runtime
6. **Execution**: Kernels are launched with appropriate parameters

### 7. Key Insights

1. **No Pre-written Add/Mul Kernels**: Unlike traditional frameworks, Luminal doesn't have pre-written kernels for basic operations. They're generated on-demand through fusion.

2. **Expression-Based Code Generation**: The fusion system works with symbolic expressions that are converted to backend-specific code strings.

3. **Shape-Aware Fusion**: The system maintains shape tracking through complex transformations, allowing fusion across reshapes and transposes.

4. **Backend Flexibility**: Each backend can choose its execution strategy - GPU backends use fusion while CPU uses pattern matching.

## Comparison with Traditional Approaches

Traditional frameworks (PyTorch, TensorFlow) typically:
- Have pre-written kernels for each operation
- Launch separate kernels for each operation
- Rely on kernel libraries (cuDNN, cuBLAS)

Luminal's approach:
- Generates kernels dynamically based on graph structure
- Aggressively fuses operations to minimize kernel launches
- Trades compilation time for runtime efficiency

## Performance Implications

**Advantages:**
- Reduced memory bandwidth usage (fewer intermediate buffers)
- Fewer kernel launches (reduced overhead)
- Better cache utilization
- Opportunity for algebraic optimizations

**Trade-offs:**
- Compilation overhead (kernels generated at runtime)
- Complexity in debugging generated code
- Limited to operations that fit the elementwise pattern

## Conclusion

Luminal takes an innovative approach to primitive operations by using **dynamic kernel generation** through elementwise fusion rather than pre-written kernels. This allows for aggressive optimization at the cost of compilation complexity. The system is designed to minimize memory traffic and kernel launch overhead, which are often the bottlenecks in deep learning workloads.