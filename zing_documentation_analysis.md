# Zing Documentation Analysis Report

## Executive Summary

This report analyzes the documentation files in `/docs/` for logical inconsistencies, repetitions, structural issues, and violations of idiomatic Zig patterns as defined in CLAUDE.md. The analysis covers the key architectural documents: overview.md, backend.md, compiler.md, graph.md, shape.md, and execution.md.

## 1. Major Logical Inconsistencies

### 1.1 Primitive Operations Count Mismatch

**Issue**: The documentation has inconsistent counts of primitive operations:

- `overview.md` line 15: States "exactly 11 primitive operations"
- `overview.md` line 191-217: Lists 11 compute operations + 2 data operations = 13 total
- `graph.md` line 194: Says "14 primitive operations (11 compute + 2 data + 1 custom)"

**Impact**: Confusion about the actual number of primitives supported.

**Recommendation**: Standardize on 14 total primitives:
- 2 data operations (constant, placeholder)
- 11 compute operations (as listed)
- 1 custom operation (for backend extensions)

### 1.2 Shape Information Location Confusion

**Issue**: Contradictory statements about where shape information lives:

- `graph.md` multiple places: "Graph contains NO shape information"
- `shape.md` line 7: "ShapeTracker lives ONLY within TensorHandles"
- `execution.md` line 139: References "output_shapes" field in ExecutionNode
- `execution.md` line 944-957: Shows resolveOutputShape accessing shape from exec_node

**Impact**: Unclear architecture boundaries between components.

**Recommendation**: Clarify that:
- Graph nodes contain NO shape information
- TensorHandles carry ShapeTrackers
- ExecutionNode in CompiledGraph may contain resolved shapes from compilation

### 1.3 Symbol Resolution Timing

**Issue**: Conflicting information about when symbols are resolved:

- `overview.md` line 257: "Runtime (Resolution)" - implies runtime resolution
- `execution.md` line 504: "per-execution symbol resolution"
- `execution.md` line 652-717: Shows symbols resolved before allocation
- `shape.md` section 8: Discusses runtime shape computation differently

**Impact**: Unclear when shape symbols are actually resolved.

**Recommendation**: Clarify two distinct mechanisms:
1. Input-derived symbols: Resolved from input shapes before execution
2. Computation-derived shapes: Resolved during execution from tensor values

## 2. Significant Repetitions

### 2.1 ShapeTracker Definition

**Issue**: ShapeTracker is defined multiple times:
- `overview.md` lines 544-569
- `shape.md` lines 64-138
- Different field orders and slight variations

**Recommendation**: Define ShapeTracker once in shape.md, reference elsewhere.

### 2.2 SymbolicDim Definition

**Issue**: SymbolicDim defined in:
- `overview.md` lines 489-507
- `shape.md` (referenced but not fully defined)
- `execution.md` lines 506-517

**Recommendation**: Single definition in shape.md.

### 2.3 Error Types

**Issue**: Error types repeated and inconsistent:
- `graph.md` lines 1177-1213: GraphError and GraphValidationError
- `backend.md` lines 579-604: BackendError
- `execution.md` lines 513-546: ExecutionError
- Some overlap without clear boundaries

**Recommendation**: Define error types once per component, with clear scope.

## 3. Data Structure Issues

### 3.1 NodeSpec Inconsistency

**Issue**: NodeSpec defined differently across docs:
- `graph.md` shows tagged union with `data` and `compute`
- `execution.md` references NodeSpec but doesn't match
- Backend operations unclear how they fit

**Recommendation**: Use consistent NodeSpec definition from graph.md.

### 3.2 Missing Type Definitions

**Issue**: Several types referenced but not defined:
- `TrackerId` (used in shape operations)
- `BufferId` (used in memory planning)
- `Device` enum
- `BackendType` enum (appears in kernel cache context)

**Recommendation**: Add missing type definitions or remove references.

### 3.3 Circular Type Dependencies

**Issue**: Types that seem to depend on each other:
- CompiledGraph contains BackendArtifact
- BackendArtifact references backend-specific types
- Backend types reference CompiledGraph

**Recommendation**: Use opaque pointers to break cycles.

## 4. Violations of Idiomatic Zig (CLAUDE.md)

### 4.1 Hidden Allocations

**Issue**: Several functions don't show allocator parameters:
- `shape.md` line 244: `logicalDims` allocates but no allocator shown in signature
- `execution.md`: Multiple helper functions allocate without clear allocator

**Violation**: "Explicit over implicit (especially allocations)"

**Fix**: All allocating functions must take allocator parameter.

### 4.2 Error Context Pattern Not Used

**Issue**: Documentation mentions "simple error handling" but doesn't follow CLAUDE.md diagnostic pattern:
- No context parameters in function signatures
- No structured error context
- Just logging at error sites

**Violation**: CLAUDE.md section "Error Handling Design Choices" specifies diagnostic pattern.

**Fix**: Update error handling examples to use diagnostic pattern:
```zig
pub fn compile(self: *Backend, graph: *const Graph, outputs: []const NodeId, ctx: *CompilationContext) !CompiledGraph
```

### 4.3 Managed vs Unmanaged Containers

**Issue**: Inconsistent use of managed/unmanaged containers:
- `graph.md` correctly uses unmanaged containers
- `execution.md` sometimes shows managed ArrayList without justification

**Violation**: "Unmanaged containers (allocator passed to methods)"

**Fix**: Use unmanaged containers consistently.

### 4.4 Deep Module Hierarchies

**Issue**: Backend implementations suggest deep hierarchies:
- backend_cpu.md, backend_cuda.md, backend_metal.md, backend_wasm.md
- Each might have sub-modules

**Violation**: "Deep module hierarchies (no more than 2 nested folders)"

**Fix**: Flatten structure to backend/cpu.zig, backend/cuda.zig, etc.

## 5. Architectural Violations

### 5.1 Component Boundary Violations

**Issue**: Components accessing data they shouldn't:
- Shape inference mentioned in graph.md (should be tensor.md)
- Execution component doing shape computation (should use pre-computed)
- Backend doing optimization (should be compiler)

**Fix**: Enforce strict boundaries:
- Graph: Structure only
- Shape: Data structures only
- Tensor API: Shape inference
- Compiler: All optimization
- Backend: Code generation only

### 5.2 Ownership Confusion

**Issue**: Unclear ownership in several places:
- Who owns ShapeTracker arrays?
- Who owns symbolic expressions?
- DataStorage lifetime vs Executor lifetime

**Fix**: Document clear ownership:
- Graph owns nodes/edges via arena
- TensorHandle owns ShapeTracker
- Executor owns DataStorage
- CompiledGraph is immutable snapshot

## 6. Missing Error Context

### 6.1 No Diagnostic Context Examples

**Issue**: Despite CLAUDE.md requiring diagnostic pattern, none shown:
```zig
// Should be:
pub fn addNode(self: *Graph, op: ComputeOp, inputs: []const NodeId, dtype: DataType, ctx: *GraphContext) !NodeId

// Not:
pub fn addNode(self: *Graph, op: ComputeOp, inputs: []const NodeId, dtype: DataType) !NodeId
```

**Fix**: Update all examples to show context parameter.

### 6.2 Error Messages Lack Context

**Issue**: Error handling shows generic returns:
```zig
if (!valid) return error.InvalidInput;
```

**Fix**: Show context population:
```zig
if (!valid) {
    ctx.setMessage("Input {} invalid: expected shape {any}, got {any}", .{i, expected, actual});
    return error.InvalidInput;
}
```

## 7. Incomplete Specifications

### 7.1 Runtime Shape Computation

**Issue**: Two different mechanisms described:
1. Symbol resolution from inputs (execution.md)
2. Lazy shape evaluation (shape.md section 8)

Not clear how they interact.

**Fix**: Unify into single coherent system:
- Phase 1: Resolve input-derived symbols
- Phase 2: Compute value-dependent shapes during execution

### 7.2 Buffer Pool Strategy

**Issue**: Execution mentions buffer pools but no complete specification:
- How are pool sizes determined?
- What happens on pool exhaustion?
- How is memory returned to pools?

**Fix**: Add complete buffer pool specification.

### 7.3 Custom Operations

**Issue**: Custom operations mentioned but poorly specified:
- How do they integrate with shape inference?
- How do they handle memory allocation?
- What's the interface contract?

**Fix**: Complete custom operation specification.

## 8. API Inconsistencies

### 8.1 Method Naming

**Issue**: Inconsistent naming patterns:
- `addNode` vs `createNode`
- `getOutput` vs `copyOutput`
- Sometimes `deinit`, sometimes `destroy`

**Fix**: Follow Zig conventions:
- `init`/`deinit` for resource management
- `create`/`destroy` for allocation
- Consistent `get` vs `copy` semantics

### 8.2 Return Types

**Issue**: Inconsistent error union usage:
- Some functions return `!void`
- Others return specific error sets
- Some use anyerror

**Fix**: Use specific error sets for each component.

## 9. Documentation Structure Issues

### 9.1 Missing Sections

**Issue**: Some docs lack important sections:
- No "Thread Safety" in several components
- No "Performance Characteristics" in backend/compiler
- No "Migration Guide" in some components

**Fix**: Standardize documentation structure.

### 9.2 Normative vs Rationale

**Issue**: Inconsistent use of ► markers:
- Some sections marked Normative that are examples
- Some critical specs marked Rationale

**Fix**: Reserve Normative for specifications, Rationale for explanations.

## 10. Code Example Issues

### 10.1 Incomplete Examples

**Issue**: Many examples use undefined functions:
- `buildTransformer` referenced but not shown
- `loadSequences` used without definition
- Import statements missing

**Fix**: Make examples self-contained or clearly mark external dependencies.

### 10.2 Outdated Patterns

**Issue**: Some examples don't follow current Zig 0.14:
- Old-style struct initialization
- Missing `.{}` for empty structs
- Wrong `@as` usage

**Fix**: Update all examples to Zig 0.14 patterns.

## 11. Recommendations Summary

### High Priority Fixes

1. **Standardize primitive operation count** (14 total)
2. **Clarify shape information location** (only in TensorHandles)
3. **Implement diagnostic error pattern** throughout
4. **Fix component boundary violations**
5. **Complete runtime shape computation specification**

### Medium Priority Fixes

1. **Remove redundant type definitions**
2. **Standardize error types per component**
3. **Fix allocation parameter visibility**
4. **Complete buffer pool specification**
5. **Update examples to Zig 0.14**

### Low Priority Fixes

1. **Standardize documentation structure**
2. **Fix method naming consistency**
3. **Add missing thread safety sections**
4. **Clarify normative vs rationale markers**
5. **Make examples self-contained**

## 12. Structural Recommendations

### 12.1 Create types.md

Consolidate all shared type definitions:
- SymbolicDim
- NodeId
- DataType
- Device
- Common error types

### 12.2 Create interfaces.md

Define all component interfaces clearly:
- Graph → Compiler interface
- Compiler → Backend interface
- Backend → Executor interface

### 12.3 Simplify Architecture

Consider removing unnecessary complexity:
- Do we need both TrackerId and ShapeTracker?
- Can we simplify the symbol resolution mechanism?
- Is the lazy shape evaluation necessary for v1?

## Conclusion

The documentation provides a solid foundation but needs consistency improvements and alignment with CLAUDE.md principles. The most critical issues are:

1. Inconsistent primitive operation counts
2. Unclear shape information ownership
3. Missing diagnostic error context pattern
4. Component boundary violations
5. Incomplete runtime shape specifications

Addressing these issues will significantly improve code clarity and maintainability.