# Memory Management Fixes for CompiledGraph

## Summary

This document details the memory management fixes applied to resolve memory leaks and ownership issues in the `CompiledGraph` structure.

## Issues Identified

### 1. Memory Leak in ExecutionNode.inputs
**Problem**: `CompiledGraph.deinit` was only freeing the `execution_order` array itself, not the `inputs` arrays within each `ExecutionNode`.

**Impact**: Memory leak for every ExecutionNode's inputs array.

### 2. Shallow Copy of Node Structures
**Problem**: The `backendCompileImpl` was using `@memcpy` to copy nodes, creating a shallow copy that shared pointers with the original Graph.

**Impact**: 
- Potential use-after-free if Graph is deinitialized before CompiledGraph
- Memory leaks if CompiledGraph doesn't properly free shared data
- Undefined behavior due to double-free attempts

## Solutions Implemented

### 1. Enhanced CompiledGraph.deinit

```zig
pub fn deinit(self: *CompiledGraph, allocator: Allocator) void {
    // Deinit all owned fields
    self.backend_artifact.deinit(allocator);
    self.symbolic_memory_plan.deinit(allocator);
    
    // Free ExecutionNode inputs (deep cleanup)
    for (self.execution_order) |exec_node| {
        allocator.free(exec_node.inputs);
        // Note: output_shapes is assumed to be arena-allocated or shared
    }
    allocator.free(self.execution_order);
    
    // Deep cleanup of nodes if they own their inputs
    for (self.nodes) |node| {
        allocator.free(node.inputs);
        if (node.metadata) |metadata| {
            allocator.destroy(metadata);
        }
        allocator.free(node.outputs);
    }
    allocator.free(self.nodes);
    
    // ... rest of cleanup
}
```

### 2. Deep Copy Implementation

```zig
// Deep copy necessary graph data
const nodes_copy = try allocator.alloc(Node, graph.nodes.items.len);
for (graph.nodes.items, 0..) |node, i| {
    // Deep copy each node
    nodes_copy[i] = Node{
        .id = node.id,
        .spec = node.spec,
        .inputs = try allocator.dupe(NodeId, node.inputs),
        .outputs = try allocator.alloc(Node.OutputInfo, node.outputs.len),
        .is_valid = node.is_valid,
        .metadata = if (node.metadata) |meta| blk: {
            const meta_copy = try allocator.create(NodeMetadata);
            meta_copy.* = meta.*;
            break :blk meta_copy;
        } else null,
    };
    // Deep copy outputs array
    @memcpy(nodes_copy[i].outputs, node.outputs);
}
```

### 3. Error Handling with Cleanup

Added proper error handling to ensure allocated memory is freed on error paths:

```zig
// Track allocated resources for cleanup on error
var allocated_nodes: ?[]Node = null;
defer if (allocated_nodes) |nodes| {
    for (nodes) |node| {
        allocator.free(node.inputs);
        allocator.free(node.outputs);
        if (node.metadata) |meta| allocator.destroy(meta);
    }
    allocator.free(nodes);
};

// ... perform deep copy ...

// Transfer ownership - allocated_nodes will not be freed by defer
allocated_nodes = null;
```

## Ownership Model

### Key Principles

1. **Complete Independence**: CompiledGraph owns all its data independently
2. **Deep Copies Required**: All data from Graph must be deep-copied
3. **No Shared References**: No pointers shared with original Graph
4. **Clean Deinitialization**: All owned memory must be freed

### What Gets Deep Copied

- Node structures and all their fields:
  - `inputs` arrays
  - `outputs` arrays  
  - `metadata` (if present)
- ExecutionNode `inputs` arrays
- Shape information (cloned)
- Symbolic pool (cloned)
- Output node IDs array

## Benefits

1. **Memory Safety**: No use-after-free or double-free errors
2. **Clear Ownership**: CompiledGraph can outlive the Graph that created it
3. **Predictable Cleanup**: All memory properly freed in deinit
4. **Error Resilience**: Proper cleanup even on error paths

## Testing Recommendations

To verify these fixes:

1. **Valgrind/AddressSanitizer**: Run with memory checking tools
2. **Stress Test**: Create and destroy many CompiledGraphs
3. **Lifetime Test**: Destroy Graph before CompiledGraph
4. **Error Path Test**: Force errors during compilation to test cleanup

## Migration Notes

Backends implementing custom compilation must:
1. Perform deep copies of all Node data
2. Handle cleanup in error paths
3. Update deinit to free all owned memory
4. Test with memory checking tools