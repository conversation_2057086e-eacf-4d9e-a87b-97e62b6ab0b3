# Luminal Execution Model Analysis

## Overview

Luminal's execution model is based on a pull-based, topologically-sorted graph execution with sophisticated memory management and backend-specific compilation. The system is designed to minimize memory allocations and enable efficient execution on different hardware backends (CPU, Metal, CUDA).

## Core Components

### 1. Graph Structure

```rust
pub struct Graph {
    /// The store of tensors in the graph. Indexed by node index and output index.
    pub tensors: FxHashMap<(NodeIndex, u8), Tensor>,
    /// A map of dynamic dimensions to concrete dimension sizes
    pub dyn_map: FxHashMap<char, usize>,
    /// Edge weights: (Input index, Output index, Input shape)
    pub graph: StorageGraph,
    /// Tensors marked in this set will not get deleted when the graph is ran
    pub no_delete: FxHashSet<NodeIndex>,
    /// Tensors marked in this set need to be retrieved later
    pub to_retrieve: FxHashMap<NodeIndex, (u8, ShapeTracker)>,
    /// A cached list of nodes to run, source nodes, and view nodes to delete after execution.
    pub(crate) linearized_graph: Option<Vec<(NodeIndex, Vec<(NodeIndex, u8, ShapeTracker)>)>>,
    /// Cached consumers (for execution only)
    consumers_map: Option<FxHashMap<(NodeIndex, u8), usize>>,
}
```

Key aspects:
- Uses petgraph's `StableGraph` for the computation graph
- Tensors are stored separately in a hashmap indexed by `(NodeIndex, output_index)`
- Maintains a linearized (topologically sorted) view for execution
- Tracks consumer counts for automatic memory management

### 2. Memory Model

#### Tensor Representation

```rust
pub struct Tensor {
    data: Box<dyn Data>,
}

pub trait Data: Any + Debug + DynClone {
    fn as_any(&self) -> &dyn Any;
    fn as_any_mut(&mut self) -> &mut dyn Any;
}

pub enum InputTensor<'a> {
    Owned(Tensor),
    Borrowed(&'a Tensor),
}
```

Key features:
- Type-erased data storage using trait objects
- Supports both owned and borrowed tensors during execution
- Different backends use different concrete types (e.g., `Vec<f32>` for CPU, `MetalBuffer` for Metal)

#### Memory Management Strategy

The execution model implements sophisticated memory management:

1. **Reference Counting**: Tracks how many consumers each tensor has
2. **Automatic Deallocation**: When a tensor's consumer count reaches 0, it can be deallocated
3. **Ownership Transfer**: Uses `InputTensor::Owned` vs `InputTensor::Borrowed` to avoid copies

```rust
fn get_source_tensors<'a>(
    no_delete: &'a FxHashSet<NodeIndex>,
    tensors: *mut FxHashMap<(NodeIndex, u8), Tensor>,
    src_ids: &'a [(NodeIndex, u8, ShapeTracker)],
    consumers: &'a FxHashMap<(NodeIndex, u8), usize>,
) -> Vec<(InputTensor<'a>, ShapeTracker)> {
    let mut srcs = vec![];
    for (id, ind, sh) in src_ids {
        let id = &(*id, *ind);
        if consumers[id] == 1 && !no_delete.contains(&id.0) {
            // Last consumer - take ownership to avoid copy
            srcs.push((
                InputTensor::Owned(unsafe { tensors.as_mut().unwrap() }.remove(id).unwrap()),
                *sh,
            ));
        } else {
            // Multiple consumers - borrow
            srcs.push((
                InputTensor::Borrowed(unsafe { tensors.as_ref().unwrap() }.get(id).unwrap()),
                *sh,
            ));
        }
    }
    srcs
}
```

### 3. Execution Flow

#### Standard Execution

```rust
pub fn execute(&mut self) {
    // Topologically sort if needed
    if self.linearized_graph.is_none() {
        self.toposort();
    }
    let mut consumers = self.consumers_map.as_ref().unwrap().clone();
    let mut dim_stack = Vec::new();

    for (node, src_ids) in self.linearized_graph.as_ref().unwrap() {
        // Skip already computed nodes
        if self.tensors.contains_key(&(*node, 0)) {
            continue;
        }

        // Get source tensors (with ownership transfer if possible)
        let mut srcs = get_source_tensors(&self.no_delete, &mut self.tensors, src_ids, &consumers);

        // Resolve dynamic dimensions
        for (_, st) in srcs.iter_mut() {
            st.resolve_global_dyn_dims_stack(&self.dyn_map, &mut dim_stack);
        }

        // Execute the operation
        let tensors = self.graph.node_weight_mut(*node).unwrap().process(srcs);
        for (i, tensor) in tensors.into_iter().enumerate() {
            self.tensors.insert((*node, i as u8), tensor);
        }

        // Update consumer counts
        for (id, ind, _) in src_ids {
            *consumers.get_mut(&(*id, *ind)).unwrap() -= 1;
        }
    }
    self.reset();
}
```

Key execution characteristics:
1. **Pull-based**: Only computes nodes when needed
2. **Lazy**: Skips nodes that already have computed values
3. **Memory efficient**: Deallocates intermediate tensors as soon as possible
4. **Dynamic shape support**: Resolves dynamic dimensions at runtime

### 4. Operator Interface

```rust
pub trait Operator: Debug + as_any::AsAny {
    fn process(&mut self, inp: Vec<(InputTensor, ShapeTracker)>) -> Vec<Tensor>;
    fn custom(&mut self, key: &str, input: Box<dyn Any>) -> Option<Box<dyn Any>> {
        None
    }
}
```

Operators:
- Take inputs with shape tracking information
- Return output tensors
- Can implement custom functionality for backend-specific optimizations

### 5. Backend Compilation

#### CPU Backend Example

The CPU backend implements operator fusion to reduce memory traffic:

```rust
pub struct UnaryFusionCompiler;

impl Compiler for UnaryFusionCompiler {
    fn compile<T: ToIdsMut>(&self, graph: &mut Graph, mut ids: T) {
        // Fuses sequential unary operations into a single operator
        // Reduces memory reads/writes between operations
    }
}
```

#### Metal Backend Command Buffer Optimization

The Metal backend groups operations into command buffers for GPU execution:

```rust
pub struct CommandBufferCompiler;

impl Compiler for CommandBufferCompiler {
    fn compile<T: ToIdsMut>(&self, graph: &mut Graph, _: T) {
        // Groups Metal operations into command buffers
        // Minimizes CPU-GPU synchronization overhead
    }
}
```

### 6. Data Flow Patterns

1. **Tensor Storage**: Tensors are stored in the graph's hashmap, not in the operators
2. **Shape Tracking**: Each tensor flow carries shape information via `ShapeTracker`
3. **View Operations**: Some operations don't copy data but create views with different shape interpretations
4. **Backend Agnostic**: The graph execution is independent of the backend data representation

## Comparison with Zing Architecture

### Similarities
- Graph-based computation model
- Topological sorting for execution order
- Separation of graph structure from execution

### Key Differences

1. **Memory Management**:
   - Luminal: Automatic reference counting and deallocation
   - Zing: Manual memory management with explicit storage

2. **Tensor Storage**:
   - Luminal: Centralized in Graph's hashmap
   - Zing: Distributed across DataStore

3. **Execution Model**:
   - Luminal: Pull-based with lazy evaluation
   - Zing: Push-based with explicit execution

4. **Shape Handling**:
   - Luminal: ShapeTracker passed with each tensor
   - Zing: Shape information stored separately

5. **Backend Integration**:
   - Luminal: Operators handle backend-specific execution
   - Zing: Backend abstraction layer (planned)

## Key Insights for Zing

1. **Memory Efficiency**: Luminal's automatic memory management with ownership transfer is highly efficient
2. **Backend Flexibility**: The `custom()` method on operators allows backend-specific optimizations
3. **Fusion Opportunities**: Compile-time graph transformations enable operation fusion
4. **Dynamic Shapes**: Runtime dimension resolution supports flexible tensor shapes
5. **Zero-Copy Views**: Shape tracking enables efficient view operations without data copies