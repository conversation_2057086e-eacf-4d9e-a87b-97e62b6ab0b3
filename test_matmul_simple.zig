const std = @import("std");
const Graph = @import("src/graph.zig").Graph;
const tensor = @import("src/tensor.zig");
const types = @import("src/types.zig");
const training = @import("src/training.zig");
const compiler = @import("src/compiler.zig");

test "trace matmul gradient decomposition" {
    const allocator = std.testing.allocator;
    var graph = try Graph.init(allocator);
    defer graph.deinit();
    
    std.debug.print("\n=== MATMUL GRADIENT TRACE ===\n", .{});
    
    // Create input tensors
    const a = try tensor.placeholder(&graph, &.{2, 3}, .f32);
    const b = try tensor.placeholder(&graph, &.{3, 4}, .f32);
    
    try graph.markParameter(a.node_id);
    try graph.markParameter(b.node_id);
    
    // Perform matmul
    const nodes_before = graph.nodes.items.len;
    const result = try a.matmul(b);
    const nodes_after = graph.nodes.items.len;
    
    std.debug.print("Matmu<PERSON> created {} nodes\n", .{nodes_after - nodes_before});
    
    // Apply autograd
    var ctx = compiler.PassContext{
        .graph = &graph,
        .allocator = allocator,
    };
    
    std.debug.print("\nApplying autograd...\n", .{});
    training.autograd.applyAutograd(&ctx, &.{a.node_id, b.node_id}, result.node_id) catch |err| {
        std.debug.print("Autograd failed: {}\n", .{err});
        return err;
    };
    
    std.debug.print("Autograd succeeded!\n", .{});
    
    const grad_map = graph.getGradientMap();
    if (grad_map) |gm| {
        if (gm.get(a.node_id)) |grad_a| {
            std.debug.print("Gradient for A: node {}\n", .{grad_a});
        }
        if (gm.get(b.node_id)) |grad_b| {
            std.debug.print("Gradient for B: node {}\n", .{grad_b});
        }
    }
}