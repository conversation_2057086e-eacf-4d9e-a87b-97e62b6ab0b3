# Elementwise Fusion Design for Zing

## Overview

Elementwise fusion is a critical optimization that combines multiple element-by-element operations into a single kernel. This design shows how to implement it idiomatically in Zing while maintaining our clean architecture.

## Why Fusion is Critical for Edge/Web Deployment

For edge and web deployment, fusion provides:
1. **5x-50x speedup** by eliminating kernel launch overhead
2. **3x-5x memory reduction** by removing intermediate buffers
3. **Better cache utilization** on resource-constrained devices
4. **Lower power consumption** through fewer memory accesses

## Implementation Strategy

### 1. Detection Phase (Backend-Specific)

Each backend implements fusion detection during its optimization pass:

```zig
// In cpu_backend.zig
fn findElementwiseChains(
    self: *CpuBackend, 
    graph: *Graph, 
    shape_info: *const HandleShapeMap
) ![]ElementwiseChain {
    var chains = std.ArrayList(ElementwiseChain).init(self.allocator);
    var visited = std.AutoHashSet(NodeId).init(self.allocator);
    defer visited.deinit();
    
    // Traverse graph looking for fusable patterns
    for (graph.nodes.items) |node| {
        if (!node.is_valid or visited.contains(node.id)) continue;
        
        if (isFusableElementwise(node)) {
            // Build chain starting from this node
            var chain = ElementwiseChain{
                .nodes = std.ArrayList(NodeId).init(self.allocator),
                .operations = std.ArrayList(ComputeOp).init(self.allocator),
                .inputs = std.ArrayList(NodeId).init(self.allocator),
            };
            
            try buildChain(graph, node.id, &chain, &visited, shape_info);
            
            if (chain.nodes.items.len >= 2) {  // Only fuse 2+ ops
                try chains.append(chain);
            }
        }
    }
    
    return chains.toOwnedSlice();
}

// Check if operation is fusable elementwise
fn isFusableElementwise(node: *const Node) bool {
    if (node.kind != .compute) return false;
    
    return switch (node.kind.compute) {
        // Unary elementwise ops
        .recip, .sqrt, .sin, .exp2, .log2 => true,
        // Binary elementwise ops  
        .add, .mul, .mod, .less_than => true,
        // Not fusable
        .sum_reduce, .max_reduce, .contiguous => false,
        .custom => false,  // Could check custom op properties
    };
}
```

### 2. Fusion Execution (Idiomatic Zig Pattern)

Use comptime code generation for zero-overhead fusion:

```zig
// CPU Backend: Comptime-generated fused functions
pub const CpuFusedElementwise = struct {
    ops: []const ComputeOp,
    
    pub fn create(allocator: Allocator, ops: []const ComputeOp) !*CpuFusedElementwise {
        const self = try allocator.create(CpuFusedElementwise);
        self.* = .{ .ops = try allocator.dupe(ComputeOp, ops) };
        return self;
    }
    
    pub fn execute(self: *const CpuFusedElementwise, args: KernelArgs) void {
        // Generate optimized execution based on op chain
        const func = comptime blk: {
            break :blk generateFusedFunction(self.ops);
        };
        func(args);
    }
    
    fn generateFusedFunction(comptime ops: []const ComputeOp) fn(KernelArgs) void {
        return struct {
            fn fused(args: KernelArgs) void {
                const n = args.work_size;
                const out = @as([*]f32, @ptrCast(@alignCast(args.outputs[0].ptr)));
                
                // SIMD vectorized loop
                const vec_size = 8;
                var i: usize = 0;
                while (i + vec_size <= n) : (i += vec_size) {
                    var vec: @Vector(vec_size, f32) = undefined;
                    
                    // Load first input
                    const in0 = @as([*]const f32, @ptrCast(@alignCast(args.inputs[0].ptr)));
                    vec = in0[i..][0..vec_size].*;
                    
                    // Apply operations in sequence
                    inline for (ops, 0..) |op, op_idx| {
                        vec = applyOpVectorized(op, vec, args.inputs, i, op_idx);
                    }
                    
                    // Store result
                    out[i..][0..vec_size].* = vec;
                }
                
                // Scalar cleanup
                while (i < n) : (i += 1) {
                    var val = @as([*]const f32, @ptrCast(@alignCast(args.inputs[0].ptr)))[i];
                    inline for (ops, 0..) |op, op_idx| {
                        val = applyOpScalar(op, val, args.inputs, i, op_idx);
                    }
                    out[i] = val;
                }
            }
        }.fused;
    }
    
    fn applyOpVectorized(
        comptime op: ComputeOp,
        vec: @Vector(8, f32),
        inputs: []const []const u8,
        base_idx: usize,
        comptime input_idx: usize
    ) @Vector(8, f32) {
        return switch (op) {
            .add => blk: {
                const in = @as([*]const f32, @ptrCast(@alignCast(inputs[input_idx + 1].ptr)));
                const operand = in[base_idx..][0..8].*;
                break :blk vec + operand;
            },
            .mul => blk: {
                const in = @as([*]const f32, @ptrCast(@alignCast(inputs[input_idx + 1].ptr)));
                const operand = in[base_idx..][0..8].*;
                break :blk vec * operand;
            },
            .recip => @as(@Vector(8, f32), @splat(1.0)) / vec,
            .sqrt => @sqrt(vec),
            .sin => blk: {
                // Vectorized sin approximation
                var result: @Vector(8, f32) = undefined;
                inline for (0..8) |j| {
                    result[j] = @sin(vec[j]);
                }
                break :blk result;
            },
            .exp2 => @exp2(vec),
            .log2 => @log2(vec),
            else => vec,
        };
    }
};
```

### 3. CUDA Backend: Dynamic Kernel Generation

For GPU backends, generate kernels at compile time:

```zig
pub const CudaFusedElementwise = struct {
    ptx_module: cuda.Module,
    kernel: cuda.Function,
    ops: []const ComputeOp,
    
    pub fn create(
        allocator: Allocator,
        device: cuda.Device,
        ops: []const ComputeOp,
        dtype: DataType
    ) !*CudaFusedElementwise {
        const self = try allocator.create(CudaFusedElementwise);
        
        // Generate CUDA kernel code
        const kernel_code = try generateCudaKernel(allocator, ops, dtype);
        defer allocator.free(kernel_code);
        
        // Compile to PTX
        const ptx = try compileToPtx(kernel_code, device);
        self.ptx_module = try device.loadPtx(ptx);
        self.kernel = try self.ptx_module.getFunction("fused_kernel");
        self.ops = try allocator.dupe(ComputeOp, ops);
        
        return self;
    }
    
    fn generateCudaKernel(
        allocator: Allocator,
        ops: []const ComputeOp,
        dtype: DataType
    ) ![]const u8 {
        var code = std.ArrayList(u8).init(allocator);
        const writer = code.writer();
        
        // Kernel signature
        const type_name = getCudaTypeName(dtype);
        try writer.print(
            \\extern "C" __global__ void fused_kernel(
            \\    {s}* output,
            \\    const {s}* input0,
            \\    const int n_elements
        , .{ type_name, type_name });
        
        // Add additional inputs
        var input_count: usize = 1;
        for (ops) |op| {
            if (isBinaryOp(op)) {
                try writer.print(
                    \\,
                    \\    const {s}* input{}
                , .{ type_name, input_count });
                input_count += 1;
            }
        }
        
        try writer.writeAll(
            \\) {
            \\    const int idx = blockIdx.x * blockDim.x + threadIdx.x;
            \\    if (idx >= n_elements) return;
            \\    
            \\    // Load first input
            \\    float acc = input0[idx];
            \\    
        );
        
        // Generate operation chain
        input_count = 1;
        for (ops) |op| {
            switch (op) {
                .add => {
                    try writer.print("    acc = acc + input{}[idx];\n", .{input_count});
                    input_count += 1;
                },
                .mul => {
                    try writer.print("    acc = acc * input{}[idx];\n", .{input_count});
                    input_count += 1;
                },
                .recip => try writer.writeAll("    acc = 1.0f / acc;\n"),
                .sqrt => try writer.writeAll("    acc = sqrtf(acc);\n"),
                .sin => try writer.writeAll("    acc = sinf(acc);\n"),
                .exp2 => try writer.writeAll("    acc = exp2f(acc);\n"),
                .log2 => try writer.writeAll("    acc = log2f(acc);\n"),
                else => {},
            }
        }
        
        try writer.writeAll(
            \\    
            \\    // Store result
            \\    output[idx] = acc;
            \\}
        );
        
        return code.toOwnedSlice();
    }
    
    pub fn execute(self: *const CudaFusedElementwise, args: KernelArgs) void {
        const block_size = 256;
        const grid_size = (args.work_size + block_size - 1) / block_size;
        
        var params: []const ?*anyopaque = &.{
            @ptrCast(args.outputs[0].ptr),
            @ptrCast(args.inputs[0].ptr),
            @ptrCast(&args.work_size),
        };
        
        // Add additional input pointers
        for (args.inputs[1..]) |input| {
            params = params ++ &.{@ptrCast(input.ptr)};
        }
        
        self.kernel.launch(
            grid_size, 1, 1,
            block_size, 1, 1,
            0, null,
            params
        );
    }
};
```

### 4. Integration with Graph Compilation

The key is to replace chains during backend optimization:

```zig
fn optimizeGraph(self: *CudaBackend, graph: *Graph, shape_info: *const HandleShapeMap) !void {
    // Find fusable chains
    const chains = try self.findElementwiseChains(graph, shape_info);
    defer self.allocator.free(chains);
    
    for (chains) |chain| {
        // Create fused operator
        const fused_op = try CudaFusedElementwise.create(
            self.allocator,
            self.device,
            chain.operations.items,
            .f32  // TODO: Get from actual operations
        );
        
        // Wrap in CustomOp
        const custom_op = CustomOp{
            .id = self.next_custom_id,
            .backend_data = @ptrToInt(fused_op),
            .debug_name = try std.fmt.allocPrint(
                self.allocator,
                "fused_elementwise_{}",
                .{self.next_custom_id}
            ),
        };
        self.next_custom_id += 1;
        
        // Create new node
        const new_node_id = try graph.createNode(.{
            .kind = .{ .compute = .{ .custom = custom_op } },
            .inputs = chain.external_inputs,
        });
        
        // Replace chain with single node
        try self.replaceChain(graph, chain, new_node_id);
    }
}
```

### 5. Memory-Efficient Chain Detection

For edge devices, we need memory-efficient algorithms:

```zig
const ElementwiseChain = struct {
    start_node: NodeId,
    end_node: NodeId,
    operations: []ComputeOp,
    external_inputs: []NodeId,
    
    // Memory optimization: store only boundaries
    fn getNodes(self: *const ElementwiseChain, graph: *Graph) []NodeId {
        // Reconstruct chain by traversing from start to end
    }
};

// Use bit sets for efficient visited tracking
fn findChainsMemoryEfficient(graph: *Graph) ![]ElementwiseChain {
    const node_count = graph.nodes.items.len;
    var visited = try std.bit_set.DynamicBitSet.initEmpty(graph.allocator, node_count);
    defer visited.deinit();
    
    // ... rest of algorithm
}
```

## Performance Impact

### Without Fusion
```
x1 = a + b    // Kernel 1: Read a,b, Write x1
x2 = x1 * c   // Kernel 2: Read x1,c, Write x2  
x3 = sin(x2)  // Kernel 3: Read x2, Write x3
x4 = x3 + d   // Kernel 4: Read x3,d, Write x4

Total: 4 kernels, 3 intermediate buffers, 11 memory operations
```

### With Fusion
```
x4 = fused_op(a, b, c, d)  // 1 kernel: Read a,b,c,d, Write x4

Total: 1 kernel, 0 intermediate buffers, 5 memory operations
```

**Results**:
- 4x fewer kernel launches (critical on GPU where each costs ~10μs)
- 2.2x fewer memory operations
- 3x less memory usage
- Better cache utilization

## Edge/Web Specific Optimizations

### 1. Compile-Time Kernel Selection
```zig
// Generate specialized kernels at compile time
const kernels = comptime blk: {
    var result: [256]Kernel = undefined;
    for (common_patterns) |pattern, i| {
        result[i] = generateOptimalKernel(pattern);
    }
    break :blk result;
};
```

### 2. WebAssembly SIMD
```zig
// Use WASM SIMD when available
fn applyOpWasm(comptime op: ComputeOp, vec: v128) v128 {
    return switch (op) {
        .add => wasm_f32x4_add(vec, ...),
        .mul => wasm_f32x4_mul(vec, ...),
        // ...
    };
}
```

### 3. Memory Pooling for Fusion
```zig
// Reuse fusion workspace buffers
const FusionWorkspace = struct {
    temp_buffers: [4][]f32,  // Most chains need <4 temps
    
    pub fn get(self: *FusionWorkspace, size: usize) []f32 {
        // Return pre-allocated buffer if fits
    }
};
```

## Conclusion

Elementwise fusion is absolutely implementable in Zing using idiomatic patterns:
1. **Comptime code generation** for zero-overhead abstractions
2. **Backend-specific optimization passes** that detect and fuse chains
3. **Memory-efficient algorithms** suitable for edge deployment
4. **Type-safe kernel generation** with compile-time guarantees

This design maintains Zing's architectural cleanliness while delivering the 5x-50x performance gains needed for competitive ML inference.