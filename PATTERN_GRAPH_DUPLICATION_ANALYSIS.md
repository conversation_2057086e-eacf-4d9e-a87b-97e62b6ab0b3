# Pattern and Graph Functionality Duplication Analysis

## Executive Summary

There is significant duplication of functionality between `src/graph.zig` and `src/compiler/pattern.zig`, as well as across multiple compiler modules. This duplication violates the DRY principle and creates maintenance issues.

## Identified Duplications

### 1. Operation Classification Functions

The following functions are duplicated across multiple files:

#### `isElementwiseOp(op: types.ComputeOp) bool`
- Defined in:
  - `src/compiler/pattern.zig` (line 411)
  - `src/compiler/validation.zig` (line 179) 
  - `src/compiler/analysis.zig` (line 324)
- Purpose: Identifies if an operation is elementwise (preserves shape, operates element-by-element)

#### `isReductionOp(op: types.ComputeOp) bool`
- Defined in:
  - `src/compiler/pattern.zig` (line 418)
- Purpose: Identifies if an operation is a reduction (reduces dimensions)

#### `isShapePreserving(op: types.ComputeOp) bool`
- Defined in:
  - `src/compiler/pattern.zig` (line 425)
- Purpose: Identifies if an operation preserves tensor shape

### 2. Input Count Validation

#### `getExpectedInputCount(op: ComputeOp) usize`
- Defined in:
  - `src/graph.zig` (line 184) - as a private function
- Used in:
  - `src/compiler/pattern.zig` (line 76) - but not imported, likely causing compilation errors
- Purpose: Returns expected number of inputs for each operation type

### 3. Data Flow Analysis

`src/compiler/analysis.zig` partially duplicates consumer tracking functionality that already exists in `src/graph.zig`:
- Graph already has efficient consumer tracking via `consumer_lists`
- Analysis.zig builds its own consumer maps

## Current Architecture Issues

1. **Broken Import**: `pattern.zig` uses `getExpectedInputCount` without importing it
2. **Triple Duplication**: `isElementwiseOp` is defined in 3 different files
3. **Inconsistent Classification**: Operation classification logic is scattered across modules
4. **Redundant Consumer Tracking**: Graph already tracks consumers efficiently, but compiler rebuilds this

## Recommended Solution

### 1. Create a Centralized Operation Utilities Module

Create `src/ops_util.zig`:

```zig
const std = @import("std");
const types = @import("types.zig");
const ComputeOp = types.ComputeOp;

/// Get expected input count for compute operations
pub fn getExpectedInputCount(op: ComputeOp) usize {
    return switch (op) {
        // Binary operations
        .add, .mul, .mod, .less_than => 2,
        
        // Unary operations  
        .recip, .sqrt, .sin, .exp2, .log2 => 1,
        
        // Reductions (single input + axis in metadata)
        .sum_reduce, .max_reduce => 1,
        
        // Memory operations
        .contiguous => 1,
        
        // Custom operations validate themselves
        .custom => 0, // Skip validation
    };
}

/// Check if operation is elementwise
pub fn isElementwiseOp(op: ComputeOp) bool {
    return switch (op) {
        .add, .mul, .mod, .less_than, .recip, .sqrt, .sin, .exp2, .log2 => true,
        else => false,
    };
}

/// Check if operation is a reduction
pub fn isReductionOp(op: ComputeOp) bool {
    return switch (op) {
        .sum_reduce, .max_reduce => true,
        else => false,
    };
}

/// Check if operation preserves shape
pub fn isShapePreserving(op: ComputeOp) bool {
    return switch (op) {
        .add, .mul, .mod, .less_than, .recip, .sqrt, .sin, .exp2, .log2 => true,
        .sum_reduce, .max_reduce => false,
        .contiguous => true,
        .custom => false,
    };
}

/// Check if operation is unary
pub fn isUnaryOp(op: ComputeOp) bool {
    return switch (op) {
        .recip, .sqrt, .sin, .exp2, .log2 => true,
        else => false,
    };
}

/// Check if operation is binary
pub fn isBinaryOp(op: ComputeOp) bool {
    return switch (op) {
        .add, .mul, .mod, .less_than => true,
        else => false,
    };
}

/// Check if operation is commutative
pub fn isCommutativeOp(op: ComputeOp) bool {
    return switch (op) {
        .add, .mul => true,
        else => false,
    };
}

/// Check if operation is associative
pub fn isAssociativeOp(op: ComputeOp) bool {
    return switch (op) {
        .add, .mul => true,
        else => false,
    };
}
```

### 2. Update Existing Modules

#### Update `src/graph.zig`:
- Remove private `getExpectedInputCount` function
- Import and use `ops_util.getExpectedInputCount`

#### Update `src/compiler/pattern.zig`:
- Remove local definitions of `isElementwiseOp`, `isReductionOp`, `isShapePreserving`
- Import from `ops_util`

#### Update `src/compiler/validation.zig` and `src/compiler/analysis.zig`:
- Remove local `isElementwiseOp` definitions
- Import from `ops_util`

### 3. Enhance Graph Consumer API

Add to `src/graph.zig`:

```zig
/// Check if node is an elementwise operation
pub fn isElementwise(self: *const Graph, node_id: NodeId) bool {
    const node = self.getNode(node_id) orelse return false;
    return switch (node.spec) {
        .compute => |op| ops_util.isElementwiseOp(op),
        .data => false,
    };
}

/// Check if node is a reduction operation
pub fn isReduction(self: *const Graph, node_id: NodeId) bool {
    const node = self.getNode(node_id) orelse return false;
    return switch (node.spec) {
        .compute => |op| ops_util.isReductionOp(op),
        .data => false,
    };
}
```

### 4. Leverage Existing Graph Infrastructure

Update `src/compiler/analysis.zig` to use Graph's existing consumer tracking:
- Remove redundant consumer map building
- Use `graph.iterateConsumers()` and `graph.getConsumerCount()`

## Benefits of This Refactoring

1. **Single Source of Truth**: Operation properties defined once
2. **Maintainability**: Changes to operation properties only need one update
3. **Consistency**: All modules use same classification logic
4. **Extensibility**: Easy to add new operation properties
5. **Performance**: Reuse existing efficient consumer tracking
6. **Correctness**: Fix broken imports and ensure all code compiles

## Implementation Priority

1. **High Priority**: Fix broken import in pattern.zig (compilation error)
2. **High Priority**: Create ops_util.zig and consolidate operation classification
3. **Medium Priority**: Update all modules to use centralized utilities
4. **Low Priority**: Add convenience methods to Graph for operation queries

## Additional Recommendations

1. Consider adding these operation properties to the `ComputeOp` enum itself as methods if Zig patterns allow
2. Add comprehensive tests for operation classification in one place
3. Document operation properties in the types.zig file
4. Consider creating a compiler-specific graph wrapper if compiler needs differ significantly from core graph