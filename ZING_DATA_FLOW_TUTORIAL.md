# Zing Data Flow Tutorial: From Graph Building to Execution

This tutorial provides a comprehensive walkthrough of how data flows through the Zing deep learning framework, from creating tensor operations to executing them on hardware.

## Overview

Zing follows a three-phase architecture:

1. **Graph Building Phase**: Build a computation graph using TensorHandle operations
2. **Compilation Phase**: Optimize the graph and prepare for execution
3. **Execution Phase**: Run the compiled graph with actual tensor data

## Phase 1: Graph Building

### The Core Components

At the heart of graph building are three key structures:

```zig
// The lightweight reference to a tensor in the graph
pub const TensorHandle = struct {
    graph: *Graph,           // Reference to computation graph
    node_id: NodeId,         // ID of the node in the graph
    shape: ShapeTracker,     // Shape information (SINGLE SOURCE OF TRUTH)
    dtype: DataType,         // Data type (f32, i32, etc.)
};

// The computation graph itself
pub const Graph = struct {
    nodes: std.ArrayListUnmanaged(Node),      // All computation nodes
    edges: std.ArrayListUnmanaged(Edge),      // Dependencies between nodes
    consumer_lists: ...                       // Efficient consumer tracking
    symbolic_pool: SymbolicPool,              // For dynamic dimensions
    // NO shape information - that lives in TensorHandle!
};

// A node in the graph
pub const Node = struct {
    id: NodeId,
    spec: NodeSpec,        // Either data source or compute operation
    inputs: []const NodeId, // Dependencies
    outputs: []const OutputInfo, // Just dtype, NO SHAPE!
};
```

### Key Architectural Decision: Shape Ownership

**TensorHandle is the SINGLE SOURCE OF TRUTH for shape information**. The Graph and its Nodes contain NO shape data. This separation ensures:
- Shape transformations (reshape, transpose) don't create new nodes
- View operations are lightweight and don't duplicate data
- Shape inference can happen independently of graph structure

### Creating Tensors

```zig
// Initialize a graph
var graph = try Graph.init(allocator);
defer graph.deinit();

// Create input placeholders
const x = try tensor.placeholder(&graph, &.{2, 3}, .f32);
// This creates:
// 1. A Node in the graph with spec = .data(.placeholder)
// 2. A TensorHandle with shape = ShapeTracker{dims=[2,3], strides=[3,1], offset=0}

// Create a constant
const w = try tensor.ones(&graph, &.{3, 4}, .f32);
// This creates:
// 1. A Node with spec = .data(.constant)
// 2. A TensorHandle with the shape information
```

### Building Operations

When you perform operations, new nodes are added to the graph:

```zig
// Matrix multiplication
const y = try x.matmul(w);
// This:
// 1. Decomposes matmul into primitives (reshape, multiply, sum_reduce)
// 2. Creates multiple nodes in the graph
// 3. Returns a TensorHandle with the output shape

// View operations (NO new nodes!)
const reshaped = try y.reshape(&.{8});
// This:
// 1. Does NOT create a new node
// 2. Returns a new TensorHandle with updated ShapeTracker
// 3. Points to the SAME node_id as y
```

### Operation Decomposition

High-level operations decompose into primitives:

```zig
// matmul(A[2,3], B[3,4]) decomposes to:
// 1. expand A to [2,1,3]
// 2. expand B to [1,3,4]
// 3. element-wise multiply -> [2,3,4]
// 4. sum_reduce along axis 1 -> [2,1,4]
// 5. reshape to [2,4]
```

## Phase 2: Compilation

### Entry Points

Zing provides two compilation APIs:

```zig
// High-level API (recommended)
const compiled = try backends.compileGraph(
    &.{output_tensor},           // Array of TensorHandles to compute
    .{ .backend_name = "cpu" },  // Options
    allocator
);

// Low-level API (for advanced use)
const compiled = try backends.compile(
    graph,           // The graph
    output_nodes,    // Which nodes to compute
    &shape_map,      // Input shapes
    "cpu",           // Backend name
    context,         // Backend context
    allocator
);
```

### Compilation Pipeline

The compilation process follows these steps:

#### 1. Shape Inference

```zig
// Infer all shapes from inputs
var shape_info = try compiler.inferAllShapes(graph, input_shapes, allocator);
// This propagates shapes through the entire graph
```

#### 2. Optimization Passes

The compiler runs three types of optimization passes:

```zig
// Backend-agnostic passes (always run)
- Dead code elimination
- Constant folding
- Algebraic simplification
- Common subexpression elimination

// Backend-conditional passes (adapt to backend)
- Vectorization (adapts SIMD width to backend)
- Operator fusion (respects backend fusion limits)
- Memory layout (optimizes for cache/GPU architecture)

// Backend-specific passes (only for certain backends)
- CPU: Cache blocking, NUMA optimization
- CUDA: Kernel fusion, tensor core mapping
- Metal: Unified memory optimization
```

#### 3. Memory Planning

```zig
// Create symbolic memory plan
const symbolic_plan = try backend.createMemoryPlan(graph, &shape_info, context);

// Resolve to concrete plan (symbols still unresolved)
const memory_plan = try resolveMemoryPlan(symbolic_plan, &shape_info, allocator);
```

#### 4. Kernel Mapping

```zig
// Each backend provides kernels for operations
const kernel_registry = try backend.createKernelRegistry(graph, context, allocator);
// Maps NodeId -> KernelFn
```

#### 5. Execution Plan Generation

```zig
// Pre-build execution order at compile time
const execution_order = try generateExecutionOrder(
    graph,
    &resolved_shapes,
    &kernel_registry,
    allocator
);
```

### The CompiledGraph Structure

```zig
pub const CompiledGraph = struct {
    source_graph: *const Graph,              // Original graph (borrowed)
    output_mapping: []const NodeId,          // Which outputs to compute
    execution_order: []const ExecutionStep,  // Pre-built execution plan
    resolved_shapes: HandleShapeMap,         // All shapes (may have symbols)
    memory_plan: ResolvedMemoryPlan,         // Memory allocation plan
    kernel_registry: KernelRegistry,         // NodeId -> kernel mapping
    backend_type: Device,                    // Target device
    backend_artifact: BackendArtifact,       // Backend-specific data
    symbolic_pool: *const SymbolicPool,      // Symbol definitions (borrowed)
};
```

### ExecutionStep: The Runtime Representation

Each ExecutionStep contains everything needed for efficient execution:

```zig
pub const ExecutionStep = struct {
    node_id: NodeId,                    // Which node this executes
    kernel_fn: KernelFn,                // Direct function pointer
    input_buffers: []const BufferId,    // Pre-resolved buffer IDs
    output_buffers: []const BufferId,   // Pre-resolved buffer IDs
    work_size: usize,                   // Number of elements to process
    custom_data: ?*anyopaque,           // Backend-specific data
    input_shapes: []const Shape,        // Shape info for kernel
    output_shapes: []const Shape,       // Shape info for kernel
    node_metadata: ?*const NodeMetadata, // E.g., reduction axis
};
```

## Phase 3: Execution

### The Executor

The Executor manages runtime state and orchestrates execution:

```zig
pub const Executor = struct {
    compiled_graph: *const CompiledGraph,    // What to execute
    data_storage: ?DataStorage,              // Memory management
    current_symbol_bindings: HashMap(...),   // Runtime symbol values
    inputs_loaded: HashMap(NodeId, TensorView), // Input data
    // ... execution state
};
```

### Execution Flow

#### 1. Initialize Executor

```zig
var executor = try Executor.init(allocator, &compiled_graph, null);
defer executor.deinit();
```

#### 2. Set Inputs

```zig
// Provide input data
try executor.setInput(input_node_id, data_bytes, &.{32, 128}, .f32);
// This also extracts symbol values (e.g., batch_size = 32)
```

#### 3. Symbol Resolution

```zig
// Resolve all symbolic dimensions to concrete values
try executor.resolveSymbolsAndPlanMemory();
// This:
// 1. Validates all symbols have values
// 2. Creates concrete memory plan
// 3. Allocates DataStorage with exact sizes
```

#### 4. Execute Steps

```zig
// Run each step in topological order
for (compiled_graph.execution_order) |exec_step| {
    try executor.executeStep(exec_step);
}
```

#### 5. Retrieve Outputs

```zig
// Get computed results
const output = try executor.getOutput(output_node_id);
// Returns TensorView with data pointer and shape
```

### Kernel Execution

Each kernel receives a KernelArgs structure:

```zig
pub const KernelArgs = struct {
    inputs: []const []const u8,      // Input buffer pointers
    outputs: [][]u8,                  // Output buffer pointers
    work_size: usize,                 // Elements to process
    custom_data: ?*anyopaque,         // Backend-specific
    input_shapes: []const Shape,      // Shape information
    output_shapes: []const Shape,     // Shape information
    node_metadata: ?*const NodeMetadata, // Operation metadata
};
```

Example kernel implementation:

```zig
fn addKernel(args: KernelArgs) void {
    const a = @as([*]const f32, @ptrCast(@alignCast(args.inputs[0].ptr)));
    const b = @as([*]const f32, @ptrCast(@alignCast(args.inputs[1].ptr)));
    const out = @as([*]f32, @ptrCast(@alignCast(args.outputs[0].ptr)));
    
    for (0..args.work_size) |i| {
        out[i] = a[i] + b[i];
    }
}
```

## Data Flow Summary

1. **Graph Building**: 
   - TensorHandles carry shape information
   - Operations create nodes in the graph
   - View operations only update ShapeTracker

2. **Compilation**:
   - Shape inference propagates shapes
   - Optimization passes transform the graph
   - Kernels are mapped to nodes
   - Execution plan is pre-built

3. **Execution**:
   - Symbols are resolved from input shapes
   - Memory is allocated based on concrete sizes
   - Kernels execute in topological order
   - Results are retrieved as TensorViews

## Key Design Principles

1. **Separation of Concerns**: Shape info (TensorHandle) vs computation (Graph)
2. **Pre-computation**: As much work as possible happens at compile time
3. **Zero-copy Views**: Reshape/transpose don't copy data
4. **Backend Flexibility**: Same graph can run on different backends
5. **Symbol Resolution**: Dynamic shapes resolved at runtime, not compile time

## Example: Complete Flow

```zig
// 1. Build graph
var graph = try Graph.init(allocator);
const x = try tensor.placeholder(&graph, &.{-1, 784}, .f32); // -1 = dynamic batch
const w = try tensor.parameter(&graph, &.{784, 10}, .f32);
const y = try x.matmul(w);

// 2. Compile
const compiled = try backends.compileGraph(&.{y}, .{}, allocator);
defer compiled.deinit(allocator);

// 3. Execute
var executor = try Executor.init(allocator, &compiled, param_store);
defer executor.deinit();

try executor.setInput(x.node_id, input_data, &.{32, 784}, .f32);
try executor.run();

const output = try executor.getOutput(y.node_id);
// output.shape = [32, 10], computed from dynamic batch size
```

This architecture enables Zing to:
- Build graphs with symbolic shapes
- Optimize aggressively at compile time
- Execute efficiently with minimal runtime overhead
- Support multiple backends with the same graph