# Batch Matrix Multiplication Implementation Plan for Zing

## Current Limitation
Zing's matmul only supports 2D tensors, which severely limits deep learning capabilities. No batch operations, no transformers, no efficient batched linear layers.

## Luminal's Approach (What We Should Adopt)

Luminal supports matmul for tensors up to 5D using a unified decomposition strategy:

### Supported Patterns:
1. **2D**: `[M,K] @ [K,N] -> [M,N]` (current Zing support)
2. **3D**: 
   - `[B,M,K] @ [K,N] -> [B,M,N]` (batch × matrix)
   - `[B,M,K] @ [B,K,N] -> [B,M,N]` (batch × batch)
3. **4D**:
   - `[A,B,M,K] @ [K,N] -> [A,B,M,N]`
   - `[A,B,M,K] @ [A,B,K,N] -> [A,B,M,N]`
4. **5D**: `[A,B,C,M,K] @ [A,B,C,K,N] -> [A,B,C,M,N]`

### Decomposition Pattern:
All matmul operations decompose to the same primitive sequence:
1. **Expand dimensions** for broadcasting alignment
2. **Permute** (transpose) to align dimensions
3. **Element-wise multiply** (broadcasted)
4. **Sum reduction** along the contraction dimension

### Example: 3D Batch Matmul
```
[B,M,K] @ [K,N] -> [B,M,N]

1. Expand left:  [B,M,K] -> [B,M,K,1] -> [B,M,K,N]
2. Permute right: [K,N] -> [N,K]
3. Expand right: [N,K] -> [1,1,N,K] -> [B,M,N,K]
4. Multiply: [B,M,K,N] * [B,M,N,K] -> [B,M,K,N]
5. Sum over K: [B,M,K,N] -> [B,M,N]
```

## Implementation Steps for Zing

### 1. Update `tensor_ops/linalg.zig`

```zig
pub fn matmul(self: TensorHandle, other: TensorHandle) !TensorHandle {
    const self_shape = self.shape.dims;
    const other_shape = other.shape.dims;
    
    // Handle different dimension combinations
    if (self_shape.len == 2 and other_shape.len == 2) {
        // Current 2D implementation
        return matmul2d(self, other);
    } else if (self_shape.len == 3) {
        if (other_shape.len == 2) {
            // [B,M,K] @ [K,N] -> [B,M,N]
            return matmul3d_2d(self, other);
        } else if (other_shape.len == 3) {
            // [B,M,K] @ [B,K,N] -> [B,M,N]
            return matmul3d_3d(self, other);
        }
    } else if (self_shape.len == 4) {
        // Similar patterns for 4D
    }
    // etc...
}

fn matmul3d_2d(self: TensorHandle, other: TensorHandle) !TensorHandle {
    // Get dimensions
    const B = self.shape.dims[0];
    const M = self.shape.dims[1];
    const K = self.shape.dims[2];
    const N = other.shape.dims[1];
    
    // Decompose following Luminal pattern:
    // 1. Permute other: [K,N] -> [N,K]
    const other_t = try other.transpose(&.{1, 0});
    
    // 2. Expand for broadcasting
    const self_expanded = try self.unsqueeze(2);    // [B,M,K,1]
    const self_broadcast = try self_expanded.expand(&.{B,M,K,N});
    
    const other_expanded = try other_t.unsqueeze(0).unsqueeze(1); // [1,1,N,K]
    const other_broadcast = try other_expanded.expand(&.{B,M,N,K});
    
    // 3. Broadcasted multiply
    const mul = try self_broadcast.mul(other_broadcast.transpose(&.{0,1,3,2}));
    
    // 4. Sum reduce along K dimension
    return mul.sum(2);
}
```

### 2. Required Supporting Operations

Ensure these operations work correctly:
- `unsqueeze` / `expand_dim` - Add dimension of size 1
- `expand` - Broadcast to larger shape
- `transpose` / `permute` - Reorder dimensions
- Multi-dimensional `sum` reduction

### 3. Test Cases

Following Luminal's test patterns:
```zig
test "batch matmul: 3D @ 2D" {
    // [2,3,4] @ [4,5] -> [2,3,5]
}

test "batch matmul: 3D @ 3D" {
    // [2,3,4] @ [2,4,5] -> [2,3,5]
}

test "transformer attention pattern" {
    // Q,K,V all [batch, heads, seq, dim]
    // scores = Q @ K^T -> [batch, heads, seq, seq]
}
```

## Benefits

1. **Enables Modern Deep Learning**: Transformers, efficient batching, etc.
2. **No Special Kernels**: Reuses existing primitives
3. **Consistent Pattern**: Same decomposition for all dimensions
4. **Memory Efficient**: Compiler can optimize the primitive sequence

## Priority

This should be **HIGH PRIORITY** as it's blocking:
- All transformer implementations
- Efficient batched operations
- Most modern deep learning architectures

## Alternative: Direct Kernel Implementation

While decomposition is elegant, a fused kernel could be more efficient:
- Implement `nd_matmul` kernel in CPU/GPU backends
- Avoid intermediate tensors from decomposition
- Better cache locality

But decomposition should work first, optimization can come later.