const std = @import("std");

pub fn build(b: *std.Build) void {
    const target = b.standardTargetOptions(.{});
    const optimize = b.standardOptimizeOption(.{});

    // Single shared build options for the entire project
    const options = b.addOptions();
    options.addOption(bool, "enable_symbolic_extended_ops", true);
    options.addOption(bool, "enable_symbolic_stats", false);
    options.addOption(bool, "enable_debug_logs", b.option(bool, "debug-logs", "Enable debug logging") orelse false);
    const use_accelerate = b.option(bool, "accelerate", "Use Apple Accelerate framework for BLAS operations") orelse false;

    // Create a single build_options module that all other modules will import
    const build_options_module = options.createModule();

    // C compilation flags
    const c_flags = if (target.result.cpu.arch == .aarch64)
        &[_][]const u8{ "-O3", "-mcpu=apple-m1", "-ffast-math" }
    else
        &[_][]const u8{ "-O3", "-march=native", "-ffast-math" };
    const gemm_file = if (use_accelerate and target.result.os.tag == .macos)
        "src/backends/gemm_accelerate.c"
    else
        "src/backends/c_src/gemm_optimized.c";

    // ===== Core Modules =====
    // Define all modules first, then set up their dependencies

    const types_module = b.addModule("types", .{
        .root_source_file = b.path("src/types.zig"),
    });

    const symbolic_module = b.addModule("symbolic", .{
        .root_source_file = b.path("src/symbolic.zig"),
    });

    const shape_module = b.addModule("shape", .{
        .root_source_file = b.path("src/shape.zig"),
    });

    const storage_module = b.addModule("storage", .{
        .root_source_file = b.path("src/storage.zig"),
    });

    const graph_module = b.addModule("graph", .{
        .root_source_file = b.path("src/graph.zig"),
    });

    const execution_module = b.addModule("execution", .{
        .root_source_file = b.path("src/execution.zig"),
    });

    const tensor_module = b.addModule("tensor", .{
        .root_source_file = b.path("src/tensor.zig"),
    });

    const compiler_module = b.addModule("compiler", .{
        .root_source_file = b.path("src/compiler.zig"),
    });

    const backend_types_module = b.addModule("backend_types", .{
        .root_source_file = b.path("src/backend_types.zig"),
    });

    const backends_module = b.addModule("backends", .{
        .root_source_file = b.path("src/backends.zig"),
    });

    const session_module = b.addModule("session", .{
        .root_source_file = b.path("src/session.zig"),
    });

    const c_api_module = b.addModule("c_api", .{
        .root_source_file = b.path("src/c_api.zig"),
    });

    const training_module = b.addModule("training", .{
        .root_source_file = b.path("src/training.zig"),
    });


    // ===== Module Dependencies =====
    // Set up dependencies in dependency order to avoid cycles

    // Types depends on build_options (for debug flags)
    types_module.addImport("build_options", build_options_module);

    // Symbolic depends on types and build_options
    symbolic_module.addImport("types", types_module);
    symbolic_module.addImport("build_options", build_options_module);

    // Shape depends on types and symbolic
    shape_module.addImport("types", types_module);
    shape_module.addImport("symbolic", symbolic_module);

    // Storage depends on types and build_options
    storage_module.addImport("types", types_module);
    storage_module.addImport("build_options", build_options_module);

    // Graph depends on types, symbolic, shape, and build_options
    graph_module.addImport("types", types_module);
    graph_module.addImport("symbolic", symbolic_module);
    graph_module.addImport("shape", shape_module);
    graph_module.addImport("build_options", build_options_module);

    // Execution depends on types, backend_types, graph, shape, symbolic, storage, and build_options
    execution_module.addImport("types", types_module);
    execution_module.addImport("backend_types", backend_types_module);
    execution_module.addImport("graph", graph_module);
    execution_module.addImport("shape", shape_module);
    execution_module.addImport("symbolic", symbolic_module);
    execution_module.addImport("storage", storage_module);
    execution_module.addImport("build_options", build_options_module);

    // Backend types depends on types and build_options
    backend_types_module.addImport("types", types_module);
    backend_types_module.addImport("build_options", build_options_module);

    // Backends depends on types, backend_types, storage, graph, shape, and build_options
    backends_module.addImport("types", types_module);
    backends_module.addImport("backend_types", backend_types_module);
    backends_module.addImport("storage", storage_module);
    backends_module.addImport("graph", graph_module);
    backends_module.addImport("shape", shape_module);
    backends_module.addImport("build_options", build_options_module);

    // Compiler depends on types, graph, shape, symbolic, execution, backend_types, backends, storage, and build_options
    compiler_module.addImport("types", types_module);
    compiler_module.addImport("graph", graph_module);
    compiler_module.addImport("shape", shape_module);
    compiler_module.addImport("symbolic", symbolic_module);
    compiler_module.addImport("execution", execution_module);
    compiler_module.addImport("backend_types", backend_types_module);
    compiler_module.addImport("backends", backends_module);
    compiler_module.addImport("storage", storage_module);
    compiler_module.addImport("build_options", build_options_module);

    // Tensor depends on types, graph, shape, symbolic, storage, and build_options
    tensor_module.addImport("types", types_module);
    tensor_module.addImport("graph", graph_module);
    tensor_module.addImport("shape", shape_module);
    tensor_module.addImport("symbolic", symbolic_module);
    tensor_module.addImport("storage", storage_module);
    tensor_module.addImport("build_options", build_options_module);

    // Session depends on types, graph, tensor, shape, compiler, execution, and build_options
    session_module.addImport("types", types_module);
    session_module.addImport("graph", graph_module);
    session_module.addImport("tensor", tensor_module);
    session_module.addImport("shape", shape_module);
    session_module.addImport("compiler", compiler_module);
    session_module.addImport("execution", execution_module);
    session_module.addImport("build_options", build_options_module);

    // C API depends on session and types
    c_api_module.addImport("session", session_module);
    c_api_module.addImport("types", types_module);

    // Training depends on types, graph, tensor, shape, compiler, execution, storage, and build_options
    training_module.addImport("types", types_module);
    training_module.addImport("graph", graph_module);
    training_module.addImport("tensor", tensor_module);
    training_module.addImport("shape", shape_module);
    training_module.addImport("compiler", compiler_module);
    training_module.addImport("execution", execution_module);
    training_module.addImport("storage", storage_module);
    training_module.addImport("build_options", build_options_module);


    // All module dependencies are now properly configured!

    // ===== Helper function to create test executable =====
    const TestConfig = struct {
        name: []const u8,
        root_source_file: std.Build.LazyPath,
        dependencies: []const struct { name: []const u8, module: *std.Build.Module },
        needs_options: bool = false,
        needs_c_sources: bool = false,
    };

    const createTest = struct {
        fn f(
            b_inner: *std.Build,
            config: TestConfig,
            target_inner: std.Build.ResolvedTarget,
            optimize_inner: std.builtin.OptimizeMode,
            build_options_module_inner: *std.Build.Module,
            gemm_file_inner: []const u8,
            c_flags_inner: []const []const u8,
            use_accelerate_inner: bool,
        ) *std.Build.Step.Compile {
            const test_exe = b_inner.addTest(.{
                .name = config.name,
                .root_source_file = config.root_source_file,
                .target = target_inner,
                .optimize = optimize_inner,
            });

            // Add module dependencies
            for (config.dependencies) |dep| {
                test_exe.root_module.addImport(dep.name, dep.module);
            }

            // Add build options if needed
            if (config.needs_options) {
                test_exe.root_module.addImport("build_options", build_options_module_inner);
            }

            // Add C sources if needed
            if (config.needs_c_sources) {
                test_exe.addCSourceFile(.{ .file = b_inner.path(gemm_file_inner), .flags = c_flags_inner });
                test_exe.linkLibC();
                if (use_accelerate_inner and target_inner.result.os.tag == .macos) {
                    test_exe.linkFramework("Accelerate");
                }
            }

            return test_exe;
        }
    }.f;

    // ===== Unit Tests =====

    const test_configs = [_]TestConfig{
        .{
            .name = "types-tests",
            .root_source_file = b.path("src/types.zig"),
            .dependencies = &.{},
        },
        .{
            .name = "symbolic-tests",
            .root_source_file = b.path("src/symbolic.zig"),
            .dependencies = &.{
                .{ .name = "types", .module = types_module },
            },
            .needs_options = true,
        },
        .{
            .name = "shape-tests",
            .root_source_file = b.path("src/shape.zig"),
            .dependencies = &.{
                .{ .name = "types", .module = types_module },
                .{ .name = "symbolic", .module = symbolic_module },
            },
        },
        .{
            .name = "storage-tests",
            .root_source_file = b.path("src/storage.zig"),
            .dependencies = &.{
                .{ .name = "types", .module = types_module },
            },
            .needs_options = true,
        },
        .{
            .name = "graph-tests",
            .root_source_file = b.path("src/graph.zig"),
            .dependencies = &.{
                .{ .name = "types", .module = types_module },
                .{ .name = "symbolic", .module = symbolic_module },
                .{ .name = "shape", .module = shape_module },
            },
            .needs_options = true,
        },
        .{
            .name = "all-tests",
            .root_source_file = b.path("src/test_runner.zig"),
            .dependencies = &.{
                .{ .name = "types", .module = types_module },
                .{ .name = "backend_types", .module = backend_types_module },
                .{ .name = "graph", .module = graph_module },
                .{ .name = "shape", .module = shape_module },
                .{ .name = "symbolic", .module = symbolic_module },
                .{ .name = "storage", .module = storage_module },
                .{ .name = "backends", .module = backends_module },
                .{ .name = "execution", .module = execution_module },
                .{ .name = "compiler", .module = compiler_module },
                .{ .name = "tensor", .module = tensor_module },
                .{ .name = "session", .module = session_module },
                .{ .name = "training", .module = training_module },
            },
            .needs_options = true,
            .needs_c_sources = true,
        },
        .{
            .name = "tensor-tests",
            .root_source_file = b.path("src/tensor.zig"),
            .dependencies = &.{
                .{ .name = "types", .module = types_module },
                .{ .name = "graph", .module = graph_module },
                .{ .name = "shape", .module = shape_module },
                .{ .name = "symbolic", .module = symbolic_module },
                .{ .name = "storage", .module = storage_module },
            },
            .needs_options = true,
        },
        .{
            .name = "session-tests",
            .root_source_file = b.path("src/session.zig"),
            .dependencies = &.{
                .{ .name = "types", .module = types_module },
                .{ .name = "graph", .module = graph_module },
                .{ .name = "tensor", .module = tensor_module },
                .{ .name = "shape", .module = shape_module },
                .{ .name = "compiler", .module = compiler_module },
                .{ .name = "execution", .module = execution_module },
            },
            .needs_options = true,
        },
        .{
            .name = "backend-types-tests",
            .root_source_file = b.path("src/backend_types.zig"),
            .dependencies = &.{
                .{ .name = "types", .module = types_module },
            },
        },
        .{
            .name = "matmul-simple-tests",
            .root_source_file = b.path("src/tests/test_matmul_simple.zig"),
            .dependencies = &.{
                .{ .name = "types", .module = types_module },
                .{ .name = "graph", .module = graph_module },
                .{ .name = "tensor", .module = tensor_module },
                .{ .name = "training", .module = training_module },
                .{ .name = "compiler", .module = compiler_module },
            },
            .needs_options = true,
        },
        .{
            .name = "autograd-transpose-trace-tests",
            .root_source_file = b.path("src/tests/test_autograd_transpose_trace.zig"),
            .dependencies = &.{
                .{ .name = "types", .module = types_module },
                .{ .name = "graph", .module = graph_module },
                .{ .name = "tensor", .module = tensor_module },
                .{ .name = "training", .module = training_module },
                .{ .name = "compiler", .module = compiler_module },
            },
            .needs_options = true,
        },
        // Compiler and backends tests are included in all-module-tests to avoid module conflicts
    };

    // Create test executables and steps
    const test_step = b.step("test", "Run all tests");

    for (test_configs) |config| {
        const test_exe = createTest(b, config, target, optimize, build_options_module, gemm_file, c_flags, use_accelerate);
        const run_test = b.addRunArtifact(test_exe);

        // Individual test step
        const step_name = b.fmt("test-{s}", .{config.name[0 .. config.name.len - 6]}); // Remove "-tests" suffix
        const step_desc = b.fmt("Run {s}", .{config.name});
        const individual_step = b.step(step_name, step_desc);
        individual_step.dependOn(&run_test.step);

        // Add to main test step
        test_step.dependOn(&run_test.step);
    }

    // ===== Integration Tests =====
    
    // Helper function to add all module imports to a test
    const addAllModules = struct {
        fn f(test_exe: *std.Build.Step.Compile, modules: anytype) void {
            test_exe.root_module.addImport("types", modules.types_module);
            test_exe.root_module.addImport("backend_types", modules.backend_types_module);
            test_exe.root_module.addImport("graph", modules.graph_module);
            test_exe.root_module.addImport("shape", modules.shape_module);
            test_exe.root_module.addImport("symbolic", modules.symbolic_module);
            test_exe.root_module.addImport("tensor", modules.tensor_module);
            test_exe.root_module.addImport("session", modules.session_module);
            test_exe.root_module.addImport("backends", modules.backends_module);
            test_exe.root_module.addImport("execution", modules.execution_module);
            test_exe.root_module.addImport("compiler", modules.compiler_module);
            test_exe.root_module.addImport("storage", modules.storage_module);
            test_exe.root_module.addImport("training", modules.training_module);
            test_exe.root_module.addImport("build_options", modules.build_options_module);
        }
    }.f;

    const modules = .{
        .types_module = types_module,
        .backend_types_module = backend_types_module,
        .graph_module = graph_module,
        .shape_module = shape_module,
        .symbolic_module = symbolic_module,
        .tensor_module = tensor_module,
        .session_module = session_module,
        .backends_module = backends_module,
        .execution_module = execution_module,
        .compiler_module = compiler_module,
        .storage_module = storage_module,
        .training_module = training_module,
        .build_options_module = build_options_module,
    };

    // Integration test configurations
    const integration_test_configs = .{
        // Test clusters for better organization
        .{ "operations-basic-cluster", "src/tests/integration/test_operations_basic_cluster.zig" },
        .{ "operations-advanced-cluster", "src/tests/integration/test_operations_advanced_cluster.zig" },
        .{ "patterns-comprehensive-cluster", "src/tests/integration/test_patterns_comprehensive_cluster.zig" },
        .{ "training-integration-cluster", "src/tests/integration/test_training_integration_cluster.zig" },
        .{ "matmul-gradient-debug", "src/tests/integration/test_matmul_gradient_debug.zig" },
        
        // System integration tests
        .{ "full-integration", "src/tests/integration/test_full_integration.zig" },
        .{ "import-smoke", "src/tests/integration/test_import_smoke.zig" },
        .{ "tensor-ops-integration", "src/tests/integration/test_tensor_ops_integration.zig" },
        .{ "session-api", "src/tests/integration/test_session_api.zig" },
        // Operation category tests
        .{ "activation-ops", "src/tests/integration/test_activation_ops.zig" },
        .{ "arithmetic-ops", "src/tests/integration/test_arithmetic_ops.zig" },
        .{ "broadcast-ops", "src/tests/integration/test_broadcast_ops.zig" },
        .{ "comparison-ops", "src/tests/integration/test_comparison_ops.zig" },
        .{ "comprehensive-ops", "src/tests/integration/test_comprehensive_ops.zig" },
        .{ "linalg-ops", "src/tests/integration/test_linalg_ops.zig" },
        .{ "logical-ops", "src/tests/integration/test_logical_ops.zig" },
        .{ "math-ops", "src/tests/integration/test_math_ops.zig" },
        .{ "memory-ops", "src/tests/integration/test_memory_ops.zig" },
        .{ "primitive-ops", "src/tests/integration/test_primitive_ops.zig" },
        .{ "reduction-ops", "src/tests/integration/test_reduction_ops.zig" },
        .{ "view-ops", "src/tests/integration/test_view_ops.zig" },
    };

    // Pattern test configurations  
    const pattern_test_configs = .{
        .{ "deep-learning-patterns", "src/tests/integration/test_deep_learning_patterns.zig" },
        .{ "realistic-patterns", "src/tests/integration/test_realistic_patterns.zig" },
        .{ "transformer-patterns", "src/tests/integration/test_transformer_patterns.zig" },
    };

    // Create integration tests
    inline for (integration_test_configs) |config| {
        const test_exe = b.addTest(.{
            .name = config[0] ++ "-tests",
            .root_source_file = b.path(config[1]),
            .target = target,
            .optimize = optimize,
        });
        
        addAllModules(test_exe, modules);
        test_exe.addCSourceFile(.{ .file = b.path(gemm_file), .flags = c_flags });
        test_exe.linkLibC();
        if (use_accelerate and target.result.os.tag == .macos) {
            test_exe.linkFramework("Accelerate");
        }
        
        const run_test = b.addRunArtifact(test_exe);
        const test_name = b.fmt("test-{s}", .{config[0]});
        const test_desc = b.fmt("Run {s} tests", .{config[0]});
        const individual_step = b.step(test_name, test_desc);
        individual_step.dependOn(&run_test.step);
        test_step.dependOn(&run_test.step);
    }

    // Create pattern tests
    inline for (pattern_test_configs) |config| {
        const test_exe = b.addTest(.{
            .name = config[0] ++ "-tests",
            .root_source_file = b.path(config[1]),
            .target = target,
            .optimize = optimize,
        });
        
        addAllModules(test_exe, modules);
        test_exe.addCSourceFile(.{ .file = b.path(gemm_file), .flags = c_flags });
        test_exe.linkLibC();
        if (use_accelerate and target.result.os.tag == .macos) {
            test_exe.linkFramework("Accelerate");
        }
        
        const run_test = b.addRunArtifact(test_exe);
        const test_name = b.fmt("test-{s}", .{config[0]});
        const test_desc = b.fmt("Run {s} tests", .{config[0]});
        const individual_step = b.step(test_name, test_desc);
        individual_step.dependOn(&run_test.step);
        test_step.dependOn(&run_test.step);
    }

    // ===== Benchmarks =====

    // Benchmark executable (uses main())
    const benchmark = b.addExecutable(.{
        .name = "benchmark",
        .root_source_file = b.path("src/benchmarks/benchmark.zig"),
        .target = target,
        .optimize = .ReleaseFast,
    });

    // Add all modules to benchmark
    benchmark.root_module.addImport("types", types_module);
    benchmark.root_module.addImport("graph", graph_module);
    benchmark.root_module.addImport("shape", shape_module);
    benchmark.root_module.addImport("symbolic", symbolic_module);
    benchmark.root_module.addImport("tensor", tensor_module);
    benchmark.root_module.addImport("backends", backends_module);
    benchmark.root_module.addImport("execution", execution_module);
    benchmark.root_module.addImport("compiler", compiler_module);
    benchmark.root_module.addImport("storage", storage_module);
    benchmark.root_module.addImport("build_options", build_options_module);
    benchmark.addCSourceFile(.{ .file = b.path(gemm_file), .flags = c_flags });
    benchmark.linkLibC();
    if (use_accelerate and target.result.os.tag == .macos) {
        benchmark.linkFramework("Accelerate");
    }

    b.installArtifact(benchmark);
    const run_benchmark = b.addRunArtifact(benchmark);
    const benchmark_step = b.step("benchmark", "Run benchmarks");
    benchmark_step.dependOn(&run_benchmark.step);

    // Matmul comparison benchmark
    const matmul_bench = b.addExecutable(.{
        .name = "matmul_bench",
        .root_source_file = b.path("src/benchmarks/matmul_comparison.zig"),
        .target = target,
        .optimize = .ReleaseFast,
    });

    matmul_bench.root_module.addImport("types", types_module);
    matmul_bench.root_module.addImport("graph", graph_module);
    matmul_bench.root_module.addImport("shape", shape_module);
    matmul_bench.root_module.addImport("symbolic", symbolic_module);
    matmul_bench.root_module.addImport("tensor", tensor_module);
    matmul_bench.root_module.addImport("backends", backends_module);
    matmul_bench.root_module.addImport("execution", execution_module);
    matmul_bench.root_module.addImport("compiler", compiler_module);
    matmul_bench.root_module.addImport("storage", storage_module);
    matmul_bench.root_module.addImport("build_options", build_options_module);
    matmul_bench.addCSourceFile(.{ .file = b.path(gemm_file), .flags = c_flags });
    matmul_bench.linkLibC();
    if (use_accelerate and target.result.os.tag == .macos) {
        matmul_bench.linkFramework("Accelerate");
    }

    const run_matmul_bench = b.addRunArtifact(matmul_bench);
    const matmul_bench_step = b.step("bench-matmul", "Run matmul benchmark comparison");
    matmul_bench_step.dependOn(&run_matmul_bench.step);

    // ===== C API Library =====

    // C API static library
    const c_api_lib = b.addStaticLibrary(.{
        .name = "zing_c",
        .root_source_file = b.path("src/c_api.zig"),
        .target = target,
        .optimize = optimize,
    });

    // Add all module dependencies to C API library
    c_api_lib.root_module.addImport("session", session_module);
    c_api_lib.root_module.addImport("types", types_module);
    c_api_lib.root_module.addImport("graph", graph_module);
    c_api_lib.root_module.addImport("tensor", tensor_module);
    c_api_lib.root_module.addImport("shape", shape_module);
    c_api_lib.root_module.addImport("symbolic", symbolic_module);
    c_api_lib.root_module.addImport("compiler", compiler_module);
    c_api_lib.root_module.addImport("execution", execution_module);
    c_api_lib.root_module.addImport("backends", backends_module);
    c_api_lib.root_module.addImport("storage", storage_module);
    c_api_lib.root_module.addImport("build_options", build_options_module);
    
    // Add C sources for backend support
    c_api_lib.addCSourceFile(.{ .file = b.path(gemm_file), .flags = c_flags });
    c_api_lib.linkLibC();
    if (use_accelerate and target.result.os.tag == .macos) {
        c_api_lib.linkFramework("Accelerate");
    }

    b.installArtifact(c_api_lib);
    b.installFile("include/zing.h", "include/zing.h");

    // C example executable
    const c_example = b.addExecutable(.{
        .name = "c_example",
        .target = target,
        .optimize = optimize,
    });
    c_example.addCSourceFile(.{ .file = b.path("examples/c_example.c"), .flags = &.{"-std=c99"} });
    c_example.linkLibrary(c_api_lib);
    c_example.linkLibC();
    c_example.addIncludePath(b.path("include"));

    b.installArtifact(c_example);

    const run_c_example = b.addRunArtifact(c_example);
    const c_example_step = b.step("run-c-example", "Run the C API example");
    c_example_step.dependOn(&run_c_example.step);

    // C test executable (pure C test suite)
    const c_test = b.addExecutable(.{
        .name = "c_test",
        .target = target,
        .optimize = optimize,
    });
    c_test.addCSourceFile(.{ .file = b.path("src/tests/c/test_basic.c"), .flags = &.{"-std=c99"} });
    c_test.linkLibrary(c_api_lib);
    c_test.linkLibC();
    c_test.addIncludePath(b.path("include"));

    b.installArtifact(c_test);

    const run_c_test = b.addRunArtifact(c_test);
    const c_test_step = b.step("test-c", "Run the pure C API test suite");
    c_test_step.dependOn(&run_c_test.step);
    
    // Add C test to main test step
    test_step.dependOn(&run_c_test.step);

    // ===== Library Artifacts (for external use) =====

    // Create the main library module using root.zig
    const zing_module = b.addModule("zing", .{
        .root_source_file = b.path("src/root.zig"),
    });

    // Add all dependencies to the zing module
    zing_module.addImport("types", types_module);
    zing_module.addImport("graph", graph_module);
    zing_module.addImport("shape", shape_module);
    zing_module.addImport("symbolic", symbolic_module);
    zing_module.addImport("tensor", tensor_module);
    zing_module.addImport("session", session_module);
    zing_module.addImport("backends", backends_module);
    zing_module.addImport("execution", execution_module);
    zing_module.addImport("compiler", compiler_module);
    zing_module.addImport("storage", storage_module);
    zing_module.addImport("build_options", build_options_module);

    // Build the main library
    const zing_lib = b.addStaticLibrary(.{
        .name = "zing",
        .root_source_file = b.path("src/root.zig"),
        .target = target,
        .optimize = optimize,
    });

    // Add all module imports to the library
    zing_lib.root_module.addImport("types", types_module);
    zing_lib.root_module.addImport("graph", graph_module);
    zing_lib.root_module.addImport("shape", shape_module);
    zing_lib.root_module.addImport("symbolic", symbolic_module);
    zing_lib.root_module.addImport("tensor", tensor_module);
    zing_lib.root_module.addImport("session", session_module);
    zing_lib.root_module.addImport("backends", backends_module);
    zing_lib.root_module.addImport("execution", execution_module);
    zing_lib.root_module.addImport("compiler", compiler_module);
    zing_lib.root_module.addImport("storage", storage_module);
    zing_lib.root_module.addImport("build_options", build_options_module);
    zing_lib.addCSourceFile(.{ .file = b.path(gemm_file), .flags = c_flags });
    zing_lib.linkLibC();
    if (use_accelerate and target.result.os.tag == .macos) {
        zing_lib.linkFramework("Accelerate");
    }

    b.installArtifact(zing_lib);

    // Debug executable
    const debug_autograd = b.addExecutable(.{
        .name = "debug_autograd",
        .root_source_file = b.path("debug_autograd.zig"),
        .target = target,
        .optimize = optimize,
    });
    debug_autograd.root_module.addImport("types", types_module);
    debug_autograd.root_module.addImport("graph", graph_module);
    debug_autograd.root_module.addImport("tensor", tensor_module);
    debug_autograd.root_module.addImport("training", training_module);
    debug_autograd.root_module.addImport("compiler", compiler_module);
    debug_autograd.root_module.addImport("execution", execution_module);
    debug_autograd.root_module.addImport("build_options", build_options_module);
    debug_autograd.addCSourceFile(.{ .file = b.path(gemm_file), .flags = c_flags });
    debug_autograd.linkLibC();
    if (use_accelerate and target.result.os.tag == .macos) {
        debug_autograd.linkFramework("Accelerate");
    }
    
    const run_debug = b.addRunArtifact(debug_autograd);
    const debug_step = b.step("debug-autograd", "Run autograd debug tool");
    debug_step.dependOn(&run_debug.step);

    // Debug helper executable
    const debug_helper = b.addExecutable(.{
        .name = "debug_helper",
        .root_source_file = b.path("debug_helper.zig"),
        .target = target,
        .optimize = optimize,
    });
    debug_helper.root_module.addImport("types", types_module);
    debug_helper.root_module.addImport("graph", graph_module);
    debug_helper.root_module.addImport("tensor", tensor_module);
    debug_helper.root_module.addImport("training", training_module);
    debug_helper.root_module.addImport("compiler", compiler_module);
    debug_helper.root_module.addImport("execution", execution_module);
    debug_helper.root_module.addImport("build_options", build_options_module);
    debug_helper.addCSourceFile(.{ .file = b.path(gemm_file), .flags = c_flags });
    debug_helper.linkLibC();
    if (use_accelerate and target.result.os.tag == .macos) {
        debug_helper.linkFramework("Accelerate");
    }
    
    const run_debug_helper = b.addRunArtifact(debug_helper);
    const debug_helper_step = b.step("debug-helper", "Run executeWithGradients debug tool");
    debug_helper_step.dependOn(&run_debug_helper.step);

    // Pipeline audit executable
    const pipeline_audit = b.addExecutable(.{
        .name = "pipeline_audit",
        .root_source_file = b.path("pipeline_audit.zig"),
        .target = target,
        .optimize = optimize,
    });
    pipeline_audit.root_module.addImport("types", types_module);
    pipeline_audit.root_module.addImport("graph", graph_module);
    pipeline_audit.root_module.addImport("tensor", tensor_module);
    pipeline_audit.root_module.addImport("training", training_module);
    pipeline_audit.root_module.addImport("compiler", compiler_module);
    pipeline_audit.root_module.addImport("execution", execution_module);
    pipeline_audit.root_module.addImport("build_options", build_options_module);
    pipeline_audit.addCSourceFile(.{ .file = b.path(gemm_file), .flags = c_flags });
    pipeline_audit.linkLibC();
    if (use_accelerate and target.result.os.tag == .macos) {
        pipeline_audit.linkFramework("Accelerate");
    }
    
    const run_pipeline_audit = b.addRunArtifact(pipeline_audit);
    const pipeline_audit_step = b.step("pipeline-audit", "Run comprehensive pipeline audit");
    pipeline_audit_step.dependOn(&run_pipeline_audit.step);

    // Debug sin gradient executable
    const debug_sin_gradient = b.addExecutable(.{
        .name = "debug_sin_gradient",
        .root_source_file = b.path("debug_sin_gradient.zig"),
        .target = target,
        .optimize = optimize,
    });
    debug_sin_gradient.root_module.addImport("types", types_module);
    debug_sin_gradient.root_module.addImport("graph", graph_module);
    debug_sin_gradient.root_module.addImport("tensor", tensor_module);
    debug_sin_gradient.root_module.addImport("training", training_module);
    debug_sin_gradient.root_module.addImport("compiler", compiler_module);
    debug_sin_gradient.root_module.addImport("execution", execution_module);
    debug_sin_gradient.root_module.addImport("build_options", build_options_module);
    debug_sin_gradient.addCSourceFile(.{ .file = b.path(gemm_file), .flags = c_flags });
    debug_sin_gradient.linkLibC();
    if (use_accelerate and target.result.os.tag == .macos) {
        debug_sin_gradient.linkFramework("Accelerate");
    }
    
    const run_debug_sin = b.addRunArtifact(debug_sin_gradient);
    const debug_sin_step = b.step("debug-sin", "Debug sin gradient precision issue");
    debug_sin_step.dependOn(&run_debug_sin.step);
}