# Structural Consistency Fixes Between Backend and Execution

## Summary of Changes

This document summarizes the fixes made to ensure structural consistency between `backend.md` and `execution.md` components.

### 1. CompiledGraph Structure Enhancement

**Issue**: The `CompiledGraph` struct in `backend.md` was missing several fields that `execution.md` relies on.

**Fix**: Added the following fields to `CompiledGraph`:
- `nodes: []const Node` - Graph nodes for node information lookup
- `symbolic_shapes: HandleShapeMap` - NodeId to ShapeTracker mapping
- `symbolic_pool: SymbolicPool` - For resolving symbolic expressions
- `kernel_registry: KernelRegistry` - NodeId to kernel function mapping
- `output_nodes: []const NodeId` - List of output node IDs
- `backend_type: Device` - Device type for this compilation
- Renamed `memory_plan` to `symbolic_memory_plan` for consistency

### 2. SymbolicMemoryPlan Enhancement

**Issue**: `SymbolicMemoryPlan` was missing buffer pool definitions that `execution.md` expects.

**Fix**: Added:
- `buffer_pools: []const BufferPoolDefinition` field
- `BufferPoolDefinition` struct with pool sizing and type information
- `deinit` method to properly clean up allocated memory

### 3. ExecutionOrder Type Correction

**Issue**: `CompiledGraph.execution_order` was typed as `[]const NodeId` but `execution.md` expects `[]const ExecutionNode`.

**Fix**: 
- Changed type to `[]const ExecutionNode`
- Added `ExecutionNode` struct definition with all required fields
- Added example showing how backends populate ExecutionNode instances

### 4. New Type Definitions

Added the following types that were referenced but not defined:

#### ExecutionNode
```zig
pub const ExecutionNode = struct {
    node_id: NodeId,
    spec: NodeSpec,
    inputs: []const NodeId,
    output_shapes: []const ShapeTracker,  // Resolved from TensorHandles
    kernel_id: ?u32 = null,              // Optional dense kernel index
    thread_count: u32 = 1,               // For CPU backend threading
};
```

#### KernelRegistry
```zig
pub const KernelRegistry = struct {
    kernels: std.AutoHashMapUnmanaged(NodeId, KernelFn) = .{},
    custom_data: std.AutoHashMapUnmanaged(NodeId, *anyopaque) = .{},
    // Methods for kernel and custom data lookup
};
```

#### BufferPoolDefinition
```zig
pub const BufferPoolDefinition = struct {
    pool_type: PoolType,
    initial_size: usize,
    max_size: usize,
    element_size: usize,
};
```

### 5. Memory Management Improvements

**Issue**: `CompiledGraph.deinit` only handled backend artifact cleanup.

**Fix**: Updated `deinit` to properly clean up all owned fields:
- Backend artifact
- Symbolic memory plan
- All allocated arrays
- Complex structures (hash maps, registries)

### 6. Backend Compilation Example

Added a comprehensive example showing how backends should populate all the new CompiledGraph fields:
- Creating ExecutionNode instances from graph nodes
- Building the kernel registry
- Creating memory plans with buffer pools
- Copying necessary graph data
- Proper error handling with diagnostic context

## Integration Flow

With these changes, the integration flow is now:

1. **Compiler** optimizes the graph and passes it to the backend
2. **Backend** creates a `CompiledGraph` with:
   - ExecutionNode array (not just NodeIds)
   - Full shape information from TensorHandles
   - Kernel registry mapping nodes to executable functions
   - Memory plan with buffer pool definitions
   - All necessary graph data for execution
3. **Executor** receives the CompiledGraph and can:
   - Look up node information
   - Resolve symbolic shapes
   - Access kernels via the registry
   - Initialize buffer pools from the memory plan
   - Execute nodes in the correct order

## Benefits

These fixes ensure:
- Type safety between components
- Clear ownership of data
- Proper memory management
- Efficient execution with pre-compiled information
- Support for both NodeId and array-based kernel lookup
- Dynamic shape handling with proper symbol resolution

## Migration Notes

Backends need to update their compile implementations to:
1. Return ExecutionNode array instead of NodeId array
2. Include buffer pool definitions in memory plans
3. Build and populate the kernel registry
4. Copy necessary graph data (nodes, shapes, symbols)
5. Handle proper cleanup in all error paths