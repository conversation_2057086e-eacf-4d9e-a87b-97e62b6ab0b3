# Zing

<p align="center">
  <img src="logo.webp" alt="Zing Logo" width="200">
</p>

**Zing**: **Z**ig **I**mplementation of **N**eural **G**raphs

## Overview

Zing is a high-performance deep learning framework written in Zig. It provides a flexible and efficient way to build, train, and deploy neural networks using a graph-based computation model.

## Features

- **Pure Zig Implementation**: Built from the ground up in Zig with no external dependencies
- **Graph-Based Computation**: Define neural networks as computational graphs
- **Symbolic Shape Tracking**: Advanced shape inference with support for symbolic dimensions
- **Lazy Graph Building**: Tensors are built lazily with data accessed only when needed
- **Immutable API Design**: Clean, functional-style API with method chaining

## Installation

```bash
# Clone the repository
git clone https://github.com/gmontana/zing.git

# Build the project
cd zing
zig build
```

## Quick Start

```zig
const std = @import("std");
const zing = @import("zing");

pub fn main() !void {
    var gpa = std.heap.GeneralPurposeAllocator(.{}){};
    defer _ = gpa.deinit();
    const allocator = gpa.allocator();
    
    // Initialize Zing core
    const core = try zing.Core.init(allocator);
    defer core.deinit();
    
    // Create a tensor
    const x = try zing.tensor.ones(core, &[_]zing.types.Dim{
        .{ .concrete = 2 },
        .{ .concrete = 3 },
    });
    
    // Perform operations
    const y = try zing.tensor.add(core, x, x);
    
    // Execute the graph
    try core.graph.compile();
    try core.graph.execute();
    
    // Get the result
    const result = try zing.tensor.getValue(core, y);
    std.debug.print("Result: {any}\n", .{result});
}
```

## Architecture

### V2 Architecture (In Development)
Zing V2 features a component-based architecture with clear separation of concerns:

- **SymbolicEngine**: Handles symbolic expressions for shape arithmetic
- **ShapeEngine**: Manages tensor shapes and view transformations  
- **GraphEngine**: Builds and manages the computational graph
- **DataStore**: Stores and manages tensor data
- **Tensor Layer**: High-level tensor operations that decompose to graph primitives
- **Compiler**: Optimizes and transforms the graph for execution

### V1 Reference
The original V1 implementation is preserved in the `src-v1/` directory for reference. V2 design documents are available in the `new_design/` directory.

## Documentation

For more detailed documentation, see the [docs](docs/) directory.

## Contributing

Contributions are welcome! Please feel free to submit a Pull Request.

## License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.
