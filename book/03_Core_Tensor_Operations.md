# Chapter 3: Core Tensor Operations - The Building Blocks of Neural Networks

In the previous chapter, we demystified the tensor, understanding it as a multi-dimensional array—the primary language of deep learning. Now that we know what tensors are, the next logical step is to explore what we can *do* with them. Tensor operations are the verbs to the tensor nouns, allowing us to transform data, extract insights, and ultimately, build and train neural networks.

This chapter will provide a conceptual overview of the fundamental categories of tensor operations. We'll focus on their mathematical behavior and their role in deep learning, without yet diving into how <PERSON><PERSON>, our custom library, will implement them. Think of this as learning the basic arithmetic and grammar rules before constructing complex sentences.

**What You'll Learn in This Chapter:**

*   **Element-wise Operations:** Applying simple mathematical functions independently to each element of a tensor.
*   **Broadcasting:** A powerful mechanism that allows element-wise operations on tensors of compatible but different shapes.
*   **Reduction Operations:** Aggregating values across tensor dimensions (e.g., sum, mean, max).
*   **Matrix Operations:** Key linear algebra operations like matrix multiplication, which are central to many neural network layers.
*   **"View" and Shaping Operations:** Restructuring tensors without changing their underlying data (e.g., reshape, transpose, slice).
*   The importance of these operations in building neural network layers and algorithms.

## 1. Element-wise Operations

Element-wise operations are perhaps the simplest to understand. They involve applying a function to each element of one or more input tensors independently to produce an output tensor of the same shape (or a broadcasted shape, which we'll cover next).

**Characteristics:**

*   **One-to-one (or corresponding element) computation:** The operation at a specific position in the output tensor depends only on the element(s) at the same position in the input tensor(s).
*   **Output shape:** Typically, if all input tensors have the same shape, the output tensor also has that shape.

**Common Examples:**

Let `A` and `B` be tensors of the same shape, and `s` be a scalar.

*   **Addition/Subtraction:**
    *   `C = A + B`  (element-wise: `C[i,j,...] = A[i,j,...] + B[i,j,...]`)
    *   `C = A - s`  (element-wise: `C[i,j,...] = A[i,j,...] - s`)
*   **Multiplication/Division:**
    *   `C = A * B`  (element-wise multiplication, also known as the Hadamard product)
    *   `C = A / s`
*   **Exponentiation/Logarithms/Roots:**
    *   `C = A ** s` (element-wise power)
    *   `C = exp(A)` (element-wise exponential, e^A[i,j,...])
    *   `C = log(A)` (element-wise natural logarithm)
    *   `C = sqrt(A)` (element-wise square root)
*   **Activation Functions (often element-wise):** Many neural network activation functions like ReLU (`max(0, x)`), Sigmoid (`1 / (1 + exp(-x))`), and Tanh are applied element-wise to tensors representing pre-activation values in a layer.
*   **Comparison Operations:**
    *   `C = A > B` (element-wise: `C[i,j,...]` is `true` if `A[i,j,...] > B[i,j,...]`, else `false`). Output is a boolean tensor.

**Why are they important?** Element-wise operations are fundamental for applying basic arithmetic, scaling, shifting values, and implementing many activation functions that introduce non-linearity into neural networks.

## 2. Broadcasting: Smart Element-wise Operations

What happens if you want to perform an element-wise operation, like addition, between a matrix (rank-2 tensor) and a vector (rank-1 tensor)? For example, adding a bias vector to each row of a matrix. This is where **broadcasting** comes in.

Broadcasting is a set of rules that allows libraries to perform element-wise operations on tensors that don't have exactly the same shape, provided their shapes are *compatible*.

**Conceptual Rules for Broadcasting (simplified):**

1.  **Align Dimensions:** If the tensors have different ranks (number of dimensions), prepend dimensions of size 1 to the shape of the lower-rank tensor until their ranks are equal.
    *   Example: Matrix `A` shape `[3, 4]`, Vector `B` shape `[4]`. `B` becomes `[1, 4]` conceptually.
2.  **Check Compatibility:** For each dimension, compare the sizes. Two dimensions are compatible if:
    *   They are equal, OR
    *   One of them is 1.
3.  **Stretch Dimensions of Size 1:** If a dimension in one tensor is 1 and the corresponding dimension in the other tensor is larger, the tensor with size 1 is conceptually "stretched" or "copied" along that dimension to match the larger size.

**Example:**

Matrix `M` (shape `[2, 3]`): `[[1, 2, 3], [4, 5, 6]]`
Vector `V` (shape `[3]`): `[10, 20, 30]`

Operation: `M + V`

1.  **Align Ranks:** `V` (shape `[3]`) becomes conceptually `V'` (shape `[1, 3]`) -> `[[10, 20, 30]]`
2.  **Check Compatibility & Stretch:**
    *   Dimension 0: `M` is 2, `V'` is 1. Compatible. `V'` is stretched to become `[[10, 20, 30], [10, 20, 30]]`.
    *   Dimension 1: `M` is 3, `V'` is 3. Compatible.
3.  **Perform Element-wise Addition:**
    `[[1, 2, 3], [4, 5, 6]] + [[10, 20, 30], [10, 20, 30]] = [[11, 22, 33], [14, 25, 36]]`

**Why is it important?** Broadcasting avoids unnecessary explicit copying of data in memory, making code cleaner and often more memory-efficient. It's extremely common when adding bias vectors, scaling by constants, or working with features of different dimensionalities.

## 3. Reduction Operations

Reduction operations (also called aggregations) reduce the number of dimensions of a tensor by performing an operation across one or more of its axes.

**Characteristics:**

*   **Summarizes data:** Calculates a summary statistic (like sum, mean, min, max) along specified dimension(s).
*   **Reduces rank (typically):** If you reduce along an axis, that axis is often removed or becomes size 1 (depending on the library and whether `keepdims` is true).

**Common Examples:**

Let `A` be a tensor.

*   **`sum(A, axis=k)`:** Sums all elements along axis `k`.
    *   If `A` has shape `[d0, d1, d2]` and we sum along `axis=1`, the output might have shape `[d0, d2]`.
*   **`mean(A, axis=k)`:** Calculates the arithmetic mean along axis `k`.
*   **`max(A, axis=k)` / `min(A, axis=k)`:** Finds the maximum/minimum value along axis `k`.
*   **`argmax(A, axis=k)` / `argmin(A, axis=k)`:** Finds the *index* of the maximum/minimum value along axis `k`.
*   **Global Reductions:** If no axis is specified, the operation is typically performed over all elements of the tensor, resulting in a scalar.
    *   `sum(A)`: Sum of all elements in `A`.

**Why are they important?** Reductions are vital for calculating loss functions (e.g., summing or averaging errors), implementing pooling layers in CNNs (e.g., max pooling), calculating statistics, and in attention mechanisms (e.g., softmax which involves a sum for normalization).

## 4. Matrix Operations (Linear Algebra)

These are fundamental operations from linear algebra, particularly important for how neural networks transform data between layers.

**Common Examples:**

*   **Matrix Multiplication (Dot Product for higher dimensions):** `C = A @ B` or `C = matmul(A, B)`
    *   If `A` is a tensor of shape `[..., M, K]` and `B` is a tensor of shape `[..., K, N]`, the output `C` will have shape `[..., M, N]`.
    *   The "..." denotes optional leading batch dimensions, which must be broadcastable.
    *   This is the workhorse of fully connected (dense) layers in neural networks: `output = activation(input @ weights + bias)`.
*   **Transpose:** `A_transposed = transpose(A, axes_permutation)`
    *   Rearranges the dimensions of a tensor. For a 2D matrix, it swaps rows and columns.
    *   Often used to align shapes for matrix multiplication or other operations.
*   **Vector Dot Product:** A special case of matrix multiplication if vectors are treated as `[1, K]` and `[K, 1]` matrices.

**Why are they important?** Matrix multiplication is the core computation in many neural network layers, including dense layers and recurrent layers. It allows the network to learn linear transformations of its input data. Transposition is a utility for aligning data correctly for these multiplications.

## 5. "View" and Shaping Operations

These operations change the way a tensor is *viewed* or interpreted, often without actually moving or copying the underlying data in memory. They manipulate the tensor's shape and strides.

**Common Examples:**

*   **`reshape(A, new_shape)`:** Changes the shape of the tensor while keeping the same total number of elements.
    *   Example: Reshaping a tensor of shape `[2, 6]` (12 elements) to `[3, 4]` (also 12 elements).
    *   The underlying data in memory typically remains unchanged; only the metadata (shape, strides) describing how to access it is modified.
*   **`transpose(A, axes_permutation)`:** (Already mentioned in matrix ops, but it's fundamentally a view operation).
*   **`slice(A, starts, ends, steps)`:** Extracts a sub-tensor (a "slice") from a larger tensor.
    *   Example: Taking rows 5-10 and columns 2-4 from a matrix.
*   **`concatenate([A, B, ...], axis=k)`:** Joins a sequence of tensors along an existing axis.
*   **`stack([A, B, ...], axis=k)`:** Joins a sequence of tensors along a *new* axis (increasing the rank).
*   **`squeeze(A, axis=k)`:** Removes dimensions of size 1.
*   **`unsqueeze(A, axis=k)`:** Adds a new dimension of size 1 at a specified position.
*   **`flatten(A)`:** Collapses all dimensions (or a range of dimensions) into a single dimension.

**Why are they important?** Shaping operations are crucial for preparing data for different types of layers (e.g., flattening the output of a convolutional layer before feeding it to a dense layer), aligning tensors for broadcasting or concatenation, and generally manipulating the structure of data as it flows through a network.

## The Big Picture: Operations as Lego Bricks

Think of these categories of tensor operations as the Lego bricks of deep learning. By combining them in various sequences and structures, we can build:

*   **Neural Network Layers:** A dense layer is essentially a matrix multiplication followed by a bias addition (broadcasting) and an element-wise activation function.
*   **Loss Functions:** Often involve element-wise subtractions (to find errors), squaring (element-wise), and then reductions (sum or mean).
*   **Data Preprocessing Pipelines:** Reshaping, slicing, and concatenating tensors to prepare raw data for a model.
*   **Complex Algorithms:** Attention mechanisms, for example, are intricate dances of matrix multiplications, transposes, element-wise operations (like scaling and softmax), and broadcasting.

## Next Steps

With a conceptual grasp of what tensors are and the kinds of operations we can perform on them, we are now ready to start thinking about how a deep learning library like Zing will actually *represent* these computations. In the next chapter, we'll introduce the **Computation Graph**, a fundamental data structure that Zing will use to define and organize sequences of tensor operations before they are executed. 