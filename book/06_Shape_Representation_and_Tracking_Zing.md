# Chapter 6: Shape Representation and Tracking in Zing: The `ShapeTracker`

In the previous chapter, we established that the `TensorHandle` in Zing holds a `ShapeTracker`, making it responsible for all shape-related information. This design choice is fundamental. But what exactly is a "shape," why is it so important, and how can a deep learning library flexibly handle situations where shapes might not be fully known until runtime?

This chapter delves into the fascinating world of tensor shapes:

*   **The Significance of Shape:** Why knowing a tensor's dimensions is critical for every operation.
*   **Static Shapes:** When dimensions are fixed and known at graph compilation time.
*   **Dynamic Shapes:** When some dimensions are variable (e.g., batch size) and resolved at runtime.
*   **Symbolic Shapes in Zing:** How <PERSON>ing uses `SymbolicDim` and a `SymbolicPool` to represent and manage dynamic dimensions gracefully.
*   **`ShapeTracker`:** <PERSON><PERSON>'s workhorse for managing a tensor's current shape, strides, and performing view operations.
*   **`ShapeInference`:** The rules and logic for calculating the output shape of an operation based on its input shapes.

## Why Shape Matters

The shape of a tensor defines its dimensionality and the size of each dimension. For example:

*   A shape `[1024]` represents a vector of 1024 elements.
*   A shape `[64, 128]` represents a matrix with 64 rows and 128 columns.
*   A shape `[32, 3, 224, 224]` could represent a batch of 32 RGB images, each 224x224 pixels.

Knowing the shape of input tensors is absolutely essential for almost every operation in a deep learning library:

1.  **Validity Checks:** Many operations have constraints on input shapes. A matrix multiplication `A @ B` requires the number of columns in `A` to match the number of rows in `B`. Attempting to add tensors of incompatible shapes will result in an error.
2.  **Output Shape Calculation:** The shape of the output tensor is determined by the operation and the shapes of its inputs. For example, adding two tensors of shape `[N, M]` results in an output tensor also of shape `[N, M]`.
3.  **Memory Allocation:** Before an operation can be executed, the library needs to know the shape of the output tensor to allocate the correct amount of memory.
4.  **Kernel Selection/Code Generation:** For hardware acceleration (e.g., on GPUs), the specific kernel (a small program that runs on the device) chosen or generated often depends on the tensor shapes involved.
5.  **View Operations:** Operations like `reshape`, `transpose`, and `slice` fundamentally manipulate a tensor's shape (or at least, how its data is interpreted).

## Types of Shapes

### 1. Static Shapes

All dimensions of the tensor are known, fixed positive integers at the time the computation graph is defined and compiled. For example, `[256, 512]` or `[1, 3, 28, 28]`.

*   **Pros:** Allows for maximum optimization during compilation. The compiler knows exactly how much memory will be needed and can potentially fuse operations more effectively.
*   **Cons:** Less flexible. If you need to process data with varying batch sizes, for instance, a purely static shape system would require recompiling the model for each batch size or using padding tricks.

### 2. Dynamic Shapes

One or more dimensions of the tensor are not known until runtime. A common example is the batch size, which might vary depending on the input data. Such dimensions are often represented by a placeholder like `-1` or a special symbol.

*   **Pros:** Much more flexible. Allows a single compiled model to handle inputs of varying sizes for the dynamic dimensions.
*   **Cons:** Can make some ahead-of-time optimizations harder. Memory allocation for these tensors must happen at runtime when the actual dimension sizes are known.

## Zing's Approach: `SymbolicDim` and `SymbolicPool`

Zing implements a powerful system for handling shapes that can be static, dynamic, or a mix, leveraging `SymbolicDim` and a `SymbolicPool`. This is detailed in `docs/graph.md` and `docs/tensor.md`.

### `SymbolicDim`: Representing a Single Dimension

Instead of just using integers for dimension sizes, Zing uses a `SymbolicDim` enum (or a similar structure) which can be:

*   A **Literal Value:** A concrete positive integer (e.g., `224`).
*   A **Symbolic Placeholder:** An identifier representing a dimension whose value will be determined at runtime (e.g., `batch_size_symbol` or `sequence_length_symbol`).

Conceptual Zig for `SymbolicDim` (derived from `docs/tensor.md` and `docs/graph.md` context):

```zig
pub const SymbolicDim = union(enum) {
    literal: usize,
    symbol_id: SymbolId, // An ID referencing a symbol in the SymbolicPool
    // Potentially: expression_id: ExpressionId for computed symbolic dimensions
};

pub const SymbolId = u32; // Or some other unique identifier type
```

A tensor's shape would then be an array of `SymbolicDim`s, like `[]const SymbolicDim`.

### `SymbolicPool`: Managing Dynamic Dimension Symbols

The `Graph` structure in Zing (from `docs/graph.md`) contains a `SymbolicPool`:

```zig
// From docs/graph.md
pub const Graph = struct {
    // ... other fields ...
    symbolic_pool: SymbolicPool, // For dynamic dimensions
    // ... other fields ...
};

pub const SymbolicPool = struct {
    // Manages symbolic dimension names, their potential concrete values at runtime,
    // and possibly relationships/constraints between symbols.
    // This might involve hashmaps to store symbol names and their resolved values,
    // or more complex structures for symbolic expressions.
    // Example fields:
    // symbols: std.AutoHashMapUnmanaged(SymbolId, ?usize), // Stores resolved values
    // next_symbol_id: SymbolId = 0,
    allocator: Allocator, // For managing symbol names or expression trees
};
```

The `SymbolicPool` is responsible for:

*   **Registering new symbolic dimensions:** When a user defines a placeholder tensor with a dynamic dimension (e.g., `placeholder(&graph, .{SymbolicDim.symbol("batch"), SymbolicDim.literal(784)}, .f32)`).
*   **Resolving symbols at runtime:** When the graph is executed with concrete inputs, the actual values for these symbolic dimensions are provided and stored in the pool.
*   **Potentially handling symbolic expressions:** More advanced systems might allow defining one symbolic dimension in terms of another (e.g., `dim_b = dim_a * 2`).

This system allows Zing to define a graph once with symbolic dimensions and then execute it multiple times with different concrete values for those symbols, without recompilation for shape changes.

## `ShapeTracker`: The Keeper of Shape and Strides

As established, each `TensorHandle` in Zing embeds a `ShapeTracker`. This struct, detailed in `docs/tensor.md`, is the workhorse for managing how a `TensorHandle` views its underlying data node. It's more than just a list of dimension sizes.

Conceptual Zig for `ShapeTracker` (expanded from `docs/tensor.md` context):

```zig
pub const ShapeTracker = struct {
    shape: [MAX_DIMS]SymbolicDim, // The logical shape, using SymbolicDims
    strides: [MAX_DIMS]isize,     // Strides to access elements in memory
    offset: usize,                // Offset into the underlying data buffer (for slices)
    rank: u3,                     // Number of dimensions
    is_contiguous: bool,          // Is the current view contiguous in memory?
    symbolic_pool: *SymbolicPool, // Reference to resolve/use symbolic dimensions

    // Methods for view operations:
    pub fn fromShape(initial_shape: []const SymbolicDim, pool: *SymbolicPool) ShapeTracker;
    pub fn reshape(self: *ShapeTracker, new_shape: []const SymbolicDim) !void;
    pub fn transpose(self: *ShapeTracker, axes: []const u8) !void;
    pub fn slice(self: *ShapeTracker, starts: []const SymbolicDim, ends: []const SymbolicDim) !void;
    pub fn expand(self: *ShapeTracker, new_shape: []const SymbolicDim) !void;
    pub fn toContiguous(self: ShapeTracker) ShapeTracker; // Returns a new tracker for a contiguous version
    pub fn isBroadcastableTo(self: ShapeTracker, target_shape: ShapeTracker) bool;
    pub fn concreteShape(self: ShapeTracker) ![MAX_DIMS]usize; // Resolves symbolic dims
    pub fn elementCount(self: ShapeTracker) !usize;
    // ... other helper methods ...
};

const MAX_DIMS = 8; // Or some reasonable maximum
```

**Key Responsibilities of `ShapeTracker`:**

1.  **Storing Logical Shape:** Holds the `SymbolicDim` array representing the tensor's current shape as seen by the `TensorHandle`.
2.  **Calculating and Storing Strides:** Determines the memory strides necessary to access elements based on the current shape and view. This is crucial for supporting non-contiguous views efficiently.
3.  **Handling Offset:** For sliced views, it stores the offset from the beginning of the original data buffer.
4.  **Contiguity Tracking:** Knows whether the current view of the data is contiguous in memory. This is important because some operations (or hardware backends) require contiguous inputs.
5.  **Performing View Operations:** Methods like `reshape`, `transpose`, `slice`, and `expand` modify the `ShapeTracker`'s internal state (shape, strides, offset) to create a new view *without copying the underlying data* (unless `makeContiguous` is called and a copy is necessary).
6.  **Symbol Resolution:** Can use its `*SymbolicPool` reference to get concrete values for symbolic dimensions when needed (e.g., for memory allocation or to pass to a backend kernel).

**Architectural Decision in Zing (from `docs/tensor.md`):** `ShapeTracker` moves from `Node` (in a hypothetical older design) to `TensorHandle`. This allows multiple `TensorHandle`s to have different views (and thus different `ShapeTracker` states) of the *same* underlying data `Node` in the graph. This is what makes zero-copy view operations efficient and powerful.

## `ShapeInference`: Calculating Output Shapes

Whenever an operation (like addition, matrix multiplication, convolution) is applied to one or more input tensors, the library must determine the shape of the resulting output tensor. This process is called **shape inference**.

The `Tensor` component/API is the **SINGLE SOURCE OF TRUTH** for shape inference in Zing (`docs/tensor.md`). Primitive operations (Tier 2) are responsible for shape inference when they create nodes.

A `ShapeInference` module or set of functions would contain the rules for each operation:

*   **Element-wise Binary Ops (e.g., `add`, `mul`):** Usually require input shapes to be identical or broadcastable. The output shape is the broadcasted shape.
*   **Matrix Multiplication (`A @ B`):** If `A` is `[M, K]` and `B` is `[K, N]`, the output is `[M, N]`. Symbolic dimensions must match or be broadcastable appropriately.
*   **Reductions (e.g., `sum_reduce` along an axis):** The reduced axis is removed or becomes 1. Other dimensions remain (or are squeezed).
*   **Convolutions:** Have more complex rules depending on input size, kernel size, padding, and stride.
*   **View Operations:** `reshape` changes the shape while preserving element count (if contiguous or made contiguous). `transpose` permutes dimensions. `slice` extracts a sub-tensor. `expand` adds dimensions of size 1 for broadcasting.

**Error Handling:** A robust shape inference system must also handle errors gracefully, such as when input shapes are incompatible for an operation. Zing's design emphasizes clear error types (see `docs/error_context.md`).

Conceptual `ShapeInference` usage:

```zig
// Conceptual - actual implementation would be part of tensor op functions
// const output_shape_tracker = try ShapeInference.forAdd(input_a.shape, input_b.shape, graph.symbolic_pool);
// const matmul_output_shape_tracker = try ShapeInference.forMatmul(input_x.shape, input_w.shape, graph.symbolic_pool);
```

In practice, these rules are embedded within the functions that create computation nodes (e.g., within `graph.addNode` for a primitive, or within the tensor API functions like `ops.add()`).

## Interaction Flow for Shapes in Zing

1.  User creates a `TensorHandle` (e.g., `placeholder`, `zeros`, or as an output of an op).
    *   The `TensorHandle` is initialized with a `ShapeTracker` based on the provided `SymbolicDim`s and the graph's `SymbolicPool`.
2.  User applies a view operation (e.g., `tensor.reshape(new_symbolic_shape)`).
    *   A *new* `TensorHandle` is created.
    *   Its `ShapeTracker` is a modified version of the original tensor's `ShapeTracker` (updated shape, strides, etc.). It points to the *same `NodeId`*.
3.  User applies a primitive compute operation (e.g., `add(tensor_a, tensor_b)`).
    *   Shape inference rules are applied to the `ShapeTracker`s of `tensor_a` and `tensor_b`.
    *   A *new node* is added to the graph representing the `add` operation.
    *   A *new `TensorHandle`* is created for the output.
    *   Its `ShapeTracker` is initialized with the inferred output shape.
4.  During graph compilation or execution:
    *   If symbolic dimensions are present, their concrete values might be required (e.g., from user input for placeholders, or resolved through constraints).
    *   The `ShapeTracker`'s `concreteShape()` method can be called to get an array of `usize` for memory allocation or backend use, using the `SymbolicPool` to resolve symbols.

## Next Steps

With a solid understanding of tensors and how their shapes (static, dynamic, and symbolic) are meticulously managed by `ShapeTracker` and `SymbolicPool`, we are ready to explore the structure that orchestrates all these operations: the computation graph. The next chapter will focus on memory management for these tensors, followed by a deep dive into Zing's `Graph` component. 