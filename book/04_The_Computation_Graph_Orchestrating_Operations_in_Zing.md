<!-- Chapter 4 Draft -->

# Chapter 4: The Computation Graph: Orchestrating Operations in Zing

In the previous chapters, we've laid the groundwork by understanding what tensors are mathematically and the kinds of core operations we can perform on them. Now, we shift our focus to a critical question: how does a deep learning library actually represent a sequence of these operations and manage their dependencies? The answer lies in the **Computation Graph**.

At its heart, a computation graph is a directed acyclic graph (DAG) where:
*   **Nodes** (or vertices) represent operations (e.g., addition, multiplication, a convolution) or data sources (e.g., input tensors, constants).
*   **Edges** (or connections) represent the flow of tensors between these operations, indicating data dependencies. If operation B takes the output of operation A as input, there's an edge from A to B.

This graph structure is the blueprint for the entire computation. It doesn't hold the actual numerical data of the tensors (we'll see where that lives later), nor does it typically store detailed shape information directly within its nodes (as we discussed, `TensorHandle` and `ShapeTracker` manage this in Zing). Instead, the graph's primary role is to define the *structure* and *order* of computations.

This chapter will explore:

*   The role and importance of the computation graph.
*   Z<PERSON>'s `Graph` data structure and its core components.
*   How nodes in Zing (`Node`, `NodeId`, `NodeSpec`) define individual computational steps or data entries.
*   The strict separation of concerns: graph structure vs. tensor data vs. shape information.
*   The concept of primitive operations as the graph's building blocks.
*   How custom operations can be integrated.
*   The lifecycle of the graph: mutable during build/compilation, immutable once compiled.

## Why is the Computation Graph So Important?

The computation graph is a cornerstone of modern deep learning libraries for several reasons:

1.  **Explicit Dependency Management:** It clearly defines which operations depend on the outputs of others. This is crucial for determining the correct order of execution.
2.  **Automatic Differentiation:** The graph structure is fundamental for algorithms like backpropagation (reverse-mode automatic differentiation), which is used to train neural networks. By traversing the graph backward, the library can efficiently compute gradients.
3.  **Optimization Opportunities:** Once the entire computation is laid out as a graph, the library can perform various optimizations *before* execution. This includes:
    *   **Operator Fusion:** Combining multiple small operations into a single, more efficient kernel.
    *   **Constant Folding:** Pre-computing parts of the graph that only involve constant inputs.
    *   **Dead Code Elimination:** Removing operations whose outputs are not used.
    *   **Memory Planning:** Optimizing memory allocation and deallocation to reduce peak memory usage.
4.  **Hardware Abstraction:** The graph provides an abstract representation of the computation, which can then be compiled and executed on different hardware backends (CPU, GPU, specialized AI accelerators).
5.  **Serialization and Portability:** A defined graph can be serialized (saved to a file) and loaded elsewhere, allowing models to be easily shared and deployed.
6.  **Visualization and Debugging:** The graph structure can be visualized, making it easier to understand the model's architecture and debug issues.

## Zing's `Graph` Component: The Central Orchestrator

In Zing, the `Graph` component is responsible for building and managing this computation structure during the "build phase" (when the user is defining their model or computation).

**Key Design Goals from `docs/graph.md`:**

*   **Structure Only:** The `Graph` owns the nodes, edges, and associated metadata. It explicitly *does not* contain any tensor data or detailed shape information for the tensors being passed between nodes.
*   **Primitive Operations:** The graph is fundamentally built from a small, well-defined set of primitive operations. More complex operations (like a full matrix multiplication or a convolutional layer) are decomposed into these primitives at the API level before nodes are added to the graph. Zing aims for a set of around 11-14 primitives.
*   **Arena Allocation:** All graph structures (nodes, lists of edges, etc.) are allocated within an arena. This allows for very fast deallocation of the entire graph structure when it's no longer needed (typically O(1) cleanup).
*   **Immutable After Compilation:** While the graph is mutable during its construction and during the compilation phase (where optimizations might modify it), the final `CompiledGraph` (the output of the compilation phase) is immutable.

Let's look at a conceptual representation of Zing's `Graph` structure, drawing from `docs/graph.md`:

```zig
// Conceptual Zig from docs/graph.md
pub const Graph = struct {
    // Memory management
    allocator: std.mem.Allocator,      // Backing allocator
    arena: std.heap.ArenaAllocator,  // Graph construction arena

    // Core structure (all arena allocated)
    nodes: std.ArrayListUnmanaged(Node), // List of all nodes
    // edges: std.ArrayListUnmanaged(Edge), // Edges might be implicitly stored in Node.inputs
    node_map: std.AutoHashMapUnmanaged(NodeId, u32), // Maps NodeId to index in `nodes` list

    // Efficient consumer tracking (O(1) lookup for who uses a node's output)
    consumer_lists: std.AutoHashMapUnmanaged(NodeId, std.ArrayListUnmanaged(NodeId)),

    // Storage for backend-specific custom operations
    custom_ops: std.AutoHashMapUnmanaged(NodeId, CustomOp),

    // Symbolic dimension management
    symbolic_pool: SymbolicPool, // Manages symbolic dimensions (details in a later chapter)

    // Compilation state & metadata
    next_node_id: NodeId,
    is_finalized: bool, // Has the graph been finalized for compilation?
    modifications_count: u32, // Tracks changes, useful for caching
    cached_topology: ?[]NodeId, // Cached topological sort of nodes
    output_nodes: std.ArrayListUnmanaged(NodeId), // Nodes marked as graph outputs

    // CRITICAL: What Graph does NOT contain:
    // - Tensor buffers or actual numerical data
    // - Device contexts or hardware handles (e.g., CUDA streams)
    // - Execution state (e.g., program counters, active threads)
    // - Memory allocators for actual tensor data (that's for DataStorage)
};

pub const NodeId = u32; // Or an opaque enum, a unique identifier for a node
```

**Key Takeaways from the `Graph` Structure:**

*   **Memory Management:** The `arena` is crucial for efficient management of the graph's internal structures.
*   **Node Storage:** `nodes` is a list holding all defined operations. `node_map` allows quick lookup of a node by its `NodeId`.
*   **Consumer Tracking:** `consumer_lists` helps quickly find all nodes that consume the output of a given node. This is useful for graph traversals and optimizations.
*   **`SymbolicPool`:** As we'll see in detail later, the graph hosts the `SymbolicPool` to manage symbolic dimensions used in `ShapeTracker`s.
*   **No Tensor Data:** This is a strict and important separation of concerns. The `Graph` knows *what* to compute, but not the *values* being computed on (until constants are involved).

## Nodes in Zing: Defining Computation and Data

Each step in the computation, or each piece of data that enters the graph, is represented by a `Node`.

```zig
// Conceptual Zig from docs/graph.md

// NodeSpec: The "what" of the node - either data or a compute operation
pub const NodeSpec = union(enum) {
    data: DataSource,
    compute: ComputeOp,
    // custom: CustomOpHandle, // Potentially, if CustomOp is not stored separately
};

// DataSource: For nodes that introduce data
pub const DataSource = enum {
    constant,    // A scalar constant (value might be in NodeMetadata or a separate store)
    placeholder, // Represents an external tensor input to the graph
    // initial_data, // Represents a tensor initialized with specific data by the user
};

// ComputeOp: The set of primitive compute operations Zing supports
// (Based on Luminal's primitives, as per docs/graph.md)
pub const ComputeOp = enum {
    // Binary operations
    add, mul, div, mod, less_than, equal, // Expanded based on typical needs

    // Unary operations
    recip, sqrt, sin, cos, exp, log, // Common mathematical functions
    neg,
    
    // Reductions
    sum_reduce, max_reduce,

    // Memory operations
    contiguous, // Ensures a tensor's data is laid out contiguously in memory
    // cast, // Change data type
    // reshape, transpose, slice, expand (These are view ops, handled by TensorHandle/ShapeTracker, NOT graph nodes)

    // Extension point
    custom, // A hook for backend-specific operations
};

// NodeMetadata: Optional extra info for a node
pub const NodeMetadata = struct {
    constant_value: ?f32 = null,   // For DataSource.constant
    reduction_axis: ?i64 = null,   // For sum_reduce, max_reduce
    debug_name: ?[]const u8 = null,
    // Other metadata as needed, e.g., padding, strides for some ops if not handled by shapes
};

// OutputInfo: Describes an output from a node (nodes can have multiple outputs)
pub const NodeOutputInfo = struct {
    dtype: DataType, // The data type of this output tensor
    // ARCHITECTURAL DECISION (from docs/graph.md and docs/tensor.md):
    // NO SHAPE INFORMATION directly in Node or NodeOutputInfo.
    // Shapes are managed at the TensorHandle level via ShapeTracker.
};

// The Node itself
pub const Node = struct {
    id: NodeId,                     // Unique identifier
    spec: NodeSpec,                 // What kind of node is this? (data or compute)
    inputs: []const NodeId,         // List of NodeIds that are inputs to this node
                                    // (arena-allocated slice pointing to an array of NodeIds)
    outputs_info: []const NodeOutputInfo, // Describes each output this node produces
                                        // (Typically one, but can be multiple)
    is_valid: bool = true,          // Used for "tombstoning" nodes during optimization passes
                                    // (e.g., if a node is fused or becomes dead code)
    metadata: ?*NodeMetadata = null, // Optional: pointer to arena-allocated metadata
};
```

**Key Points about Zing's `Node` Design:**

*   **`NodeId`:** A simple, unique identifier (likely a `u32` or an opaque enum) to refer to nodes.
*   **`NodeSpec`:** This is a critical tagged union. It clearly distinguishes between:
    *   `DataSource` nodes: These introduce data into the graph (e.g., user-provided inputs via `placeholder`, or compile-time `constant`s).
    *   `ComputeOp` nodes: These perform actual computations using the 11 primitive operations.
*   **Primitive Operations (`ComputeOp`):** Zing standardizes on a small set of fundamental compute operations (e.g., `add`, `mul`, `recip`, `sum_reduce`, `contiguous`). More complex user-facing operations (like `matmul` or `conv2d`) are *decomposed* into these primitives by the API layer *before* corresponding `Node`s are created in the `Graph`. This simplifies the compiler and backends immensely.
*   **`inputs`:** A list of `NodeId`s representing the direct dependencies for this node. These are the outputs of other nodes that this node consumes.
*   **`outputs_info`:** Describes the data types of the tensors this node will produce. Crucially, Zing's design (as per `docs/graph.md` and `docs/tensor.md`) dictates that **shape information is NOT stored in the `Node`**. It resides with the `TensorHandle` and its `ShapeTracker`. This allows multiple `TensorHandle`s to have different views (shapes) of the *same* underlying computed data (represented by a single `NodeId`).
*   **`CustomOp`:** Provides an extension mechanism. If a backend has a highly optimized kernel for an operation not in the standard primitives, it can be registered as a `CustomOp`. The `Graph` stores a reference to backend-specific data for such operations.

### The Golden Rule: Separation of Concerns

It's worth re-emphasizing Zing's strict separation:

1.  **`Graph` & `Node`:** Define the *computational structure* and dependencies using primitive operations. They know *what* operations to perform and in *what order* on abstract data identifiers (`NodeId`). They are largely ignorant of concrete tensor shapes and completely ignorant of tensor data.
2.  **`TensorHandle` & `ShapeTracker`:** As discussed in Chapter 2 (conceptually) and to be detailed for Zing in Chapter 5 and 6, these are the user-facing and internal components that manage a tensor's *identity*, its *shape* (which can be static, dynamic, or symbolic), its *data type*, and how its data is to be interpreted (strides, offset for views). A `TensorHandle` links to a `NodeId` in the `Graph` that produces its data.
3.  **`DataStorage` / Backend Allocators (Execution Phase):** These components (covered much later) are responsible for allocating and managing the actual memory buffers for tensor data when the graph is executed.

This separation is key to Zing's flexibility and efficiency. For instance, view operations like `reshape` or `transpose` primarily involve creating a new `TensorHandle` with a new `ShapeTracker` that points to the *same* `NodeId` as the original tensor. No new nodes are added to the graph for these views, and no data is copied (it's a metadata-only change until data needs to be made contiguous for a specific operation).

## Graph Lifecycle and Mutability

*   **Build Phase:** The user interacts with Zing's API (e.g., `zing.add(tensor_a, tensor_b)`). The API functions translate these calls into the creation of `Node`s and `TensorHandle`s. The `Graph` is mutable during this phase. New nodes and edges are added.
*   **Compilation Phase:** Once the user has defined the full computation, the `Graph` is passed to a `CompilationPipeline`. Here, various optimization passes might modify the graph (e.g., fusing nodes, eliminating dead code). The graph is still considered mutable by the compiler. The output of this phase is often a `CompiledGraph` (or a similar structure) which is then immutable.
*   **Execution Phase:** The `CompiledGraph` is used by an `Executor` along with a specific `Backend` to run the computation. No changes are made to the graph structure at this stage.

The `is_finalized` flag and `modifications_count` in the `Graph` structure help manage this lifecycle, potentially invalidating cached information like topological sorts if the graph is altered after such information was computed.

## Next Steps

Understanding the `Graph` as the skeleton of our computations is essential. It dictates the flow and dependencies. In the next chapter, we'll connect this back to the user's view by diving into Zing's `TensorHandle` – how it acts as the user's "key" to tensors, linking the abstract data and shape information it carries to the specific `Node` in the `Graph` that will produce its data. This will bridge the conceptual tensor with its concrete representation within Zing's operational framework. 