const std = @import("std");
const testing = std.testing;

const Graph = @import("src/graph.zig").Graph;
const tensor = @import("src/tensor.zig");
const compiler = @import("src/compiler.zig");
const execution = @import("src/execution.zig");

test "debug gradient flow" {
    const allocator = testing.allocator;
    
    var graph = try Graph.init(allocator);
    defer graph.deinit();
    
    // Create simple computation with gradients
    const x = try tensor.placeholderWithOptions(&graph, &.{2}, .f32, .{ .requires_grad = true });
    const y = try tensor.placeholderWithOptions(&graph, &.{2}, .f32, .{ .requires_grad = true });
    
    const z = try x.add(y);
    
    // Mark as output and loss
    try graph.markOutput(z.node_id);
    try graph.setLossOutput(z.node_id);
    
    std.debug.print("\n=== Before compilation ===\n", .{});
    std.debug.print("Graph has {} nodes\n", .{graph.nodes.items.len});
    std.debug.print("Has gradient support: {}\n", .{graph.hasGradientSupport()});
    std.debug.print("Loss output: {?}\n", .{graph.loss_output});
    
    // Compile
    var compiled = try compiler.compile.compileCpu(&graph, allocator);
    defer compiled.deinit(allocator);
    
    std.debug.print("\n=== After compilation ===\n", .{});
    std.debug.print("Compiled has gradient_map: {}\n", .{compiled.gradient_map != null});
    if (compiled.gradient_map) |*gmap| {
        std.debug.print("Gradient map size: {}\n", .{gmap.count()});
        var it = gmap.iterator();
        while (it.next()) |entry| {
            std.debug.print("  Node {} -> Gradient node {}\n", .{entry.key_ptr.*, entry.value_ptr.*.node_id});
        }
    }
    
    // Create executor
    var executor = try execution.Executor.init(allocator, &compiled, null);
    defer executor.deinit();
    
    std.debug.print("\n=== Executor state ===\n", .{});
    std.debug.print("Executor has gradient_map: {}\n", .{executor.gradient_map != null});
    
    // Set inputs
    const x_data = [_]f32{ 1.0, 2.0 };
    const y_data = [_]f32{ 3.0, 4.0 };
    try executor.setInput(x.node_id, std.mem.sliceAsBytes(&x_data), &.{2}, .f32);
    try executor.setInput(y.node_id, std.mem.sliceAsBytes(&y_data), &.{2}, .f32);
    
    // Run
    try executor.run();
    
    std.debug.print("\n=== After execution ===\n", .{});
    
    // Try to get gradients
    const result = executor.getGradient(x.node_id) catch |err| {
        std.debug.print("Failed to get gradient for x: {}\n", .{err});
        return err;
    };
    
    std.debug.print("Got gradient for x!\n", .{});
    const grad_data = std.mem.bytesAsSlice(f32, result.data);
    std.debug.print("Gradient values: [{}, {}]\n", .{grad_data[0], grad_data[1]});
}