# Compiler Passes Improvement Plan

## Current Issues

### 1. Pattern Matching Inefficiency
- **Problem**: Each pattern in algebraic.zig iterates through ALL nodes
- **Impact**: O(patterns × nodes) complexity
- **Solution**: Use pattern indexing or visitor pattern

### 2. Underutilized Shape API
- **Problem**: Most passes ignore shape information
- **Impact**: Missing optimization opportunities
- **Examples**:
  - Fusion could use shape info for better decisions
  - Memory pass could use actual tensor sizes
  - Optimization could eliminate ops based on shape (e.g., mul by 1×1 tensor)

### 3. Graph API Usage Gaps
- **Problem**: Not using all available Graph APIs efficiently
- **Examples**:
  - `iterateConsumers()` vs `getConsumers()` (allocation vs non-allocating)
  - Not using consumer tracking for pattern matching
  - Not leveraging node metadata effectively

### 4. Missing Reusable Infrastructure
- **Problem**: Each pass reimplements common patterns
- **Examples**:
  - Node iteration patterns
  - Shape compatibility checking
  - Constant value extraction

## Recommended Improvements

### 1. Create Pattern Index for Efficient Matching

```zig
// In compiler/pattern.zig
pub const PatternIndex = struct {
    // Index patterns by root operation type
    by_op: std.AutoHashMap(ComputeOp, std.ArrayList(*GraphPattern)),
    
    pub fn addPattern(self: *PatternIndex, pattern: *GraphPattern) !void {
        const root_op = pattern.getRootOp() orelse return;
        const entry = try self.by_op.getOrPut(root_op);
        if (!entry.found_existing) {
            entry.value_ptr.* = std.ArrayList(*GraphPattern).init(self.allocator);
        }
        try entry.value_ptr.append(pattern);
    }
    
    pub fn findMatches(self: *PatternIndex, graph: *Graph, node: *Node) ![]MatchResult {
        const op = switch (node.spec) {
            .compute => |op| op,
            else => return &.{},
        };
        
        const patterns = self.by_op.get(op) orelse return &.{};
        // Only check patterns that could match this operation
        ...
    }
};
```

### 2. Shape-Aware Pass Infrastructure

```zig
// In compiler/passes/base.zig
pub const ShapeAwarePass = struct {
    pub fn getStaticShape(ctx: *PassContext, node_id: NodeId) ?[]const i64 {
        const shape_map = ctx.handle_shapes orelse return null;
        const tracker = shape_map.get(node_id) orelse return null;
        return tracker.getStaticDims();
    }
    
    pub fn getTensorSize(ctx: *PassContext, node_id: NodeId) ?usize {
        const shape = getStaticShape(ctx, node_id) orelse return null;
        var size: usize = 1;
        for (shape) |dim| {
            size *= @intCast(dim);
        }
        return size;
    }
    
    pub fn areBroadcastCompatible(ctx: *PassContext, a: NodeId, b: NodeId) bool {
        const shape_map = ctx.handle_shapes orelse return true; // Assume compatible if no info
        const shape_a = shape_map.get(a) orelse return true;
        const shape_b = shape_map.get(b) orelse return true;
        return shape.utils.areBroadcastable(
            shape_a.getStaticDims(), 
            shape_b.getStaticDims()
        );
    }
};
```

### 3. Improved Fusion Pass

```zig
// Better fusion using shape information
pub fn elementwiseFusion(ctx: *PassContext) !void {
    // Use shape info to determine fusion profitability
    const estimated_memory_traffic = estimateMemoryTraffic(chain, ctx);
    const estimated_compute = estimateCompute(chain, ctx);
    
    // Consider tensor sizes for fusion decisions
    const total_tensor_size = getTotalTensorSize(chain, ctx);
    if (total_tensor_size > cache_size) {
        // Different fusion strategy for large tensors
    }
}

fn estimateMemoryTraffic(chain: ElementwiseChain, ctx: *PassContext) usize {
    var traffic: usize = 0;
    for (chain.nodes) |node_id| {
        if (ShapeAwarePass.getTensorSize(ctx, node_id)) |size| {
            traffic += size * @sizeOf(f32); // Actual memory traffic
        }
    }
    return traffic;
}
```

### 4. Constant Folding with Shape Awareness

```zig
// In optimization.zig
fn constantFolding(ctx: *PassContext) !void {
    // ... existing code ...
    
    // Additional: fold operations with scalar constants
    if (isScalarConstant(node, ctx)) {
        // Can fold scalar operations more aggressively
    }
    
    // Fold identity operations based on shape
    if (isIdentityReshape(node, ctx)) {
        try queue.markForSubstitution(node.id, node.inputs[0]);
    }
}

fn isScalarConstant(node: *Node, ctx: *PassContext) bool {
    if (node.spec != .data or node.spec.data != .constant) return false;
    const shape = ShapeAwarePass.getStaticShape(ctx, node.id) orelse return false;
    return shape.len == 0 or (shape.len == 1 and shape[0] == 1);
}
```

### 5. Memory Pass with Actual Sizes

```zig
// In memory.zig
pub fn analyzeBufferLifetimes(graph: *Graph, ctx: *PassContext, allocator: Allocator) !std.ArrayList(BufferLifetime) {
    var lifetimes = std.ArrayList(BufferLifetime).init(allocator);
    
    const topo_order = try graph.topologicalSort();
    for (topo_order, 0..) |node_id, step| {
        const node = graph.getNode(node_id) orelse continue;
        
        // Get actual buffer size from shape
        const size = ShapeAwarePass.getTensorSize(ctx, node_id) orelse {
            // Fallback to estimate
            1024
        };
        
        const lifetime = BufferLifetime{
            .buffer_id = node.id,
            .start_step = @intCast(step),
            .end_step = determineEndStep(graph, node_id, step),
            .size = size * @sizeOf(f32), // Actual memory size
        };
        
        try lifetimes.append(lifetime);
    }
    
    return lifetimes;
}
```

### 6. Autodiff with Shape Validation

```zig
// In autodiff.zig
fn validateGradientShapes(ctx: *AutodiffContext) !void {
    var iter = ctx.gradients.iterator();
    while (iter.next()) |entry| {
        const param_id = entry.key_ptr.*;
        const grad_id = entry.value_ptr.node_id;
        
        if (!ShapeAwarePass.haveSameShape(ctx.ctx, param_id, grad_id)) {
            return error.GradientShapeMismatch;
        }
    }
}
```

### 7. Common Pass Utilities

```zig
// In compiler/passes/utils.zig
pub const PassUtils = struct {
    /// Iterate nodes in topological order with operation filter
    pub fn iterateNodesOfType(
        graph: *Graph, 
        comptime op_type: ComputeOp,
        context: anytype,
        visitor: fn(@TypeOf(context), *Node) anyerror!void
    ) !void {
        const topo_order = try graph.topologicalSort();
        for (topo_order) |node_id| {
            const node = graph.getNode(node_id) orelse continue;
            if (node.spec == .compute and node.spec.compute == op_type) {
                try visitor(context, node);
            }
        }
    }
    
    /// Find all nodes matching a predicate
    pub fn findNodes(
        graph: *Graph,
        predicate: fn(*Node) bool,
        allocator: Allocator
    ) ![]NodeId {
        var results = std.ArrayList(NodeId).init(allocator);
        const topo_order = try graph.topologicalSort();
        for (topo_order) |node_id| {
            const node = graph.getNode(node_id) orelse continue;
            if (predicate(node)) {
                try results.append(node_id);
            }
        }
        return results.toOwnedSlice();
    }
};
```

## Implementation Priority

1. **High Priority**:
   - Pattern indexing for algebraic simplification
   - Shape-aware base infrastructure
   - Memory pass with actual sizes

2. **Medium Priority**:
   - Improved fusion with shape consideration
   - Autodiff shape validation
   - Common utilities

3. **Low Priority**:
   - Advanced constant folding
   - Additional algebraic patterns

## Testing Strategy

1. **Unit tests** for each improvement
2. **Performance benchmarks** to verify efficiency gains
3. **Integration tests** with real models
4. **Memory usage tracking** to ensure optimizations work

## Metrics to Track

- Pattern matching time reduction
- Memory allocation reduction
- Optimization opportunities found
- Compilation time improvement
- Generated code efficiency