test-gradient-debug-output
+- run gradient-debug-output-tests stderr
=== ADDITION TEST ===
Expression: z = x + y
Inputs: x = 5.00, y = 7.00
Output: z = 12.00
Gradients:
  dz/dx = 1.000000 (expected: 1.0)
  dz/dy = 1.000000 (expected: 1.0)
Status: ✅ CORRECT

=== MULTIPLICATION TEST ===
Expression: z = x * y
Inputs: x = 3.00, y = 4.00
Output: z = 12.00
Gradients:
  dz/dx = 4.000000 (expected: y = 4.0)
  dz/dy = 3.000000 (expected: x = 3.0)
Status: ✅ CORRECT

=== CHAIN RULE TEST ===
Expression: z = x^3
Input: x = 2.00
Output: z = 8.00 (expected: 8.0)
Gradient:
  dz/dx = 12.000000 (expected: 3x^2 = 3*4 = 12.0)
Status: ✅ CORRECT

==================================================
GRADIENT VERIFICATION COMPLETE
==================================================

These tests verify that autograd computes the
CORRECT numerical gradient values!
