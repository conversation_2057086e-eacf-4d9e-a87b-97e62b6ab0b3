# Pattern and Graph Duplication Migration Guide

This guide provides step-by-step instructions for eliminating code duplication between graph.zig and compiler modules.

## Step 1: Update graph.zig

### Changes needed:

1. Add import at the top:
```zig
const ops_util = @import("ops_util.zig");
```

2. Remove the private `getExpectedInputCount` function (lines 184-201)

3. Update the usage in `createNode` function (around line 232):
```zig
// Change from:
const expected = getExpectedInputCount(op);
// To:
const expected = ops_util.getExpectedInputCount(op);
```

4. Add new convenience methods at the end of Graph struct:
```zig
/// Check if node is an elementwise operation
pub fn isElementwise(self: *const Graph, node_id: NodeId) bool {
    const node = self.getNode(node_id) orelse return false;
    return switch (node.spec) {
        .compute => |op| ops_util.isElementwiseOp(op),
        .data => false,
    };
}

/// Check if node is a reduction operation
pub fn isReduction(self: *const Graph, node_id: NodeId) bool {
    const node = self.getNode(node_id) orelse return false;
    return switch (node.spec) {
        .compute => |op| ops_util.isReductionOp(op),
        .data => false,
    };
}

/// Check if node preserves shape
pub fn isShapePreserving(self: *const Graph, node_id: NodeId) bool {
    const node = self.getNode(node_id) orelse return false;
    return switch (node.spec) {
        .compute => |op| ops_util.isShapePreserving(op),
        .data => true, // Data sources preserve shape
    };
}
```

## Step 2: Update src/compiler/pattern.zig

### Changes needed:

1. Add import at the top:
```zig
const ops_util = @import("../ops_util.zig");
```

2. Fix the broken usage of `getExpectedInputCount` (line 76):
```zig
// Change from:
const expected_input_count = getExpectedInputCount(op);
// To:
const expected_input_count = ops_util.getExpectedInputCount(op);
```

3. Remove duplicate function definitions (lines 411-432):
- Remove `fn isElementwiseOp`
- Remove `fn isReductionOp`  
- Remove `fn isShapePreserving`

4. Update references in `checkConstraint` function (around line 296):
```zig
// Change from:
.elementwise => switch (node.spec) {
    .compute => |op| isElementwiseOp(op),
    .data => false,
},
// To:
.elementwise => switch (node.spec) {
    .compute => |op| ops_util.isElementwiseOp(op),
    .data => false,
},
```

Similarly update for `isReductionOp` and `isShapePreserving`.

## Step 3: Update src/compiler/validation.zig

### Changes needed:

1. Add import at the top:
```zig
const ops_util = @import("../ops_util.zig");
```

2. Remove the duplicate `isElementwiseOp` function (around line 179)

3. Update all usages to use `ops_util.isElementwiseOp`

## Step 4: Update src/compiler/analysis.zig

### Changes needed:

1. Add import at the top:
```zig
const ops_util = @import("../ops_util.zig");
```

2. Remove the duplicate `isElementwiseOp` function (around line 324)

3. Update all usages to use `ops_util.isElementwiseOp`

4. Consider refactoring data flow analysis to use Graph's existing consumer tracking:

Replace custom consumer building logic with:
```zig
// Instead of building custom consumer maps:
for (graph.nodes.items) |node| {
    if (!node.is_valid) continue;
    
    // Use Graph's existing API
    const consumers = graph.iterateConsumers(node.id) orelse continue;
    // Process consumers...
}
```

## Step 5: Update build.zig (if needed)

Ensure ops_util.zig is included in any relevant modules or libraries.

## Step 6: Run Tests

After making changes:

```bash
zig build test
```

## Benefits After Migration

1. **Fixed Compilation Error**: pattern.zig will now compile correctly
2. **Single Source of Truth**: All operation properties defined in one place
3. **Easier Maintenance**: Changes to operation properties only need one update
4. **Better Type Safety**: Centralized functions ensure consistent behavior
5. **Reduced Code Size**: Eliminated ~100 lines of duplicate code
6. **Improved Modularity**: Clear separation of concerns

## Future Enhancements

Consider these additional improvements:

1. **Operation Metadata**: Add more operation properties to ops_util.zig as needed
2. **Compiler-Specific Wrapper**: If compiler needs differ significantly, create a thin wrapper around Graph
3. **Operation Registry**: Consider a more data-driven approach with operation properties as data
4. **Performance Optimizations**: Cache operation properties if lookup becomes a bottleneck

## Verification Checklist

- [ ] All files compile without errors
- [ ] All tests pass
- [ ] No duplicate operation classification functions remain
- [ ] Graph operations use centralized utilities
- [ ] Compiler modules use centralized utilities
- [ ] Documentation updated to reflect new structure