# Compilation and Execution Workflow with NodeSpec Design

## Overview

This document traces the complete workflow from graph building through compilation to execution with the new NodeSpec design, highlighting how the tagged union architecture flows through the entire system.

## 1. Graph Building Phase

### Node Creation with NodeSpec

```zig
// Creating data nodes
const input = try graph.addPlaceholder(&.{-1, 768}, .f32);
// Creates: Node { spec: .{ .data = .placeholder }, ... }

const const_val = try graph.addConstant(2.0);
// Creates: Node { spec: .{ .data = .constant }, ... }

// Creating compute nodes  
const mul = try graph.addNode(.{ .compute = .mul }, &.{input, const_val});
// Creates: Node { spec: .{ .compute = .mul }, ... }

// Custom operations use same pattern
const custom_op = CustomOp{ .backend_op_id = 42, .backend_data = ... };
const custom = try graph.addCustomNode(custom_op, &.{mul}, .f32);
// Creates: Node { spec: .{ .compute = .custom }, ... }
// Plus: graph.custom_ops[custom] = custom_op
```

### Key Design Benefits:
- **Type Safety**: Can't accidentally treat data as computation
- **Clear Intent**: Tagged union makes node category explicit
- **Unified API**: All nodes created through consistent interface

## 2. Compilation Pipeline Phase

### Pass Context with NodeSpec Awareness

```zig
pub const PassContext = struct {
    core: *Core,
    handle_shapes: *const HandleShapeMap,  // Shape info from TensorHandles
    
    // Helper to check node type
    pub fn isComputeNode(self: *PassContext, node_id: NodeId) bool {
        const node = self.core.graph.getNode(node_id) orelse return false;
        return node.spec == .compute;
    }
    
    pub fn isDataNode(self: *PassContext, node_id: NodeId) bool {
        const node = self.core.graph.getNode(node_id) orelse return false;
        return node.spec == .data;
    }
};
```

### How Passes Handle NodeSpec

1. **Contiguous Insertion Pass**:
```zig
// Skip data nodes - they don't need contiguous
if (node.spec == .data) continue;

const op_type = switch (node.spec) {
    .compute => |op| op,
    .data => unreachable,  // Already filtered
};

// Custom ops handle their own memory layout
if (op_type == .custom) continue;
```

2. **Constant Folding**:
```zig
// Check if input is a constant
const input_op = switch (input.spec) {
    .data => |data_source| data_source,
    .compute => break false,  // Not a constant
};
if (input_op != .constant) break false;
```

3. **Pattern Matching**:
```zig
fn checkConstraint(constraint: Constraint, node: *const Node) bool {
    return switch (constraint) {
        .is_constant => switch (node.spec) {
            .data => |source| source == .constant,
            .compute => false,
        },
        .is_compute => node.spec == .compute,
        // ...
    };
}
```

## 3. Backend Compilation Phase

### Building Kernel Registry

```zig
pub fn buildKernelRegistry(backend: *Backend, graph: *Graph) !CompiledGraph {
    const num_nodes = graph.nodes.items.len;
    var kernels = try allocator.alloc(?KernelFn, num_nodes);
    var custom_data = try allocator.alloc(?*anyopaque, num_nodes);
    
    // Initialize all to null
    for (0..num_nodes) |i| {
        kernels[i] = null;
        custom_data[i] = null;
    }
    
    // Populate kernels based on NodeSpec
    for (graph.nodes.items, 0..) |node, idx| {
        if (!node.is_valid) continue;
        
        kernels[idx] = switch (node.spec) {
            .data => null,  // Data nodes have no kernels
            .compute => |op| switch (op) {
                // Primitive operations get direct kernels
                .add => backend.getAddKernel(),
                .mul => backend.getMulKernel(),
                .sin => backend.getSinKernel(),
                // ... other primitives
                
                // Custom operations get special handling
                .custom => blk: {
                    const custom_op = graph.custom_ops.get(node.id).?;
                    custom_data[idx] = @intToPtr(*anyopaque, custom_op.backend_data);
                    break :blk backend.getCustomKernel(custom_op.backend_op_id);
                },
            },
        };
    }
    
    return CompiledGraph{
        .kernels = kernels,
        .custom_op_data = custom_data,
        // ... other fields
    };
}
```

### Backend-Specific Optimization

```zig
// GPU backend fusing elementwise operations
fn fuseElementwiseChains(backend: *CudaBackend, graph: *Graph) !void {
    const chains = try findChains(graph);
    
    for (chains) |chain| {
        // Create fused custom operation
        const kernel = try backend.generateFusedKernel(chain);
        const custom_op = CustomOp{
            .backend_op_id = backend.registerKernel(kernel),
            .backend_data = @ptrToInt(kernel),
            .debug_name = "fused_elementwise",
        };
        
        // Replace chain with custom node
        const fused = try graph.addCustomNode(custom_op, chain.inputs, .f32);
        try replaceChainWithNode(graph, chain, fused);
    }
}
```

## 4. Execution Phase

### Kernel Dispatch with NodeSpec

```zig
pub fn executeNode(executor: *Executor, exec_node: ExecutionNode) !void {
    const node_idx = @intFromEnum(exec_node.node_id);
    const node = &executor.compiled_graph.nodes[node_idx];
    
    // Kernel dispatch based on NodeSpec
    switch (node.spec) {
        .data => {
            // Data nodes should already have their data loaded
            // No execution needed
            return;
        },
        .compute => {
            // Get kernel from registry
            const kernel = executor.compiled_graph.kernels[node_idx] orelse {
                return error.NoKernelForComputeNode;
            };
            
            // Prepare kernel arguments
            var args = KernelArgs{
                .inputs = try gatherInputBuffers(executor, exec_node),
                .outputs = try gatherOutputBuffers(executor, exec_node),
                .work_size = try computeWorkSize(executor, exec_node),
                .custom_data = executor.compiled_graph.custom_op_data[node_idx],
            };
            
            // Execute kernel
            kernel(args);
        },
    }
}
```

### Input Loading for Data Nodes

```zig
pub fn setInput(executor: *Executor, node_id: NodeId, data: TensorView) !void {
    const node = executor.compiled_graph.getNode(node_id) orelse return error.InputNotFound;
    
    // Verify this is an input node
    switch (node.spec) {
        .data => |source| {
            if (source != .placeholder) {
                return error.NotAnInput;
            }
        },
        .compute => return error.CustomOperatorCannotBeInput,
    }
    
    // Store input data
    try executor.inputs_loaded.put(node_id, data);
}
```

## 5. Complete Example Flow

```zig
// 1. BUILD GRAPH
var graph = try Graph.init(allocator);
const a = try graph.addPlaceholder(&.{-1, 768}, .f32);  // spec: .{ .data = .placeholder }
const b = try graph.addConstant(2.0);                     // spec: .{ .data = .constant }
const c = try graph.addNode(.{ .compute = .add }, &.{a, b}); // spec: .{ .compute = .add }

// 2. COMPILE
var pipeline = try CompilationPipeline.init(allocator);
try pipeline.run(&ctx);  // Passes handle NodeSpec correctly

var backend = try CudaBackend.init(allocator);
const compiled = try backend.compile(&graph, &.{c});
// Backend may transform to:
// - Node a: spec: .{ .data = .placeholder }
// - Node b: spec: .{ .data = .constant }
// - Node gpu_copy: spec: .{ .compute = .custom } (copy to GPU)
// - Node c_fused: spec: .{ .compute = .custom } (fused add with constant)

// 3. EXECUTE
var executor = try Executor.init(&compiled);
try executor.setInput(a, input_data);  // Validates a is .data = .placeholder
try executor.run();
// - Skips execution for data nodes (a, b)
// - Executes custom kernel for gpu_copy
// - Executes fused kernel for c_fused

const output = try executor.getOutput(c);
```

## 6. Benefits of NodeSpec Design in Practice

### Type Safety Throughout Pipeline

1. **Compilation**: Passes can't accidentally fold non-constant nodes
2. **Backend**: Clear separation of what needs kernels vs what's just data
3. **Execution**: Can't try to execute data nodes or set inputs on compute nodes

### Clean Extension Point

Custom operations integrate seamlessly:
- Same node structure as primitives
- Backend-specific data in separate registry
- Type-safe kernel dispatch

### Performance Benefits

1. **Direct Dispatch**: No vtable lookups like trait objects
2. **Cache Efficiency**: Node structs are compact with embedded metadata
3. **Clear Optimization Boundaries**: Backends know exactly what can be fused

## 7. Comparison with Flat Enum

### Before (Flat Enum):
```zig
// Ambiguous - is constant a computation or data?
switch (node.operation) {
    .constant => {
        // Is this executable? Does it need a kernel?
    },
    .add => {
        // Clearly a computation
    },
}
```

### After (NodeSpec):
```zig
// Unambiguous - clear categories
switch (node.spec) {
    .data => {
        // Definitely not executable, no kernel needed
    },
    .compute => |op| {
        // Definitely needs execution, has kernel
    },
}
```

## Conclusion

The NodeSpec design provides a clean, type-safe flow from graph building through compilation to execution. The tagged union architecture ensures that data sources and computations are never confused, while the separate custom operation registry provides a clean extension point for backend-specific optimizations. This design achieves better performance than trait objects while maintaining clear semantic boundaries.