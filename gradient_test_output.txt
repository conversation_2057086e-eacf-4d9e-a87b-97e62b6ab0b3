test-gradient-verification-comprehensive
+- run gradient-verification-comprehensive-tests
   +- zig test gradient-verification-comprehensive-tests Debug native 4 errors
src/tests/test_gradient_verification_comprehensive.zig:145:6: error: expected type 'fn (*graph.Graph) anyerror!test_gradient_verification_comprehensive.runAndVerifyGradient__struct_1881', found 'fn (*graph.Graph) @typeInfo(@typeInfo(@TypeOf(test_gradient_verification_comprehensive.test.comprehensive gradient verification - basic operations__struct_2121.setup)).@"fn".return_type.?).error_union.error_set!test_gradient_verification_comprehensive.test.comprehensive gradient verification - basic operations__struct_2121.setup__struct_2122'
    }.setup);
     ^~~~~~
src/tests/test_gradient_verification_comprehensive.zig:145:6: note: return type '@typeInfo(@typeInfo(@TypeOf(test_gradient_verification_comprehensive.test.comprehensive gradient verification - basic operations__struct_2121.setup)).@"fn".return_type.?).error_union.error_set!test_gradient_verification_comprehensive.test.comprehensive gradient verification - basic operations__struct_2121.setup__struct_2122' cannot cast into return type 'anyerror!test_gradient_verification_comprehensive.runAndVerifyGradient__struct_1881'
src/tests/test_gradient_verification_comprehensive.zig:145:6: note: error union payload 'test_gradient_verification_comprehensive.test.comprehensive gradient verification - basic operations__struct_2121.setup__struct_2122' cannot cast into error union payload 'test_gradient_verification_comprehensive.runAndVerifyGradient__struct_1881'
src/tests/test_gradient_verification_comprehensive.zig:127:34: note: struct declared here
        fn setup(graph: *Graph) !struct{loss: TensorHandle, params: []const u64, expected_grads: []const f32, input_values: []const struct{id: u64, data: []const f32, shape: []const i64}} {
                                 ^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
src/tests/test_gradient_verification_comprehensive.zig:20:35: note: struct declared here
    setup_fn: fn(*Graph) anyerror!struct{loss: TensorHandle, params: []const u64, expected_grads: []const f32, input_values: []const struct{id: u64, data: []const f32, shape: []const i64}},
                                  ^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
src/tests/test_gradient_verification_comprehensive.zig:20:15: note: parameter type declared here
    setup_fn: fn(*Graph) anyerror!struct{loss: TensorHandle, params: []const u64, expected_grads: []const f32, input_values: []const struct{id: u64, data: []const f32, shape: []const i64}},
              ^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
src/tests/test_gradient_verification_comprehensive.zig:235:6: error: expected type 'fn (*graph.Graph) anyerror!test_gradient_verification_comprehensive.runAndVerifyGradient__struct_1881', found 'fn (*graph.Graph) @typeInfo(@typeInfo(@TypeOf(test_gradient_verification_comprehensive.test.comprehensive gradient verification - unary operations__struct_17679.setup)).@"fn".return_type.?).error_union.error_set!test_gradient_verification_comprehensive.test.comprehensive gradient verification - unary operations__struct_17679.setup__struct_17680'
    }.setup);
     ^~~~~~
src/tests/test_gradient_verification_comprehensive.zig:235:6: note: return type '@typeInfo(@typeInfo(@TypeOf(test_gradient_verification_comprehensive.test.comprehensive gradient verification - unary operations__struct_17679.setup)).@"fn".return_type.?).error_union.error_set!test_gradient_verification_comprehensive.test.comprehensive gradient verification - unary operations__struct_17679.setup__struct_17680' cannot cast into return type 'anyerror!test_gradient_verification_comprehensive.runAndVerifyGradient__struct_1881'
src/tests/test_gradient_verification_comprehensive.zig:235:6: note: error union payload 'test_gradient_verification_comprehensive.test.comprehensive gradient verification - unary operations__struct_17679.setup__struct_17680' cannot cast into error union payload 'test_gradient_verification_comprehensive.runAndVerifyGradient__struct_1881'
src/tests/test_gradient_verification_comprehensive.zig:220:34: note: struct declared here
        fn setup(graph: *Graph) !struct{loss: TensorHandle, params: []const u64, expected_grads: []const f32, input_values: []const struct{id: u64, data: []const f32, shape: []const i64}} {
                                 ^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
src/tests/test_gradient_verification_comprehensive.zig:20:35: note: struct declared here
    setup_fn: fn(*Graph) anyerror!struct{loss: TensorHandle, params: []const u64, expected_grads: []const f32, input_values: []const struct{id: u64, data: []const f32, shape: []const i64}},
                                  ^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
src/tests/test_gradient_verification_comprehensive.zig:20:15: note: parameter type declared here
    setup_fn: fn(*Graph) anyerror!struct{loss: TensorHandle, params: []const u64, expected_grads: []const f32, input_values: []const struct{id: u64, data: []const f32, shape: []const i64}},
              ^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
src/tests/test_gradient_verification_comprehensive.zig:284:6: error: expected type 'fn (*graph.Graph) anyerror!test_gradient_verification_comprehensive.runAndVerifyGradient__struct_1881', found 'fn (*graph.Graph) @typeInfo(@typeInfo(@TypeOf(test_gradient_verification_comprehensive.test.comprehensive gradient verification - reduction operations__struct_17695.setup)).@"fn".return_type.?).error_union.error_set!test_gradient_verification_comprehensive.test.comprehensive gradient verification - reduction operations__struct_17695.setup__struct_17696'
    }.setup);
     ^~~~~~
src/tests/test_gradient_verification_comprehensive.zig:284:6: note: return type '@typeInfo(@typeInfo(@TypeOf(test_gradient_verification_comprehensive.test.comprehensive gradient verification - reduction operations__struct_17695.setup)).@"fn".return_type.?).error_union.error_set!test_gradient_verification_comprehensive.test.comprehensive gradient verification - reduction operations__struct_17695.setup__struct_17696' cannot cast into return type 'anyerror!test_gradient_verification_comprehensive.runAndVerifyGradient__struct_1881'
src/tests/test_gradient_verification_comprehensive.zig:284:6: note: error union payload 'test_gradient_verification_comprehensive.test.comprehensive gradient verification - reduction operations__struct_17695.setup__struct_17696' cannot cast into error union payload 'test_gradient_verification_comprehensive.runAndVerifyGradient__struct_1881'
src/tests/test_gradient_verification_comprehensive.zig:264:34: note: struct declared here
        fn setup(graph: *Graph) !struct{loss: TensorHandle, params: []const u64, expected_grads: []const f32, input_values: []const struct{id: u64, data: []const f32, shape: []const i64}} {
                                 ^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
src/tests/test_gradient_verification_comprehensive.zig:20:35: note: struct declared here
    setup_fn: fn(*Graph) anyerror!struct{loss: TensorHandle, params: []const u64, expected_grads: []const f32, input_values: []const struct{id: u64, data: []const f32, shape: []const i64}},
                                  ^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
src/tests/test_gradient_verification_comprehensive.zig:20:15: note: parameter type declared here
    setup_fn: fn(*Graph) anyerror!struct{loss: TensorHandle, params: []const u64, expected_grads: []const f32, input_values: []const struct{id: u64, data: []const f32, shape: []const i64}},
              ^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
src/tests/test_gradient_verification_comprehensive.zig:330:6: error: expected type 'fn (*graph.Graph) anyerror!test_gradient_verification_comprehensive.runAndVerifyGradient__struct_1881', found 'fn (*graph.Graph) @typeInfo(@typeInfo(@TypeOf(test_gradient_verification_comprehensive.test.comprehensive gradient verification - complex expressions__struct_17711.setup)).@"fn".return_type.?).error_union.error_set!test_gradient_verification_comprehensive.test.comprehensive gradient verification - complex expressions__struct_17711.setup__struct_17712'
    }.setup);
     ^~~~~~
src/tests/test_gradient_verification_comprehensive.zig:330:6: note: return type '@typeInfo(@typeInfo(@TypeOf(test_gradient_verification_comprehensive.test.comprehensive gradient verification - complex expressions__struct_17711.setup)).@"fn".return_type.?).error_union.error_set!test_gradient_verification_comprehensive.test.comprehensive gradient verification - complex expressions__struct_17711.setup__struct_17712' cannot cast into return type 'anyerror!test_gradient_verification_comprehensive.runAndVerifyGradient__struct_1881'
src/tests/test_gradient_verification_comprehensive.zig:330:6: note: error union payload 'test_gradient_verification_comprehensive.test.comprehensive gradient verification - complex expressions__struct_17711.setup__struct_17712' cannot cast into error union payload 'test_gradient_verification_comprehensive.runAndVerifyGradient__struct_1881'
src/tests/test_gradient_verification_comprehensive.zig:294:34: note: struct declared here
        fn setup(graph: *Graph) !struct{loss: TensorHandle, params: []const u64, expected_grads: []const f32, input_values: []const struct{id: u64, data: []const f32, shape: []const i64}} {
                                 ^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
src/tests/test_gradient_verification_comprehensive.zig:20:35: note: struct declared here
    setup_fn: fn(*Graph) anyerror!struct{loss: TensorHandle, params: []const u64, expected_grads: []const f32, input_values: []const struct{id: u64, data: []const f32, shape: []const i64}},
                                  ^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
src/tests/test_gradient_verification_comprehensive.zig:20:15: note: parameter type declared here
    setup_fn: fn(*Graph) anyerror!struct{loss: TensorHandle, params: []const u64, expected_grads: []const f32, input_values: []const struct{id: u64, data: []const f32, shape: []const i64}},
              ^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
error: the following command failed with 4 compilation errors:
/opt/homebrew/Cellar/zig/0.14.0_2/bin/zig test -cflags -O3 -mcpu=apple-m1 -ffast-math -- /Users/<USER>/Library/CloudStorage/<EMAIL>/My Drive/mycode/zing/src/backends/c_src/gemm_optimized.c -ODebug --dep types --dep graph --dep tensor --dep training --dep compiler --dep execution --dep storage --dep build_options -Mroot=/Users/<USER>/Library/CloudStorage/<EMAIL>/My Drive/mycode/zing/src/tests/test_gradient_verification_comprehensive.zig --dep build_options -Mtypes=/Users/<USER>/Library/CloudStorage/<EMAIL>/My Drive/mycode/zing/src/types.zig --dep types --dep symbolic --dep shape --dep build_options -Mgraph=/Users/<USER>/Library/CloudStorage/<EMAIL>/My Drive/mycode/zing/src/graph.zig --dep types --dep graph --dep shape --dep symbolic --dep storage --dep build_options -Mtensor=/Users/<USER>/Library/CloudStorage/<EMAIL>/My Drive/mycode/zing/src/tensor.zig --dep types --dep graph --dep tensor --dep shape --dep compiler --dep execution --dep storage --dep build_options -Mtraining=/Users/<USER>/Library/CloudStorage/<EMAIL>/My Drive/mycode/zing/src/training.zig --dep types --dep graph --dep shape --dep symbolic --dep execution --dep backend_types --dep backends --dep storage --dep build_options -Mcompiler=/Users/<USER>/Library/CloudStorage/<EMAIL>/My Drive/mycode/zing/src/compiler.zig --dep types --dep backend_types --dep graph --dep shape --dep symbolic --dep storage --dep build_options -Mexecution=/Users/<USER>/Library/CloudStorage/<EMAIL>/My Drive/mycode/zing/src/execution.zig --dep types --dep build_options -Mstorage=/Users/<USER>/Library/CloudStorage/<EMAIL>/My Drive/mycode/zing/src/storage.zig -Mbuild_options=/Users/<USER>/Library/CloudStorage/<EMAIL>/My Drive/mycode/zing/.zig-cache/c/b6612c95cead9a1f8b89ea846fa38a7c/options.zig --dep types --dep build_options -Msymbolic=/Users/<USER>/Library/CloudStorage/<EMAIL>/My Drive/mycode/zing/src/symbolic.zig --dep types --dep symbolic -Mshape=/Users/<USER>/Library/CloudStorage/<EMAIL>/My Drive/mycode/zing/src/shape.zig --dep types --dep build_options -Mbackend_types=/Users/<USER>/Library/CloudStorage/<EMAIL>/My Drive/mycode/zing/src/backend_types.zig --dep types --dep backend_types --dep storage --dep graph --dep shape --dep build_options -Mbackends=/Users/<USER>/Library/CloudStorage/<EMAIL>/My Drive/mycode/zing/src/backends.zig -lc --cache-dir /Users/<USER>/Library/CloudStorage/<EMAIL>/My Drive/mycode/zing/.zig-cache --global-cache-dir /Users/<USER>/.cache/zig --name gradient-verification-comprehensive-tests --zig-lib-dir /opt/homebrew/Cellar/zig/0.14.0_2/lib/zig/ --listen=- 
Build Summary: 1/4 steps succeeded; 1 failed
test-gradient-verification-comprehensive transitive failure
+- run gradient-verification-comprehensive-tests transitive failure
   +- zig test gradient-verification-comprehensive-tests Debug native 4 errors
error: the following build command failed with exit code 1:
/Users/<USER>/Library/CloudStorage/<EMAIL>/My Drive/mycode/zing/.zig-cache/o/67acf90f9059226a1df1fd2ac8931910/build /opt/homebrew/Cellar/zig/0.14.0_2/bin/zig /opt/homebrew/Cellar/zig/0.14.0_2/lib/zig /Users/<USER>/Library/CloudStorage/<EMAIL>/My Drive/mycode/zing /Users/<USER>/Library/CloudStorage/<EMAIL>/My Drive/mycode/zing/.zig-cache /Users/<USER>/.cache/zig --seed 0x24c826b9 -Z95fddafef472fd0e test-gradient-verification-comprehensive
