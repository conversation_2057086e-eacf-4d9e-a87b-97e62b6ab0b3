# Broadcasting Design in Zing

## Current State

Zing has the infrastructure for broadcasting but it's not fully implemented:

1. **ShapeTracker** has a `fake` field for marking broadcast dimensions (similar to Luminal)
2. **Binary operations** call `inferBroadcastShape` to compute output shapes
3. **expand()** function exists but isn't used in the matmul decomposition
4. **Backend kernels** don't handle index mapping for broadcasted tensors

## How Luminal Handles Broadcasting

Based on analysis of Luminal's CPU backend:

- **Broadcasting is NOT handled in backend kernels**
- Uses ShapeTracker's "fake" dimensions to mark broadcasted axes
- Index expressions in ShapeTracker map logical indices to physical memory
- Kernels remain simple - they just iterate and let ShapeTracker handle mapping
- No explicit broadcasting loops or logic in kernels

## Design Decision

**Broadcasting should be handled at the graph/ShapeTracker level, NOT in backend kernels**

Reasons:
1. Keeps backend kernels simple and focused
2. Reuses the same mechanism across all backends
3. Avoids duplicating broadcasting logic in each backend
4. Enables zero-copy broadcasting through index mapping

## What Needs to Be Fixed

1. **Tensor operations** should properly use `expand()` with fake dimensions instead of reshape
2. **ShapeTracker** needs index mapping functions that respect fake dimensions
3. **Backend kernels** need to use ShapeTracker's index mapping instead of direct array access
4. **Matmul decomposition** should use proper broadcasting operations

## Temporary Workaround

Until proper broadcasting is implemented, operations requiring broadcasting (like matmul) will produce incorrect results. The architecture is sound, but the implementation is incomplete.