const std = @import("std");
const testing = std.testing;
const print = std.debug.print;

// Import Zing components
const types = @import("src/types.zig");
const Graph = @import("src/graph.zig").Graph;
const tensor = @import("src/tensor.zig");
const training = @import("src/training.zig");
const compiler = @import("src/compiler.zig");
const execution = @import("src/execution.zig");

test "simple matmul gradient test" {
    const allocator = testing.allocator;
    
    print("\n=== Testing: simple matmul gradient ===\n", .{});
    
    var graph = try Graph.init(allocator);
    defer graph.deinit();
    
    // Create simple inputs
    const a = try tensor.placeholder(&graph, &.{2, 3}, .f32); // [2x3]
    const b = try tensor.placeholder(&graph, &.{3, 2}, .f32); // [3x2]
    
    // Matmul: [2x3] @ [3x2] = [2x2]
    const c = try a.matmul(b);
    
    // Sum to scalar loss
    const loss = try c.sumAll();
    
    print("Created graph with {} nodes\n", .{graph.nodes.items.len});
    print("a: node_id={}, shape=[{},{}]\n", .{a.node_id, a.shape.dims[0].concrete, a.shape.dims[1].concrete});
    print("b: node_id={}, shape=[{},{}]\n", .{b.node_id, b.shape.dims[0].concrete, b.shape.dims[1].concrete});
    print("c: node_id={}, shape=[{},{}]\n", .{c.node_id, c.shape.dims[0].concrete, c.shape.dims[1].concrete});
    print("loss: node_id={}\n", .{loss.node_id});
    
    try graph.output_nodes.append(graph.arena.allocator(), loss.node_id);
    
    // Apply autograd
    print("\nApplying autograd...\n", .{});
    try training.autograd.applyAutograd(&training.autograd.PassContext{
        .allocator = allocator,
        .graph = &graph,
    }, &.{a.node_id, b.node_id}, loss.node_id);
    
    print("Graph after autograd has {} nodes\n", .{graph.nodes.items.len});
    
    // Check gradients exist
    const a_grad_id = graph.getNodeGradient(a.node_id);
    const b_grad_id = graph.getNodeGradient(b.node_id);
    
    if (a_grad_id == null or b_grad_id == null) {
        print("ERROR: Gradients not created - a_grad={}, b_grad={}\n", .{a_grad_id, b_grad_id});
        return error.NoGradients;
    }
    
    print("✓ Gradients created: a_grad={}, b_grad={}\n", .{a_grad_id.?, b_grad_id.?});
    
    // Compile and check gradient shapes
    var compiled = try compiler.compile.compileCpu(&graph, allocator);
    defer compiled.deinit(allocator);
    
    print("\n✓ simple matmul gradient test passed!\n", .{});
}