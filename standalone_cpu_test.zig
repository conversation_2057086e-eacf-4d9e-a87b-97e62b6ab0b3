/// Standalone CPU Backend Test
/// 
/// This is a standalone test that demonstrates the CPU backend working correctly
/// for matrix multiplication without the complex modular build system.

const std = @import("std");
const testing = std.testing;

// Import everything directly to avoid module conflicts
const types = @import("src/types.zig");
const Graph = @import("src/graph.zig").Graph;
const backends = @import("src/backends.zig");
const cpu_backend = @import("src/backends/cpu.zig");

const NodeId = types.NodeId;
const DataType = types.DataType;
const ComputeOp = types.ComputeOp;

test "Standalone CPU backend matrix multiplication test" {
    const allocator = testing.allocator;
    
    std.debug.print("\n=== Standalone CPU Backend Test ===\n", .{});
    
    // ===== STEP 1: Create Simple Graph =====
    std.debug.print("Step 1: Creating computation graph...\n", .{});
    
    var graph = try Graph.init(allocator);
    defer graph.deinit();
    
    // Create input placeholders
    const input_a_id = try graph.addNode(.{
        .data = .{ .placeholder = .{ .dtype = .f32 } }
    });
    const input_b_id = try graph.addNode(.{
        .data = .{ .placeholder = .{ .dtype = .f32 } }
    });
    
    // Create multiplication operation
    const mul_id = try graph.addNode(.{
        .compute = .mul
    });
    try graph.addEdge(input_a_id, mul_id, 0);
    try graph.addEdge(input_b_id, mul_id, 1);
    
    std.debug.print("  ✓ Created graph with {} nodes\n", .{graph.nodes.items.len});
    std.debug.print("    Input A: node {}\n", .{input_a_id});
    std.debug.print("    Input B: node {}\n", .{input_b_id});
    std.debug.print("    Multiply: node {}\n", .{mul_id});
    
    // ===== STEP 2: Register CPU Backend =====
    std.debug.print("Step 2: Testing CPU backend registration...\n", .{});
    
    try cpu_backend.registerCpuBackend();
    const registry = try backends.getGlobalRegistry();
    const cpu_vtable = registry.get("cpu").?;
    
    std.debug.print("  ✓ Registered backend: {s}\n", .{cpu_vtable.getName()});
    
    // ===== STEP 3: Test Backend Context =====
    std.debug.print("Step 3: Creating backend context...\n", .{});
    
    const cpu_context = try cpu_vtable.createContext(allocator, "{}");
    defer cpu_vtable.destroyContext(cpu_context);
    
    const capabilities = cpu_vtable.getCapabilities(cpu_context);
    std.debug.print("  ✓ Context created with capabilities:\n", .{});
    std.debug.print("    SIMD support: {}\n", .{capabilities.supports_simd});
    std.debug.print("    SIMD width: {} bits\n", .{capabilities.simd_width});
    std.debug.print("    Max threads: {}\n", .{capabilities.max_threads});
    std.debug.print("    Cache line: {} bytes\n", .{capabilities.cache_line_size});
    std.debug.print("    Kernel fusion: {}\n", .{capabilities.supports_kernel_fusion});
    
    // ===== STEP 4: Test Kernel Registry =====
    std.debug.print("Step 4: Creating kernel registry...\n", .{});
    
    var kernel_registry = try cpu_vtable.createKernelRegistry(&graph, cpu_context, allocator);
    defer kernel_registry.deinit(allocator);
    
    std.debug.print("  ✓ Kernel registry created with {} kernels\n", .{kernel_registry.kernels.count()});
    
    // ===== STEP 5: Test Kernel Lookup =====
    std.debug.print("Step 5: Testing kernel lookup...\n", .{});
    
    const mul_kernel = kernel_registry.getKernel(mul_id);
    try testing.expect(mul_kernel != null);
    
    std.debug.print("  ✓ Found multiply kernel for node {}\n", .{mul_id});
    
    // ===== STEP 6: Test Kernel Execution =====
    std.debug.print("Step 6: Testing kernel execution...\n", .{});
    
    // Test data: simple 2x2 matrix element-wise multiplication
    // A = [[1, 2], [3, 4]]  B = [[2, 2], [2, 2]]
    // Expected: [[2, 4], [6, 8]]
    const input_a_data = [_]f32{ 1.0, 2.0, 3.0, 4.0 };
    const input_b_data = [_]f32{ 2.0, 2.0, 2.0, 2.0 };
    var output_data = [_]f32{ 0.0, 0.0, 0.0, 0.0 };
    
    const inputs = [_][]const u8{
        std.mem.asBytes(&input_a_data),
        std.mem.asBytes(&input_b_data),
    };
    const outputs = [_][]u8{
        std.mem.asBytes(&output_data),
    };
    
    const kernel_args = backends.KernelArgs{
        .inputs = &inputs,
        .outputs = &outputs,
        .work_size = 4, // 4 elements
        .custom_data = null,
    };
    
    // Execute the kernel
    mul_kernel.?(kernel_args);
    
    // Verify results
    const expected_result = [_]f32{ 2.0, 4.0, 6.0, 8.0 };
    
    var all_correct = true;
    for (output_data, expected_result, 0..) |actual, expected, i| {
        const error_abs = @abs(actual - expected);
        if (error_abs > 1e-6) {
            std.debug.print("    ❌ Mismatch at element {}: got {d:.6}, expected {d:.6}\n", .{ i, actual, expected });
            all_correct = false;
        }
    }
    
    if (all_correct) {
        std.debug.print("  ✓ Kernel execution successful:\n", .{});
        std.debug.print("    Input A: [1, 2, 3, 4]\n", .{});
        std.debug.print("    Input B: [2, 2, 2, 2]\n", .{});
        std.debug.print("    Result:  [{d:.0}, {d:.0}, {d:.0}, {d:.0}]\n", 
            .{ output_data[0], output_data[1], output_data[2], output_data[3] });
    }
    
    try testing.expect(all_correct);
    
    // ===== STEP 7: Test Backend Capabilities =====
    std.debug.print("Step 7: Testing backend capability queries...\n", .{});
    
    const vec_hints = capabilities.getVectorizationHints();
    const fusion_hints = capabilities.getFusionHints();
    const memory_hints = capabilities.getMemoryHints();
    
    // Verify reasonable values
    try testing.expect(vec_hints.preferred_width >= 128);
    try testing.expect(fusion_hints.max_fused_ops > 0);
    try testing.expect(memory_hints.preferred_alignment > 0);
    
    std.debug.print("  ✓ Capability queries working:\n", .{});
    std.debug.print("    Vectorization width: {} bits\n", .{vec_hints.preferred_width});
    std.debug.print("    Max fused ops: {}\n", .{fusion_hints.max_fused_ops});
    std.debug.print("    Memory alignment: {} bytes\n", .{memory_hints.preferred_alignment});
    
    std.debug.print("\n=== CPU Backend Test COMPLETED SUCCESSFULLY! ===\n", .{});
    std.debug.print("✓ All components working:\n", .{});
    std.debug.print("  • Backend registration\n", .{});
    std.debug.print("  • Context creation\n", .{});
    std.debug.print("  • Capability queries\n", .{});
    std.debug.print("  • Kernel registry\n", .{});
    std.debug.print("  • Kernel execution\n", .{});
    std.debug.print("  • Result verification\n", .{});
}