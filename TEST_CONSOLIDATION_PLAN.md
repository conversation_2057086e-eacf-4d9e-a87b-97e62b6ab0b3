# Test Consolidation Plan

## Current Structure
- Unit tests embedded in `src/*.zig` files
- 30+ debug/diagnostic test files in `src/tests/`
- 12 integration test files in `src/tests/integration/`
- Pattern-based test files in `src/tests/`

## Proposed Structure

### 1. Keep Embedded Unit Tests (No Change)
- Leave unit tests in implementation files as-is
- These provide immediate verification and documentation

### 2. Reorganize `src/tests/` into:

#### `src/tests/unit/` - Consolidated Unit Tests
Merge debug tests into organized files:

1. **test_broadcast.zig**
   - From: test_broadcast_issue_debug.zig
   - From: test_broadcasting_comprehensive.zig (keep comprehensive tests)

2. **test_reduction.zig**
   - From: test_reduction_debug.zig
   - From: test_reduction_comprehensive.zig
   - From: test_complex_reduction_debug.zig
   - From: test_minimal_reduction.zig
   - From: test_reduction_memory_debug.zig

3. **test_activation.zig**
   - From: test_relu_debug.zig
   - From: test_relu_minimal.zig
   - From: test_relu_simple.zig
   - From: test_relu_trace.zig
   - From: test_softmax_debug.zig
   - From: test_sinh_debug.zig

4. **test_arithmetic.zig**
   - From: test_exp_*.zig (all exp tests)
   - From: test_multiplication_debug.zig
   - From: test_division_debug.zig
   - From: test_constant_*.zig

5. **test_memory.zig**
   - From: test_multi_output_debug.zig
   - From: test_multi_output_memory_debug.zig
   - From: test_metadata_issue.zig
   - From: test_shape_metadata_debug.zig

6. **test_operations.zig**
   - From: test_operations_comprehensive.zig
   - From: test_edge_cases_comprehensive.zig

#### `src/tests/integration/` - Keep As-Is
Already well-organized:
- full_integration.zig
- minimal_integration.zig
- tensor_ops_integration.zig
- test_*_ops.zig (by operation category)

#### `src/tests/patterns/` - Real-World Patterns
Move pattern tests:
- test_deep_learning_patterns.zig
- test_transformer_patterns.zig
- test_realistic_patterns.zig

### 3. Files to Remove After Consolidation
- All individual debug files that have been merged
- temp_test.zig

## Benefits
1. Reduces 30+ test files to ~10 organized files
2. Easier to find and run related tests
3. No test coverage is lost
4. Clear separation: unit vs integration vs patterns
5. Easier maintenance and discovery

## Migration Steps
1. Create new directory structure
2. Merge tests file by file, preserving all test cases
3. Update build.zig to reference new test locations
4. Run full test suite to ensure nothing broke
5. Remove old debug files