# Zing Memory Allocation Patterns - Comprehensive Audit Report

## Executive Summary

This audit examines memory allocation patterns across the Zing codebase to identify areas using Arena allocators, explicit allocations, ownership patterns, and potential memory management issues. The goal is to establish a consistent memory ownership model.

## 1. Arena Allocations

### graph.zig
- **Primary Arena**: `arena: ArenaAllocator` (line 54)
- **Usage Pattern**: Graph uses arena for ALL internal allocations
- **Arena-allocated structures**:
  - Node inputs arrays (line 237)
  - Node outputs arrays (line 231) 
  - Node indexes arrays (line 245)
  - Node fake arrays (line 253)
  - Consumer lists (line 101, 337)
  - Custom ops (line 303)
  - Node metadata (line 282)
  - Mask arrays (line 327)
  - Padding arrays (line 335)
- **Ownership**: Graph owns arena, all allocations freed on `deinit()`
- **Key Pattern**: Uses `arena.allocator()` for all allocations

### compiler/pipeline.zig
- **No Direct Arena Usage**
- **Allocation Pattern**: Uses passed allocator for temporary structures
- **Memory Management**: Caller-owned allocators

### Shape Module (shape/tracker.zig)
- **No Direct Arena Usage**
- **Allocation Pattern**: ShapeTracker owns all its arrays
- **Ownership Rules** (lines 26-42):
  - ShapeTracker ALWAYS owns dims, strides, indexes, fake, mask, padding
  - For arena-allocated ShapeTrackers (in TensorHandle), no explicit cleanup needed
  - For non-arena ShapeTrackers, call `deinit()` to free all arrays
- **Key Methods**:
  - `clone()` (line 66): Deep copies all arrays
  - `deinit()` (line 48): Frees all owned arrays
  - `fromDims()` (line 212): Allocates all arrays

## 2. Explicit Allocations

### graph.zig
- **Cached Topology** (line 631): `allocator.alloc(NodeId, ...)`
  - Freed in `invalidateCaches()` (line 639)
  - Owned by Graph
- **Temporary Arrays**:
  - Consumer updates (line 503): `allocator.dupe(NodeId, ...)`
  - Validation (line 423): `allocator.alloc(bool, ...)`
  - All use defer for cleanup

### storage.zig
- **DataStorage** (line 225):
  - Single arena allocation: `arena: []u8` (line 232)
  - Metadata shape arrays (lines 269, 379)
  - Uses `DeviceMemory.allocate()` for main arena
  - **Pattern**: Bump allocator within arena
- **ParameterStore** (line 428):
  - Owns all OwnedTensor instances
  - Each OwnedTensor owns its data, shape, strides
  - Gradients stored separately in `grads` map
  - **Clear Ownership**: ParameterStore owns tensors, tensors own arrays

### execution.zig
- **ResolvedMemoryPlan** (line 84):
  - Owns allocation shapes: `allocator.dupe(i64, ...)` (line 409)
  - Has `deinit()` method (line 88)
- **Temporary Arrays**:
  - Symbol resolution (line 254): Uses centralized function
  - Shape resolution (line 443): Uses centralized function
  - All properly freed

### compiler.zig
- **inferAllShapes** (line 117):
  - Creates HandleShapeMap for compilation
  - Clones shapes from input (line 135)
  - Temporary allocations for shape inference (line 162)
  - All use defer for cleanup

### backends/cpu.zig
- **Pattern Compilation**:
  - Uses graph's arena for fusion patterns (lines 305, 366, 436)
  - Temporary tracking maps (line 693)
  - Output remapping array (line 716)
  - All temporary allocations properly freed

## 3. Ownership Patterns

### Clear Ownership Hierarchies

1. **Graph Ownership**:
   - Graph → Arena → All node data
   - Graph → SymbolicPool
   - Graph → Cached topology (explicit)

2. **Storage Ownership**:
   - DataStorage → Arena buffer → All allocations
   - DataStorage → Metadata (shapes, strides)
   - ParameterStore → OwnedTensors → tensor data

3. **Shape Ownership**:
   - ShapeTracker → All arrays (dims, strides, etc.)
   - TensorHandle → ShapeTracker (embedded)
   - HandleShapeMap → Cloned ShapeTrackers

4. **Execution Ownership**:
   - Executor → Current symbol bindings
   - Executor → Current memory plan
   - CompiledGraph → Execution order, shapes, memory plan

## 4. Mixed Allocation Patterns

### graph.zig
- **Mixed Pattern**: Arena for most, explicit for cached topology
- **Reason**: Topology can be invalidated/recreated independently
- **Risk**: Low - clear ownership and cleanup

### storage.zig
- **DataStorage**: Arena for buffers, explicit for metadata
- **Reason**: Metadata needs to persist across resets
- **Risk**: Medium - must track metadata cleanup in reset()

### execution.zig
- **Mixed Pattern**: Uses both graph arena and explicit allocations
- **Reason**: Some data outlives graph modifications
- **Risk**: Low - clear delineation

## 5. Potential Issues

### Memory Leaks

1. **storage.zig DataStorage.reset()** (line 373):
   - Properly frees metadata shapes/strides
   - ✓ No leak risk

2. **graph.zig GraphSnapshot** (line 646):
   - Deep copies all data
   - Has proper `deinit()` method
   - ✓ No leak risk if deinit called

3. **shape/tracker.zig** view operations:
   - Old arrays not freed in transpose/expand/etc.
   - ⚠️ **ISSUE**: Memory leak if not arena-allocated
   - **Fix**: Free old arrays before reassignment

### Unclear Ownership

1. **TensorHandle.shape**:
   - ✓ Clear: TensorHandle owns embedded ShapeTracker
   - Arena-allocated handles don't need cleanup

2. **HandleShapeMap in compilation**:
   - ✓ Clear: Temporary map owns cloned shapes
   - Properly cleaned up after compilation

3. **Custom op data**:
   - ✓ Clear: Graph owns via custom_ops map
   - Arena-allocated with graph

### Double-Free Risks

1. **No double-free risks identified**
   - Clear ownership boundaries
   - No shared ownership without reference counting

## 6. Recommendations

### 1. Fix ShapeTracker View Operations
```zig
// In transpose(), slice(), expand(), pad()
pub fn transpose(self: *ShapeTracker, axes: []const usize, allocator: Allocator) !void {
    // ... create new arrays ...
    
    // Free old arrays if not arena-allocated
    if (!self.isArenaAllocated()) {
        allocator.free(self.dims);
        allocator.free(self.strides);
        allocator.free(self.indexes);
        allocator.free(self.fake);
        if (self.mask) |m| allocator.free(m);
        if (self.padding) |p| allocator.free(p);
    }
    
    // Update with new arrays
    self.dims = new_dims;
    // ...
}
```

### 2. Standardize Allocation Strategy
- **Primary Rule**: Use arena allocation for all graph-lifetime data
- **Exception**: Use explicit allocation only for:
  - Data that needs independent lifecycle (cached topology)
  - Cross-execution persistent data (ParameterStore)
  - Temporary computation data with clear scope

### 3. Document Ownership Model
```zig
/// Memory Ownership Model:
/// 
/// 1. Graph owns all computation structure via arena
/// 2. Storage owns execution buffers and persistent parameters
/// 3. ShapeTrackers own their arrays (arena or explicit)
/// 4. Executors borrow from Graph/Storage, own only temporary data
/// 
/// Arena Usage:
/// - Graph: All node/edge/consumer data
/// - TensorHandle: Embedded ShapeTrackers
/// - Compilation: Temporary analysis structures
/// 
/// Explicit Usage:
/// - ParameterStore: Long-lived model parameters
/// - DataStorage metadata: Survives buffer resets
/// - Cached computations: Independent lifecycle
```

### 4. Add Memory Ownership Assertions
```zig
const OwnershipMode = enum { arena, explicit, borrowed };

pub fn assertOwnership(comptime expected: OwnershipMode, actual: OwnershipMode) void {
    if (expected != actual) {
        @compileError("Ownership mode mismatch");
    }
}
```

### 5. Consistent Cleanup Patterns
- Always provide `deinit()` for explicit allocations
- Use `defer` for temporary allocations
- Document arena vs explicit in struct comments

## 7. Summary Statistics

- **Files Analyzed**: 7 core files
- **Arena Allocators**: 2 (Graph, DataStorage)
- **Mixed Patterns**: 3 locations
- **Memory Leak Risks**: 1 (ShapeTracker view ops)
- **Ownership Issues**: 0 (all clear)
- **Double-Free Risks**: 0

## Conclusion

The Zing codebase demonstrates generally good memory management practices with clear ownership patterns. The primary issue is potential memory leaks in ShapeTracker view operations when not arena-allocated. The mixed allocation patterns are justified by lifecycle requirements and are well-managed. With the recommended fixes, the memory model would be robust and consistent.