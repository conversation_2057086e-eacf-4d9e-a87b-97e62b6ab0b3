# CLAUDE.md

# Workflow Guidelines for Claude

- Before creating new files, and adding new functionality, make sure we don't already have that functionality; if we already have it, but needs to be improved, work on the same file when possible.
- Before making any code changes, always explain your intended approach and the reasoning behind it.
- Wait for explicit user approval before proceeding to write or modify any code.
- When given a task, first outline a step-by-step plan or to-do list for how you will approach the problem.
- Only after the plan is reviewed and approved should you begin implementation.
- If you are unsure about the requirements or the next step, ask for clarification before continuing.
- Summarize all changes made after implementation and provide a rationale for each change.
- Avoid implementing quick fixes or patches that only make tests pass but do not address the underlying problem or meet long-term quality standards.
- Prioritize elegant, maintainable, and idiomatic solutions over short-term workarounds.
- Do not declare a feature or code change as complete until its functionality is verified by passing unit tests
- Unit tests should be added at the bottom of the relevant Zig files containing the implementation not in separate files so we can always run them during the build
- Creating temporary test files during debugging is fine, but when you are done, delete them, and add the final unit test in the right place

# Idiomatic Zig 0.14 Guidelines

## Quick Reference

**Key Principles:**
- Explicit over implicit (especially allocations and errors)
- No hidden control flow (avoid implicit try or catch-all)
- Compile-time verification when possible
- Zero-cost abstractions
- Composition over inheritance

**Essential Patterns:**
- Unmanaged containers (allocator passed to methods)
- Direct imports (no abstraction layers)
- Root export files for packages
- Arena allocators for batch operations
- Tagged unions for state machines
- Generic type functions for reusable data structures
- Direct error logging at failure sites (no context parameters)

**Common Anti-Patterns to Avoid:**
- Hidden allocations
- Silent error handling (log errors at failure site)
- Over-generic APIs
- Deep module hierarchies (no more than 2 nested folders)
- Circular dependencies
- Cryptic error messages (log with context at error site)
- Error context structs (over-engineering for Zig)

## Formatting and Naming

**Automatically handled by `zig fmt`:**
- 4 spaces per indent
- Opening braces on same line
- Trailing commas in multi-line lists

**Naming Conventions:**
- **Functions**: `camelCase` (e.g., `myFunction`, `calculateValue`)
- **Types/Structs**: `PascalCase` (e.g., `MyStruct`, `StatusEnum`)
- **Variables/Constants**: `snake_case` (e.g., `my_variable`, `max_value`)
- **Files**: `snake_case` (e.g., `my_module.zig`) - Note: Many projects use one public type per file with matching name (e.g., `ArrayList.zig` for `ArrayList` type). Follow your repository's convention.
- **Error Sets/Enum Variants**: `PascalCase` (e.g., `FileNotFound`, `MyVariant`)

## Documentation

```zig
//! Module-level documentation (at file top)

/// Public declaration documentation
pub fn myFunction() void {}

// Regular comments (not in docs)
```

Focus on *why* code exists, not *what* it obviously does.

## Error Handling

**Core Patterns:**
```zig
// Propagate errors
const result = try potentiallyFailingFunction();

// Handle errors explicitly  
const result = potentiallyFailingFunction() catch |err| {
    // handle err
};

// Cleanup patterns
        const file = try std.fs.cwd().openFile("data.txt", .{});
defer file.close(); // Always runs

const data = try file.readToEndAlloc(allocator, 1024);
errdefer allocator.free(data); // Only on error
// data is freed only on error; pointer still valid on success
```

**Key Points:**
- Use error unions (`!T`) for recoverable failures
- Always handle or propagate errors explicitly
- Use `defer`/`errdefer` for resource cleanup
- Document all possible errors in function comments

**Idiomatic Zig Error Handling (Production Approach):**

Based on production Zig codebases like TigerBeetle, idiomatic error handling in Zig focuses on:
1. Specific, meaningful error types
2. Direct logging at error sites with all relevant context
3. Using `errdefer` for conditional error context
4. NO error context structs or diagnostic patterns

```zig
// ✅ IDIOMATIC - Specific error enums
pub const ExecutionError = error{
    BufferNotFound,
    BufferTooSmall,
    UnresolvedSymbol,
    InvalidNodeId,
    KernelExecutionFailed,
};

// ✅ IDIOMATIC - Direct error logging at failure site
pub fn executeStep(self: *Self, exec_step: ExecutionStep) !void {
    const kernel = exec_step.kernel_fn;
    
    // Gather input buffers
    var input_buffers: [MAX_INPUTS][]const u8 = undefined;
    for (exec_step.input_buffers, 0..) |buffer_id, i| {
        input_buffers[i] = self.data_storage.?.getBufferById(buffer_id) orelse {
            std.log.err("executeStep: input buffer {} not found at index {} for node {}", 
                       .{ buffer_id, i, exec_step.node_id });
            return error.BufferNotFound;
        };
    }
    
    // Execute with errdefer for failure context
    errdefer |err| {
        std.log.err("kernel execution failed for node {}: {}", 
                   .{ exec_step.node_id, err });
    };
    
    try kernel(args);
}

// ✅ IDIOMATIC - Meaningful error types that are self-documenting
pub const SymbolResolutionResult = enum {
    ok,
    unbound_symbol,
    circular_dependency,
    type_mismatch,
};

// ❌ NON-IDIOMATIC - Error context structs (over-engineered)
pub const ExecutionContext = struct {
    operation: []const u8,
    node_id: ?NodeId,
    message: [256]u8,
    // This pattern is NOT used in production Zig
};
```

**Production Error Handling Principles:**
- **Specific error types**: Create error sets that clearly indicate what went wrong
- **Log at error site**: Include all relevant context in the log message where the error occurs
- **Use errdefer**: For adding context when an error is propagated
- **Keep it simple**: No context structs, no diagnostic patterns
- **Self-documenting errors**: Error names should make the problem clear

**When to log errors:**
- ✅ At the point of failure with all available context
- ✅ In errdefer blocks for additional context on propagation
- ✅ At API boundaries for user-facing errors
- ❌ NOT through complex context structs
- ❌ NOT by passing context parameters everywhere

## Memory Management

**Explicit Allocators (Required):**
        ```zig
// ✅ GOOD - Explicit allocator
        fn createObject(allocator: std.mem.Allocator, size: usize) !*MyObject {
            const ptr = try allocator.create(MyObject);
    return ptr;
}

// ❌ BAD - Hidden allocation
fn createObject(size: usize) !*MyObject {
    const ptr = try global_allocator.create(MyObject); // Hidden!
            return ptr;
        }
        ```

**Unmanaged Containers:**
        ```zig
// Unmanaged: allocator passed to each method (cache-sensitive hot structs)
var list = std.ArrayListUnmanaged(u32){};
try list.append(allocator, 42);
defer list.deinit(allocator);

// Managed: stores allocator internally (fine when list and allocator share lifetime)
var list = std.ArrayList(u32).init(allocator);
try list.append(42);
defer list.deinit();
```

**Note**: Managed lists remain fine when the list and its allocator share lifetime. Unmanaged avoids storing allocator for cache-sensitive hot structs.

**Memory Safety - `undefined` vs Zero-initialization:**
```zig
// Performance-critical but unsafe (must write before read)
var buffer: [1024]u8 = undefined;

// Safe default
var buffer: [1024]u8 = std.mem.zeroes([1024]u8);
var buffer = [_]u8{0} ** 1024; // Alternative syntax

// Struct initialization
var my_struct = MyStruct{}; // Zero-initialized
var my_struct: MyStruct = undefined; // Uninitialized (dangerous)
```

**Common Patterns:**
- **`init`/`deinit`**: For resource management without direct allocation
- **`create`/`destroy`**: For functions that allocate/deallocate the main resource
- **Arena Allocators**: For batch operations with same lifetime
- **`defer`/`errdefer`**: Pair allocation with cleanup in same scope

**Arena Allocator Pattern:**
```zig
pub const BatchProcessor = struct {
    backing_allocator: Allocator,
    arena: ArenaAllocator,
    
    pub fn init(allocator: Allocator) BatchProcessor {
        return .{
            .backing_allocator = allocator,
            .arena = ArenaAllocator.init(allocator),
        };
    }
    
    pub fn deinit(self: *BatchProcessor) void {
        self.arena.deinit();
    }
    
    pub fn processBatch(self: *BatchProcessor, items: []const Item) !Results {
        defer _ = self.arena.reset(.retain_capacity);
        const temp_data = try self.arena.child_allocator.alloc(TempData, items.len);
        // All temp allocations freed automatically on reset
    }
};
```

## Modules and Imports

**Import Organization:**
```zig
// 1. Standard library
const std = @import("std");

// 2. External packages  
const ext_lib = @import("external");

// 3. Local project files
const my_module = @import("my_module.zig");
```

**Key Points:**
- Each `.zig` file is a namespace struct
- Use `pub` for public declarations
- Avoid deep directory hierarchies
- Prefer direct imports over abstraction layers

**API Design with Opaque Types:**
        ```zig
// Hide implementation details
        const MyHandle = opaque {};

        pub fn createHandle() *MyHandle { /* ... */ }
pub fn destroyHandle(handle: *MyHandle) void { /* ... */ }
pub fn useHandle(handle: *MyHandle, data: []const u8) !void { /* ... */ }
        ```

## Package Structure Patterns

**Root Export Pattern:**
    ```zig
// lib.zig - Main export file
    pub const ArrayList = @import("array_list.zig").ArrayList;
pub const HashMap = @import("hash_map.zig").HashMap;

// Namespace modules
    pub const mem = @import("mem.zig");
pub const fmt = @import("fmt.zig");

// Use struct for nested namespaces (unless you intend compile-time constant)
pub const advanced = struct {
    pub const parser = @import("advanced/parser.zig");
    pub const optimizer = @import("advanced/optimizer.zig");
};
    ```

**Type File Pattern:**
```zig
// SomeType.zig - File IS the type
const Self = @This();

allocator: Allocator,
data: []u8,

pub fn init(allocator: Allocator) Self {
    return .{ .allocator = allocator, .data = &.{} };
}

pub fn deinit(self: *Self) void {
    self.allocator.free(self.data);
}
```

## Compile-Time Features

**Generic Functions:**
```zig
fn max(comptime T: type, a: T, b: T) T {
    return if (a > b) a else b;
}

// Generic type function with @This()
pub fn ArrayList(comptime T: type) type {
    return struct {
        const Self = @This(); // Critical Zig pattern
        
        items: []T,
        allocator: Allocator,
        
        pub fn init(allocator: Allocator) Self {
            return Self{
                .items = &.{},
                .allocator = allocator,
            };
        }
    };
}
```

**Type Introspection and Reflection:**
```zig
// Get type information
const type_info = @typeInfo(MyStruct);
const fields = type_info.Struct.fields;

// Access fields by name
const value = @field(obj, "field_name");

// Get parent pointer from field
const parent = @fieldParentPtr(ParentType, "field_name", field_ptr);

// Type and tag names
const type_name = @typeName(T);
const tag_name = @tagName(enum_value);

// Field access in generic code
pub fn getFieldByName(obj: anytype, comptime field_name: []const u8) FieldType(@TypeOf(obj), field_name) {
    return @field(obj, field_name);
}

fn FieldType(comptime T: type, comptime field_name: []const u8) type {
    inline for (@typeInfo(T).Struct.fields) |field| {
        if (std.mem.eql(u8, field.name, field_name)) {
            return field.type;
        }
    }
    // In 0.14, you can also return anytype and rely on inference
    @compileError("Field '" ++ field_name ++ "' not found in type " ++ @typeName(T));
}
```

**Comptime Code Generation:**
```zig
// Generate enum from string array
pub fn StringEnum(comptime strings: []const []const u8) type {
    var fields: [strings.len]std.builtin.Type.EnumField = undefined;
    for (strings, 0..) |str, i| {
        fields[i] = .{ .name = str, .value = i };
    }
    
    return @Type(.{
        .Enum = .{
            .tag_type = std.math.IntFittingRange(0, strings.len - 1),
            .fields = &fields,
            .decls = &.{},
            .is_exhaustive = true,
        },
    });
}

// Usage
const Color = StringEnum(&.{ "red", "green", "blue" });
```

**Compile-time Type Validation:**
```zig
fn assertIsNumeric(comptime T: type) void {
    switch (@typeInfo(T)) {
        .Int, .Float, .ComptimeInt, .ComptimeFloat => {},
        else => @compileError("Type " ++ @typeName(T) ++ " is not numeric"),
    }
}

pub fn sum(comptime T: type, values: []const T) T {
    comptime assertIsNumeric(T);
    var result: T = @as(T, 0); // Avoids lossy warning for floats
    for (values) |v| result += v;
    return result;
}
```

**Inline Loops and Performance:**
        ```zig
// Compile-time loop unrolling
pub fn dot4(a: [4]f32, b: [4]f32) f32 {
    var result: f32 = 0;
    comptime var i = 0;
    inline while (i < 4) : (i += 1) {
        result += a[i] * b[i];
    }
    return result;
}

// Inline for loops
inline for (@typeInfo(MyStruct).Struct.fields) |field| {
    // Unrolled at compile time
    std.debug.print("Field: {s}\n", .{field.name});
}
```

**Use `comptime` for:**
- Generic data structures and algorithms
- Type validation and constraints
- Code generation and metaprogramming
- Static data initialization
- Performance-critical loop unrolling

## Advanced Zig Patterns

**Anonymous Struct Literals:**
```zig
// Very common Zig pattern
const config = .{ .dtype = .f32, .device = .cpu, .requires_grad = false };

// In function calls
try createTensor(allocator, &.{2, 3}, .{ .dtype = .f64 });

// Namespace-like usage
pub const tensor = .{
    .zeros = createZeros,
    .ones = createOnes,
    .creation = @import("creation.zig"),
};
```

**Sentinel-Terminated Types:**
```zig
// Null-terminated strings (C compatibility)
const c_string: [:0]const u8 = "hello";
const z_string = "hello"; // Same as [:0]const u8

// Sentinel-terminated slices (array sentinel form removed in 0.14)
const array = [_]u32{1, 2, 3, 0}; // Manual sentinel
const slice: [:0]const u32 = &.{1, 2, 3, 0}; // Slice with sentinel

// Multi-byte sentinels
const wide_string: [:0x0000]const u16 = &.{0x48, 0x65, 0x6c, 0x6c, 0x6f, 0};
```

**Tagged Unions and State Machines:**
```zig
pub const State = union(enum) {
    idle: void,
    connecting: struct {
        start_time: i64,
        attempts: u32,
    },
    connected: struct {
        socket: Socket,
        peer_addr: Address,
    },
    error: struct {
        code: ErrorCode,
    message: []const u8,
    },
    
    pub fn isActive(self: State) bool {
        return switch (self) {
            .connecting, .connected => true,
            .idle, .error => false,
        };
    }
};
```

**Labeled Switch Statements (0.14.0):**
```zig
// Control flow in complex state machines
outer: switch (state) {
    .parsing => |data| {
        for (data.tokens) |token| {
            switch (token.type) {
                .error => break :outer, // Break from outer switch
                .skip => {}, // Skip this token
                else => {}, // Handle token
            }
        }
    },
    .done => {},
}
```

## Build System

**Basic `build.zig`:**
        ```zig
const std = @import("std");

pub fn build(b: *std.Build) void {
    const target = b.standardTargetOptions(.{});
    const optimize = b.standardOptimizeOption(.{});

    const exe = b.addExecutable(.{
        .name = "my_app",
        .root_source_file = .{ .path = "src/main.zig" },
        .target = target,
        .optimize = optimize,
    });

    b.installArtifact(exe);

    // Test step
    const unit_tests = b.addTest(.{
        .root_source_file = .{ .path = "src/main.zig" },
        .target = target,
        .optimize = optimize,
    });

    const test_step = b.step("test", "Run unit tests");
    test_step.dependOn(&b.addRunArtifact(unit_tests).step);
}
```

**Build Options:**
        ```zig
// Configurable build options
const enable_feature = b.option(bool, "feature", "Enable special feature") orelse false;
if (enable_feature) {
    exe.defineCMacro("ENABLE_FEATURE", "1");
}

// Dependencies
const dep = b.dependency("some_dependency", .{
    .target = target,
    .optimize = optimize,
});
exe.addModule("dep", dep.module("some_module"));
```

## Testing

**Basic Test Structure:**
        ```zig
const std = @import("std");
const expect = std.testing.expect;
const expectEqual = std.testing.expectEqual;

test "my function works" {
    const result = myFunction(42);
    try expectEqual(@as(i32, 84), result);
}
```

**Advanced Testing Patterns:**
        ```zig
// Parameterized tests
test "parameterized operations" {
    const test_cases = [_]struct {
        input: i32,
        expected: i32,
    }{
        .{ .input = 0, .expected = 0 },
        .{ .input = 5, .expected = 25 },
        .{ .input = -3, .expected = 9 },
    };
    
    for (test_cases) |tc| {
        const result = square(tc.input);
        try expectEqual(tc.expected, result);
    }
}

// Test-only exports
pub const testing = struct {
    pub const helper = if (@import("builtin").is_test) internalHelper else {};
        };
        ```

**Key Points:**
- Use `std.testing.allocator` for test allocations (detects leaks)
- Write tests in same files as implementation
- Use `zig build test` to run all tests

**Leak Detection:**
```zig
test "no memory leaks" {
    const allocator = std.testing.allocator;
    
    // Your test code here
    var list = std.ArrayList(u32).init(allocator);
    defer list.deinit();
    try list.append(42);
    
    // Verify no leaks at end of test
    try std.testing.expect(std.testing.allocator_instance.detectLeaks() == false);
}
```

## C Interoperability

**Importing C Headers:**
        ```zig
const c = @cImport({
    @cInclude("stdio.h");
    @cInclude("my_library.h");
});
```

**Exporting to C:**
        ```zig
export fn zig_function(arg: c_int) c_int {
    return arg * 2;
}

// 0.14.0: @export for functions (no pointer needed)
fn my_func() void {}
comptime {
    @export(my_func, .{ .name = "my_func", .linkage = .Strong });
}

// Variables do take pointers
var my_global: i32 = 10;
comptime {
    @export(&my_global, .{ .name = "exported_global", .linkage = .Strong });
}
```

**Best Practices:**
- Wrap C APIs in safe Zig functions
- Convert C errors to Zig error unions
- Manage C memory explicitly
- Use `build.zig` for C compilation and linking

## Architecture Adherence Protocol

When working with existing architectures (especially those with clear component separation and data flow), follow this protocol to prevent architectural assumption mistakes:

### Understanding Component Boundaries

**Always identify the architectural layers first:**
1. **Read the architecture overview** to understand component separation
2. **Identify the data flow direction** (usually unidirectional)
3. **Understand which components handle which responsibilities**
4. **Note any constraints** (e.g., "only handles primitive operations")

### Component Responsibility Mapping

**Before making any architectural decisions, explicitly map:**
- **What does each component do?** (GraphEngine, ShapeEngine, SymbolicEngine, etc.)
- **What operations does each handle?** (primitives vs high-level)
- **How do components communicate?** (via IDs, direct calls, etc.)
- **What creates nodes vs what creates trackers?** (computation vs views)

### Anti-Pattern Prevention

**Prevent recurring mistakes by asking:**
1. **"Does this component actually handle this operation?"**
   - Example: ShapeEngine only handles ~12 primitives, NOT high-level ops like matmul
   - High-level ops decompose BEFORE reaching shape inference

2. **"Am I making assumptions about monolithic behavior?"**
   - Example: Don't assume shape operations create graph nodes
   - View operations (reshape, transpose) create trackers, NOT nodes

3. **"Am I respecting the data flow direction?"**
   - Example: In layered architecture, lower layers don't call higher layers
   - SymbolicEngine → ShapeEngine → GraphEngine, not the reverse

4. **"Have I checked what the actual primitive set is?"**
   - Always verify the OpType enum before assuming operations exist
   - Check if an operation decomposes vs being a primitive

### Verification Questions Before Implementation

**Always ask these questions:**
1. **Architecture Scope**: "What is the actual scope of what this component handles?"
2. **Operation Validation**: "Is this operation actually a primitive, or does it decompose?"
3. **Component Boundaries**: "Which component is responsible for this functionality?"
4. **Data Flow**: "Does this respect the unidirectional data flow?"
5. **Node vs Tracker**: "Does this operation create graph nodes or just shape trackers?"

### When Working with Existing Code

**Process for understanding existing architectures:**
1. **Start with the architecture overview document**
2. **Identify the primitive operation set** (usually ~12 operations)
3. **Map component responsibilities** before making changes
4. **Verify assumptions** against the actual implementation
5. **Check examples** of how operations are supposed to decompose

### Red Flags That Indicate Architectural Errors

**Stop and re-read architecture if you find yourself:**
- Adding operations to shape inference that aren't in the primitive set
- Making components call back to higher layers
- Creating graph nodes for view operations
- Assuming monolithic behavior in separated architectures
- Mixing responsibilities between components

### Example Application

```zig
// ❌ WRONG - Architectural assumption error
pub fn inferOpTracker(op: OpType, inputs: []const TrackerId) !TrackerId {
    return switch (op) {
        .add, .multiply => // primitives ✓
        .matmul => // ❌ NOT A PRIMITIVE! This decomposes first
        .reshape => // ❌ This is a VIEW operation, not a graph operation
    };
}

// ✅ CORRECT - Respecting architecture
pub fn inferOpTracker(op: OpType, inputs: []const TrackerId) !TrackerId {
    return switch (op) {
        // Only the actual primitives from OpType enum
        .add, .multiply, .reduce_sum => // handle primitives
        else => error.NonPrimitiveOperation, // reject non-primitives
    };
}
```

## API Design Best Practices

**Configuration Structs:**
```zig
pub const Config = struct {
    dtype: DataType = .f32,
    device: Device = .cpu,
    requires_grad: bool = false,
};

// Usage with defaults and anonymous struct literals
const tensor = try zeros(allocator, &[_]i64{2, 3}, .{});
const tensor_f64 = try zeros(allocator, &[_]i64{2, 3}, .{ .dtype = .f64 });
```

**Factory Functions:**
```zig
pub fn MyType(comptime T: type) type {
    return struct {
        const Self = @This();
        data: T,
        
        pub fn init(data: T) Self {
            return .{ .data = data };
        }
    };
}
```

**Method Chaining with Explicit Errors:**
```zig
pub const TensorHandle = struct {
    core: *Core,
    node_id: NodeId,
    
    pub fn reshape(self: TensorHandle, new_shape: []const i64) !TensorHandle {
        const new_node = try manipulation.reshape(self.core, self.node_id, new_shape);
        return TensorHandle{ .core = self.core, .node_id = new_node };
    }
};
```

## Key Anti-Patterns to Avoid

1. **Hidden Allocations**: Always pass allocator explicitly
2. **Silent Error Handling**: Always log errors with context
   ```zig
   // ❌ BAD - Silent error
   pub fn process(data: []const u8) !Result {
       if (invalid) return error.ProcessingFailed; // No context!
   }
   
   // ✅ GOOD - Direct logging at error site
   pub fn process(data: []const u8) !Result {
       if (data.len < MIN_SIZE) {
           std.log.err("process: data too small ({} bytes, need {})", 
                      .{ data.len, MIN_SIZE });
           return error.DataTooSmall;
       }
       
       const header = parseHeader(data) catch |err| {
           std.log.err("process: invalid header at offset 0: {}", .{err});
           return error.InvalidHeader;
       };
   }
   ```
3. **Over-Generic APIs**: Use generics only when truly needed
4. **Global State**: Make all dependencies explicit (exception: thread-local singletons for logging or statistics can be OK when documented)
5. **Circular Dependencies**: Design unidirectional data flow
6. **Forgetting `@This()`**: Use in generic type functions
7. **Not using `inline for`**: Miss compile-time optimization opportunities
8. **Ignoring `undefined` safety**: Can cause hard-to-debug issues

## Zig 0.14.0 Specific Features

- **`@branchHint`**: Use `.Likely`/`.Unlikely` for performance-critical hot paths
- **Labeled `switch`**: For complex state machine control flow
- **Unmanaged containers**: Preferred over managed variants
- **`@export` takes pointers**: `@export(&my_func, options)`
- **ZON Support**: Use `std.zon.parse` for Zig Object Notation parsing (or `std.json.parseFromSlice` for JSON)
- **Improved type system**: Better `@typeInfo` and reflection capabilities
- **Wyhash usage**: The 0.14 idiom is `std.hash.wyhash.hash(bytes, seed)` not `Wyhash.init()`

## Performance Optimization Patterns

**Branch Hints:**
```zig
pub fn processItems(items: []const Item) !void {
    for (items) |item| {
        if (item.isSpecial()) {
            @branchHint(.Unlikely);
            try handleSpecialItem(item);
        } else {
            @branchHint(.Likely);
            processNormalItem(item);
        }
    }
}
```

**Warning**: Hinting too liberally can hurt performance. Reserve branch hints for very hot, predictable branches where profiling has shown a benefit.

**Aligned Allocations:**
```zig
// For SIMD and cache optimization
const aligned_ptr = try allocator.alignedAlloc(f32, 16, count);
defer allocator.free(aligned_ptr);
```

---

**Key Insight**: Architecture documents exist precisely to prevent assumption errors. When in doubt, re-read the component boundaries and responsibilities section.
