test-gradient-debug-output
+- run gradient-debug-output-tests 8/9 passed, 1 failed
=== COMPLEX POLYNOMIAL TEST ===
Expression: z = 3x^2 + 2xy + y^2
Inputs: x = 2.00, y = 3.00
Output: z = 33.00 (expected: 3*4 + 2*2*3 + 9 = 33)
Gradients:
  dz/dx = 18.000000 (expected: 6x + 2y = 12 + 6 = 18.0)
  dz/dy = 10.000000 (expected: 2x + 2y = 4 + 6 = 10.0)
Status: ✅ CORRECT

=== NESTED OPERATIONS TEST ===
Expression: z = sqrt(x^2 + y^2)  (distance formula)
Inputs: x = 3.00, y = 4.00
Output: z = 5.00 (expected: sqrt(9+16) = 5.0)
Gradients:
  dz/dx = 0.600000 (expected: x/z = 3/5 = 0.6)
  dz/dy = 0.800000 (expected: y/z = 4/5 = 0.8)
Status: ✅ CORRECT

============================================================
COMPREHENSIVE GRADIENT VERIFICATION COMPLETE
============================================================

Tested operations:
  ✓ Basic arithmetic: +, *, x^3
  ✓ Unary operations: sqrt(x), 1/x
  ✓ Broadcasting and reduction
  ✓ Complex polynomials
  ✓ Nested operations with chain rule

Each test shows ACTUAL computed gradient values
compared against hand-calculated expected values.
If all tests show ✅ CORRECT, autograd works!
============================================================
error: 'test_gradient_debug_output.test.debug gradient output - broadcast reduction' failed: === ADDITION TEST ===
Expression: z = x + y
Inputs: x = 5.00, y = 7.00
Output: z = 12.00
Gradients:
  dz/dx = 1.000000 (expected: 1.0)
  dz/dy = 1.000000 (expected: 1.0)
Status: ✅ CORRECT

=== MULTIPLICATION TEST ===
Expression: z = x * y
Inputs: x = 3.00, y = 4.00
Output: z = 12.00
Gradients:
  dz/dx = 4.000000 (expected: y = 4.0)
  dz/dy = 3.000000 (expected: x = 3.0)
Status: ✅ CORRECT

=== CHAIN RULE TEST ===
Expression: z = x^3
Input: x = 2.00
Output: z = 8.00 (expected: 8.0)
Gradient:
  dz/dx = 12.000000 (expected: 3x^2 = 3*4 = 12.0)
Status: ✅ CORRECT

=== SQUARE ROOT TEST ===
Expression: z = sqrt(x)
Input: x = 4.00
Output: z = 2.00 (expected: sqrt(4) = 2.0)
Gradient:
  dz/dx = 0.250000 (expected: 1/(2*sqrt(x)) = 1/4 = 0.25)
Status: ✅ CORRECT

=== RECIPROCAL TEST ===
Expression: z = 1/x
Input: x = 2.00
Output: z = 0.50 (expected: 1/2 = 0.5)
Gradient:
  dz/dx = -0.250000 (expected: -1/x^2 = -1/4 = -0.25)
Status: ✅ CORRECT
[default] (err): Output node 8 not found in data storage
/Users/<USER>/Library/CloudStorage/<EMAIL>/My Drive/mycode/zing/src/execution.zig:216:13: 0x1049507ff in getOutput (gradient-debug-output-tests)
            return error.OutputNotComputed;
            ^
/Users/<USER>/Library/CloudStorage/<EMAIL>/My Drive/mycode/zing/src/tests/test_gradient_debug_output.zig:398:25: 0x104a71dab in test.debug gradient output - broadcast reduction (gradient-debug-output-tests)
    const grad_b_view = try executor.getOutput(grad_b_id);
                        ^
error: while executing test 'test_gradient_debug_output.test.summary', the following test command failed:
/Users/<USER>/Library/CloudStorage/<EMAIL>/My Drive/mycode/zing/.zig-cache/o/5821c7b0334ee0aa1ccf7727ca28d4b8/gradient-debug-output-tests --seed=0x3c853d0e --cache-dir=/Users/<USER>/Library/CloudStorage/<EMAIL>/My Drive/mycode/zing/.zig-cache --listen=- 
Build Summary: 2/4 steps succeeded; 1 failed; 8/9 tests passed; 1 failed
test-gradient-debug-output transitive failure
+- run gradient-debug-output-tests 8/9 passed, 1 failed
error: the following build command failed with exit code 1:
/Users/<USER>/Library/CloudStorage/<EMAIL>/My Drive/mycode/zing/.zig-cache/o/cf02eda582292e5dce576c3d7d4b95b5/build /opt/homebrew/Cellar/zig/0.14.0_2/bin/zig /opt/homebrew/Cellar/zig/0.14.0_2/lib/zig /Users/<USER>/Library/CloudStorage/<EMAIL>/My Drive/mycode/zing /Users/<USER>/Library/CloudStorage/<EMAIL>/My Drive/mycode/zing/.zig-cache /Users/<USER>/.cache/zig --seed 0x3c853d0e -Zcfef55d78ca532fc test-gradient-debug-output
