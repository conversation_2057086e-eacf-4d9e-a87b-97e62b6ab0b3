# Luminal Elementwise Fusion Implementation Analysis

## Overview

Luminal implements elementwise fusion as a graph compiler pass that automatically merges multiple elementwise operations into single kernels. This analysis examines how fusion is implemented across the Metal, CUDA, and CPU backends.

## Core Fusion Algorithm

### Key Components

1. **Graph Traversal**: The fusion compiler traverses the computation graph looking for adjacent elementwise operations
2. **Pattern Matching**: Operations marked with `"elementwise"` custom property are candidates for fusion
3. **Subexpression Tracking**: Fused operations maintain a list of subexpressions representing the merged computation
4. **View Stacking**: The compiler tracks and merges shape transformations (views) across fused operations

### Fusion Process

The fusion algorithm works in several phases:

1. **Identification Phase**:
   - Scan all nodes for operations with `"elementwise"` custom property
   - Build a map of fuseable operations

2. **Merging Phase**:
   - Look for edges between elementwise operations
   - Check fusion constraints (no multiple views, matching shapes, etc.)
   - Merge adjacent operations by combining their subexpressions

3. **Compilation Phase**:
   - Generate kernel code from merged subexpressions
   - Compile the kernel for the target backend

## Backend-Specific Implementations

### Metal Backend (`crates/luminal_metal/src/elementwise_fusion.rs`)

```rust
pub struct FusedElementwiseOp<T> {
    pub kernel: Option<ComputePipelineState>,
    pub kernel_str: String,
    pub subexpressions: Vec<(String, ShapeTracker)>,
    // ... other fields
}
```

**Key Features**:
- Generates Metal Shading Language kernels
- Uses `ComputePipelineState` for GPU execution
- Supports dynamic shapes through runtime parameters

**Kernel Generation**:
```metal
kernel void mkernel(device float *input0 [[buffer(0)]], 
                   device float *out [[buffer(n)]], 
                   device uint& n_elements [[buffer(n+1)]], 
                   uint idx [[thread_position_in_grid]]) {
    if (idx < n_elements) {
        float intermediate0 = input0[idx];
        float intermediate1 = cos(intermediate0);
        out[idx] = exp(intermediate1);
    }
}
```

### CUDA Backend (`crates/luminal_cuda/src/elementwise_fusion.rs`)

```rust
pub struct FusedElementwiseOp<T> {
    kernel: Option<CudaFunction>,
    subexpressions: Vec<(String, ShapeTracker)>,
    // ... other fields
}
```

**Key Features**:
- Generates CUDA C++ kernels
- Uses `CudaFunction` for GPU execution
- Similar structure to Metal but with CUDA-specific APIs

**Kernel Generation**:
```cuda
extern "C" __global__ void kernel(const float* input0, 
                                  float* out, 
                                  const int n_elements) {
    int idx = blockIdx.x * blockDim.x + threadIdx.x;
    if (idx < n_elements) {
        float intermediate0 = input0[idx];
        float intermediate1 = cos(intermediate0);
        out[idx] = exp(intermediate1);
    }
}
```

### CPU Backend

The CPU backend in Luminal doesn't appear to implement elementwise fusion in the same way as GPU backends. Instead, it focuses on pattern matching for specific operation combinations (like Sub = Add + Mul(-1)).

## Fusion Constraints

The fusion algorithm has several important constraints:

1. **View Constraints**: Cannot fuse if multiple different views of the same input exist
2. **Shape Matching**: All edges between operations must have the same shape
3. **No Deletion Constraint**: Cannot fuse operations marked as `no_delete`
4. **Output Dependencies**: Cannot fuse if an intermediate output is used elsewhere

## Subexpression Management

The fusion system manages subexpressions carefully:

1. **Intermediate Naming**: Each subexpression result is named `intermediate0`, `intermediate1`, etc.
2. **Input Re-referencing**: When merging operations, input references are updated to point to correct intermediates
3. **View Stacking**: Shape transformations are accumulated and applied to index expressions

Example subexpression evolution:
```
Op A: output = exp(input0)
Op B: output = cos(input0)
Fused: intermediate0 = exp(input0)
       output = cos(intermediate0)
```

## Index Expression Handling

A sophisticated part of the fusion is handling index expressions when views are involved:

```rust
// Stack index expressions
let stacked_index_expressions = stacked_shapes
    .iter()
    .map(|s| {
        s.iter()
            .rev()
            .fold(Expression::from('z'), |acc, inp| {
                inp.index_expression().substitute('z', acc)
            })
    })
    .collect::<Vec<_>>();
```

This allows the fused kernel to correctly handle reshapes, transposes, and other view operations.

## Performance Benefits

Elementwise fusion provides several performance benefits:

1. **Reduced Memory Traffic**: Eliminates intermediate buffers between operations
2. **Fewer Kernel Launches**: Reduces GPU kernel launch overhead
3. **Better Cache Utilization**: Intermediate values stay in registers/cache
4. **Simplified Memory Management**: Fewer allocations and deallocations

## Example Fusion

Consider the expression: `a.cos().exp().sqrt()`

**Without Fusion**:
- Allocate intermediate1, launch cos kernel: intermediate1 = cos(a)
- Allocate intermediate2, launch exp kernel: intermediate2 = exp(intermediate1)
- Allocate output, launch sqrt kernel: output = sqrt(intermediate2)

**With Fusion**:
- Single kernel launch: output = sqrt(exp(cos(a)))
- No intermediate allocations

## Integration with Other Compilers

The elementwise fusion compiler works as part of a compiler pipeline:

1. Primitive operation replacement (backend-specific)
2. Pattern matching for specialized operations (matmul, etc.)
3. **Elementwise fusion** ← This stage
4. Memory optimization (buffer sharing)
5. Command buffer optimization (for GPU backends)

## Key Insights

1. **Graph-Based Design**: The fusion algorithm leverages the graph structure to identify and merge operations safely
2. **Backend Agnostic**: The core fusion logic is similar across backends, only kernel generation differs
3. **Shape Awareness**: The system maintains full shape tracking through complex view transformations
4. **Extensibility**: New elementwise operations can be added by simply marking them with the `"elementwise"` custom property

## Comparison to Other Frameworks

Luminal's approach is similar to:
- **XLA**: Fusion of elementwise operations into single kernels
- **TorchScript**: JIT compilation with fusion passes
- **TVM**: Graph-level optimizations with kernel fusion

Key differences:
- Luminal generates kernels at runtime based on actual graph structure
- The fusion is more aggressive, handling complex view operations
- Integration with Luminal's expression-based shape tracking system

## Future Improvements

Potential areas for enhancement:
1. More sophisticated cost models for fusion decisions
2. Support for reduction operation fusion
3. Cross-device fusion strategies
4. Profile-guided fusion optimization