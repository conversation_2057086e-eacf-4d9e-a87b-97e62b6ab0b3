const std = @import("std");
const zing = @import("src/root.zig");
const autograd = @import("src/training/autograd.zig");
const GraphEngine = @import("src/graph.zig").GraphEngine;
const ShapeEngine = @import("src/shape.zig").ShapeEngine;
const ops = @import("src/ops.zig");
const types = @import("src/types.zig");

pub fn main() !void {
    const allocator = std.heap.page_allocator;
    
    // Initialize engines
    var graph = try GraphEngine.init(allocator);
    defer graph.deinit();
    
    var shape_engine = try ShapeEngine.init(allocator);
    defer shape_engine.deinit();
    
    var autograd_engine = try autograd.AutogradEngine.init(allocator, &graph, &shape_engine);
    defer autograd_engine.deinit();
    
    // Create a core-like structure for ops
    var core = struct {
        graph: *GraphEngine,
        shape_engine: *ShapeEngine,
        autograd: *autograd.AutogradEngine,
    }{
        .graph = &graph,
        .shape_engine = &shape_engine,
        .autograd = &autograd_engine,
    };
    
    std.debug.print("\n=== MATMUL GRADIENT TRACE ===\n", .{});
    std.debug.print("Creating tensors A[2,3] and B[3,4]\n\n", .{});
    
    // Create input tensors
    const a = try ops.tensor(&core, &[_]i64{ 2, 3 }, .{ .requires_grad = true });
    const b = try ops.tensor(&core, &[_]i64{ 3, 4 }, .{ .requires_grad = true });
    
    // Record initial graph state
    const nodes_before = core.graph.nodes.items.len;
    std.debug.print("Nodes before matmul: {d}\n", .{nodes_before});
    
    // Perform matmul
    std.debug.print("\nPerforming matmul...\n", .{});
    const result = try ops.matmul(&core, a, b);
    
    // Record nodes created
    const nodes_after = core.graph.nodes.items.len;
    std.debug.print("Nodes after matmul: {d}\n", .{nodes_after});
    std.debug.print("New nodes created: {d}\n\n", .{nodes_after - nodes_before});
    
    // Trace the decomposition
    std.debug.print("=== FORWARD PASS TRACE ===\n", .{});
    for (nodes_before..nodes_after) |i| {
        const node = core.graph.nodes.items[i];
        const node_id = @as(types.NodeId, @intCast(i));
        
        std.debug.print("Node {d}: {s}\n", .{ node_id, @tagName(node.op) });
        
        // Get shape if available
        if (node.output_tracker) |tracker_id| {
            if (core.shape_engine.getTracker(tracker_id)) |tracker| {
                std.debug.print("  Shape: [", .{});
                for (tracker.shape, 0..) |dim, j| {
                    if (j > 0) std.debug.print(", ", .{});
                    std.debug.print("{d}", .{dim});
                }
                std.debug.print("]\n", .{});
            }
        }
        
        // Print inputs
        switch (node.op) {
            .add, .multiply => {
                std.debug.print("  Inputs: {d}, {d}\n", .{ node.data.binary.lhs, node.data.binary.rhs });
            },
            .reduce_sum => {
                std.debug.print("  Input: {d}\n", .{node.data.reduce.input});
                std.debug.print("  Axes: [", .{});
                for (node.data.reduce.axes, 0..) |axis, j| {
                    if (j > 0) std.debug.print(", ", .{});
                    std.debug.print("{d}", .{axis});
                }
                std.debug.print("]\n", .{});
                std.debug.print("  Keep dims: {}\n", .{node.data.reduce.keep_dims});
            },
            .reshape => {
                std.debug.print("  Input: {d}\n", .{node.data.reshape.input});
                std.debug.print("  New shape: [", .{});
                for (node.data.reshape.new_shape, 0..) |dim, j| {
                    if (j > 0) std.debug.print(", ", .{});
                    std.debug.print("{d}", .{dim});
                }
                std.debug.print("]\n", .{});
            },
            else => {},
        }
        std.debug.print("\n", .{});
    }
    
    // Try backward pass
    std.debug.print("\n=== BACKWARD PASS ===\n", .{});
    
    // Create gradient for output
    const grad_output = try ops.ones_like(&core, result);
    try core.autograd.setGradient(result.node_id, grad_output.node_id);
    
    std.debug.print("Starting backward from node {d}\n", .{result.node_id});
    std.debug.print("Gradient shape: ", .{});
    if (grad_output.shape) |shape| {
        std.debug.print("[", .{});
        for (shape, 0..) |dim, i| {
            if (i > 0) std.debug.print(", ", .{});
            std.debug.print("{d}", .{dim});
        }
        std.debug.print("]\n", .{});
    }
    
    // Attempt backward
    core.autograd.backward(&.{result.node_id}) catch |err| {
        std.debug.print("\nBackward failed with error: {}\n", .{err});
        
        // Print gradient status for all nodes
        std.debug.print("\n=== GRADIENT STATUS ===\n", .{});
        for (0..core.graph.nodes.items.len) |i| {
            const node_id = @as(types.NodeId, @intCast(i));
            if (core.autograd.getGradient(node_id)) |grad_id| {
                std.debug.print("Node {d} has gradient (node {d})", .{ node_id, grad_id });
                
                // Get gradient shape
                const grad_node = core.graph.nodes.items[grad_id];
                if (grad_node.output_tracker) |tracker_id| {
                    if (core.shape_engine.getTracker(tracker_id)) |tracker| {
                        std.debug.print(" with shape [", .{});
                        for (tracker.shape, 0..) |dim, j| {
                            if (j > 0) std.debug.print(", ", .{});
                            std.debug.print("{d}", .{dim});
                        }
                        std.debug.print("]", .{});
                    }
                }
                std.debug.print("\n", .{});
            }
        }
        
        return err;
    };
    
    std.debug.print("\nBackward pass succeeded!\n", .{});
    
    // Print final gradients
    if (core.autograd.getGradient(a.node_id)) |grad_a| {
        std.debug.print("Gradient for A (node {d}): node {d}\n", .{ a.node_id, grad_a });
    }
    
    if (core.autograd.getGradient(b.node_id)) |grad_b| {
        std.debug.print("Gradient for B (node {d}): node {d}\n", .{ b.node_id, grad_b });
    }
}