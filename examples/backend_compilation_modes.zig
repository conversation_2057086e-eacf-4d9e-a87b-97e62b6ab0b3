/// Example: Different Backend Compilation Modes
///
/// This example demonstrates the three ways to compile a graph
/// with a backend, showing when to use each approach.

const std = @import("std");
const zing = @import("zing");

pub fn main() !void {
    var gpa = std.heap.GeneralPurposeAllocator(.{}){};
    defer _ = gpa.deinit();
    const allocator = gpa.allocator();

    // Create a simple graph
    var graph = zing.Graph.init(allocator);
    defer graph.deinit();

    // Add some operations
    const a = try graph.addPlaceholder(&.{ 10, 10 }, .f32);
    const b = try graph.addPlaceholder(&.{ 10, 10 }, .f32);
    const c = try graph.addNode(.{
        .compute = .{
            .op = .mul,
            .inputs = &.{ a, b },
        },
    });
    const d = try graph.addNode(.{
        .compute = .{
            .op = .sum_reduce,
            .inputs = &.{ c },
            .axis = 1,
        },
    });

    // Initialize backend
    try zing.backends.registerAllBackends();
    const backend_context = try createCpuContext(allocator);
    defer backend_context.deinit();

    // ===== Mode 1: Standard Compilation (RECOMMENDED) =====
    std.debug.print("\n=== Mode 1: Standard Compilation ===\n", .{});
    {
        // This is the simplest and most common approach
        const compiled = try zing.backends.compileWithOptimization(
            &graph,
            &.{d}, // output nodes
            null, // shape info (will be inferred)
            "cpu",
            backend_context,
            allocator,
        );
        defer compiled.deinit(allocator);

        std.debug.print("Compiled graph has {} execution steps\n", .{compiled.execution_order.len});
        // The graph is optimized and ready to execute
    }

    // ===== Mode 2: Separate Optimization and Planning (ADVANCED) =====
    std.debug.print("\n=== Mode 2: Separate Optimization and Planning ===\n", .{});
    {
        // Step 1: Run standard optimizations
        try zing.backends.optimizeGraph(
            &graph,
            "cpu",
            backend_context,
            allocator,
        );

        // Step 2: Add custom backend-specific transformations
        // For example, you might add custom tiling or memory layout optimizations
        try addCustomCpuOptimizations(&graph);

        // Step 3: Generate execution plan
        const compiled = try zing.backends.generateExecutionPlan(
            &graph,
            &.{d}, // output nodes
            null, // shape info
            "cpu",
            backend_context,
            allocator,
        );
        defer compiled.deinit(allocator);

        std.debug.print("Compiled graph has {} execution steps\n", .{compiled.execution_order.len});
    }

    // ===== Mode 3: Direct Execution Planning (SPECIAL CASES) =====
    std.debug.print("\n=== Mode 3: Direct Execution Planning ===\n", .{});
    {
        // Skip all optimization - useful for:
        // - Pre-optimized graphs
        // - Debugging
        // - Testing specific execution patterns
        const compiled = try zing.backends.generateExecutionPlan(
            &graph,
            &.{d}, // output nodes
            null, // shape info
            "cpu",
            backend_context,
            allocator,
        );
        defer compiled.deinit(allocator);

        std.debug.print("Compiled graph has {} execution steps (no optimization)\n", .{compiled.execution_order.len});
    }
}

fn createCpuContext(allocator: std.mem.Allocator) !*zing.backends.BackendContext {
    const cpu_backend = @import("zing").backends.cpu;
    const ctx = try cpu_backend.CpuContext.init(allocator, .{
        .enable_luminal_patterns = true,
    });
    return @ptrCast(ctx);
}

fn addCustomCpuOptimizations(graph: *zing.Graph) !void {
    // Example: Add custom CPU-specific optimizations
    // This might include:
    // - Cache tiling for matrix operations
    // - Memory layout transformations
    // - CPU-specific fusion patterns
    _ = graph;
    std.debug.print("  Adding custom CPU optimizations...\n", .{});
}