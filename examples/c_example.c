/**
 * @file c_example.c
 * @brief Zing C API Example - Neural Network Forward Pass
 * 
 * This example demonstrates how to use the Zing C API to create and execute
 * a simple neural network forward pass with automatic memory management.
 */

#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include "zing.h"

/**
 * @brief Check for errors and print error message if any
 * @param operation Description of the operation that was performed
 * @return 0 if no error, 1 if error occurred
 */
int check_error(const char* operation) {
    zing_error_t error = zing_get_last_error();
    if (error != ZING_OK) {
        printf("❌ Error in %s: %s\n", operation, zing_get_error_message());
        return 1;
    }
    return 0;
}

/**
 * @brief Print tensor information (shape, size, etc.)
 */
void print_tensor_info(const char* name, zing_tensor_t* tensor) {
    if (!tensor) {
        printf("  %s: NULL\n", name);
        return;
    }
    
    size_t rank = zing_tensor_rank(tensor);
    int64_t size = zing_tensor_size(tensor);
    int dtype = zing_tensor_dtype(tensor);
    
    const char* dtype_name = "unknown";
    switch (dtype) {
        case ZING_DTYPE_F32: dtype_name = "f32"; break;
        case ZING_DTYPE_F64: dtype_name = "f64"; break;
        case ZING_DTYPE_I32: dtype_name = "i32"; break;
        case ZING_DTYPE_I64: dtype_name = "i64"; break;
    }
    
    printf("  %s: rank=%zu, size=%lld, dtype=%s", name, rank, (long long)size, dtype_name);
    
    if (rank > 0 && rank <= 4) {
        int64_t shape[4];
        if (zing_tensor_shape(tensor, shape, rank) == ZING_OK) {
            printf(", shape=[");
            for (size_t i = 0; i < rank; i++) {
                printf("%lld", (long long)shape[i]);
                if (i < rank - 1) printf(", ");
            }
            printf("]");
        }
    }
    printf("\n");
}

/**
 * @brief Simple neural network forward pass example
 */
int neural_network_example(void) {
    printf("\n🧠 Neural Network Forward Pass Example\n");
    printf("=====================================\n");
    
    // Create session with CPU backend
    zing_session_t* session = zing_session_create_with_backend(ZING_DEVICE_CPU);
    if (!session || check_error("session creation")) {
        return 1;
    }
    
    printf("✅ Created session with CPU backend\n");
    
    // Network architecture: 8 -> 16 -> 4
    printf("\nCreating network layers:\n");
    
    // Input layer: batch of 4 samples, 8 features each
    int64_t input_shape[] = {4, 8};
    zing_tensor_t* input = zing_ones(session, input_shape, 2, ZING_DTYPE_F32);
    if (!input || check_error("input creation")) {
        zing_session_destroy(session);
        return 1;
    }
    print_tensor_info("Input", input);
    
    // First layer: 8 -> 16
    int64_t weight1_shape[] = {8, 16};
    zing_tensor_t* weight1 = zing_ones(session, weight1_shape, 2, ZING_DTYPE_F32);
    if (!weight1 || check_error("weight1 creation")) {
        zing_session_destroy(session);
        return 1;
    }
    print_tensor_info("Weight1", weight1);
    
    int64_t bias1_shape[] = {1, 16};
    zing_tensor_t* bias1 = zing_ones(session, bias1_shape, 2, ZING_DTYPE_F32);
    if (!bias1 || check_error("bias1 creation")) {
        zing_session_destroy(session);
        return 1;
    }
    print_tensor_info("Bias1", bias1);
    
    // Second layer: 16 -> 4
    int64_t weight2_shape[] = {16, 4};
    zing_tensor_t* weight2 = zing_ones(session, weight2_shape, 2, ZING_DTYPE_F32);
    if (!weight2 || check_error("weight2 creation")) {
        zing_session_destroy(session);
        return 1;
    }
    print_tensor_info("Weight2", weight2);
    
    // Forward pass computation
    printf("\nComputing forward pass:\n");
    
    // Layer 1: input @ weight1 + bias1
    zing_tensor_t* hidden_linear = zing_matmul(input, weight1);
    if (!hidden_linear || check_error("hidden linear computation")) {
        zing_session_destroy(session);
        return 1;
    }
    printf("  ✓ Hidden linear: input @ weight1\n");
    
    zing_tensor_t* hidden_bias = zing_add(hidden_linear, bias1);
    if (!hidden_bias || check_error("hidden bias addition")) {
        zing_session_destroy(session);
        return 1;
    }
    printf("  ✓ Hidden with bias: + bias1\n");
    
    // Activation: ReLU
    zing_tensor_t* hidden_activated = zing_relu(hidden_bias);
    if (!hidden_activated || check_error("hidden activation")) {
        zing_session_destroy(session);
        return 1;
    }
    printf("  ✓ Hidden activated: ReLU\n");
    
    // Layer 2: hidden @ weight2
    zing_tensor_t* output = zing_matmul(hidden_activated, weight2);
    if (!output || check_error("output computation")) {
        zing_session_destroy(session);
        return 1;
    }
    printf("  ✓ Output: hidden @ weight2\n");
    
    print_tensor_info("Final output", output);
    
    // Get and display results
    printf("\nGetting computed results:\n");
    
    // Get hidden layer results (4 samples × 16 units = 64 values)
    float hidden_data[64];
    if (zing_tensor_get_data_f32(hidden_activated, hidden_data, 64) != ZING_OK ||
        check_error("hidden data retrieval")) {
        zing_session_destroy(session);
        return 1;
    }
    
    printf("  Hidden layer (first 8 values): ");
    for (int i = 0; i < 8; i++) {
        printf("%.1f ", hidden_data[i]);
    }
    printf("...\n");
    
    // Get output results (4 samples × 4 units = 16 values)
    float output_data[16];
    if (zing_tensor_get_data_f32(output, output_data, 16) != ZING_OK ||
        check_error("output data retrieval")) {
        zing_session_destroy(session);
        return 1;
    }
    
    printf("  Output layer: ");
    for (int i = 0; i < 16; i++) {
        printf("%.1f ", output_data[i]);
        if ((i + 1) % 4 == 0 && i < 15) printf("| ");
    }
    printf("\n");
    
    // Cleanup (automatically frees all tensors)
    zing_session_destroy(session);
    printf("\n✅ Neural network example completed successfully!\n");
    
    return 0;
}

/**
 * @brief Execution modes demonstration
 */
int execution_modes_example(void) {
    printf("\n⚡ Execution Modes Example\n");
    printf("=========================\n");
    
    zing_session_t* session = zing_session_create();
    if (!session || check_error("session creation")) {
        return 1;
    }
    
    // Test eager mode (default)
    int mode = zing_session_get_mode(session);
    printf("Default mode: %s\n", (mode == ZING_MODE_EAGER) ? "Eager" : "Lazy");
    
    int64_t shape[] = {2, 2};
    zing_tensor_t* a = zing_ones(session, shape, 2, ZING_DTYPE_F32);
    zing_tensor_t* b = zing_ones(session, shape, 2, ZING_DTYPE_F32);
    zing_tensor_t* eager_result = zing_add(a, b);
    
    if (!a || !b || !eager_result || 
        check_error("eager mode tensor operations")) {
        zing_session_destroy(session);
        return 1;
    }
    
    float eager_data[4];
    if (zing_tensor_get_data_f32(eager_result, eager_data, 4) != ZING_OK ||
        check_error("eager data retrieval")) {
        zing_session_destroy(session);
        return 1;
    }
    
    printf("Eager result: [%.1f, %.1f, %.1f, %.1f]\n", 
           eager_data[0], eager_data[1], eager_data[2], eager_data[3]);
    
    // Switch to lazy mode
    if (zing_session_set_mode(session, ZING_MODE_LAZY) != ZING_OK ||
        check_error("switching to lazy mode")) {
        zing_session_destroy(session);
        return 1;
    }
    
    mode = zing_session_get_mode(session);
    printf("Switched to: %s\n", (mode == ZING_MODE_LAZY) ? "Lazy" : "Eager");
    
    // Build computation graph
    zing_tensor_t* c = zing_ones(session, shape, 2, ZING_DTYPE_F32);
    zing_tensor_t* lazy_intermediate = zing_multiply(eager_result, c);
    zing_tensor_t* lazy_result = zing_relu(lazy_intermediate);
    
    if (!c || !lazy_intermediate || !lazy_result ||
        check_error("lazy mode graph building")) {
        zing_session_destroy(session);
        return 1;
    }
    
    printf("Built computation graph (not executed yet)\n");
    
    // Execute the graph
    if (zing_session_run(session) != ZING_OK ||
        check_error("lazy execution")) {
        zing_session_destroy(session);
        return 1;
    }
    
    printf("Executed computation graph\n");
    
    float lazy_data[4];
    if (zing_tensor_get_data_f32(lazy_result, lazy_data, 4) != ZING_OK ||
        check_error("lazy data retrieval")) {
        zing_session_destroy(session);
        return 1;
    }
    
    printf("Lazy result: [%.1f, %.1f, %.1f, %.1f]\n", 
           lazy_data[0], lazy_data[1], lazy_data[2], lazy_data[3]);
    
    zing_session_destroy(session);
    printf("✅ Execution modes example completed!\n");
    
    return 0;
}

/**
 * @brief Backend availability and version info
 */
void print_system_info(void) {
    printf("🔧 Zing System Information\n");
    printf("==========================\n");
    
    printf("Version: %s\n", zing_get_version());
    
    printf("Available backends:\n");
    printf("  CPU: %s\n", zing_is_backend_available(ZING_DEVICE_CPU) ? "✅ Available" : "❌ Not available");
    printf("  CUDA: %s\n", zing_is_backend_available(ZING_DEVICE_CUDA) ? "✅ Available" : "❌ Not available");
    printf("  Metal: %s\n", zing_is_backend_available(ZING_DEVICE_METAL) ? "✅ Available" : "❌ Not available");
}

/**
 * @brief Main function demonstrating Zing C API
 */
int main() {
    printf("🚀 Zing C API Demo\n");
    printf("==================\n");
    
    print_system_info();
    
    // Run examples
    if (neural_network_example() != 0) {
        printf("❌ Neural network example failed\n");
        return 1;
    }
    
    if (execution_modes_example() != 0) {
        printf("❌ Execution modes example failed\n");
        return 1;
    }
    
    printf("\n🎉 All examples completed successfully!\n");
    printf("\nKey Features Demonstrated:\n");
    printf("  ✅ Automatic memory management (session-owned tensors)\n");
    printf("  ✅ Neural network forward pass computation\n");
    printf("  ✅ Matrix operations and broadcasting\n");
    printf("  ✅ Activation functions (ReLU)\n");
    printf("  ✅ Eager and lazy execution modes\n");
    printf("  ✅ Backend selection and availability checking\n");
    printf("  ✅ Comprehensive error handling\n");
    
    return 0;
}