/// Session API Demo - PyTorch-like Tensor Operations in Zig
///
/// This example demonstrates the high-level Session API that provides
/// automatic lifecycle management and method chaining for tensor operations.

const std = @import("std");
const zing = @import("zing");

pub fn main() !void {
    var gpa = std.heap.GeneralPurposeAllocator(.{}){};
    defer _ = gpa.deinit();
    const allocator = gpa.allocator();

    // Create a Session - manages graph/compile/execute lifecycle automatically
    var session = try zing.Session.init(allocator);
    defer session.deinit();

    std.debug.print("🚀 Zing Session API Demo\n");
    std.debug.print("========================\n\n");

    // Example 1: Basic tensor creation and operations
    std.debug.print("📊 Example 1: Basic Operations\n");
    {
        // Create tensors using session methods
        const a = try session.zeros(&.{2, 3}, .f32);
        const b = try session.ones(&.{2, 3}, .f32);
        
        // PyTorch-like method chaining
        const result = try a.add(b).mul(b);
        
        std.debug.print("  Created tensors a[2,3] and b[2,3]\n");
        std.debug.print("  Computed (a + b) * b\n");
        std.debug.print("  Result shape: [{}, {}]\n", .{ result.shape[0], result.shape[1] });
        std.debug.print("  Result size: {} elements\n", .{result.size()});
    }

    // Example 2: Neural network-like operations
    std.debug.print("\n🧠 Example 2: Neural Network Operations\n");
    {
        // Create input and weight matrices
        const input = try session.ones(&.{32, 128}, .f32);  // Batch of 32, 128 features
        const weight = try session.ones(&.{128, 64}, .f32); // 128 -> 64 hidden units
        const bias = try session.ones(&.{1, 64}, .f32);     // Bias vector
        
        // Forward pass: linear layer with ReLU activation
        const linear_output = try input.matmul(weight);
        const with_bias = try linear_output.add(bias);
        const activated = try with_bias.relu();
        
        std.debug.print("  Input: [32, 128] -> Weight: [128, 64] -> Output: [32, 64]\n");
        std.debug.print("  Applied linear transformation + bias + ReLU\n");
        std.debug.print("  Final output shape: [{}, {}]\n", .{ activated.shape[0], activated.shape[1] });
    }

    // Example 3: Broadcasting and reshape operations
    std.debug.print("\n📐 Example 3: Broadcasting and Reshaping\n");
    {
        // Broadcasting: [2, 1] + [1, 3] = [2, 3]
        const a = try session.ones(&.{2, 1}, .f32);
        const b = try session.ones(&.{1, 3}, .f32);
        const broadcasted = try a.add(b);
        
        // Reshape the result
        const reshaped = try broadcasted.reshape(&.{6});
        
        std.debug.print("  Broadcasting: [2, 1] + [1, 3] = [2, 3]\n");
        std.debug.print("  Reshaped to: [{}]\n", .{reshaped.shape[0]});
    }

    // Example 4: Execution modes
    std.debug.print("\n⚡ Example 4: Execution Modes\n");
    {
        std.debug.print("  Default mode: {s}\n", @tagName(session.getMode()));
        
        // Switch to lazy execution
        session.setMode(.lazy);
        std.debug.print("  Switched to: {s}\n", @tagName(session.getMode()));
        
        // Build computation graph without executing
        const a = try session.ones(&.{10, 10}, .f32);
        const b = try session.ones(&.{10, 10}, .f32);
        const c = try a.add(b);
        
        std.debug.print("  Built computation graph (not executed yet)\n");
        
        // Execute the computation
        try session.run();
        std.debug.print("  Executed computation graph\n");
        
        // Switch back to eager mode
        session.setMode(.eager);
        std.debug.print("  Switched back to: {s}\n", @tagName(session.getMode()));
    }

    // Example 5: Backend selection
    std.debug.print("\n🔧 Example 5: Backend Selection\n");
    {
        // Create session with explicit CPU backend
        const backend_config = zing.session.BackendConfig{ .device = .cpu };
        var cpu_session = try zing.Session.initWithBackend(allocator, backend_config);
        defer cpu_session.deinit();
        
        const a = try cpu_session.ones(&.{2, 2}, .f32);
        const b = try cpu_session.ones(&.{2, 2}, .f32);
        const result = try a.add(b);
        
        std.debug.print("  Created session with explicit CPU backend\n");
        std.debug.print("  Computed tensor operation successfully\n");
        std.debug.print("  Result shape: [{}, {}]\n", .{ result.shape[0], result.shape[1] });
        
        // Demonstrate unsupported backend fallback
        const cuda_config = zing.session.BackendConfig{ .device = .cuda };
        var cuda_session = try zing.Session.initWithBackend(allocator, cuda_config);
        defer cuda_session.deinit();
        
        const x = try cuda_session.ones(&.{3, 3}, .f32);
        const y = try x.relu();
        
        std.debug.print("  Created session with CUDA backend (falls back to CPU)\n");
        std.debug.print("  Computed activation function successfully\n");
        std.debug.print("  Result shape: [{}, {}]\n", .{ y.shape[0], y.shape[1] });
    }

    std.debug.print("\n✅ Session API Demo Complete!\n");
    std.debug.print("\nKey Features Demonstrated:\n");
    std.debug.print("  • Automatic lifecycle management (no manual graph/compile/execute)\n");
    std.debug.print("  • PyTorch-like method chaining (tensor.add().mul().relu())\n");
    std.debug.print("  • Broadcasting operations\n");
    std.debug.print("  • Matrix operations (matmul)\n");
    std.debug.print("  • Shape operations (reshape)\n");
    std.debug.print("  • Activation functions (ReLU)\n");
    std.debug.print("  • Eager and lazy execution modes\n");
    std.debug.print("  • Backend selection with automatic fallback\n");
    std.debug.print("  • Memory management handled automatically\n");
}