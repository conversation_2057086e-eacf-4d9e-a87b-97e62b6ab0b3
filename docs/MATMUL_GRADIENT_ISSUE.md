# Matmul Gradient Issue

## Problem Description

The matmul gradient computation fails with a shape mismatch error when trying to propagate gradients through the matmul decomposition. The issue occurs because:

1. **Matmul Decomposition**: 
   - A[m,k] @ B[k,n] decomposes into:
   - A expands to [m,n,k]
   - B transposes to [n,k] then expands to [m,n,k]
   - Element-wise multiply: [m,n,k] * [m,n,k]
   - Sum reduce along axis 2 to get [m,n]

2. **Transpose Creates New Node**:
   - When B is transposed, it calls `makeContiguous()` which creates a new node
   - This new node has shape [n,k] (transposed shape)
   - The original B node has shape [k,n]

3. **Gradient Flow Issue**:
   - During backward pass, gradients flow to the transposed node
   - The contiguous operation passes gradient through unchanged
   - When gradient reaches original B, shapes don't match ([n,k] vs [k,n])

## Root Cause

The fundamental issue is that the `contiguous` operation doesn't store information about what transformation it represents. When transpose creates a contiguous node, we lose the information that this is a transposed view, so we can't automatically transpose the gradient back.

## Potential Solutions

1. **Store Transformation Metadata**: 
   - Add metadata to contiguous nodes indicating what transformation they represent
   - During backward pass, apply inverse transformation to gradients

2. **Create Transpose Operation**:
   - Instead of using contiguous for transpose, create an explicit transpose operation
   - Implement gradient rule for transpose that transposes the gradient back

3. **Alternative Matmul Decomposition**:
   - Use a decomposition that doesn't require transpose
   - For example, using einsum-style operations

4. **Handle in Shape Inference**:
   - Track shape transformations through the graph
   - Automatically insert necessary transformations during gradient computation

## Current Workaround

For now, users should avoid using matmul in training graphs. Instead, they can:
- Use element-wise operations that don't require transpose
- Implement custom gradient rules for specific use cases
- Use pre-transposed weights where possible

## Test Status

- Element-wise operations: ✓ Working
- Broadcasting gradients: ✓ Working  
- Chain rule: ✓ Working
- Matmul gradients: ✗ Shape mismatch due to transpose issue

## References

- Luminal handles this by tracking "fake" dimensions and reducing them during backward pass
- The issue is specific to operations that create new nodes for view transformations
- Similar issues may occur with other view operations that create contiguous nodes