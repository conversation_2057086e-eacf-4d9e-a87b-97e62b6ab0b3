# Zing Documentation Index

Welcome to the Zing tensor computation library documentation. This index will help you find the information you need.

## Quick Start Guides

### 🚀 **New to <PERSON><PERSON>?**
- **[SESSION_API_QUICK_START.md](SESSION_API_QUICK_START.md)** - **5-minute start!** Get running immediately
- **[session_api.md](session_api.md)** - Complete Session API documentation with automatic lifecycle management
- **[SESSION_API_GUIDE.md](SESSION_API_GUIDE.md)** - Complete high-level PyTorch-like API guide
- **[TENSOR_API_REFERENCE.md](TENSOR_API_REFERENCE.md)** - Low-level API for advanced users

### 💻 **For C/C++ Developers**
- **[c_api.md](c_api.md)** - Complete C API documentation with session-owned memory management
- See `/examples/c_example.c` for a working example
- See `/tests/c/test_basic.c` for comprehensive test coverage

### ⚡ **Understanding Performance**
- **[COMPTIME_VS_RUNTIME_GUIDE.md](COMPTIME_VS_RUNTIME_GUIDE.md)** - **Complete guide to Zing's hybrid optimization strategy**

## Core System Documentation

### 📐 **Shape System**
- **[SHAPE_README.md](SHAPE_README.md)** - Comprehensive tutorial on the shape system

### 🔧 **Compiler and Optimization**
- **[COMPILER_README.md](COMPILER_README.md)** - Complete compiler architecture and passes reference

### 🏗️ **Architecture**
- **[MODULE_ARCHITECTURE_REVIEW.md](MODULE_ARCHITECTURE_REVIEW.md)** - Overall system architecture
- **[METADATA_OWNERSHIP_DESIGN.md](METADATA_OWNERSHIP_DESIGN.md)** - Ownership and memory management design

## Topic-Specific Guides

### 🔥 **Performance Optimization**

**Start here:** [COMPTIME_VS_RUNTIME_GUIDE.md](COMPTIME_VS_RUNTIME_GUIDE.md)

This document explains:
- ✅ **When comptime optimization is used** (automatic)
- ✅ **When runtime flexibility is needed** (automatic)  
- ✅ **How the hybrid system works** (transparent to users)
- ✅ **Performance implications** of each approach
- ✅ **User experience** (no configuration needed)

This comprehensive guide covers all optimization strategies and implementation details.

### 📊 **Tensor Operations**

**For most users:** [session_api.md](session_api.md) - Complete Session API documentation
- ✅ **Automatic lifecycle management** (no manual graph/compile/execute)
- ✅ **Method chaining** (`tensor.add(b).mul(c).relu()`)
- ✅ **Backend selection** with automatic fallback
- ✅ **Eager and lazy execution modes**
- ✅ **Memory management handled automatically**
- ✅ **Automatic recompilation** for mixed execution modes

**For C/C++ developers:** [c_api.md](c_api.md) - Pure C interface
- ✅ **Session-owned memory** (no manual free)
- ✅ **Thread-local error handling**
- ✅ **Zero-copy data access**
- ✅ **Compatible with C89/C99/C++**

**For advanced users:** [TENSOR_API_REFERENCE.md](TENSOR_API_REFERENCE.md) - Low-level tensor API
- Full control over graph construction and execution
- Direct access to compilation and backend systems
- Maximum performance and flexibility

**Related:** [SHAPE_README.md](SHAPE_README.md) for understanding how shapes work

### 🏭 **Compiler Internals**

**Start here:** [COMPILER_README.md](COMPILER_README.md)

For contributors who want to understand or modify the compiler.

This guide includes complete compiler pass documentation and optimization strategies.

## FAQ: Common Questions

### Q: Do I need to choose between comptime and runtime?
**A:** No! The system automatically uses the best approach. See [COMPTIME_VS_RUNTIME_GUIDE.md](COMPTIME_VS_RUNTIME_GUIDE.md)

### Q: How do I optimize my model's performance?
**A:** Just write normal tensor code - optimization is automatic. For details: [COMPTIME_VS_RUNTIME_GUIDE.md](COMPTIME_VS_RUNTIME_GUIDE.md)

### Q: What tensor operations are available?
**A:** See the complete list: [TENSOR_API_REFERENCE.md](TENSOR_API_REFERENCE.md)

### Q: How do shapes work in Zing?
**A:** Complete tutorial: [SHAPE_README.md](SHAPE_README.md)

### Q: How does the compiler work?
**A:** Architecture overview: [COMPILER_README.md](COMPILER_README.md)

### Q: What's the overall system architecture?
**A:** System design: [MODULE_ARCHITECTURE_REVIEW.md](MODULE_ARCHITECTURE_REVIEW.md)

## Document Relationship Map

```
COMPTIME_VS_RUNTIME_GUIDE.md (★ MAIN PERFORMANCE GUIDE)
└── Complete optimization strategy and implementation details

TENSOR_API_REFERENCE.md (★ MAIN API GUIDE)
└── SHAPE_README.md (shape system tutorial)

MODULE_ARCHITECTURE_REVIEW.md (★ SYSTEM OVERVIEW)
├── METADATA_OWNERSHIP_DESIGN.md (memory management)
└── COMPILER_README.md (complete compiler guide with passes)
```

## For Different User Types

### 🧑‍💻 **Application Developers**
1. [TENSOR_API_REFERENCE.md](TENSOR_API_REFERENCE.md) - Learn the API
2. [COMPTIME_VS_RUNTIME_GUIDE.md](COMPTIME_VS_RUNTIME_GUIDE.md) - Understand performance

### 🔬 **ML Researchers**  
1. [COMPTIME_VS_RUNTIME_GUIDE.md](COMPTIME_VS_RUNTIME_GUIDE.md) - Performance implications
2. [SHAPE_README.md](SHAPE_README.md) - Broadcasting and shape operations
3. [TENSOR_API_REFERENCE.md](TENSOR_API_REFERENCE.md) - Available operations

### 🛠️ **System Contributors**
1. [MODULE_ARCHITECTURE_REVIEW.md](MODULE_ARCHITECTURE_REVIEW.md) - System overview
2. [COMPILER_README.md](COMPILER_README.md) - Complete compiler internals and passes
3. [METADATA_OWNERSHIP_DESIGN.md](METADATA_OWNERSHIP_DESIGN.md) - Memory management design

### 🚀 **Performance Engineers**
1. [COMPTIME_VS_RUNTIME_GUIDE.md](COMPTIME_VS_RUNTIME_GUIDE.md) - **Essential reading with complete optimization details**
2. [COMPILER_README.md](COMPILER_README.md) - Optimization passes and compilation strategies

---

## 📋 **Documentation Status**

✅ **Fully Consolidated - 9 Essential Documents**

- ✅ **session_api.md**: Complete Session API documentation with automatic lifecycle management
- ✅ **c_api.md**: Complete C API documentation with session-owned memory management
- ✅ **COMPTIME_VS_RUNTIME_GUIDE.md**: Complete performance optimization guide
- ✅ **TENSOR_API_REFERENCE.md**: Complete tensor operations API
- ✅ **SHAPE_README.md**: Complete shape system tutorial
- ✅ **COMPILER_README.md**: Complete compiler architecture with all passes
- ✅ **MODULE_ARCHITECTURE_REVIEW.md**: Complete system architecture
- ✅ **METADATA_OWNERSHIP_DESIGN.md**: Memory management design
- ✅ **README.md**: Navigation and quick start guide

**All documentation is consolidated by topic into single comprehensive files. No redundancy, no temporary documents.**