# Training Architecture

This document explains the training infrastructure in Zing, focusing on how weight updates are handled during training.

## Overview

The training system in Zing follows a functional approach inspired by Luminal, where:
- Loss functions are pure functions that return new tensors
- Optimizers are stateful but return new weight tensors rather than mutating existing ones
- The trainer orchestrates the training loop and manages weight updates

## Components

### 1. Loss Functions (`src/training/loss.zig`)
- Pure functions that take predictions and targets, return loss tensors
- Examples: MSE, MAE, Huber, Cross-entropy
- Built using the Tensor API for graph construction

### 2. Optimizers (`src/training/optimizer.zig`)
- Maintain internal state (momentum, Adam moments, etc.)
- Take old weights and gradients, return new weight tensors
- Examples: SGD, SGD with <PERSON><PERSON>, <PERSON>, AdamW

### 3. Learning Rate Schedulers (`src/training/scheduler.zig`)
- Adjust learning rate during training
- Support various schedules: constant, step decay, exponential, cosine, etc.

### 4. Trainer (`src/training/trainer.zig`)
- Main training loop orchestrator
- Manages compilation, execution, and weight updates
- Integrates with autodiff for gradient computation

## Weight Update Mechanism

The weight update process is one of the key architectural decisions in the training system. There are two approaches:

### Option 1: Direct Graph Updates (Simple)
- Update constant values directly in the graph
- Triggers recompilation for each training step
- Simple but has overhead from repeated compilation

### Option 2: Parameter Store Integration (Recommended)
- Use `ParameterStore` from `src/storage.zig` to maintain parameters
- Parameters persist across training runs
- More efficient as it avoids recompilation

## Current Implementation Status

The current implementation provides the foundation for both approaches:

1. **Graph-based updates**: The graph's modification count is incremented to trigger recompilation
2. **ParameterStore integration**: The Trainer has fields for parameter store and mapping

### What's Implemented:
- ✅ Loss functions using Tensor API
- ✅ Optimizer algorithms that return new weights
- ✅ Learning rate scheduling
- ✅ Basic training loop with forward/backward passes
- ✅ Gradient computation via autodiff
- ✅ Framework for weight updates

### What Needs Completion:
- ⚠️ Actual tensor data extraction from execution results
- ⚠️ Graph API extension for updating tensor constants (not just scalars)
- ⚠️ Full ParameterStore integration with NodeId → StateId mapping
- ⚠️ Efficient weight loading before forward pass

## Usage Example

```zig
// Create a graph with parameters
var graph = try Graph.init(allocator);
const weight = try graph.addConstant(0.5); // This needs to support tensors
const bias = try graph.addConstant(0.1);

// Create trainer
var trainer = try Trainer.init(allocator, &graph, &.{weight, bias}, .adam, .{});
defer trainer.deinit();

// Optional: Use parameter store for persistence
var param_store = ParameterStore.init(allocator);
try trainer.setParameterStore(&param_store);

// Compile the model
try trainer.compile(output_node, loss_node, 0.001);

// Training loop
for (0..epochs) |epoch| {
    const loss = try trainer.trainStep(input_data, target_data, input_node, target_node, loss_node);
    std.log.info("Epoch {}: loss = {}", .{epoch, loss});
}
```

## Design Rationale

The functional approach (optimizers returning new tensors) was chosen because:
1. It's cleaner and more composable
2. Avoids hidden state mutations
3. Allows for easier parallelization
4. Matches the computational graph paradigm

The challenge is efficiently applying these new tensors back to the parameters, which is why we provide both direct graph updates and parameter store integration options.