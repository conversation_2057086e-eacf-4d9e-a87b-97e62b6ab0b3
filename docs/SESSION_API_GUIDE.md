# Session API Guide

The Session API provides a high-level, PyTorch-like interface for tensor operations with automatic lifecycle management, configurable backend selection, and both eager and lazy execution modes.

## Table of Contents

1. [Quick Start](#quick-start)
2. [Core Concepts](#core-concepts)
3. [Backend Selection](#backend-selection)
4. [Execution Modes](#execution-modes)
5. [API Reference](#api-reference)
6. [Examples](#examples)
7. [Best Practices](#best-practices)
8. [Performance Considerations](#performance-considerations)

## Quick Start

```zig
const std = @import("std");
const zing = @import("zing");

pub fn main() !void {
    var gpa = std.heap.GeneralPurposeAllocator(.{}){};
    defer _ = gpa.deinit();
    const allocator = gpa.allocator();

    // Create a Session - manages graph/compile/execute lifecycle automatically
    var session = try zing.Session.init(allocator);
    defer session.deinit();

    // PyTorch-like tensor operations with method chaining
    const a = try session.ones(&.{2, 3}, .f32);
    const b = try session.zeros(&.{2, 3}, .f32);
    const result = try a.add(b).mul(a).relu();

    // Access computed data (triggers compilation and execution)
    const data = try result.data(f32);
    std.debug.print("Result: {any}\n", .{data});
}
```

## Core Concepts

### Session Management

The `Session` acts as a high-level coordinator that manages:
- **Computation Graph**: Automatically builds and maintains the computational graph
- **Compilation**: Compiles the graph for the selected backend when needed
- **Execution**: Manages execution artifacts and data storage
- **Memory**: Automatically tracks and cleans up allocated resources

### SessionTensor

`SessionTensor` represents a tensor within a session context and provides:
- **Method chaining**: PyTorch-like syntax (`tensor.add(b).mul(c).relu()`)
- **Shape tracking**: Automatic shape inference for all operations
- **Session binding**: Ensures all operations happen within the same session context

### Automatic Lifecycle Management

Unlike the low-level tensor API, the Session API handles:
- Graph construction and management
- Backend compilation when needed
- Executor creation and management
- Memory allocation and cleanup
- Shape tracking and validation

## Backend Selection

### Default CPU Backend

```zig
// Uses CPU backend by default
var session = try Session.init(allocator);
defer session.deinit();
```

### Explicit Backend Configuration

```zig
// Explicit CPU backend
const cpu_config = zing.session.BackendConfig{ .device = .cpu };
var cpu_session = try zing.Session.initWithBackend(allocator, cpu_config);
defer cpu_session.deinit();

// Request CUDA backend (falls back to CPU with warning if not available)
const cuda_config = zing.session.BackendConfig{ .device = .cuda };
var cuda_session = try zing.Session.initWithBackend(allocator, cuda_config);
defer cuda_session.deinit();
```

### Backend Fallback

When a requested backend is not available:
1. **Warning logged**: Informative message about fallback
2. **CPU fallback**: Automatically uses CPU backend
3. **Transparent operation**: All operations continue to work normally
4. **Configuration preserved**: `session.backend_config` reflects original request

## Execution Modes

### Eager Execution (Default)

**Behavior**: Operations build the graph immediately, execution happens when data is requested.

```zig
var session = try Session.init(allocator);  // Default: eager mode
defer session.deinit();

const a = try session.ones(&.{2, 2}, .f32);
const b = try session.ones(&.{2, 2}, .f32);
const result = try a.add(b);  // Graph built, execution deferred

// Compilation and execution happen here ↓
const data = try result.data(f32);
```

**Advantages**:
- PyTorch-like immediate feedback
- Great for experimentation and debugging
- Simple mental model

**Use Cases**:
- Interactive development
- Small to medium computations
- When you need immediate results

### Lazy Execution

**Behavior**: Operations only build the graph, execution happens on explicit `.run()` call.

```zig
var session = try Session.init(allocator);
defer session.deinit();

// Switch to lazy mode
session.setMode(.lazy);

// Build computation graph (no execution)
const a = try session.ones(&.{2, 2}, .f32);
const b = try session.ones(&.{2, 2}, .f32);
const intermediate = try a.add(b);
const result = try intermediate.mul(a);

// Execute the entire graph at once
try session.run();

// Data is already computed, just return it
const data = try result.data(f32);
```

**Advantages**:
- Potential for whole-graph optimization
- Better for complex computational graphs
- More efficient for batch operations

**Use Cases**:
- Large neural networks
- Complex mathematical computations
- Batch processing workflows

### Mode Switching

```zig
var session = try Session.init(allocator);
defer session.deinit();

// Check current mode
std.debug.print("Current mode: {s}\n", @tagName(session.getMode()));

// Switch modes as needed
session.setMode(.lazy);
// ... build complex graph ...
try session.run();

session.setMode(.eager);
// ... interactive operations ...
```

## API Reference

### Session Creation

```zig
// Default initialization (CPU backend, eager mode)
pub fn init(allocator: Allocator) !Session

// Explicit backend configuration
pub fn initWithBackend(allocator: Allocator, backend_config: BackendConfig) !Session

// Backend configuration struct
pub const BackendConfig = struct {
    device: Device = .cpu,
    // Future: backend-specific options
};
```

### Session Management

```zig
// Clean up all resources
pub fn deinit(self: *Session) void

// Execution mode control
pub fn setMode(self: *Session, mode: ExecutionMode) void
pub fn getMode(self: *const Session) ExecutionMode

// Explicit execution (lazy mode)
pub fn run(self: *Session) !void
```

### Tensor Creation

```zig
// Create tensor with data
pub fn tensor(self: *Session, shape: []const i64, dtype: DataType, data: anytype) !SessionTensor

// Create tensors filled with specific values
pub fn zeros(self: *Session, shape: []const i64, dtype: DataType) !SessionTensor
pub fn ones(self: *Session, shape: []const i64, dtype: DataType) !SessionTensor

// Create placeholder for later data binding
pub fn placeholder(self: *Session, shape: []const i64, dtype: DataType) !SessionTensor
```

### SessionTensor Operations

```zig
// Arithmetic operations
pub fn add(self: SessionTensor, other: SessionTensor) !SessionTensor
pub fn mul(self: SessionTensor, other: SessionTensor) !SessionTensor
pub fn subtract(self: SessionTensor, other: SessionTensor) !SessionTensor
pub fn divide(self: SessionTensor, other: SessionTensor) !SessionTensor

// Linear algebra
pub fn matmul(self: SessionTensor, other: SessionTensor) !SessionTensor

// Activation functions
pub fn relu(self: SessionTensor) !SessionTensor

// Shape operations
pub fn reshape(self: SessionTensor, new_shape: []const i64) !SessionTensor

// Data access
pub fn data(self: SessionTensor, comptime T: type) ![]const T
pub fn copy(self: SessionTensor, comptime T: type, allocator: Allocator) ![]T

// Shape information
pub fn rank(self: SessionTensor) usize
pub fn size(self: SessionTensor) i64
```

## Examples

### Basic Neural Network Layer

```zig
pub fn linearLayer(
    session: *zing.Session,
    input: zing.SessionTensor,
    weight: zing.SessionTensor,
    bias: zing.SessionTensor,
) !zing.SessionTensor {
    const linear = try input.matmul(weight);
    const with_bias = try linear.add(bias);
    return with_bias.relu();
}

// Usage
const input = try session.ones(&.{32, 128}, .f32);     // Batch of 32, 128 features
const weight = try session.ones(&.{128, 64}, .f32);   // 128 -> 64 hidden units
const bias = try session.ones(&.{1, 64}, .f32);       // Bias vector

const hidden = try linearLayer(&session, input, weight, bias);
const output_data = try hidden.data(f32);
```

### Broadcasting and Complex Operations

```zig
// Broadcasting example: [2, 1] + [1, 3] = [2, 3]
const a = try session.ones(&.{2, 1}, .f32);
const b = try session.ones(&.{1, 3}, .f32);
const broadcasted = try a.add(b);

// Reshape the result
const flattened = try broadcasted.reshape(&.{6});

// Complex computation chain
const processed = try flattened
    .add(try session.ones(&.{6}, .f32))
    .mul(try session.ones(&.{6}, .f32))
    .relu();
```

### Lazy Mode for Complex Graphs

```zig
// Switch to lazy mode for complex computation
session.setMode(.lazy);

// Build entire forward pass without execution
const layer1 = try input.matmul(weight1).add(bias1).relu();
const layer2 = try layer1.matmul(weight2).add(bias2).relu();
const layer3 = try layer2.matmul(weight3).add(bias3);

// Execute entire graph at once
try session.run();

// All results are now available
const output_data = try layer3.data(f32);
const hidden1_data = try layer1.data(f32);
const hidden2_data = try layer2.data(f32);
```

### Error Handling

```zig
// Session validation
const a = try session1.ones(&.{2, 2}, .f32);
const b = try session2.ones(&.{2, 2}, .f32);

// This will fail with IncompatibleTensors
const result = a.add(b) catch |err| switch (err) {
    error.IncompatibleTensors => {
        std.debug.print("Tensors from different sessions!\n", .{});
        return;
    },
    else => return err,
};
```

## Best Practices

### Session Management

1. **One session per computation**: Use a single session for related operations
2. **Proper cleanup**: Always `defer session.deinit()` after creation
3. **Session scope**: Keep session lifetime longer than any SessionTensor

### Memory Management

1. **Automatic cleanup**: Session automatically manages shape and execution memory
2. **Data copying**: Use `.copy()` when you need data to outlive the session
3. **Large tensors**: Be mindful of memory usage with very large tensors

### Performance

1. **Backend selection**: Choose appropriate backend for your hardware
2. **Execution mode**: Use lazy mode for complex graphs, eager for interactive work
3. **Batch operations**: Group related operations when possible

### Error Handling

1. **Session compatibility**: Never mix tensors from different sessions
2. **Shape validation**: Session automatically validates shapes and provides clear errors
3. **Backend fallback**: Handle backend unavailability gracefully

## Performance Considerations

### Execution Mode Impact

**Eager Mode**:
- ✅ Lower latency for small operations
- ✅ Immediate error detection
- ❌ May recompile graph multiple times
- ❌ Less opportunity for optimization

**Lazy Mode**:
- ✅ Potential for whole-graph optimization
- ✅ Single compilation pass
- ✅ Better for large graphs
- ❌ Higher latency until `.run()`
- ❌ Deferred error detection

### Backend Selection

**CPU Backend**:
- ✅ Always available
- ✅ Good for small to medium operations
- ✅ Excellent for development and testing
- ❌ Limited parallelism for large operations

**GPU Backends** (when available):
- ✅ Massive parallelism for large tensors
- ✅ Optimized for deep learning workloads
- ❌ Higher setup overhead
- ❌ Memory transfer costs

### Memory Usage

1. **Shape tracking**: Session stores shape copies for memory safety
2. **Compilation artifacts**: Compiled graphs and executors are cached
3. **Data lifetime**: SessionTensor data is tied to session lifetime
4. **Automatic cleanup**: All memory freed on `session.deinit()`

### Optimization Tips

1. **Reuse sessions**: Create one session per computation workflow
2. **Batch operations**: Group related operations in lazy mode
3. **Backend selection**: Profile different backends for your workload
4. **Memory monitoring**: Use testing allocator to detect leaks during development

## Integration with Low-Level API

The Session API is built on top of the low-level tensor API and can be mixed when needed:

```zig
// Access underlying TensorHandle if needed
const session_tensor = try session.ones(&.{2, 2}, .f32);
const handle: zing.TensorHandle = session_tensor.handle;

// Access the computation graph
const graph: *zing.Graph = &session.graph;

// Mix with low-level operations when necessary
const low_level_result = try zing.tensor.advanced_operation(handle);
```

The Session API provides the convenience and safety of automatic management while still allowing access to the full power of the underlying system when needed.