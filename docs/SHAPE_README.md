# Zing Shape System: Comprehensive Tutorial

## Table of Contents
1. [Overview](#overview)
2. [Core Concepts](#core-concepts)
3. [The Shape Pipeline](#the-shape-pipeline)
4. [Data Structures](#data-structures)
5. [Graph Construction Phase](#graph-construction-phase)
6. [Shape Inference Pass](#shape-inference-pass)
7. [Compilation Phase](#compilation-phase)
8. [Execution Phase](#execution-phase)
9. [Broadcasting Deep Dive](#broadcasting-deep-dive)
10. [View Operations](#view-operations)
11. [Memory Layout](#memory-layout)
12. [<PERSON>rro<PERSON> Handling](#error-handling)
13. [Best Practices](#best-practices)

## Overview

The Zing shape system is a sophisticated framework for tracking tensor dimensions, strides, and transformations throughout the compilation and execution pipeline. It enables:

- **Zero-copy view operations** (reshape, transpose, slice, broadcast)
- **Symbolic dimensions** for dynamic shapes
- **Automatic broadcasting** following NumPy semantics
- **Efficient index computation** at runtime
- **Compile-time shape validation** and optimization

## Core Concepts

### 1. **Shape vs View**
- **Shape**: The logical dimensions of a tensor (e.g., [2, 3, 4])
- **View**: A specific interpretation of memory with strides and offsets

### 2. **Symbolic vs Concrete**
- **Concrete**: Known dimensions at compile time (e.g., 256)
- **Symbolic**: Dynamic dimensions resolved at runtime (e.g., batch_size)

### 3. **Broadcasting**
- Automatic expansion of dimensions to make tensors compatible
- Zero-copy operation using stride manipulation

## The Shape Pipeline

```
Graph Construction → Shape Inference → Compilation → Execution
      ↓                    ↓                ↓            ↓
 ShapeTracker      ShapeTracker       Shape        IndexExpr
  (symbolic)       (broadcasted)    (concrete)    (runtime)
```

## Data Structures

### 1. **SymbolicDim** (`shape/types.zig`)

Represents a dimension that can be either concrete or symbolic:

```zig
pub const SymbolicDim = union(enum) {
    concrete: i64,      // Known size (e.g., 256)
    dynamic: ExprId,    // Symbolic expression ID
};
```

### 2. **ShapeTracker** (`shape/tracker.zig`)

The primary shape representation during graph construction:

```zig
pub const ShapeTracker = struct {
    dims: []const SymbolicDim,     // Logical dimensions
    strides: []const SymbolicDim,  // Memory strides
    offset: SymbolicDim,           // Starting offset
    indexes: []const u8,           // Dimension permutation
    fake: []const bool,            // Broadcasted dimensions
    mask: ?[]const MaskRange,      // Optional masking
    padding: ?[]const [2]SymbolicDim, // Optional padding
};
```

Key fields explained:
- **dims**: The logical shape (e.g., [batch, height, width, channels])
- **strides**: Bytes to skip for each dimension increment
- **offset**: Starting position in memory
- **indexes**: Tracks dimension reordering from transposes
- **fake**: Marks broadcasted dimensions (stride=0)
- **mask/padding**: Advanced indexing features

### 3. **Shape** (`types.zig`)

Simplified runtime representation:

```zig
pub const Shape = struct {
    dims: []const i64,      // Concrete dimensions
    strides: []const i64,   // Concrete strides
    offset: i64,            // Concrete offset
    index_expr: IndexExpr,  // Pre-computed indexing
};
```

### 4. **IndexExpr** (`types.zig`)

Efficient runtime index computation:

```zig
pub const IndexExpr = struct {
    coeffs: []const i64,    // Stride coefficients
    mods: []const i64,      // Modulo values for wrapping
    offset: i64,            // Base offset
    
    pub fn compute(self: *const IndexExpr, idx: usize) usize {
        var result: usize = @intCast(self.offset);
        var temp_idx = idx;
        
        // Compute: offset + sum(coeffs[i] * (idx % mods[i]))
        for (self.mods, self.coeffs) |mod, coeff| {
            if (mod > 0) {
                result += @intCast(coeff * @as(i64, @intCast(temp_idx % @as(usize, @intCast(mod)))));
                temp_idx /= @as(usize, @intCast(mod));
            }
        }
        return result;
    }
};
```

## Graph Construction Phase

During graph construction, operations create `TensorHandle` objects with `ShapeTracker`:

```zig
// Create tensors
const a = try tensor.placeholder(&graph, &.{2, 3}, .f32);    // ShapeTracker: dims=[2,3], strides=[3,1]
const b = try tensor.placeholder(&graph, &.{3}, .f32);       // ShapeTracker: dims=[3], strides=[1]

// Operations infer output shapes
const c = try a.add(b);  // Broadcasting: output shape [2,3]
```

### Shape Tracking in TensorHandle

```zig
pub const TensorHandle = struct {
    graph: *Graph,          // Reference to computation graph
    node_id: NodeId,        // Node in graph (may be shared across views!)
    shape: ShapeTracker,    // This tensor's view of the data
    dtype: DataType,        // Data type
};
```

**Critical insight**: View operations (reshape, transpose, etc.) return the SAME `node_id` with a DIFFERENT `shape`. This enables zero-copy views but requires careful handling in shape inference.

## Shape Inference Pass

The shape inference pass (`compiler/passes/shape_inference.zig`) runs after graph construction to:

1. **Compute output shapes** for all operations
2. **Create broadcasted input shapes** for elementwise ops
3. **Store shape metadata** in graph nodes

### The Process

```zig
// For each node in topological order:
fn inferNodeShapeVisitor(ctx: *PassContext, node_id: NodeId, node: *const Node) !void {
    // 1. Get or compute output shape
    const output_shape = if (has_output_shape) 
        existing_shape 
    else 
        try inferNodeShape(graph, node, allocator);
    
    // 2. For elementwise operations, create broadcasted input shapes
    if (node.spec == .compute and node.spec.compute.isElementwise()) {
        // Get original input shapes (from pre-stored metadata or nodes)
        const original_shapes = getInputShapes(node);
        
        // Create broadcasted versions matching output shape
        for (original_shapes, input_shapes) |orig, *input| {
            input.* = try createBroadcastedShape(orig, output_shape, allocator, pool);
        }
    }
}
```

### Broadcasting Shape Creation

```zig
fn createBroadcastedShape(input: ShapeTracker, target: ShapeTracker, ...) !ShapeTracker {
    // 1. Handle rank differences (prepend dimensions)
    // 2. For each dimension:
    //    - If input dim = 1 and target dim > 1: broadcast
    //    - Set stride = 0 and fake = true for broadcast dims
    // 3. Return new ShapeTracker with broadcasting applied
}
```

Example:
- Input: [1, 3] → Broadcasted to [2, 3]: dims=[2,3], strides=[0,1], fake=[true,false]
- Input: [2, 1] → Broadcasted to [2, 3]: dims=[2,3], strides=[1,0], fake=[false,true]

## Compilation Phase

During compilation, `ShapeTracker` is converted to runtime `Shape`:

```zig
fn convertShapeTrackerToShape(shape_tracker: ShapeTracker, allocator: Allocator) !Shape {
    // Extract concrete values
    const dims = extractConcreteDims(shape_tracker);
    const strides = extractConcreteStrides(shape_tracker);
    
    // Build IndexExpr for efficient runtime indexing
    for (shape_tracker.dims, shape_tracker.strides, shape_tracker.fake) |dim, stride, is_fake| {
        coeffs[i] = if (is_fake) 0 else stride.concrete;  // 0 for broadcast dims
        mods[i] = dim.concrete;  // Always use actual dimension for modulo
    }
    
    return Shape{
        .dims = dims,
        .strides = strides,
        .offset = shape_tracker.offset.concrete,
        .index_expr = IndexExpr{ .coeffs = coeffs, .mods = mods, .offset = offset },
    };
}
```

## Execution Phase

At runtime, kernels use the pre-computed `IndexExpr` for efficient element access:

```zig
fn cpuAddKernel(args: KernelArgs) void {
    const a_ptr = @ptrCast([*]const f32, args.inputs[0].ptr);
    const b_ptr = @ptrCast([*]const f32, args.inputs[1].ptr);
    const out_ptr = @ptrCast([*]f32, args.outputs[0].ptr);
    
    const a_expr = &args.input_shapes[0].index_expr;
    const b_expr = &args.input_shapes[1].index_expr;
    
    // Compute each output element
    for (0..args.work_size) |i| {
        const a_idx = a_expr.compute(i);  // Handles broadcasting automatically
        const b_idx = b_expr.compute(i);  // via coeffs=0 for fake dims
        out_ptr[i] = a_ptr[a_idx] + b_ptr[b_idx];
    }
}
```

## Broadcasting Deep Dive

### Broadcasting Rules (NumPy Compatible)

1. **Dimension Alignment**: Start from the rightmost dimension
2. **Compatibility**: Dimensions are compatible if:
   - They are equal, OR
   - One of them is 1
3. **Result Shape**: Take the maximum of each dimension pair

### Implementation Details

```zig
// During shape inference:
[1, 3] + [2, 1] → [2, 3]

// Input shape transformations:
Input A: [1, 3] → dims=[2,3], strides=[0,1], fake=[true,false]
Input B: [2, 1] → dims=[2,3], strides=[1,0], fake=[false,true]

// At runtime (IndexExpr):
For output index 5 (row=1, col=2):
- Input A: idx = 0*(5/3) + 1*(5%3) = 0 + 2 = 2
- Input B: idx = 1*(5/3) + 0*(5%3) = 1 + 0 = 1
```

## View Operations

View operations modify the `ShapeTracker` without creating new graph nodes:

### Reshape
```zig
pub fn reshape(self: TensorHandle, new_shape: []const i64) !TensorHandle {
    // 1. Ensure tensor is contiguous (may create new node if not)
    const contiguous = try self.makeContiguous();
    
    // 2. Create new ShapeTracker with target dimensions
    const new_tracker = try ShapeTracker.fromDims(new_dims, allocator, pool);
    
    // 3. Return SAME node_id with NEW shape
    return TensorHandle{
        .graph = self.graph,
        .node_id = contiguous.node_id,  // Same data
        .shape = new_tracker,            // New view
        .dtype = self.dtype,
    };
}
```

### Transpose
```zig
pub fn transpose(self: TensorHandle, axes: ?[]const usize) !TensorHandle {
    var new_tracker = try self.shape.clone(allocator);
    try new_tracker.transpose(axes, allocator);  // Permutes dims, strides, indexes
    
    return TensorHandle{
        .graph = self.graph,
        .node_id = self.node_id,  // Same data
        .shape = new_tracker,      // Transposed view
        .dtype = self.dtype,
    };
}
```

### Broadcast
```zig
pub fn broadcast(self: TensorHandle, target_shape: []const i64) !TensorHandle {
    // Validate broadcasting is possible
    // Update dimensions and set fake flags
    // Return view with expanded shape
}
```

## Memory Layout

### Contiguity

A tensor is contiguous if elements are laid out sequentially in memory:

```zig
pub fn isContiguous(self: *const ShapeTracker) bool {
    // Check if strides follow row-major order
    var expected_stride: i64 = 1;
    var i = self.dims.len;
    while (i > 0) : (i -= 1) {
        if (self.strides[i-1].concrete != expected_stride) return false;
        expected_stride *= self.dims[i-1].concrete;
    }
    return true;
}
```

### When Contiguity Matters

Some operations require contiguous memory:
- Reshape (except special cases)
- Certain BLAS operations
- Memory-mapped I/O

The system automatically inserts `contiguous` operations when needed.

## Error Handling

The shape system validates operations and provides clear error messages:

```zig
pub const ShapeError = error{
    IncompatibleShapes,      // Shapes cannot be broadcast
    InvalidAxis,             // Axis out of bounds
    InvalidReshape,          // Total elements don't match
    RankTooHigh,            // Exceeds MAX_RANK
    NegativeDimension,      // Negative dimension value
    TensorTooLarge,         // Exceeds MAX_TENSOR_SIZE
    // ... etc
};
```

## Best Practices

### 1. **Understand View vs Copy**
- View operations are cheap (O(1))
- Only create copies when necessary (e.g., non-contiguous reshape)

### 2. **Leverage Broadcasting**
- Avoid explicit expansion when broadcasting suffices
- Broadcasting is memory-efficient

### 3. **Shape Metadata**
- Always set shape metadata for new operations
- Use `setNodeShapeMetadata` to store input/output shapes

### 4. **Error Handling**
- Check shape compatibility early
- Provide meaningful error messages

### 5. **Performance**
- Prefer contiguous layouts when possible
- Use broadcasting instead of explicit repeat/tile

## Example: Complete Shape Flow

```zig
// 1. Graph Construction
const a = try tensor.placeholder(&graph, &.{1, 3}, .f32);
const b = try tensor.placeholder(&graph, &.{2, 1}, .f32);
const c = try a.add(b);  // Infers output shape [2, 3]

// 2. Shape Inference Pass
// - Computes c's output shape: [2, 3]
// - Creates broadcasted input shapes:
//   - a: [2, 3] with strides [0, 1], fake=[true, false]
//   - b: [2, 3] with strides [1, 0], fake=[false, true]

// 3. Compilation
// - Converts ShapeTracker to Shape
// - Builds IndexExpr for runtime

// 4. Execution
// - Kernel uses IndexExpr to compute indices
// - Broadcasting handled automatically via coeffs=0
```

## Advanced Topics

### Symbolic Dimensions

For dynamic shapes, dimensions can be symbolic expressions:

```zig
const batch = try pool.variable("batch");
const seq_len = try pool.variable("seq_len");
const shape = &[_]SymbolicDim{
    .{ .dynamic = batch },
    .{ .dynamic = seq_len },
    .{ .concrete = 512 },
};
```

### Shape Constraints

The symbolic system can express relationships:

```zig
// Express that two dimensions must be equal
const constraint = try pool.equal(dim1, dim2);

// Express that dimension must be even
const half = try pool.div(dim, 2);
const even_constraint = try pool.equal(dim, try pool.mul(half, 2));
```

### Optimization Opportunities

The shape system enables optimizations:
- **View fusion**: Combine multiple view ops
- **Broadcast elimination**: When unnecessary
- **Layout optimization**: Choose optimal memory layout

## Summary

The Zing shape system provides a robust foundation for tensor operations:

1. **ShapeTracker** during graph construction (supports symbolic dimensions)
2. **Shape inference** creates properly broadcasted shapes
3. **Shape conversion** prepares efficient runtime representation
4. **IndexExpr** enables fast element access with automatic broadcasting

This design achieves zero-copy views, automatic broadcasting, and efficient execution while maintaining clear separation between compile-time and runtime concerns.