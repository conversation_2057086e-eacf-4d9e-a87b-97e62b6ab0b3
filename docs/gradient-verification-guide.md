# Gradient Verification Guide

This guide explains how to use the gradient verification system to test <PERSON><PERSON>'s autograd implementation against PyTorch ground truth.

## Overview

The gradient verification system consists of:
1. A Python script (`test_gradients.py`) that generates test cases using PyTorch
2. A Zig module (`src/tests/gradient_verification.zig`) that loads and verifies gradients
3. Integration examples showing how to use verification in tests

## Generating Test Data

First, run the Python script to generate test cases:

```bash
python3 test_gradients.py
```

This creates JSON files in `test_data/` with various gradient test cases:
- `add_simple.json` - Simple addition gradients
- `mul_simple.json` - Simple multiplication gradients
- `broadcast_add.json` - Broadcasting addition gradients
- `matmul_simple.json` - Matrix multiplication gradients
- `linear_layer.json` - Linear layer (x @ W + b) gradients
- `activations.json` - ReLU, sigmoid, tanh gradients
- `sum_reduce_axis1.json` - Reduction operation gradients
- `complex_chain.json` - Complex operation chain gradients
- `residual_connection.json` - Residual connection gradients
- `math_functions.json` - Square, exp, log gradients

## Using Gradient Verification in Tests

### Basic Usage

```zig
const gradient_verification = @import("gradient_verification.zig");

test "verify my gradients" {
    const allocator = testing.allocator;
    
    // Load PyTorch test case
    var test_case = try gradient_verification.loadTestCase(
        allocator, 
        "test_data/add_simple.json"
    );
    defer test_case.deinit();
    
    // ... run your autograd computation ...
    
    // Collect computed gradients
    const computed_grads = try gradient_verification.collectGradients(
        allocator,
        &autograd,
        &graph,
        loss_node,
        param_nodes,
    );
    defer computed_grads.deinit();
    
    // Verify against PyTorch
    const result = try gradient_verification.verifyGradients(
        allocator,
        &test_case,
        computed_grads,
        1e-5, // tolerance
    );
    
    try testing.expect(result.passed);
}
```

### Integration with Existing Tests

To add verification to an existing autograd test:

1. Create a corresponding PyTorch test case
2. Load the test case at the beginning of your test
3. After computing gradients, verify them against PyTorch

Example:
```zig
test "existing autograd test with verification" {
    // Original test setup
    var graph = try GraphEngine.init(allocator);
    defer graph.deinit();
    
    // Add verification
    var test_case = try gradient_verification.loadTestCase(
        allocator, 
        "test_data/my_test.json"
    );
    defer test_case.deinit();
    
    // ... run computation and backward pass ...
    
    // Verify gradients
    const result = try gradient_verification.verifyGradients(...);
    try testing.expect(result.passed);
}
```

## Test Case Format

Test cases are JSON files with the following structure:

```json
{
  "name": "test_name",
  "description": "Description of what's being tested",
  "inputs": {
    "x": [[1.0, 2.0], [3.0, 4.0]],
    "y": [[5.0, 6.0], [7.0, 8.0]]
  },
  "grad_outputs": {
    "x": [[1.0, 1.0], [1.0, 1.0]],
    "y": [[1.0, 1.0], [1.0, 1.0]]
  }
}
```

## Adding New Test Cases

To add a new gradient test case:

1. Add a new test function to `test_gradients.py`:
```python
def test_my_operation():
    x = torch.tensor(..., requires_grad=True)
    y = my_operation(x)
    loss = y.sum()
    loss.backward()
    
    save_test_case("my_operation",
                   {"x": x.detach()},
                   {"x": x.grad},
                   "Description of my operation")
```

2. Run the script to generate the JSON file
3. Create a Zig test that loads and verifies the gradients

## Debugging Failed Verifications

When gradient verification fails, the system provides:
- Maximum absolute error
- Maximum relative error
- Detailed error messages for each parameter

Common causes of failures:
1. Incorrect gradient computation in autograd
2. Numerical precision issues (try increasing tolerance)
3. Shape mismatches between Zig and PyTorch
4. Missing gradient computations for some parameters

## Best Practices

1. **Start Simple**: Begin with basic operations before testing complex chains
2. **Use Appropriate Tolerances**: Float32 computations typically need 1e-5 to 1e-6
3. **Test Edge Cases**: Include tests for zero gradients, broadcasting, etc.
4. **Document Decompositions**: If an operation decomposes to primitives, document how

## Limitations

Currently, the verification system:
- Only supports float32 tensors
- Requires manual mapping between Zig graph nodes and PyTorch parameters
- Cannot directly test operations that don't exist as primitives in Zing

Future improvements could include:
- Support for other data types
- Automatic graph construction from test cases
- Verification of intermediate gradient values