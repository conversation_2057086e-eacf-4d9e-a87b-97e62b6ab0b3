# Session API Quick Start

This is a 5-minute guide to get you started with <PERSON><PERSON>'s Session API. For complete documentation, see [SESSION_API_GUIDE.md](SESSION_API_GUIDE.md).

## Installation and Setup

```zig
const std = @import("std");
const zing = @import("zing");
```

## Basic Usage

### 1. Create a Session

```zig
pub fn main() !void {
    var gpa = std.heap.GeneralPurposeAllocator(.{}){};
    defer _ = gpa.deinit();
    const allocator = gpa.allocator();

    // Create session (manages everything automatically)
    var session = try zing.Session.init(allocator);
    defer session.deinit();  // Cleans up everything
}
```

### 2. Create Tensors

```zig
// Create tensors with specific values
const zeros = try session.zeros(&.{2, 3}, .f32);  // 2x3 matrix of zeros
const ones = try session.ones(&.{2, 3}, .f32);    // 2x3 matrix of ones

// Create placeholder for data you'll provide later
const input = try session.placeholder(&.{2, 3}, .f32);
```

### 3. PyTorch-Style Operations

```zig
// Method chaining (just like PyTorch!)
const a = try session.ones(&.{2, 3}, .f32);
const b = try session.ones(&.{2, 3}, .f32);

const result = try a.add(b)      // Element-wise addition
                    .mul(a)      // Element-wise multiplication  
                    .relu();     // ReLU activation

// Get the computed data
const data = try result.data(f32);
std.debug.print("Result: {any}\n", .{data});
```

## Common Patterns

### Neural Network Layer

```zig
// Typical neural network forward pass
const input = try session.ones(&.{32, 128}, .f32);    // Batch of 32, 128 features
const weight = try session.ones(&.{128, 64}, .f32);   // Weight matrix
const bias = try session.ones(&.{1, 64}, .f32);       // Bias vector

// Forward pass: linear layer + activation
const linear = try input.matmul(weight);
const with_bias = try linear.add(bias);
const activated = try with_bias.relu();

const output = try activated.data(f32);
```

### Matrix Operations

```zig
// Matrix multiplication
const a = try session.ones(&.{2, 3}, .f32);
const b = try session.ones(&.{3, 4}, .f32);
const product = try a.matmul(b);  // Result: [2, 4]

// Reshape operations
const flattened = try product.reshape(&.{8});  // Flatten to 1D
```

### Broadcasting

```zig
// Broadcasting: [2, 1] + [1, 3] = [2, 3]
const a = try session.ones(&.{2, 1}, .f32);
const b = try session.ones(&.{1, 3}, .f32);
const broadcasted = try a.add(b);  // Automatically broadcasts to [2, 3]
```

## Execution Modes

### Eager Mode (Default)
Operations execute when you request data:

```zig
// This is the default - executes immediately when you call .data()
const result = try a.add(b).mul(c);
const data = try result.data(f32);  // ← Execution happens here
```

### Lazy Mode
Build the entire graph first, then execute:

```zig
session.setMode(.lazy);

// Build complex computation (no execution yet)
const layer1 = try input.matmul(w1).add(b1).relu();
const layer2 = try layer1.matmul(w2).add(b2).relu();
const output = try layer2.matmul(w3);

// Execute everything at once
try session.run();

// Data is already computed
const result = try output.data(f32);
```

## Backend Selection

```zig
// Default: CPU backend
var session = try zing.Session.init(allocator);

// Explicit backend selection
const config = zing.session.BackendConfig{ .device = .cpu };
var cpu_session = try zing.Session.initWithBackend(allocator, config);

// Request GPU (falls back to CPU if not available)
const gpu_config = zing.session.BackendConfig{ .device = .cuda };
var gpu_session = try zing.Session.initWithBackend(allocator, gpu_config);
```

## Complete Example

```zig
const std = @import("std");
const zing = @import("zing");

pub fn main() !void {
    var gpa = std.heap.GeneralPurposeAllocator(.{}){};
    defer _ = gpa.deinit();
    const allocator = gpa.allocator();

    // Create session
    var session = try zing.Session.init(allocator);
    defer session.deinit();

    std.debug.print("🚀 Zing Session API Demo\n");

    // Create input data
    const input = try session.ones(&.{4, 8}, .f32);   // Input: 4 samples, 8 features
    const weight = try session.ones(&.{8, 3}, .f32);  // Weight: 8 -> 3 hidden units
    const bias = try session.ones(&.{1, 3}, .f32);    // Bias: 3 units

    // Neural network forward pass
    const hidden = try input.matmul(weight).add(bias).relu();
    
    std.debug.print("Input shape: [{}, {}]\n", .{ input.shape[0], input.shape[1] });
    std.debug.print("Output shape: [{}, {}]\n", .{ hidden.shape[0], hidden.shape[1] });
    
    // Get results
    const output_data = try hidden.data(f32);
    std.debug.print("First few outputs: [{d:.2}, {d:.2}, {d:.2}]\n", 
                    .{ output_data[0], output_data[1], output_data[2] });

    std.debug.print("✅ Demo complete!\n");
}
```

## Next Steps

- **[SESSION_API_GUIDE.md](SESSION_API_GUIDE.md)** - Complete API documentation
- **[TENSOR_API_REFERENCE.md](TENSOR_API_REFERENCE.md)** - Low-level API for advanced users
- **[COMPTIME_VS_RUNTIME_GUIDE.md](COMPTIME_VS_RUNTIME_GUIDE.md)** - Performance optimization guide

## Key Advantages

✅ **No manual memory management** - Session handles everything  
✅ **No manual graph/compile/execute** - Automatic lifecycle  
✅ **PyTorch-like syntax** - Familiar method chaining  
✅ **Backend flexibility** - CPU/GPU with automatic fallback  
✅ **Execution modes** - Eager for development, lazy for production  
✅ **Type safety** - Compile-time error checking  
✅ **Zero-cost abstractions** - High-level API, low-level performance