# The Zing Compilation Pipeline: A Comprehensive Guide

## Table of Contents
1. [Overview](#overview)
2. [The Journey of a Tensor Operation](#the-journey-of-a-tensor-operation)
3. [Core Architecture](#core-architecture)
4. [The Pass Template System](#the-pass-template-system)
5. [Compilation Flow](#compilation-flow)
6. [Backend Integration](#backend-integration)
7. [Memory Planning](#memory-planning)
8. [Execution Integration](#execution-integration)
9. [Adding New Optimizations](#adding-new-optimizations)
10. [Debugging the Compiler](#debugging-the-compiler)

## Overview

The Zing compiler is the heart of the tensor computation framework. When you write `a.matmul(b).add(c)`, a complex journey begins that transforms these high-level operations into highly optimized machine code. This document tells that story.

At its core, the Zing compiler is a **transformation engine**. It takes a graph of tensor operations—nodes representing computations like matrix multiplication, addition, or activation functions, connected by edges representing data flow—and transforms it through a series of optimization passes until it becomes an efficient execution plan tailored to your specific hardware.

What makes Zing special is its **separation of concerns**. The compiler doesn't need to know whether you're running on a CPU, GPU, or specialized accelerator when it's figuring out that `x + 0` equals `x`, or that two identical subexpressions can share the same computation. These backend-agnostic optimizations form the foundation of the compiler. Only in the final stages does hardware-specific knowledge come into play, allowing each backend to generate code that takes full advantage of its unique capabilities.

### Key Design Principles

1. **Zero-Cost Abstractions**: We leverage Zig's comptime features extensively. When you compile for a specific backend, there's no runtime dispatch overhead—everything is resolved at compile time.

2. **Backend Agnostic Core**: The majority of optimizations work on the abstract computation graph. Whether you're targeting CPU or GPU, algebraic simplification remains the same.

3. **Template-Based Passes**: Every compiler pass follows the same template pattern, making it easy to understand, test, and compose optimizations.

4. **Explicit Memory Management**: Following Zig's philosophy, memory ownership is always clear. The compiler tracks exactly when buffers are created, used, and can be freed.

5. **Composable Transformations**: Passes are designed to work together. The output of one pass seamlessly becomes the input to the next.

## The Journey of a Tensor Operation

Let's follow a simple example through the entire compilation pipeline to understand how everything fits together:

```zig
// User code
const a = tensor.constant(&graph, 2.0, .f32);
const b = tensor.placeholder(&graph, &[_]i64{10, 20}, .f32);
const c = a.add(b).relu();
```

This innocent-looking code triggers a fascinating journey:

### Step 1: Graph Construction

When you call `a.add(b)`, you're not performing addition—you're building a computation graph. The `TensorHandle` returned by each operation contains:
- A reference to the graph
- A node ID representing this operation
- Shape information (which might be symbolic)
- Data type information

The graph at this stage looks like:
```
Node 0 (Constant: 2.0)
Node 1 (Placeholder: [10, 20])
Node 2 (Add: Node 0 + Node 1)
Node 3 (ReLU: Node 2)
```

But this is just the beginning. The graph is unoptimized, shapes might be unknown, and there's no execution plan.

### Step 2: Compilation Begins

When you call `graph.compile(CpuBackend, &mut c)`, the transformation begins. The compiler first needs to understand what it's working with. This is where our infrastructure comes into play.

The `compile.zig` module serves as the entry point. It's remarkably simple because of our comptime backend selection:

```zig
pub fn compile(comptime Backend: type, graph: *Graph, targets: []NodeId) !CompiledProgram {
    // Validate at compile time that Backend follows our interface
    comptime validateBackend(Backend);
    
    // Execute the unified pipeline with this backend
    return executeUnifiedPipeline(Backend, graph, .{}, allocator);
}
```

Notice how the backend type is `comptime`—Zig will generate specialized code for each backend with zero runtime overhead.

## Compiler Passes Reference

### Pass Execution Order

Passes are executed based on their priority (higher priority runs first):

1. **Shape Inference** (priority 100)
2. **Shape Validation** (priority 95)  
3. **Early Optimizations** (priority 80-90)
4. **Memory Planning** (priority 60)
5. **Backend-Specific Optimizations** (priority 40-50)
6. **Late Optimizations** (priority 20-30)

### Core Analysis Passes

#### shape_inference.zig - Shape Inference Pass
- **Priority**: 100 (runs first)
- **Purpose**: Computes output shapes for all operations in the graph
- **Key Features**:
  - Infers output shapes based on operation type and input shapes
  - Handles broadcasting rules for elementwise operations
  - Computes reduction output shapes
  - Essential prerequisite for memory planning
- **Modifies Graph**: No (only adds metadata)

#### shape_fixes.zig - Shape Validation and Correction
- **Contains 3 passes**:
  1. `ShapeValidationPass` (priority 95): Validates broadcast compatibility
  2. `BroadcastInsertionPass` (priority 85): Inserts broadcast nodes where needed
  3. `MemoryLayoutPass` (priority 80): Optimizes memory layout for performance

#### memory_planning.zig - Memory Planning Pass
- **Priority**: 60
- **Purpose**: Computes buffer allocation strategy for graph execution
- **Key Features**:
  - Determines buffer sizes for all tensors
  - Optimizes memory reuse through lifetime analysis
  - Handles multi-output operations correctly
  - Essential for efficient execution

### Optimization Passes

#### constant_folding.zig - Constant Folding
- **Priority**: 90
- **Purpose**: Evaluates constant expressions at compile time
- **Benefits**: Reduces runtime computation overhead

#### cse.zig - Common Subexpression Elimination
- **Priority**: 85
- **Purpose**: Eliminates duplicate computations
- **Benefits**: Reduces computation and memory usage

#### algebraic_simplification.zig - Algebraic Simplification
- **Priority**: 80
- **Purpose**: Simplifies mathematical expressions
- **Examples**: x + 0 → x, x * 1 → x, x * 0 → 0

#### matmul_recognition.zig - Matrix Multiplication Recognition
- **Priority**: 70
- **Purpose**: Identifies and optimizes matrix multiplication patterns
- **Benefits**: Uses optimized BLAS kernels where possible

### Backend-Specific Passes

#### fusion.zig - Operation Fusion
- **Priority**: 50
- **Purpose**: Combines multiple operations into single kernels
- **Benefits**: Reduces memory bandwidth and kernel launch overhead

#### layout_optimization.zig - Memory Layout Optimization
- **Priority**: 45
- **Purpose**: Optimizes tensor memory layouts for target backend
- **Benefits**: Improves cache performance and vectorization

## Core Architecture

Now let's dive deeper into how the compiler is structured. The architecture is built around several key components that work together like a well-oiled machine.

### The Graph: Our Central Data Structure

At the heart of everything is the `Graph` structure. Think of it as a living document that gets continuously refined through the compilation process. When our example creates nodes for constant, placeholder, add, and relu operations, they're stored in a petgraph-based directed graph structure.

But the graph is more than just nodes and edges. Each node carries metadata—information about shapes, data types, and optimization hints that passes can read and update. This metadata system is crucial because it allows passes to communicate. The shape inference pass adds shape information that the memory planning pass later uses to allocate buffers.

```zig
// Inside graph.zig
pub const Node = struct {
    id: NodeId,
    spec: NodeSpec,  // What kind of operation (compute, data, control)
    inputs: []NodeId,
    outputs: []TensorSpec,
    metadata: ?*NodeMetadata,  // Extensible metadata system
};
```

### The Pass Infrastructure: Templates for Transformation

Here's where things get interesting. Every optimization in Zing follows the same pattern, defined by our pass template system. This isn't just about code reuse—it's about creating a uniform mental model for understanding and composing optimizations.

The `pass_templates.zig` module defines the blueprint:

```zig
pub fn createTraversalPass(comptime config: PassConfig) type {
    return struct {
        pub const name = config.name;
        pub const priority = config.priority;
        
        pub fn run(ctx: *PassContext) !void {
            // Traverse nodes in topological order
            const nodes = try ctx.graph.topologicalSort();
            for (nodes) |node_id| {
                const node = ctx.graph.getNodeMut(node_id) orelse continue;
                _ = try config.visitor(node, ctx);
            }
        }
    };
}
```

This template system means that whether you're writing constant folding or dead code elimination, the structure is the same. You define what happens when visiting each node, and the infrastructure handles the traversal, error propagation, and state management.

### The PassContext: Thread of Continuity

As passes execute, they need to share information. The `PassContext` is the thread that weaves through the entire compilation, accumulating knowledge and decisions:

```zig
pub const PassContext = struct {
    graph: *Graph,
    allocator: Allocator,
    backend_capabilities: ?BackendCapabilities = null,
    memory_plan: ?ResolvedMemoryPlan = null,
    pattern_matches: ?[]PatternMatch = null,
    // ... more shared state
};
```

When the shape inference pass determines that our add operation produces a [10, 20] tensor, it stores this in the graph's metadata. When the memory planning pass runs later, it reads this information to determine that it needs 10 * 20 * 4 = 800 bytes for the result.

### The Unified Pipeline: Orchestrating the Symphony

The `unified_pipeline.zig` module is the conductor of our optimization orchestra. It knows which passes to run, in what order, and how to handle the transition from backend-agnostic to backend-specific optimizations.

The pipeline executes in phases:

```zig
pub fn executeUnifiedPipeline(comptime Backend: type, graph: *Graph, config: PipelineConfig, allocator: Allocator) !CompiledProgram {
    var ctx = PassContext.init(graph, allocator);
    
    // Phase 1: Core optimizations (backend-agnostic)
    try executeCorePasses(&ctx);
    
    // Phase 2: Backend-specific preparation
    ctx.backend_capabilities = Backend.getCapabilities();
    try executeBackendPreparation(Backend, &ctx);
    
    // Phase 3: Final memory planning
    try MemoryPlanningPass.run(&ctx);
    
    // Phase 4: Backend compilation
    return Backend.compile(graph, ctx);
}
```

Notice how the backend only comes into play in the later phases. The core optimizations—algebraic simplification, constant folding, common subexpression elimination—all happen without knowing whether we're targeting CPU or GPU.

### The Backend Interface: Contract for Code Generation

The `backend_interface.zig` defines what it means to be a backend in Zing. It's a contract that says "if you implement these functions, you can plug into our compiler":

```zig
pub fn BackendInterface(comptime Self: type) type {
    return struct {
        pub fn getCapabilities() BackendCapabilities {
            return Self.getCapabilities();
        }
        
        pub fn compile(graph: *Graph, ctx: PassContext) !CompiledProgram {
            return Self.compile(graph, ctx);
        }
        
        pub fn getPatterns() []const Pattern {
            return Self.patterns;
        }
    };
}
```

This interface is validated at compile time. If a backend doesn't implement the required functions, you'll know immediately, not at runtime.

## The Pass Template System

Let's dig deeper into how passes work, because this is where the real magic happens. The pass system is designed around a simple observation: most compiler optimizations follow similar patterns. They traverse the graph, look for specific patterns or conditions, and either transform the graph or collect information.

### The Philosophy Behind Templates

Instead of having each pass implement its own traversal logic, error handling, and state management, we provide templates that handle the boilerplate. This isn't just about reducing code—it's about ensuring consistency and composability.

When you write a pass using our template system, you're filling in the blanks of a well-tested pattern:

```zig
// This is what you write:
pub const ConstantFoldingPass = pass_templates.createTraversalPass(.{
    .name = "constant_folding",
    .priority = 85,
    .visitor = foldConstants,
});

// The template system generates all of this:
pub const ConstantFoldingPass = struct {
    pub const name = "constant_folding";
    pub const priority = 85;
    
    pub fn run(ctx: *PassContext) !void {
        const start_time = std.time.milliTimestamp();
        defer {
            const elapsed = std.time.milliTimestamp() - start_time;
            std.log.debug("[PASS] {} completed in {} ms", .{name, elapsed});
        }
        
        const nodes = try ctx.graph.topologicalSort();
        var modified = false;
        
        for (nodes) |node_id| {
            const node = ctx.graph.getNodeMut(node_id) orelse continue;
            if (try foldConstants(node, ctx)) {
                modified = true;
            }
        }
        
        if (modified) {
            ctx.graph.clearCachedTopology();
        }
    }
};
```

### Types of Passes

We have three main categories of passes, each suited to different kinds of optimizations:

#### 1. Traversal Passes: The Workhorses

These are the most common type. They visit each node once in topological order (inputs before outputs), making local decisions about transformations. Constant folding is a perfect example:

```zig
fn foldConstants(node: *Node, ctx: *PassContext) !bool {
    // Can we fold this operation?
    if (node.spec != .compute) return false;
    
    // Are all inputs constants?
    for (node.inputs) |input_id| {
        const input = ctx.graph.getNode(input_id) orelse return false;
        if (input.spec != .data or input.spec.data != .constant) {
            return false;
        }
    }
    
    // Compute the result at compile time
    const result = try evaluateConstantOperation(node, ctx);
    
    // Replace this node with a constant
    const constant_node = try ctx.graph.addConstant(result);
    try replaceNode(ctx.graph, node.id, constant_node);
    
    return true; // We modified the graph
}
```

#### 2. Analysis Passes: The Observers

These passes don't modify the graph—they observe and collect information. The memory planning pass is our prime example. It studies the graph to understand when each tensor is created and when it's last used:

```zig
fn analyzeAndPlanMemory(ctx: *PassContext) !void {
    // First, understand lifetimes
    const lifetimes = try computeBufferLifetimes(ctx.graph, ctx.allocator);
    defer lifetimes.deinit();
    
    // Then, plan memory allocation with reuse
    const allocations = try optimizeMemoryLayout(lifetimes.items, ctx.allocator);
    defer allocations.deinit();
    
    // Store the plan in the context for backends to use
    ctx.memory_plan = try createMemoryPlan(allocations.items, ctx.allocator);
}
```

The beauty of analysis passes is that they can make complex decisions without worrying about maintaining graph consistency—they're read-only.

#### 3. Transformation Passes: The Architects

These passes make structural changes to the graph. Operation fusion is a classic example—it looks for sequences of elementwise operations and merges them into a single fused kernel:

```zig
fn fuseOperations(ctx: *PassContext) !void {
    const fusion_groups = try identifyFusionOpportunities(ctx.graph);
    
    for (fusion_groups.items) |group| {
        // Create a new fused operation
        const fused_op = try createFusedOperation(group, ctx);
        
        // Redirect edges from the original operations
        for (group.operations) |op_id| {
            try redirectOutputs(ctx.graph, op_id, fused_op);
        }
        
        // Mark original operations for deletion
        for (group.operations) |op_id| {
            try ctx.graph.markForDeletion(op_id);
        }
    }
}
```

### The Pass Context: More Than Just State

The `PassContext` is more than a struct—it's the communication medium between passes. It accumulates knowledge as compilation progresses:

```zig
// Early in compilation
ctx.backend_capabilities = null;  // We don't know the target yet

// After backend selection
ctx.backend_capabilities = CpuBackend.getCapabilities();
// Now passes can make CPU-specific decisions

// After memory planning
ctx.memory_plan = ResolvedMemoryPlan{ ... };
// Backend can use this for buffer allocation
```

This progressive enrichment of context allows passes to make increasingly informed decisions without tight coupling.

## Compilation Flow

Now let's walk through how all these pieces come together during compilation. Remember our simple example from earlier? Let's see what actually happens to it at each stage.

### Stage 1: Shape Inference and Validation

The journey begins with shape inference—arguably the most critical pass in our entire pipeline. Why? Because without knowing the shapes of tensors, we can't allocate memory, we can't validate operations, and we can't generate efficient code.

```
Input Graph
    ↓
┌─────────────────────┐
│ Shape Inference     │ (priority 100)
│ - Compute shapes    │
│ - Handle broadcast  │
└─────────────────────┘
    ↓
┌─────────────────────┐
│ Shape Validation    │ (priority 95)
│ - Check compat      │
│ - Insert fixes      │
└─────────────────────┘
```

When our example hits shape inference, here's what happens:

1. **The constant node (2.0)**: Easy—it's a scalar, shape is `[]`
2. **The placeholder `[10, 20]`**: Shape is already known
3. **The add operation**: This is where it gets interesting

The shape inference pass sees a scalar being added to a `[10, 20]` tensor. It applies broadcasting rules:
- Scalar `[]` can broadcast to any shape
- Output shape will be `[10, 20]`
- The pass adds metadata indicating that broadcasting is needed

But shape inference does more than just compute output shapes. It also:
- Tracks which dimensions are symbolic (unknown at compile time)
- Identifies operations that will need special handling (like broadcasting)
- Builds a shape dependency graph for operations that derive shapes from their inputs

After shape inference, the validation pass runs. This is our safety net. It checks:
- Are the shapes compatible for each operation?
- Do we need to insert any fix-up operations?
- Are there any operations that require contiguous memory but might receive non-contiguous inputs?

For our example, the validation pass might insert an explicit broadcast operation to make the scalar→tensor conversion explicit for the backend.

**Key Passes**:
- `shape_inference.zig`: The detective that figures out what shape everything should be
- `shape_fixes.zig`: The fixer that ensures everything will work correctly

### Stage 2: Early Optimizations

Once we know the shapes, we can start optimizing. These early passes look for obvious improvements that don't require deep analysis:

```
┌─────────────────────┐
│ Algebraic Simp.     │ (priority 90)
│ x + 0 → x           │
└─────────────────────┘
    ↓
┌─────────────────────┐
│ Constant Folding    │ (priority 85)
│ 2 + 3 → 5           │
└─────────────────────┘
    ↓
┌─────────────────────┐
│ Trivial Reduce      │ (priority 70)
│ reduce(x, dim=1)→x  │
└─────────────────────┘
```

These passes are like a good editor—they clean up the obvious issues before we get to the deeper work:

**Algebraic Simplification** is our first editor. It knows the rules of mathematics:
- Adding zero changes nothing, so `x + 0` becomes just `x`
- Multiplying by one is identity, so `x * 1` becomes `x`
- Subtracting yourself gives zero, so `x - x` becomes `0`

Why do these "obvious" optimizations matter? Because they often arise from other transformations or from how users naturally express computations. Every operation we eliminate is one less kernel launch, one less memory access.

**Constant Folding** takes this further. If both inputs to an operation are constants, why wait until runtime to compute the result?
```zig
// Before: Runtime computation
const a = tensor.constant(&graph, 2.0);
const b = tensor.constant(&graph, 3.0);
const c = a.add(b);  // Will compute 5.0 at runtime

// After constant folding: Compile-time computation
const c = tensor.constant(&graph, 5.0);  // Computed during compilation!
```

**Trivial Reduction Removal** catches a subtle but important case. Sometimes, after other optimizations or due to broadcasting, we end up with reductions over dimensions of size 1. These are no-ops:
```zig
// If x has shape [10, 1, 20]
sum_reduce(x, axis=1)  // Summing over dimension of size 1
// Result has shape [10, 20] with same values - the reduction did nothing!
```

**Key Passes**:
- `algebraic.zig`: The mathematician who simplifies expressions
- `constant_folding.zig`: The eager evaluator who computes what it can at compile time
- `trivial_reduce.zig`: The efficiency expert who removes pointless operations

### Stage 3: Memory Planning

This is where Zing shows its efficiency muscles. Memory planning is about being smart with our most precious resource—memory bandwidth and capacity.

```
┌─────────────────────┐
│ Lifetime Analysis   │
│ - When created      │
│ - Last use          │
└─────────────────────┘
    ↓
┌─────────────────────┐
│ Buffer Allocation   │
│ - Reuse analysis    │
│ - Pack buffers      │
└─────────────────────┘
    ↓
┌─────────────────────┐
│ Memory Plan         │
│ - Offsets           │
│ - Total size        │
└─────────────────────┘
```

The memory planning pass is like a hotel manager trying to maximize room occupancy. It needs to know:
- When each guest (tensor) arrives (is created)
- When they leave (last use)
- How much space they need (tensor size)

Here's how it works on our example:

1. **Lifetime Analysis**: 
   ```
   Constant 2.0:    Created at start, used in Add operation
   Placeholder b:   Created at start, used in Add operation
   Add result:      Created by Add, used in ReLU operation
   ReLU result:     Created by ReLU, used as final output
   ```

2. **Reuse Opportunities**:
   - Once the Add operation completes, we don't need the constant anymore
   - The Add result buffer could potentially be reused for ReLU (if ReLU can work in-place)

3. **Memory Plan Creation**:
   ```zig
   Buffer 0: Size 4 bytes (constant)     [Offset: 0]
   Buffer 1: Size 800 bytes (placeholder) [Offset: 4]
   Buffer 2: Size 800 bytes (add/relu)    [Offset: 804]
   Total: 1604 bytes (without reuse would be 2404 bytes!)
   ```

But here's the clever part: the memory planning pass doesn't actually allocate memory. It creates a *plan* that backends can adapt:

- **CPU Backend**: Might use the plan as-is with simple malloc
- **CUDA Backend**: Might align buffers for coalesced access patterns
- **Metal Backend**: Might implement sophisticated buffer pooling with different memory tiers

This separation is key to Zing's design philosophy—the compiler figures out *what* needs to be done, and backends figure out *how* to do it best for their hardware.

The memory planning pass (`memory_planning.zig`) creates this backend-agnostic allocation plan, complete with lifetime information and reuse statistics.

### Stage 4: Mid-Level Optimizations

Now we get to the sophisticated optimizations—the ones that require understanding patterns and relationships in the computation graph:

```
┌─────────────────────┐
│ CSE                 │ (priority 70)
│ Eliminate duplicates│
└─────────────────────┘
    ↓
┌─────────────────────┐
│ Operation Fusion    │ (priority 50)
│ Merge kernels       │
└─────────────────────┘
    ↓
┌─────────────────────┐
│ Pattern Recognition │ (priority 40)
│ GEMM patterns, etc  │
└─────────────────────┘
```

**Common Subexpression Elimination (CSE)** is like having a perfect memory. It notices when we're computing the same thing twice:

```zig
// Before CSE:
const x_plus_y = x.add(y);
const also_x_plus_y = x.add(y);  // Same computation!
const result = x_plus_y.mul(also_x_plus_y);

// After CSE:
const x_plus_y = x.add(y);
const result = x_plus_y.mul(x_plus_y);  // Reuse the first computation
```

This isn't just about saving computation—it's about cache efficiency. If we've already computed something, it's likely still in cache.

**Operation Fusion** is where we start thinking like hardware. Modern accelerators are hungry for work—launching a kernel has overhead, so we want each kernel to do as much as possible:

```zig
// Before fusion: Three separate kernel launches
const a = x.add(y);      // Kernel 1: Read x,y, write a
const b = a.mul(z);      // Kernel 2: Read a,z, write b  
const c = b.relu();      // Kernel 3: Read b, write c

// After fusion: One fused kernel
const c = fused_add_mul_relu(x, y, z);  // One kernel does it all!
```

The fused kernel reads each input once and writes the output once, dramatically reducing memory bandwidth usage.

**Pattern Recognition** is our most sophisticated optimization. It looks for high-level patterns that can be replaced with optimized implementations:

```zig
// The compiler recognizes this pattern:
sum_reduce(mul(a, b), axis=1)

// As a matrix multiplication!
matmul(a, transpose(b))
```

Why does this matter? Because hardware vendors spend enormous effort optimizing operations like matrix multiplication. By recognizing these patterns, we can leverage years of optimization work.

The pattern system is also extensible—backends can register their own patterns. A GPU backend might recognize convolution patterns, while a DSP backend might recognize FFT patterns.

### Stage 5: Backend-Specific Compilation

This is where the rubber meets the road. All our optimization and planning culminates in generating code for specific hardware:

```
┌─────────────────────┐
│ Backend Selection   │
│ CPU/CUDA/Metal/etc  │
└─────────────────────┘
    ↓
┌─────────────────────┐
│ Pattern Matching    │
│ Backend patterns    │
└─────────────────────┘
    ↓
┌─────────────────────┐
│ Code Generation     │
│ Kernels/operators   │
└─────────────────────┘
```

Remember how we selected the backend at compile time? Now that choice pays off. The backend takes our optimized graph and transforms it into executable code.

**Backend Selection** was actually done way back when we called `compile()`, but its effects are felt here. Each backend brings:
- Its own capability set (Does it support dynamic shapes? Fusion? Which data types?)
- Hardware-specific patterns to recognize
- Optimized kernel implementations

**Backend Pattern Matching** is a second round of pattern recognition, but now with hardware-specific knowledge:

```zig
// The CPU backend might recognize:
add(mul(a, transpose(b)), c)  // GEMM with bias!

// And replace with:
cpu_gemm_bias(a, b, c)  // Optimized BLAS call

// While the CUDA backend might recognize:
elementwise_chain(x, [add, mul, relu])

// And generate:
cuda_fused_elementwise<<<blocks, threads>>>(x, ops)
```

**Code Generation** is where each backend shines. The CPU backend might:
- Generate calls to optimized BLAS libraries
- Implement custom SIMD kernels for elementwise operations
- Use OpenMP for parallelization

While a CUDA backend would:
- Generate PTX kernels
- Optimize for coalesced memory access
- Balance register usage vs occupancy

The beauty is that all this complexity is hidden. The same graph compiles efficiently to any backend.

### Stage 6: Late Optimizations

Finally, we clean up:

```
┌─────────────────────┐
│ Dead Code Elim.     │ (priority 20)
│ Remove unused       │
└─────────────────────┘
```

**Dead Code Elimination** is our janitor. After all the transformations, we might have nodes that are no longer needed:
- Operations whose results aren't used
- Branches that are never taken
- Intermediate values that got optimized away

This pass ensures we don't waste time computing things we don't need. It's particularly important after pattern recognition and fusion, which can make entire subgraphs obsolete.

## Backend Integration

Now let's talk about how backends plug into our compiler. This is where Zing's design really shines—we've created a clean contract that lets backend implementers focus on what they do best: generating fast code for their hardware.

### The Backend Contract

Think of the backend interface as a job description. If you want to be a Zing backend, here's what you need to provide:

```zig
pub const BackendInterface = struct {
    // Transform the optimized graph into executable code
    compileGraph: fn (*Graph, PassContext) CompiledProgram,
    
    // Tell us what you can do
    getCapabilities: fn () BackendCapabilities,
    
    // Give us your special patterns to look for
    getPatterns: fn () []const Pattern,
};
```

It's remarkably simple, but that's the point. The compiler has done the heavy lifting—shape inference, memory planning, optimization. The backend just needs to turn the optimized graph into code.

### Backend Capabilities: Advertising Your Strengths

Every backend is different. A CPU backend has different strengths than a GPU backend. Capabilities let backends tell the compiler what they're good at:

```zig
pub const BackendCapabilities = struct {
    supports_dynamic_shapes: bool,        // Can you handle runtime-determined shapes?
    supports_fusion: bool,                // Can you fuse operations?
    preferred_memory_layout: MemoryLayout,// Row-major? Column-major?
    supported_dtypes: []const DataType,   // f32, f16, i32, etc.
    
    // Hardware-specific hints
    cache_line_size: usize,              // For alignment decisions
    memory_coalescing_alignment: usize,   // For GPU memory access patterns
};
```

These capabilities flow back through the compilation pipeline. If a backend doesn't support fusion, the fusion pass can skip it. If it needs specific alignment, the memory planner can accommodate.

### Comptime Backend Selection: Zero-Cost Abstraction

Here's where Zig's comptime features make magic happen:

```zig
pub fn compile(comptime Backend: type, graph: *Graph) !CompiledProgram {
    comptime validateBackend(Backend);
    return executeUnifiedPipeline(Backend, graph);
}

// Usage:
const program = try compile(CpuBackend, &graph);  // No runtime dispatch!
```

The `comptime` keyword means Zig generates a completely specialized version of the compiler for each backend. There's no vtable, no dynamic dispatch, no overhead. It's as if we hand-wrote a custom compiler for each backend, but we only maintain one codebase.

### How Backends Participate in Compilation

Let's trace how a backend influences compilation:

1. **Pattern Registration**: Before optimization passes run, we ask the backend for its patterns:
   ```zig
   const patterns = Backend.getPatterns();
   // These get added to the pattern matching pass
   ```

2. **Capability-Aware Optimization**: Passes check capabilities before optimizing:
   ```zig
   if (ctx.backend_capabilities.?.supports_fusion) {
       // Attempt operation fusion
   }
   ```

3. **Final Compilation**: The backend transforms the graph into executable code:
   ```zig
   const program = try Backend.compileGraph(graph, ctx);
   ```

### Example: CPU Backend Integration

Here's a simplified view of how a CPU backend might look:

```zig
pub const CpuBackend = struct {
    pub fn getCapabilities() BackendCapabilities {
        return .{
            .supports_dynamic_shapes = true,
            .supports_fusion = true,
            .preferred_memory_layout = .row_major,
            .supported_dtypes = &.{.f32, .f64, .i32},
            .cache_line_size = 64,
            .memory_coalescing_alignment = 1, // No special requirements
        };
    }
    
    pub fn getPatterns() []const Pattern {
        return &cpu_patterns; // BLAS patterns, SIMD patterns, etc.
    }
    
    pub fn compileGraph(graph: *Graph, ctx: PassContext) !CompiledProgram {
        // Generate optimized CPU kernels
        // Link with BLAS libraries
        // Set up parallel execution strategy
    }
};
```

The beauty is that adding a new backend doesn't require touching the core compiler. Just implement the interface, and everything works.

## Memory Planning: The Art of Efficient Allocation

Memory planning deserves its own deep dive because it's so crucial to performance. The goal is simple: use as little memory as possible while maintaining peak performance. The implementation is sophisticated.

### The Lifetime Analysis Dance

Imagine you're organizing a conference with limited rooms. Each presentation needs a room for a specific time slot. The memory planner faces the same challenge with tensor buffers:

```
Node A (created at step 0, last used at step 2)
Node B (created at step 1, last used at step 3)
Node C (created at step 3, last used at step 4)

Timeline visualization:
Step 0: [A created      ]
Step 1: [A in use] [B created      ]
Step 2: [A last use] [B in use    ]
Step 3: [A free   ] [B last use] [C created]
Step 4: [A free   ] [B free   ] [C last use]

Memory reuse opportunity: C can use A's memory!
```

The lifetime analysis walks the graph in execution order, tracking:
- **Creation point**: When a tensor is computed
- **Last use**: When it's no longer needed
- **Size requirements**: How much memory it needs
- **Alignment constraints**: Special hardware requirements

### The Reuse Optimization Problem

This is actually a graph coloring problem in disguise. Two buffers can share the same memory if their lifetimes don't overlap. The memory planner uses a greedy algorithm:

```zig
for (each new buffer) {
    for (each existing allocation) {
        if (lifetimes don't overlap && size fits) {
            reuse this allocation
            break
        }
    }
    if (no reuse found) {
        create new allocation
    }
}
```

### Backend-Specific Memory Strategies

Here's where our design philosophy pays off. The memory planning pass provides the *information* (lifetimes, sizes, dependencies), but each backend implements its own *strategy*:

#### CPU Backend: Simplicity and Cache Awareness
```zig
// Simple allocation with cache-line alignment
pub fn allocateBuffer(size: usize) !*anyopaque {
    const aligned_size = align(size, CACHE_LINE_SIZE);
    return std.heap.c_allocator.alignedAlloc(u8, CACHE_LINE_SIZE, aligned_size);
}
```

The CPU backend keeps it simple—malloc with cache alignment. Why? Because CPU memory is relatively plentiful and the cost of complexity often outweighs the benefits.

#### CUDA Backend: Coalesced Access Optimization
```zig
// Align for coalesced memory access patterns
pub fn allocateBuffer(size: usize, access_pattern: AccessPattern) !CudaBuffer {
    const alignment = switch (access_pattern) {
        .sequential => 128,  // Warp-aligned
        .strided => 256,     // Extra alignment for strided access
        .random => 64,       // Minimum alignment
    };
    return cudaMalloc(align(size, alignment));
}
```

GPUs are sensitive to memory access patterns. Misaligned access can cut performance in half.

#### Metal Backend: Sophisticated Buffer Pooling
```zig
// Multi-tier buffer pool with different memory types
const BufferPool = struct {
    shared_pool: BufferSet,     // CPU+GPU visible
    private_pool: BufferSet,    // GPU-only, faster
    large_pool: BufferSet,      // For big allocations
    
    pub fn allocate(self: *BufferPool, size: usize, lifetime: Lifetime) !BufferId {
        // Try to find a reusable buffer
        if (size < SMALL_BUFFER_THRESHOLD) {
            if (self.shared_pool.findReusable(size, lifetime)) |buf| {
                return buf;
            }
        }
        
        // Allocate new buffer in appropriate pool
        const pool = self.selectPool(size, lifetime);
        return pool.allocateNew(size);
    }
};
```

Metal (like Luminal shows) benefits from sophisticated pooling because:
- Creating Metal buffers has high overhead
- Different memory types have different performance characteristics
- Reusing buffers improves GPU memory locality

### Memory Planning Statistics

The memory planner doesn't just allocate—it provides insights:

```
Memory Plan Statistics:
  Total buffers: 127
  Total memory: 45.3 MB
  Peak memory: 12.7 MB (28% of total)
  Memory saved through reuse: 32.6 MB (72%)
  Buffer reuses: 89 (70% reuse rate)
  Largest buffer: 4.2 MB
  Average lifetime: 4.3 operations
```

These statistics help identify optimization opportunities. Low reuse rate? Maybe operations could be reordered. High peak memory? Perhaps some computations could be staged differently.

## Execution Integration: Where Computation Happens

The compiler has done its job—the graph is optimized, memory is planned, kernels are generated. Now it's time to actually run the computation. This is where the `execution.zig` module takes over.

### The Journey from Compilation to Results

```
CompiledProgram
    ↓
┌─────────────────────┐
│ Executor Creation   │
│ - Load program      │
│ - Setup storage     │
└─────────────────────┘
    ↓
┌─────────────────────┐
│ Input Binding       │
│ - Set placeholders  │
│ - Resolve symbols   │
└─────────────────────┘
    ↓
┌─────────────────────┐
│ Execution           │
│ - Run kernels       │
│ - Manage memory     │
└─────────────────────┘
    ↓
┌─────────────────────┐
│ Output Retrieval    │
│ - Get results       │
│ - Handle views      │
└─────────────────────┘
```

### The Executor: Your Runtime Orchestrator

The `Executor` is like a symphony conductor. It doesn't play any instruments (run kernels) itself, but it ensures everyone plays at the right time with the right resources:

```zig
pub const Executor = struct {
    program: CompiledProgram,
    data_storage: DataStorage,
    symbol_resolver: SymbolResolver,
    stats: ExecutionStats,
    
    pub fn init(program: CompiledProgram, allocator: Allocator) !Executor {
        // Set up data storage based on memory plan
        var data_storage = try DataStorage.init(
            allocator,
            program.memory_plan,
            program.backend_config
        );
        
        // Prepare for symbol resolution
        var symbol_resolver = SymbolResolver.init();
        
        return Executor{
            .program = program,
            .data_storage = data_storage,
            .symbol_resolver = symbol_resolver,
            .stats = ExecutionStats{},
        };
    }
};
```

### Storage Management: The Two-Tier System

Zing separates storage into two distinct systems, each with its own purpose:

#### DataStorage: Ephemeral Execution Memory
```zig
// From storage.zig
pub const DataStorage = struct {
    buffers: BufferManager,      // Execution buffers
    backend_storage: ?*anyopaque,// Backend-specific storage
    
    // Manages memory during execution
    pub fn getBuffer(self: *DataStorage, key: BufferKey) ![]u8 {
        return self.buffers.get(key);
    }
};
```

This is your working memory during execution. It's created fresh for each run, optimized based on the memory plan, and freed when execution completes.

#### ParameterStore: Persistent Model State
```zig
// Also from storage.zig
pub const ParameterStore = struct {
    parameters: std.StringHashMap(OwnedTensor),
    allocator: Allocator,
    
    // Persists across executions
    pub fn getParameter(self: *ParameterStore, name: []const u8) ?TensorView {
        if (self.parameters.get(name)) |owned| {
            return owned.view();
        }
        return null;
    }
};
```

This holds your model weights, persisting across multiple executions. It's separate because:
- Model parameters don't change during inference
- They can be memory-mapped from disk
- They might live in special memory regions (like GPU persistent memory)

### Input Binding and Symbol Resolution

Before execution can begin, we need to provide concrete values for placeholders and resolve any symbolic dimensions:

```zig
// User provides input
const input_data = try allocator.alloc(f32, 784);
// ... fill input_data ...

// Bind to placeholder
try executor.bindInput("input_image", input_data);

// If we have symbolic dimensions, resolve them
try executor.setSymbol("batch_size", 32);
```

The symbol resolver is clever—it propagates known values through the graph:
- If batch_size is 32 and we have shape [batch_size, 10], it becomes [32, 10]
- This allows dynamic shapes while maintaining efficiency

### The Execution Loop

This is where the magic happens. The executor runs through the execution plan:

```zig
pub fn execute(self: *Executor) !void {
    // Reset statistics
    self.stats.reset();
    
    for (self.program.execution_steps) |step| {
        const start_time = std.time.nanoTimestamp();
        
        // Get input buffers
        var inputs = try self.gatherInputs(step.inputs);
        
        // Get output buffer
        var output = try self.data_storage.getBuffer(step.output);
        
        // Execute the kernel
        try step.kernel_fn(inputs, output, step.kernel_params);
        
        // Update statistics
        const elapsed = std.time.nanoTimestamp() - start_time;
        self.stats.addKernelTime(step.name, elapsed);
    }
}
```

Notice how clean this is. All the complexity—memory management, kernel selection, optimization—was handled at compile time. The runtime just executes.

### Output Retrieval and View Handling

After execution, users want their results. But there's a subtlety: the output might be a view into a larger buffer:

```zig
pub fn getOutput(self: *Executor, name: []const u8) !TensorView {
    const output_spec = self.program.outputs.get(name) orelse return error.OutputNotFound;
    
    // Get the buffer
    const buffer = try self.data_storage.getBuffer(output_spec.buffer_key);
    
    // Apply any view transformations (stride, offset, shape)
    return TensorView{
        .data = buffer[output_spec.offset..],
        .shape = output_spec.shape,
        .strides = output_spec.strides,
        .dtype = output_spec.dtype,
    };
}
```

This zero-copy approach means getting results is extremely fast—we're just returning a different view of existing memory.

### Execution Statistics and Profiling

The executor collects detailed statistics:

```
Execution Statistics:
  Total time: 45.3ms
  Kernel breakdown:
    - matmul_1: 22.1ms (48.8%)
    - relu_batch: 8.3ms (18.3%)
    - softmax: 14.9ms (32.9%)
  Memory transfers: 2.1ms
  Peak memory usage: 12.7MB
  Cache hit rate: 94.2%
```

This profiling information feeds back into optimization—identifying bottlenecks and opportunities for improvement.

### The Storage Architecture

The `storage/` directory contains specialized components:

- **`comptime_layout.zig`**: Compile-time memory layout optimization
  ```zig
  // Computes optimal memory layout at compile time
  pub fn computeOptimalLayout(comptime T: type, comptime options: LayoutOptions) type {
      // ... sophisticated layout computation ...
  }
  ```

The `execution/` directory has:

- **`buffer_pool.zig`**: Runtime buffer pooling for allocation reuse
  ```zig
  // Reduces allocation overhead during execution
  pub const BufferPool = struct {
      free_lists: [NUM_SIZE_CLASSES]FreeList,
      
      pub fn acquire(self: *BufferPool, size: usize) ![]u8 {
          const size_class = computeSizeClass(size);
          return self.free_lists[size_class].pop() orelse try self.allocateNew(size);
      }
  };
  ```

This organization separates concerns: core functionality in the main files, specialized optimizations in subdirectories.

## Adding New Optimizations

Want to add your own optimization? Zing makes it straightforward. Let's walk through adding a hypothetical "strength reduction" pass that converts expensive operations to cheaper ones.

### Step 1: Define the Pass

Create a new file in `passes/`. The template system does the heavy lifting:

```zig
// strength_reduction.zig
const std = @import("std");
const pass_templates = @import("../pass_templates.zig");
const Node = @import("graph").Node;

pub const StrengthReductionPass = pass_templates.createTraversalPass(.{
    .name = "strength_reduction",
    .priority = 75, // Runs after algebraic simplification (90) but before CSE (70)
    .visitor = reduceStrength,
});

fn reduceStrength(node_id: NodeId, node: *Node, ctx: *PassContext) !bool {
    // Example: Convert division by power of 2 to bit shift
    if (node.spec == .compute and node.spec.compute.op == .div) {
        // Check if second input is a power of 2 constant
        const divisor_node = ctx.graph.getNode(node.inputs[1]) orelse return false;
        
        if (divisor_node.spec == .data and divisor_node.spec.data == .constant) {
            const value = divisor_node.spec.data.constant.value;
            
            // Is it a power of 2?
            if (isPowerOfTwo(value)) {
                // Replace with bit shift
                const shift_amount = @ctz(value); // Count trailing zeros
                const shift_node = try createShiftNode(ctx, node.inputs[0], shift_amount);
                
                var rewriter = transforms.GraphRewriter.init(ctx.graph, ctx.allocator);
                try rewriter.replaceNode(node_id, shift_node);
                try rewriter.commit();
                
                return true; // We modified the graph
            }
        }
    }
    
    return false; // No modification
}
```

### Step 2: Register in Pipeline

Add your pass to the compilation pipeline:

```zig
// In unified_pipeline.zig
const StrengthReductionPass = @import("passes/strength_reduction.zig").StrengthReductionPass;

fn executeCorePasses(ctx: *PassContext) !void {
    // ... existing passes ...
    
    // Add your pass in the appropriate position
    try StrengthReductionPass.run(ctx);
    
    // ... more passes ...
}
```

The priority system ensures passes run in the right order. Your strength reduction pass runs:
- After algebraic simplification (might create opportunities)
- Before CSE (so we can eliminate duplicate shifts)

### Step 3: Test Your Pass

Testing is crucial. Write comprehensive tests:

```zig
test "strength reduction: division by power of 2" {
    var graph = try Graph.init(testing.allocator);
    defer graph.deinit();
    
    // Build test graph: x / 8
    const x = try graph.addPlaceholder(.f32);
    const eight = try graph.addConstant(8.0);
    const div = try graph.addNode(.div, &.{x, eight}, .f32);
    
    // Run pass
    var ctx = PassContext.init(&graph, testing.allocator);
    try StrengthReductionPass.run(&ctx);
    
    // Verify the division was replaced with a shift
    const new_node = graph.getNode(div);
    try testing.expect(new_node == null or new_node.spec.compute.op == .shift_right);
}

test "strength reduction: preserves non-power-of-2 divisions" {
    var graph = try Graph.init(testing.allocator);
    defer graph.deinit();
    
    // Build test graph: x / 7 (not a power of 2)
    const x = try graph.addPlaceholder(.f32);
    const seven = try graph.addConstant(7.0);
    const div = try graph.addNode(.div, &.{x, seven}, .f32);
    
    // Run pass
    var ctx = PassContext.init(&graph, testing.allocator);
    try StrengthReductionPass.run(&ctx);
    
    // Verify division was NOT replaced
    const node = graph.getNode(div).?;
    try testing.expectEqual(ComputeOp.div, node.spec.compute.op);
}
```

### Advanced: Pattern-Based Optimization

For more complex patterns, use the pattern system:

```zig
// Define patterns declaratively
pub const strength_reduction_patterns = [_]Pattern{
    // x * 2^n → x << n
    Pattern{
        .name = "mul_to_shift",
        .match = .{ .op = .{ 
            .op = .mul, 
            .inputs = &.{
                .{ .var = "x" },
                .{ .constant = .{ .constraint = isPowerOfTwo } }
            }
        }},
        .replace = .{ .custom = .{ 
            .name = "shift_left", 
            .inputs = &.{ "x", "@shift_amount" } 
        }},
    },
    
    // x / 2^n → x >> n
    Pattern{
        .name = "div_to_shift",
        .match = .{ .op = .{ 
            .op = .div, 
            .inputs = &.{
                .{ .var = "x" },
                .{ .constant = .{ .constraint = isPowerOfTwo } }
            }
        }},
        .replace = .{ .custom = .{ 
            .name = "shift_right", 
            .inputs = &.{ "x", "@shift_amount" } 
        }},
    },
};

// Use the pattern system
pub const StrengthReductionPass = pass_templates.createPatternPass(.{
    .name = "strength_reduction",
    .priority = 75,
    .patterns = &strength_reduction_patterns,
});
```

### Tips for Writing Good Passes

1. **Be Conservative**: Only optimize when you're certain it's safe
2. **Preserve Semantics**: Your optimization must not change the program's meaning
3. **Think About Interactions**: How does your pass interact with others?
4. **Profile Impact**: Measure if your optimization actually improves performance
5. **Document Thoroughly**: Explain what patterns you're looking for and why

## Debugging the Compiler

Even the best compilers need debugging. Zing provides powerful tools to understand what's happening inside the black box.

### Enable Debug Logging

First, build with debug logging enabled:

```bash
zig build -Denable_debug_logs=true
```

This activates detailed logging throughout the compilation pipeline. You'll see exactly what each pass is doing.

### Graph Visualization: See Your Computation

One of the most powerful debugging tools is visualizing the computation graph. Add this to any pass:

```zig
// In your pass or debugging code
if (@import("build_options").enable_debug_logs) {
    try graph.exportDot("graph_after_my_pass.dot");
}
```

Then visualize with Graphviz:
```bash
dot -Tpng graph_after_my_pass.dot -o graph.png
open graph.png  # On macOS
```

You'll see something like:
```
┌─────────────┐
│ Constant(2) │
└──────┬──────┘
       │
       v
   ┌───────┐     ┌──────────────┐
   │  Add  │←────│Placeholder(b)│
   └───┬───┘     └──────────────┘
       │
       v
   ┌──────┐
   │ ReLU │
   └──────┘
```

This visual representation helps you:
- Verify the graph structure is correct
- See which optimizations have been applied
- Identify unexpected transformations
- Debug shape propagation issues

### Pass Execution Tracing

With debug logging enabled, you get a detailed trace of pass execution:

```
[PASS] shape_inference starting (priority 100)
  - Processing node_0 (constant): shape=[]
  - Processing node_1 (placeholder): shape=[10, 20]
  - Processing node_2 (add): broadcasting [] to [10, 20]
  - Processing node_3 (relu): shape=[10, 20]
[PASS] shape_inference completed in 0.23ms

[PASS] algebraic_simplification starting (priority 90)
  - Checking node_5: x + 0
  - Simplified to: x
  - Graph modified, clearing cached topology
[PASS] algebraic_simplification completed in 0.45ms (1 modification)

[PASS] constant_folding starting (priority 85)
  - Checking node_8: constant(2) + constant(3)
  - Folded to: constant(5)
[PASS] constant_folding completed in 0.31ms (1 fold)
```

This trace tells you:
- Which passes ran and in what order
- What each pass examined
- What changes were made
- Performance of each pass

### Memory Planning Deep Dive

The memory planner provides detailed statistics:

```
=== Memory Planning Analysis ===

Buffer Lifetimes:
  Buffer 0 (constant, 4B): created=0, last_use=2, lifetime=2
  Buffer 1 (placeholder, 800B): created=0, last_use=2, lifetime=2
  Buffer 2 (add_result, 800B): created=2, last_use=3, lifetime=1
  Buffer 3 (relu_result, 800B): created=3, last_use=4, lifetime=1

Reuse Analysis:
  Buffer 2 can reuse Buffer 0's allocation (non-overlapping lifetimes)
  Buffer 3 can reuse Buffer 1's allocation (non-overlapping lifetimes)

Final Memory Plan:
  Allocation 0: 800B (used by buffers 1, 3)
  Allocation 1: 800B (used by buffers 0, 2)
  Total: 1600B (would be 2404B without reuse)

Memory Plan Statistics:
  Total buffers: 4
  Unique allocations: 2
  Peak memory: 1600B
  Memory saved: 804B (33.5%)
  Buffer reuses: 2 (50% reuse rate)
```

### Debugging Specific Issues

#### Shape Mismatch Errors

```zig
// Add shape assertions in your tests
try testing.expectEqualSlices(i64, expected_shape, actual_shape);

// Or use the shape debugging helper
try graph.debugPrintShapes();
// Outputs:
// Node 0 (constant): shape=[], dtype=f32
// Node 1 (placeholder): shape=[?, 784], dtype=f32 (? = symbolic)
// Node 2 (matmul): shape=[?, 10], dtype=f32
```

#### Memory Corruption

```zig
// Enable memory safety checks
const debug_storage = try DataStorage.initWithDebug(allocator, plan, .{
    .check_bounds = true,
    .poison_freed = true,
    .track_access = true,
});

// This will catch:
// - Buffer overruns
// - Use after free
// - Uninitialized reads
```

#### Performance Bottlenecks

```zig
// Enable per-pass profiling
var ctx = PassContext.initWithProfiling(&graph, allocator);

// After compilation:
ctx.printProfilingReport();
// Outputs:
// Pass Profiling Report:
//   shape_inference: 0.23ms (2.1%)
//   pattern_matching: 8.45ms (76.3%) ← Bottleneck!
//   memory_planning: 2.39ms (21.6%)
```

### Interactive Debugging

For complex issues, use Zig's built-in debugging:

```zig
// Add breakpoint in your pass
fn myPassVisitor(node: *Node, ctx: *PassContext) !bool {
    if (node.id == suspicious_node_id) {
        @breakpoint();  // Debugger will stop here
    }
    // ... rest of logic
}

// Or use debug prints with context
std.log.debug("Processing node {} with inputs {:any}", .{node.id, node.inputs});
```

### Common Debugging Patterns

1. **Binary Search for Bad Passes**: Disable half the passes to isolate which one causes issues
2. **Graph Diffing**: Export graphs before/after passes and diff them
3. **Minimal Reproduction**: Reduce your graph to the smallest example that shows the bug
4. **Assertion Injection**: Add temporary assertions to verify invariants

## Best Practices

After working with the Zing compiler, these practices have proven essential:

### 1. Pass Priority: Order Matters

The priority system isn't arbitrary. Here's the canonical ordering:

```
100-95: Shape Analysis (must run first)
90-80:  Early Algebraic Optimizations
70-60:  Memory and Structure Analysis
50-40:  Pattern Matching and Fusion
30-20:  Cleanup (must run last)
```

Why? Each phase builds on the previous:
- You can't optimize without knowing shapes
- Algebraic simplification creates fusion opportunities
- Pattern matching needs simplified graphs
- Cleanup removes debris from other passes

### 2. Safe Graph Modifications

Never modify the graph directly. Always use the `GraphRewriter`:

```zig
// ❌ WRONG - Direct modification
node.inputs[0] = new_input;  // Breaks invariants!

// ✅ CORRECT - Using GraphRewriter
var rewriter = try GraphRewriter.init(graph, allocator);
try rewriter.rewireInput(node_id, 0, new_input);
try rewriter.commit();  // Validates changes
```

The rewriter ensures:
- Topological order is maintained
- No dangling references
- Metadata stays consistent
- Changes can be rolled back

### 3. Memory Management: The Zig Way

Follow Zig's ownership principles religiously:

```zig
// ❌ WRONG - Hidden allocation
pub fn processNode(node: *Node) !void {
    const temp = try allocator.alloc(f32, 1000); // Where's the allocator from?
}

// ✅ CORRECT - Explicit allocation
pub fn processNode(node: *Node, allocator: Allocator) !void {
    const temp = try allocator.alloc(f32, 1000);
    defer allocator.free(temp);  // Clear cleanup
}
```

For passes, use arena allocation for temporary data:
```zig
var arena = std.heap.ArenaAllocator.init(allocator);
defer arena.deinit();  // Frees everything at once

// All temporary allocations use arena
const temp_data = try arena.allocator().alloc(u8, size);
// No individual frees needed!
```

### 4. Error Messages: Context is King

When things go wrong, future you needs context:

```zig
// ❌ WRONG - Cryptic error
if (shape.dims.len != 2) return error.InvalidShape;

// ✅ CORRECT - Helpful error with context
if (shape.dims.len != 2) {
    std.log.err(
        "MatMul requires 2D inputs, got {}D tensor with shape {:any} at node {}", 
        .{ shape.dims.len, shape.dims, node_id }
    );
    return error.InvalidMatMulShape;
}
```

### 5. Testing: The Three-Layer Approach

Every pass needs three levels of testing:

#### Layer 1: Unit Tests (in the same file)
```zig
test "algebraic simplification: x + 0 = x" {
    // Test specific optimization
}
```

#### Layer 2: Integration Tests (pass interaction)
```zig
test "simplification enables constant folding" {
    // Test that your pass works with others
}
```

#### Layer 3: End-to-End Tests
```zig
test "full compilation with all optimizations" {
    // Test real-world scenarios
}
```

### 6. Documentation: Write for Your Future Self

Every pass should document:
- **What** it optimizes (with examples)
- **When** it should run (priority reasoning)
- **Why** the optimization is valid
- **How** it preserves correctness

```zig
/// Eliminates redundant type conversions
/// 
/// Optimizes patterns like cast(cast(x, T1), T2) → cast(x, T2)
/// when the conversion is safe.
/// 
/// Priority: 75 (after type inference, before fusion)
/// Safety: Only combines casts that preserve all values
pub const CastEliminationPass = ...
```

### 7. Performance: Measure, Don't Guess

Before optimizing:
```zig
const start = std.time.nanoTimestamp();
defer {
    const elapsed = std.time.nanoTimestamp() - start;
    std.log.debug("Pass took {} ns", .{elapsed});
}
```

Profile-guided optimization beats intuition every time.

### 8. Debugging: Leave Breadcrumbs

Add optional verbose logging:
```zig
const verbose = @import("build_options").verbose_passes;

if (verbose) {
    std.log.debug("Considering fusion of {} and {}", .{node1.id, node2.id});
}
```

Future you will thank present you.

## Conclusion

The Zing compilation pipeline embodies a philosophy: **separation of concerns with zero-cost abstractions**. By clearly separating what needs to be done (optimization) from how it's done (backend implementation), we achieve both portability and performance.

The journey from a simple `a.add(b).relu()` to optimized machine code involves:
- Shape inference to understand the computation
- Algebraic optimization to simplify
- Memory planning to minimize allocation
- Pattern matching to leverage hardware
- Backend compilation to generate fast code
- Execution orchestration to run efficiently

Each component has a clear responsibility. Each optimization has a specific purpose. Each abstraction has zero runtime cost.

This is the Zing way: powerful abstractions that compile away, leaving only fast, efficient code.

Welcome to the Zing compiler. May your tensors be fast and your memory usage low! 🚀