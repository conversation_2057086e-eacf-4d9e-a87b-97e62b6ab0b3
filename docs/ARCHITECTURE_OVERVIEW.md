# Zing Architecture: Systematic Component Specification

---

## Executive Summary

Zing is a tensor computation framework inspired by Luminal's elegant design but implemented following idiomatic Zig patterns. This architecture eliminates the over-engineered "engine" pattern, removes unnecessary abstractions, and creates a simple, efficient system where the Graph is the central organizing structure.

### Key Architectural Decisions

1. **Single Graph Structure**: Everything flows through the Graph
2. **Direct Component Integration**: No abstraction layers between components
3. **Arena-Based Memory Management**: Efficient bulk allocation/deallocation
4. **Primitive-Only Graph Layer**: ~12 operations that compose into everything
5. **Pure View Operations**: Shape changes are metadata-only
6. **Separated Data Storage**: Clean separation between graph metadata and tensor data

---

## Core Philosophy

### Learning from Luminal's Architecture

Based on analysis of Luminal's actual implementation, our architecture adopts these proven patterns:

#### 1. **Central Graph as Single Source of Truth**
- **Graph owns everything**: All tensors, metadata, execution state live in one place
- **Simple lifecycle**: Create → Build → Compile → Execute
- **Unified memory**: Centralized tensor storage with reference counting
- **Direct integration**: No abstraction layers between components

#### 2. **Immediate Primitive Decomposition**
- **Decompose at tensor level**: High-level operations (matmul, softmax) immediately create primitive graph nodes
- **No compilation-time decomposition**: Unlike other frameworks, decomposition happens when operations are called
- **~12 primitive operations**: Minimal set that composes into everything else
- **Simple graph structure**: Only primitive operations create nodes

#### 3. **Pure View Operations**
- **Zero-copy shape operations**: reshape, transpose, slice are pure metadata (ShapeTracker)
- **No graph nodes for views**: Shape operations never create computation nodes
- **Metadata-only transformations**: View operations modify shape tracking, not computation graph

#### 4. **Sophisticated Symbolic System**
- **Dynamic dimension support**: Symbolic variables ('a', 'b') for unknown dimensions
- **Runtime resolution**: dyn_map resolves symbols to concrete values during execution
- **Advanced simplification**: Algebraic optimization of symbolic expressions

### Zig's Contribution

We follow Luminal's proven design while leveraging Zig's strengths:
- **Explicit allocators**: No hidden memory allocation, following Zig principles
- **Unmanaged containers**: Full control over memory lifecycle  
- **Arena allocation**: Efficient bulk memory management for graph construction
- **Zero-cost abstractions**: Performance without complexity
- **Compile-time safety**: Catch errors during compilation

---

## System Overview

### High-Level Architecture

```
┌─────────────────────────────────────────────────────────────┐
│                      Tensor API Layer                        │
│  • User-facing operations (add, matmul, reshape)            │
│  • Immediate decomposition to primitives                     │
│  • Returns lightweight Tensor handles                        │
└─────────────────────────────────────────────────────────────┘
                              │
                              ▼
┌─────────────────────────────────────────────────────────────┐
│                    Graph Core (Build Time)                   │
│  ┌─────────────┐  ┌──────────────┐  ┌─────────────────┐   │
│  │   Nodes     │  │    Edges     │  │   ShapeTracker  │   │
│  │ ~12 prims   │  │  Producer/   │  │  Dims+Strides   │   │
│  │ only!       │  │  Consumer    │  │  View metadata  │   │
│  └─────────────┘  └──────────────┘  └─────────────────┘   │
│                                                             │
│  ┌─────────────┐  ┌──────────────┐  ┌─────────────────┐   │
│  │ SymbolicPool│  │   Compiler   │  │   dyn_map       │   │
│  │ Expr intern │  │ Pattern match│  │ Symbol→Value    │   │
│  │ Algebraic   │  │ CSE/Fusion   │  │ Runtime dims    │   │
│  └─────────────┘  └──────────────┘  └─────────────────┘   │
└─────────────────────────────────────────────────────────────┘
                              │
                              ▼ compile()
┌─────────────────────────────────────────────────────────────┐
│                 CompiledGraph (Execution Time)               │
│  ┌─────────────┐  ┌──────────────┐  ┌─────────────────┐   │
│  │ Execution   │  │ Memory Plan  │  │  DataStorage    │   │
│  │ Order       │  │ Buffer alloc │  │ Tensor buffers  │   │
│  │ Topo-sorted │  │ Reuse strategy│  │ Per-device     │   │
│  └─────────────┘  └──────────────┘  └─────────────────┘   │
└─────────────────────────────────────────────────────────────┘
                              │
                              ▼
┌─────────────────────────────────────────────────────────────┐
│                    Execution Backend                         │
│  • CPU: SIMD vectorization, multi-threading                 │
│  • GPU: Kernel dispatch (CUDA/Metal/Vulkan)                 │
│  • Implements only ~12 primitive operations                  │
└─────────────────────────────────────────────────────────────┘
```

### Data Flow Example: Matrix Multiplication

```zig
// User code
const c = try a.matmul(b);  // a: [M,K], b: [K,N] → c: [M,N]

// 1. Tensor layer decomposes immediately
pub fn matmul(a: Tensor, b: Tensor) !Tensor {
    // Transpose for broadcasting
    const b_t = try b.transpose(&.{1, 0});      // [N,K]
    
    // Expand for element-wise multiply (where a is [M,K] and b is [K,N])
    const M = a.shape[0]; // Matrix A rows
    const K = a.shape[1]; // Matrix A cols / Matrix B rows
    const N = b.shape[1]; // Matrix B cols
    const a_exp = try a.expand(&.{M, 1, K});    // [M,1,K]
    const b_exp = try b_t.expand(&.{1, N, K});  // [1,N,K]
    
    // Element-wise multiply (broadcasts to [M,N,K])
    const mul = try a_exp.multiply(b_exp);
    
    // Sum reduce along K dimension
    return mul.reduce_sum(&.{2});               // [M,N]
}

// 2. Graph only sees primitives
// - transpose: Creates new ShapeTracker (no node!)
// - expand: Creates new ShapeTracker (no node!)
// - multiply: Creates primitive node
// - reduce_sum: Creates primitive node

// 3. Shape tracking example
// a.shape_tracker = {dims: [M,K], strides: [K,1]}
// b_t.shape_tracker = {dims: [N,K], strides: [1,N]}  // transposed!
// No data movement, just metadata changes
```

---

## Component Specifications



### **The Autograd Compiler Pass: Automatic Differentiation**

Based on Luminal's proven implementation, the Autograd pass is a **critical first-phase compiler pass** that extends the forward computation graph by adding gradient computation nodes. Following Luminal's two-compilation approach, this happens in a **separate compilation phase** before backend optimization.

#### **Two-Phase Compilation Strategy (Following Luminal)**

```zig
// PHASE 1: Autograd compilation - Adds gradient nodes to graph
pub fn setupTraining(graph: *Graph, model_params: []const NodeId, loss: NodeId) ![]GradientInfo {
    const autograd_pass = AutogradPass{
        .params = model_params,
        .loss_node = loss,
        .allocator = allocator,
    };
    
    // FIRST COMPILATION: Autograd only
    const gradients = try graph.compile(autograd_pass, .{});
    
    // After this: graph contains BOTH forward and backward nodes
    // But NOT yet optimized for execution
    
    return gradients;
}

// PHASE 2: Backend optimization compilation - Optimizes complete graph
pub fn optimizeForExecution(
    graph: *Graph, 
    tensors: []TensorHandle,
    backend: BackendType
) !void {
    const backend_passes = switch (backend) {
        .cpu => createCpuOptimizationPasses(),
        .cuda => createCudaOptimizationPasses(),
        .metal => createMetalOptimizationPasses(),
    };
    
    // SECOND COMPILATION: Backend optimization of forward+backward graph
    try graph.compile(backend_passes, tensors);
    
    // After this: graph is fully optimized and ready for execution
}
```

#### **How Autograd Works in Zing's Compilation Pipeline**

```zig
pub const AutogradPass = struct {
    params: []const NodeId,     // Parameters to compute gradients for
    loss_node: NodeId,          // Scalar loss node
    allocator: Allocator,
    
    // Autograd implements the Compiler interface for first-phase compilation
    pub const GradientInfo = struct {
        grad_node: NodeId,          // Node computing the gradient
        shape_tracker: TrackerId,   // Shape of the gradient
        accumulation_count: u32 = 1, // Number of gradient contributions accumulated
    };
    
    // Return type: Gradient information for each parameter
    pub const Output = []GradientInfo;
    
    pub fn compile(self: AutogradPass, graph: *Graph, _: anytype) !Output {
        // 1. BUILD VALID NODE SET: Only nodes that matter for gradients
        const forward_set = try buildDfsSet(self.params, graph, .outgoing);
        const backward_set = try buildDfsSet(&.{self.loss_node}, graph, .incoming);
        const valid_set = setIntersection(forward_set, backward_set);
        
        // 2. INITIALIZE GRADIENT MAP: Track gradient nodes for each forward node
        var grads = std.AutoHashMapUnmanaged(NodeId, GradientInfo){};
        defer grads.deinit(self.allocator);
        
        // Start with loss gradient = 1.0 (scalar)
        const ones_node = try graph.createConstant(1.0, .f32);
        try graph.setNodeMetadata(ones_node, .{ .name = "loss_grad_ones" });
        
        try grads.put(self.allocator, self.loss_node, GradientInfo{
            .grad_node = ones_node,
            .shape_tracker = try graph.createShapeTracker(&.{}), // Scalar
        });
        
        // 3. REVERSE-MODE AUTODIFF: Walk backwards through computation graph
        const topo_order = try graph.topologicalSort();
        defer self.allocator.free(topo_order);
        
        // Process nodes in reverse topological order
        var i = topo_order.len;
        while (i > 0) {
            i -= 1;
            const fwd_node = topo_order[i];
            
            if (!valid_set.contains(fwd_node)) continue;
            if (!grads.contains(fwd_node)) continue; // No gradient computed yet
            
            // GET GRADIENT FOR THIS NODE
            const node_grad = grads.get(fwd_node).?;
            const node = graph.getNode(fwd_node).?;
            
            // DIFFERENTIATE: Add gradient nodes to the graph
            try differentiateNode(graph, fwd_node, node.op_type, node_grad, &grads);
        }
        
        // 4. EXTRACT GRADIENTS FOR REQUESTED PARAMETERS
        var param_gradients = try self.allocator.alloc(GradientInfo, self.params.len);
        for (self.params, 0..) |param_id, i| {
            if (grads.get(param_id)) |grad_info| {
                param_gradients[i] = grad_info;
            } else {
                // Parameter not used in loss computation - create zero gradient
                const zero_grad = try graph.createConstant(0.0, .f32);
                const param_shape = graph.getNodeShapeTracker(param_id);
                param_gradients[i] = GradientInfo{
                    .grad_node = zero_grad,
                    .shape_tracker = param_shape,
                };
            }
        }
        
        return param_gradients;
    }
};

// Usage following Luminal's pattern:
pub fn createTrainingWorkflow(
    graph: *Graph, 
    model_params: []const NodeId, 
    loss_node: NodeId,
    allocator: Allocator
) !void {
    // 1. FIRST COMPILATION PHASE: Add gradient computation nodes
    const autograd_compiler = AutogradPass{
        .params = model_params,
        .loss_node = loss_node,
        .allocator = allocator,
    };
    
    const gradients = try graph.compile(autograd_compiler, .{});
    defer allocator.free(gradients);
    
    // 2. CREATE OPTIMIZER NODES: Add weight update computation
    const optimizer = SGDOptimizer{ .learning_rate = 0.001 };
    const new_weights = try optimizer.addToGraph(graph, model_params, gradients);
    
    // 3. MARK IMPORTANT TENSORS: Prevent deletion during optimization
    try graph.keepTensors(model_params);
    try graph.keepTensors(new_weights);
    
    // 4. SECOND COMPILATION PHASE: Optimize complete forward+backward graph
    const backend_compiler = createBackendCompiler(.cpu); // or .cuda, .metal
    try graph.compile(backend_compiler, .{
        .inputs = &.{input_tensor, target_tensor},
        .outputs = &.{loss_node},
        .weights = model_params,
        .new_weights = new_weights,
    });
    
    // Now graph is ready for training execution:
    // - Contains optimized forward pass
    // - Contains optimized gradient computation  
    // - Contains optimized weight updates
    // - All fused and optimized for target backend
}
```

#### **Key Insights from Luminal's Two-Phase Approach**

1. **Phase Separation**: Autograd compilation and backend optimization are **separate concerns**
2. **Graph Evolution**: The graph evolves from forward-only → forward+backward → optimized for execution
3. **Unified Optimization**: Backend optimizers see the **complete computational workflow**
4. **Cross-Boundary Fusion**: Can optimize across forward/backward boundaries (e.g., fuse gradient computation with forward ops)
5. **Memory Efficiency**: Single unified memory plan for entire training step

#### **Graph Structure Analysis: Do We Have What Autograd Needs?**

✅ **Yes! Our graph structure already contains all the information needed for autograd:**

```zig
// FROM EXISTING GRAPH STRUCTURE:

pub const Node = struct {
    id: NodeId,                    // ✅ NEEDED: Unique identifier for gradient tracking
    op_type: OpType,              // ✅ NEEDED: Differentiation rules based on operation type
    inputs: []const NodeId,       // ✅ NEEDED: Know which nodes to backpropagate to
    shape_tracker: TrackerId,     // ✅ NEEDED: Gradient shapes must match forward shapes
    metadata: ?NodeMetadata = null, // ✅ USEFUL: Can mark gradient nodes, debugging
};

pub const Edge = struct {
    source: NodeId,               // ✅ NEEDED: Which node produces the tensor
    target: NodeId,               // ✅ NEEDED: Which node consumes the tensor  
    dependency: Dependency,       // ✅ NEEDED: Input/output slot information
};

pub const Dependency = union(enum) {
    data: struct {
        input_order: u8,          // ✅ CRITICAL: Which input slot for gradient flow
        output_order: u8,         // ✅ CRITICAL: Which output slot for gradient flow
        shape: TrackerId,         // ✅ NEEDED: Shape information for gradient computation
    },
    schedule: void,               // ✅ USEFUL: For gradient computation ordering
};

// RUNTIME GRAPH TRAVERSAL CAPABILITIES:
// ✅ Forward traversal: inputs[] → follow dependencies forward  
// ✅ Backward traversal: edges[] → trace dependencies backward
// ✅ Topological ordering: linearized_graph for reverse iteration
// ✅ Shape tracking: shape_trackers[] for gradient shape matching
// ✅ Dynamic dimensions: dyn_map for runtime shape resolution
```

#### **What Makes Our Graph Structure Autograd-Ready**

1. **Complete Connectivity Information**: 
   - `inputs[]` provides forward connectivity
   - `edges[]` with `Dependency` provides backward connectivity with slot information
   
2. **Operation Type Tracking**: 
   - `op_type` enables differentiation rule dispatch
   - Covers all primitive operations that need gradients
   
3. **Shape Consistency**: 
   - `shape_tracker` ensures gradients have matching shapes
   - `TrackerId` system handles view operations correctly
   
4. **Multiple Output Support**: 
   - `(NodeId, u8)` indexing supports multi-output nodes
   - `input_order`/`output_order` tracks specific input/output slots
   
5. **Metadata Support**: 
   - Can mark gradient nodes with metadata for debugging
   - Can track gradient computation provenance

#### **Additional Autograd-Specific Information We'll Add**

```zig
// Extend NodeMetadata for gradient tracking:
pub const NodeMetadata = struct {
    name: ?[]const u8 = null,
    debug_info: ?DebugInfo = null,
    backend_data: ?*anyopaque = null,
    compile_hints: CompileHints = .{},
    
    // NEW: Autograd-specific metadata
    gradient_info: ?GradientMetadata = null,
};

pub const GradientMetadata = struct {
    is_gradient_node: bool = false,           // Mark gradient computation nodes
    forward_node: ?NodeId = null,             // Which forward node this gradients
    gradient_type: GradientType = .parameter, // Parameter vs intermediate gradient
    accumulation_count: u32 = 0,              // How many gradients accumulated
};

pub const GradientType = enum {
    parameter,     // Gradient w.r.t. trainable parameter
    intermediate,  // Intermediate gradient for backprop
    loss,         // Loss gradient (always 1.0)
};
```

#### **Differentiation Rules: Using Our Graph Structure**

```zig
fn differentiateNode(
    graph: *Graph, 
    fwd_node: NodeId, 
    op_type: OpType, 
    node_grad: GradientInfo,
    grads: *GradientMap
) !void {
    // Get input information from our graph structure
    const node = graph.getNode(fwd_node).?;
    const inputs = node.inputs; // ✅ Available in our Node structure
    
    switch (op_type) {
        .add => {
            // f(a, b) = a + b
            // df/da = 1, df/db = 1
            for (inputs) |input_id| {
                try addGradient(graph, input_id, node_grad, grads);
            }
        },
        
        .multiply => {
            // f(a, b) = a * b  
            // df/da = b, df/db = a
            const a_id = inputs[0];
            const b_id = inputs[1];
            
            // Gradient for a: grad * b
            const grad_a = try graph.createNode(.multiply, &.{node_grad.grad_node, b_id});
            try graph.setNodeMetadata(grad_a, .{ 
                .gradient_info = .{ 
                    .is_gradient_node = true, 
                    .forward_node = a_id,
                    .gradient_type = .intermediate,
                }
            });
            
            try addGradient(graph, a_id, GradientInfo{
                .grad_node = grad_a,
                .shape_tracker = node_grad.shape_tracker,
            }, grads);
            
            // Gradient for b: grad * a  
            const grad_b = try graph.createNode(.multiply, &.{node_grad.grad_node, a_id});
            try graph.setNodeMetadata(grad_b, .{ 
                .gradient_info = .{ 
                    .is_gradient_node = true, 
                    .forward_node = b_id,
                    .gradient_type = .intermediate,
                }
            });
            
            try addGradient(graph, b_id, GradientInfo{
                .grad_node = grad_b,
                .shape_tracker = node_grad.shape_tracker,
            }, grads);
        },
        
        .sum_reduce => {
            // f(x) = sum_reduce(x, axis)
            // df/dx = 1 (broadcast back to original shape)
            const input_id = inputs[0];
            const input_shape = graph.getTracker(node.shape_tracker); // ✅ Available
            
            // Use our shape tracker system to handle broadcasting
            var expanded_grad = node_grad;
            const input_tracker = graph.getNodeShapeTracker(input_id);
            expanded_grad.shape_tracker = input_tracker; // Gradient matches input shape
            
            try addGradient(graph, input_id, expanded_grad, grads);
        },
        
        .max_reduce => {
            // f(x) = max_reduce(x, axis)  
            // df/dx = (x == max_reduce(x)) (one-hot mask)
            const input_id = inputs[0];
            
            // Create equality mask: input == max_value (broadcasted)
            const eq_node = try graph.createNode(.less_than, &.{input_id, fwd_node}); // Use available ops
            const masked_grad = try graph.createNode(.multiply, &.{eq_node, node_grad.grad_node});
            
            try addGradient(graph, input_id, GradientInfo{
                .grad_node = masked_grad,
                .shape_tracker = graph.getNodeShapeTracker(input_id), // Match input shape
            }, grads);
        },
        
        // Unary operations: df/dx = local_derivative * incoming_grad
        .exp2 => {
            // f(x) = exp2(x), f'(x) = exp2(x) * ln(2)
            const exp_result = try graph.createNode(.exp2, &.{inputs[0]});
            const ln2 = try graph.createConstant(@log(2.0), .f32);
            const local_grad = try graph.createNode(.multiply, &.{exp_result, ln2});
            const total_grad = try graph.createNode(.multiply, &.{node_grad.grad_node, local_grad});
            
            try addGradient(graph, inputs[0], GradientInfo{
                .grad_node = total_grad,
                .shape_tracker = node_grad.shape_tracker,
            }, grads);
        },
        
        .log2 => {
            // f(x) = log2(x), f'(x) = 1/(x * ln(2))
            const ln2 = try graph.createConstant(@log(2.0), .f32);
            const denom = try graph.createNode(.multiply, &.{inputs[0], ln2});
            const recip = try graph.createNode(.reciprocal, &.{denom});
            const total_grad = try graph.createNode(.multiply, &.{node_grad.grad_node, recip});
            
            try addGradient(graph, inputs[0], GradientInfo{
                .grad_node = total_grad,
                .shape_tracker = node_grad.shape_tracker,
            }, grads);
        },
        
        .reciprocal => {
            // f(x) = 1/x, f'(x) = -1/x²
            const x_squared = try graph.createNode(.multiply, &.{inputs[0], inputs[0]});
            const neg_recip = try graph.createNode(.reciprocal, &.{x_squared});
            const neg_one = try graph.createConstant(-1.0, .f32);
            const local_grad = try graph.createNode(.multiply, &.{neg_recip, neg_one});
            const total_grad = try graph.createNode(.multiply, &.{node_grad.grad_node, local_grad});
            
            try addGradient(graph, inputs[0], GradientInfo{
                .grad_node = total_grad,
                .shape_tracker = node_grad.shape_tracker,
            }, grads);
        },
        
        .sqrt => {
            // f(x) = sqrt(x), f'(x) = 1/(2*sqrt(x))
            const two = try graph.createConstant(2.0, .f32);
            const sqrt_result = try graph.createNode(.sqrt, &.{inputs[0]});
            const denom = try graph.createNode(.multiply, &.{two, sqrt_result});
            const local_grad = try graph.createNode(.reciprocal, &.{denom});
            const total_grad = try graph.createNode(.multiply, &.{node_grad.grad_node, local_grad});
            
            try addGradient(graph, inputs[0], GradientInfo{
                .grad_node = total_grad,
                .shape_tracker = node_grad.shape_tracker,
            }, grads);
        },
        
        .negate => {
            // f(x) = -x, f'(x) = -1
            const neg_grad = try graph.createNode(.negate, &.{node_grad.grad_node});
            try addGradient(graph, inputs[0], GradientInfo{
                .grad_node = neg_grad,
                .shape_tracker = node_grad.shape_tracker,
            }, grads);
        },
        
        // Non-differentiable operations
        .constant, .variable, .input, .function => {
            // Constants don't contribute gradients
            // Variables/inputs are leaf nodes (gradients computed elsewhere)
            // Functions are opaque (user-defined, skip)
            return;
        },
        
        .mod, .less_than => {
            // These operations are not differentiable
            // If they're marked as parameters, that's an error
            return error.UndifferentiableOperation;
        },
        
        else => {
            std.log.warn("Differentiation rule not implemented for: {}", .{op_type});
            return error.UnsupportedOperation;
        }
    }
}
```

#### **Gradient Accumulation: Handling Multiple Paths**

```zig
fn addGradient(
    graph: *Graph,
    fwd_node: NodeId,
    new_grad: GradientInfo,
    grads: *GradientMap
) !void {
    if (grads.get(fwd_node)) |existing_grad| {
        // ACCUMULATE: Multiple gradient paths to this node
        // grad_total = existing_grad + new_grad
        const accumulated = try graph.createNode(.add, &.{
            existing_grad.grad_node,
            new_grad.grad_node
        });
        
        // Mark as gradient accumulation node
        try graph.setNodeMetadata(accumulated, .{ 
            .gradient_info = .{ 
                .is_gradient_node = true, 
                .forward_node = fwd_node,
                .gradient_type = .intermediate,
                .accumulation_count = existing_grad.accumulation_count + 1,
            }
        });
        
        try grads.put(graph.allocator, fwd_node, GradientInfo{
            .grad_node = accumulated,
            .shape_tracker = existing_grad.shape_tracker, // Should match
            .accumulation_count = existing_grad.accumulation_count + 1,
        });
    } else {
        // FIRST GRADIENT: Just store it
        try grads.put(graph.allocator, fwd_node, new_grad);
    }
}
```

#### **Summary: Why Our Graph Structure is Perfect for Autograd**

✅ **Complete Information Available**:
- **Connectivity**: `inputs[]` and `edges[]` provide all needed graph traversal
- **Operation Types**: `op_type` enables differentiation rule dispatch
- **Shape Consistency**: `shape_tracker` ensures gradients match forward shapes
- **Multi-output Support**: `(NodeId, u8)` indexing handles complex nodes
- **Metadata**: Can track gradient provenance and debugging info

✅ **No Additional Graph Structure Needed**:
- **Forward Traversal**: Use `node.inputs[]` 
- **Backward Traversal**: Use `edges[]` with reverse iteration
- **Shape Information**: Use existing `shape_tracker` system
- **Node Creation**: Use existing `createNode()` infrastructure
- **Topological Ordering**: Use existing `topologicalSort()` function

✅ **Integration with Compilation Pipeline**:
- **Phase 1**: Autograd adds gradient nodes to existing graph
- **Phase 2**: Backend optimization sees complete forward+backward graph
- **Memory Management**: Arena allocation handles gradient node creation
- **Pattern Matching**: Existing pattern matching can optimize gradient computations

The key insight: **Our graph architecture was designed correctly from the start** - it naturally supports autograd without requiring additional graph structures or major modifications. The autograd compiler pass simply adds more nodes to the existing graph using the same infrastructure that handles forward computation.

#### **Critical Design Point: Differentiating Primitive Operations Only**

🎯 **Yes, we are absolutely clear on this fundamental design principle:**

```zig
// ✅ CORRECT: By the time autograd runs, the graph contains ONLY primitives

// User writes high-level operation:
const c = try a.matmul(b);  // High-level operation

// Tensor layer IMMEDIATELY decomposes to primitives:
// matmul(a, b) becomes:
// 1. transpose(b) → ShapeTracker operation (NO graph node)
// 2. expand(a) → ShapeTracker operation (NO graph node)  
// 3. expand(b_t) → ShapeTracker operation (NO graph node)
// 4. multiply(a_exp, b_exp) → PRIMITIVE graph node
// 5. sum_reduce(mul_result, axis=2) → PRIMITIVE graph node

// When autograd runs, it ONLY sees:
// - multiply node (primitive)
// - sum_reduce node (primitive)
// - NO matmul node exists in the graph!

pub const PRIMITIVE_OPERATIONS = &[_]OpType{
    // Data nodes
    .constant, .variable, .input, .function,
    
    // Binary primitives (what autograd actually differentiates)
    .add, .multiply, .mod, .less_than, .divide,
    
    // Unary primitives (what autograd actually differentiates)
    .reciprocal, .sqrt, .sin, .exp2, .log2, .negate,
    
    // Reduction primitives (what autograd actually differentiates)
    .sum_reduce, .max_reduce,
    
    // View operations primitives (autograd flows through these)
    .permute, .expand, .pad, .slice,
};

// ❌ WRONG: Autograd does NOT differentiate high-level operations
// The graph NEVER contains these by design:
const HIGH_LEVEL_OPERATIONS = &[_]fn(){
    matmul,     // Decomposes to: transpose + expand + multiply + sum_reduce
    softmax,    // Decomposes to: exp + sum_reduce + divide
    layer_norm, // Decomposes to: mean + subtract + square + mean + sqrt + divide
    conv2d,     // Decomposes to: im2col + matmul → primitives
    attention,  // Decomposes to: matmul + softmax + matmul → primitives
};
```

#### **Why This Design is Crucial for Autograd**

1. **Minimal Differentiation Rules**: Only ~12 primitive operations need differentiation rules
2. **No High-Level Complexity**: Autograd doesn't need to know about matmul, conv2d, attention, etc.
3. **Compositionality**: Complex gradients emerge from primitive combinations
4. **Correctness**: High-level operation gradients are automatically correct via primitive composition
5. **Maintainability**: Adding new high-level ops doesn't require new differentiation rules

#### **Example: Matmul Gradient Composition**

```zig
// User code:
const C = try A.matmul(B);  // A: [M,K], B: [K,N] → C: [M,N]

// Graph contains (after immediate decomposition):
// Node 1: multiply(A_expanded, B_expanded) → primitive
// Node 2: sum_reduce(multiply_result, axis=2) → primitive

// Autograd differentiates ONLY the primitives:
fn differentiateMultiply(/* ... */) {
    // dC/dA_expanded = dC/dC * B_expanded
    // dC/dB_expanded = dC/dC * A_expanded
}

fn differentiateSumReduce(/* ... */) {
    // dC/d(multiply_result) = expand(dC/dC, original_shape)
}

// The combination of these primitive gradients AUTOMATICALLY gives correct matmul gradients!
// No explicit matmul differentiation rule needed.
```

#### **Autograd Simplicity Through Primitive-Only Design**

```zig
pub const AutogradPass = struct {
    // Autograd only needs to handle ~12 operations, not 100s of high-level ops
    fn differentiateNode(/* ... */) !void {
        switch (op_type) {
            // Only primitive operations reach autograd
            .add => /* simple rule */,
            .multiply => /* simple rule */,
            .sum_reduce => /* simple rule */,
            .exp2 => /* simple rule */,
            .reciprocal => /* simple rule */,
            // ... ~8 more primitives
            
            // High-level operations NEVER appear here:
            // .matmul => NEVER REACHED (decomposed before autograd)
            // .softmax => NEVER REACHED (decomposed before autograd)  
            // .conv2d => NEVER REACHED (decomposed before autograd)
            
            else => @panic("Non-primitive operation in graph during autograd!"),
        }
    }
};
```

This primitive-only design is what makes Zing's autograd both **simple** and **correct** - we differentiate the elementary building blocks, and complex operations get correct gradients automatically through composition.

### **Memory Management: Following Luminal's Proven Simple Approach**

Based on analysis of Luminal's production-proven architecture, Zing adopts a **simple and effective** memory management strategy that focuses on **buffer elimination** rather than **buffer optimization**.

#### **Core Memory Management Architecture (Inspired by Luminal)**

```zig
pub const Graph = struct {
    // Memory management
    allocator: Allocator,              // Backing allocator
    arena: ArenaAllocator,             // Graph construction arena
    
    // Tensor data lives in separate storage (following extraction pattern)
    storage: *DataStorage,             // External tensor storage
    
    // Simple reference counting for automatic cleanup (like Luminal)
    consumers_map: ?std.AutoHashMapUnmanaged((NodeId, u8), u32) = .{},
    no_delete: std.AutoHashSetUnmanaged(NodeId) = .{}, // Keep these tensors
    
    // Focus optimization efforts on elimination, not management
    fusion_opportunities: ?[]FusionGroup = null,  // Eliminate buffers via fusion
    cse_opportunities: ?[]CommonSubexpression = null, // Eliminate redundant computation
};

pub const TensorData = struct {
    data: []u8,              // Raw tensor data (type-erased)
    shape: []const i64,      // Concrete shape at execution time
    dtype: DataType,         // Data type information
    device: Device,          // Target device (CPU, CUDA, Metal)
    version: u32 = 0,        // For cache invalidation
    
    pub fn sizeInBytes(self: TensorData) usize {
        const element_count = std.math.product(usize, self.shape);
        return element_count * self.dtype.sizeInBytes();
    }
};

// Simple tensor ownership for automatic memory management
pub const TensorOwnership = union(enum) {
    owned: TensorData,       // Last consumer - automatically freed
    borrowed: *const TensorData, // Still has consumers - read-only access
};
```

#### **Simple Memory Management (Following Luminal's Success)**

```zig
// Reference counting for automatic tensor cleanup (like Luminal)
pub fn buildConsumersMap(graph: *Graph) !void {
    var consumers = std.AutoHashMapUnmanaged((NodeId, u8), u32){};
    
    // Count how many times each tensor output is consumed
    for (graph.nodes.items) |node| {
        for (node.inputs, 0..) |input_id, input_idx| {
            const key = .{ input_id, @as(u8, @intCast(input_idx)) };
            const current_count = consumers.get(key) orelse 0;
            try consumers.put(graph.arena.child_allocator, key, current_count + 1);
        }
    }
    
    graph.consumers_map = consumers;
}

// Simple tensor access with automatic cleanup (like Luminal)
pub fn getTensorForExecution(storage: *DataStorage, graph: *const Graph, node_id: NodeId, output_idx: u8) TensorOwnership {
    const key = .{ node_id, output_idx };
    const consumer_count = graph.consumers_map.?.get(key) orelse 0;
    
    if (consumer_count == 1 and !graph.no_delete.contains(node_id)) {
        // Last consumer - take ownership (automatic cleanup)
        return TensorOwnership{ .owned = storage.remove(node_id, output_idx).? };
    } else {
        // Still has consumers - borrow only
        return TensorOwnership{ .borrowed = storage.get(node_id, output_idx).? };
    }
}

// Mark tensors that should never be automatically deleted
pub fn keepTensors(graph: *Graph, nodes: []const NodeId) !void {
    for (nodes) |node_id| {
        try graph.no_delete.put(graph.arena.child_allocator, node_id, {});
    }
}
```

#### **Why This Simple Approach Works (Luminal's Success Strategy)**

1. **Focus on Elimination**: Aggressive operator fusion eliminates intermediate buffers entirely
2. **Simple Reference Counting**: Correct and efficient - tensors freed when safe
3. **Immediate Cleanup**: Minimal memory pressure through automatic cleanup
4. **Optimization via Compilation**: Performance comes from graph transforms, not memory management

#### **Memory Optimization Through Graph Transformation**

Instead of complex buffer management, focus optimization efforts on:

```zig
// Operator fusion eliminates intermediate tensors
pub const FusionPass = struct {
    pub fn run(self: FusionPass, graph: *Graph, remap: *NodeRemap) !void {
        // Find elementwise chains that can be fused into single operations
        const fusion_opportunities = try self.findElementwiseChains(graph);
        
        for (fusion_opportunities) |chain| {
            // Replace chain with single fused operation - eliminates intermediate buffers
            const fused_node = try graph.createNode(.fused_elementwise, chain.inputs);
            try remap.updateReference(chain.output_node, fused_node);
            
            // Remove intermediate nodes - buffers eliminated, not optimized
            for (chain.intermediate_nodes) |node_id| {
                graph.removeNode(node_id);
            }
        }
    }
};

// CSE eliminates redundant computations and their buffers
pub const CSEPass = struct {
    pub fn run(self: CSEPass, graph: *Graph, remap: *NodeRemap) !void {
        const common_expressions = try self.findCommonSubexpressions(graph);
        
        for (common_expressions) |cse| {
            // Keep first occurrence, eliminate duplicates
            for (cse.duplicate_nodes) |dup_node| {
                try remap.updateReference(dup_node, cse.canonical_node);
                graph.removeNode(dup_node); // Buffer eliminated
            }
        }
    }
};
```

#### **Key Design Insight: Elimination Over Optimization**

**Luminal's proven strategy shows that:**
- **Eliminating buffers** (via fusion) is more effective than **optimizing buffers** (via pooling)
- **Simple reference counting** is sufficient for remaining tensors
- **Compile-time optimization** scales better than **runtime memory management**
- **Focus on correctness** in memory management, **focus on performance** in graph optimization

This approach is simpler, more maintainable, and proven effective in production ML workloads.

    grad_info: GradientInfo,
    fwd_shape: TrackerId,
    op_type: OpType
) !GradientInfo {
    switch (op_type) {
        .reshape => {
            // Gradient flows through reshape: reshape gradient back to input shape
            const reshaped_grad = try graph.createReshapeOp(
                grad_info.grad_node,
                fwd_shape  // Original input shape
            );
            return GradientInfo{
                .grad_node = reshaped_grad,
                .shape_tracker = fwd_shape,
            };
        },
        
        .transpose => |permutation| {
            // Undo the transpose permutation
            const inverse_perm = computeInversePermutation(permutation);
            const transposed_grad = try graph.createTransposeOp(
                grad_info.grad_node,
                inverse_perm
            );
            return GradientInfo{
                .grad_node = transposed_grad,
                .shape_tracker = fwd_shape,
            };
        },
        
        else => return grad_info,
    }
}
```

#### **Integration with Compilation Pipeline**

```zig
// Usage in training workflow
pub fn setupTraining(graph: *Graph, model_params: []const NodeId, loss: NodeId) !void {
    // 1. Create compilation pipeline
    var pipeline = CompilationPipeline.init(allocator);
    
    // 2. Add autograd as FIRST pass
    const autograd_pass = AutogradPass{
        .params = model_params,
        .loss_node = loss,
        .allocator = allocator,
    };
    try pipeline.addGenericPass(Compiler.GenericPass(autograd_pass));
    
    // 3. Add optimization passes AFTER autograd
    try pipeline.addGenericPass(fusionPass);
    try pipeline.addGenericPass(csePass);
    try pipeline.addBackendPass(.cpu, cpuOptimizationPass);
    
    // 4. Compile: autograd runs first, then optimizations
    const compiled = try pipeline.compile(graph, outputs);
}
```

#### **Key Design Insights from Luminal**

1. **Autograd is a Compiler Pass**: Not runtime autodiff, compile-time graph transformation
2. **Modifies Graph In-Place**: Adds gradient nodes to existing graph structure  
3. **DFS-based Valid Set**: Only processes nodes that matter for gradients
4. **Reverse Topological Order**: Ensures gradients flow correctly
5. **Shape-Aware**: Handles broadcasting, reductions, reshapes correctly
6. **Accumulation**: Handles multiple gradient paths via addition
7. **Runs Before Optimization**: Fusion/CSE can optimize gradient computations

This design makes automatic differentiation a **first-class compilation feature**, not a runtime overhead.

// Layered compilation system like Luminal
pub const CompilationPipeline = struct {
    allocator: Allocator,
    generic_passes: std.ArrayListUnmanaged(Compiler.GenericPass()) = .{},
    backend_passes: std.ArrayListUnmanaged(BackendCompilerPass) = .{},
    backend_config: BackendConfig,
    
    const BackendCompilerPass = union(enum) {
        cpu: Compiler.BackendPass(CpuBackend),
        cuda: Compiler.BackendPass(CudaBackend),
        metal: Compiler.BackendPass(MetalBackend),
    };
    
    pub fn addGenericPass(self: *CompilationPipeline, pass: Compiler.GenericPass()) !void {
        try self.generic_passes.append(self.allocator, pass);
    }
    
    pub fn addBackendPass(self: *CompilationPipeline, pass: BackendCompilerPass) !void {
        try self.backend_passes.append(self.allocator, pass);
    }
    
    pub fn compile(self: *CompilationPipeline, graph: *Graph, outputs: []const NodeId) !*anyopaque {
        var node_remap = NodeRemap{ .allocator = self.allocator };
        defer node_remap.remap.deinit(self.allocator);
        
        // Phase 1: Generic optimization passes (platform-agnostic)
        for (self.generic_passes.items) |pass| {
            try pass.run(graph, &node_remap);
            try node_remap.applyToGraph(graph);
        }
        
        // Phase 2: Backend-specific optimization passes
        for (self.backend_passes.items) |pass| {
            switch (pass) {
                .cpu => |cpu_pass| {
                    if (self.backend_config.backend_type == .cpu) {
                        try cpu_pass.run(graph, &node_remap);
                        try node_remap.applyToGraph(graph);
                    }
                },
                .cuda => |cuda_pass| {
                    if (self.backend_config.backend_type == .cuda) {
                        try cuda_pass.run(graph, &node_remap);
                        try node_remap.applyToGraph(graph);
                    }
                },
                .metal => |metal_pass| {
                    if (self.backend_config.backend_type == .metal) {
                        try metal_pass.run(graph, &node_remap);
                        try node_remap.applyToGraph(graph);
                    }
                },
            }
        }
        
        // Phase 3: Backend-specific compilation to execution format
        return try self.compileForBackend(graph, outputs, &node_remap);
    }
    
    fn compileForBackend(
        self: *CompilationPipeline,
        graph: *Graph,
        outputs: []const NodeId,
        remap: *NodeRemap,
    ) !*anyopaque {
        switch (self.backend_config.backend_type) {
            .cpu => {
                const cpu_compiled = try self.allocator.create(CpuCompiledGraph);
                cpu_compiled.* = try CpuBackend.compile(self.allocator, graph, outputs, remap);
                return cpu_compiled;
            },
            .cuda => {
                const cuda_compiled = try self.allocator.create(CudaCompiledGraph);
                cuda_compiled.* = try CudaBackend.compile(self.allocator, graph, outputs, remap);
                return cuda_compiled;
            },
            .metal => {
                const metal_compiled = try self.allocator.create(MetalCompiledGraph);
                metal_compiled.* = try MetalBackend.compile(self.allocator, graph, outputs, remap);
                return metal_compiled;
            },
        }
    }
};

// Backend-specific data structures and compilation passes
pub const CpuBackend = struct {
    allocator: Allocator,
    
    // CPU-specific optimized operations
    pub const MatMul2D = struct {
        node_id: NodeId,
        lhs_shape: []const i64,
        rhs_shape: []const i64,
        
        pub fn execute(self: MatMul2D, lhs_data: []const f32, rhs_data: []const f32, output: []f32) void {
            // Use optimized BLAS implementation
            const M = self.lhs_shape[0];
            const K = self.lhs_shape[1];
            const N = self.rhs_shape[1];
            
            // Call into optimized matrix multiplication library
            cblas_sgemm(CblasRowMajor, CblasNoTrans, CblasNoTrans,
                       @intCast(M), @intCast(N), @intCast(K),
                       1.0, lhs_data.ptr, @intCast(K),
                       rhs_data.ptr, @intCast(N),
                       0.0, output.ptr, @intCast(N));
        }
    };
    
    // 🔑 KEY PATTERN: Backend Operation Structure
    // Each optimized operation follows this pattern:
    // 1. Struct contains metadata (node_id, config, etc.)
    // 2. execute() method contains hardware-specific implementation
    // 3. Created during compilation, called during execution
    
    pub const FusedElementwise = struct {
        node_id: NodeId,
        operations: []const ElementwiseOp,  // Sequence of operations to fuse
        
        pub const ElementwiseOp = union(enum) {
            add: void,
            multiply: void,
            exp: void,
            sqrt: void,
            sin: void,
        };
        
        // 🚀 EXECUTION: This is called during graph execution, not compilation
        // The runtime system calls this when executing the node_id
        pub fn execute(self: FusedElementwise, inputs: []const []const f32, output: []f32) void {
            // CPU-specific: Vectorized SIMD execution  
            const len = output.len;
            var i: usize = 0;
            while (i < len) : (i += 8) { // Process 8 elements at a time (AVX2)
                var values: @Vector(8, f32) = undefined;
                
                // Load initial values with bounds checking
                if (i + 8 <= len) {
                    values = inputs[0][i..i+8][0..8].*;
                } else {
                    // Handle remainder elements
                    @memset(@as([*]f32, @ptrCast(&values))[0..8], 0);
                    @memcpy(@as([*]f32, @ptrCast(&values))[0..len-i], inputs[0][i..len]);
                }
                
                // Apply fused operations in sequence (CRITICAL: no intermediate memory)
                for (self.operations) |op| {
                    values = switch (op) {
                        .add => values + @as(@Vector(8, f32), @splat(1.0)),
                        .multiply => values * @as(@Vector(8, f32), @splat(2.0)),
                        .exp => @exp(values),
                        .sqrt => @sqrt(values),
                        .sin => @sin(values),
                    };
                }
                
                // Store results back to memory
                if (i + 8 <= len) {
                    output[i..i+8][0..8].* = values;
                } else {
                    @memcpy(output[i..len], @as([*]const f32, @ptrCast(&values))[0..len-i]);
                }
            }
        }
    };
    
    // 🔧 COMPILATION: Backend provides compilation passes that create these optimized ops
    // This happens during graph optimization, not execution
    
### **In-Place Graph Modification Patterns: How Compiler Passes Work**

All compiler passes follow the same fundamental pattern of **modifying the graph in-place**. Here's the systematic approach used throughout the compilation pipeline:

#### **1. The Standard Compiler Pass Template**

```zig
pub fn compilerPassTemplate(
    context: anytype,        // Backend context, pass-specific data
    graph: *Graph,           // MODIFIED IN-PLACE
    remap: *NodeRemap,       // Tracks node replacements
) !void {
    // PHASE 1: COLLECT - Find optimization candidates
    var candidates = std.ArrayList(CandidateInfo).init(context.allocator);
    defer candidates.deinit();
    
    for (graph.nodes.items) |node| {
        if (shouldOptimize(node)) {
            try candidates.append(extractCandidateInfo(node));
        }
    }
    
    // PHASE 2: TRANSFORM - Create new optimized nodes
    var removed_nodes = std.ArrayList(NodeId).init(context.allocator);
    defer removed_nodes.deinit();
    
    for (candidates.items) |candidate| {
        // CREATE: Add new optimized node to graph
        const optimized_node = try graph.createNode(.custom, candidate.inputs);
        
        // REMAP: Update all references to point to new node
        try remap.updateReference(candidate.output_node, optimized_node);
        
        // MARK FOR REMOVAL: Collect old nodes to remove
        for (candidate.nodes_to_remove) |old_node| {
            try removed_nodes.append(old_node);
        }
    }
    
    // PHASE 3: CLEANUP - Remove old nodes from graph
    for (removed_nodes.items) |node_id| {
        graph.removeNode(node_id);  // IN-PLACE GRAPH MODIFICATION
    }
    
    // PHASE 4: APPLY REMAPPING - Update all node references
    try remap.applyToGraph(graph);  // IN-PLACE REFERENCE UPDATES
}
```

#### **2. Critical In-Place Operations**

```zig
// These functions modify the graph structure directly:

graph.removeNode(node_id)               // Deletes node and all its edges
graph.replaceNode(old_id, new_id)       // Redirects all edges 
graph.moveOutgoingEdges(from, to)       // Transfers connections
graph.addEdge(from, to, dependency)     // Adds new connections
remap.applyToGraph(graph)               // Updates all references

// The graph object passed to compiler passes is THE SAME object
// that was passed to the user - no copying, no separate instances
```

#### **3. NodeRemap: Tracking Transformations**

The `NodeRemap` system is crucial for maintaining consistency during in-place modifications:

```zig
pub const NodeRemap = struct {
    remap: std.AutoHashMapUnmanaged(NodeId, NodeId) = .{},
    
    // TRACK: Record that old_id should become new_id
    pub fn updateReference(self: *NodeRemap, old_id: NodeId, new_id: NodeId) !void {
        try self.remap.put(self.allocator, old_id, new_id);
    }
    
    // APPLY: Update all node inputs to use new IDs
    pub fn applyToGraph(self: *NodeRemap, graph: *Graph) !void {
        for (graph.nodes.items) |*node| {
            for (node.inputs) |*input_id| {
                input_id.* = self.resolve(input_id.*);  // IN-PLACE UPDATE
            }
        }
        
        // Also update edge references, output mappings, etc.
        for (graph.edges.items) |*edge| {
            edge.source = self.resolve(edge.source);
            edge.target = self.resolve(edge.target);
        }
    }
    
    // RESOLVE: Get the final ID for a node (follows remap chain)
    pub fn resolve(self: *NodeRemap, id: NodeId) NodeId {
        var current = id;
        while (self.remap.get(current)) |mapped| {
            current = mapped;
        }
        return current;
    }
};
```

#### **4. Example: Fusion Pass In-Place Modifications**

```zig
pub fn elementwiseFusionPass(graph: *Graph, remap: *NodeRemap) !void {
    // BEFORE: Graph has separate add, mul, exp nodes
    // add_node: result = a + b
    // mul_node: result = add_result * c  
    // exp_node: result = exp(mul_result)
    
    var fusion_groups = std.ArrayList(FusionGroup).init(allocator);
    defer fusion_groups.deinit();
    
    // PHASE 1: IDENTIFY fusion candidates  
    for (graph.nodes.items) |node| {
        if (isElementwise(node.op_type)) {
            const group = try findFusionGroup(graph, node.id);
            if (group.nodes.len > 1) {
                try fusion_groups.append(group);
            }
        }
    }
    
    // PHASE 2: CREATE fused operations and REMOVE old ones
    for (fusion_groups.items) |group| {
        // CREATE: Single fused node that replaces entire group
        const fused_node = try graph.createNode(.fused_elementwise, group.root_inputs);
        try graph.setNodeMetadata(fused_node, NodeMetadata{
            .backend_data = try createFusedOperation(group),
        });
        
        // REMAP: All references to the output node now point to fused node
        try remap.updateReference(group.output_node, fused_node);
        
        // REMOVE: Delete all intermediate nodes IN-PLACE
        for (group.nodes) |node_id| {
            if (node_id != group.output_node) {
                graph.removeNode(node_id);  // GRAPH MODIFIED IN-PLACE
            }
        }
    }
    
    // PHASE 3: UPDATE all references throughout graph
    try remap.applyToGraph(graph);  // ALL NODE INPUTS UPDATED IN-PLACE
    
    // AFTER: Graph has single fused_elementwise node
    // fused_node: result = exp((a + b) * c)  [computed in single kernel]
}
```

#### **5. Memory Management During In-Place Operations**

```zig
// The Mark-and-Compact system handles memory fragmentation from node removal:

pub fn removeNode(graph: *Graph, node_id: NodeId) void {
    // MARK: Don't actually free memory immediately
    graph.removed_nodes.set(node_id);
    graph.compaction_needed = true;
    
    // REMOVE: Clear edges and references
    graph.removeAllEdges(node_id);
    
    // AUTO-COMPACT: If too many holes, rebuild arrays
    const removed_ratio = @as(f32, @floatFromInt(graph.removed_nodes.count())) / 
                         @as(f32, @floatFromInt(graph.nodes.items.len));
    if (removed_ratio > graph.compaction_threshold) {
        try graph.compactMemory();  // Rebuild arrays without removed nodes
    }
}
```

#### **6. Why In-Place Modification is Critical**

1. **Memory Efficiency**: No duplication of large graph structures
2. **Performance**: Avoid copying entire graphs between passes  
3. **Reference Consistency**: User tensor handles remain valid
4. **Incremental Optimization**: Each pass builds on previous transformations
5. **Backend Flexibility**: Multiple passes can run in sequence

This in-place modification pattern is used by **all** compiler passes:
- **Autograd Pass**: Adds gradient nodes to existing graph
- **Fusion Passes**: Replace node groups with optimized nodes  
- **CSE Pass**: Eliminates duplicate computations
- **Dead Code Elimination**: Removes unused nodes
- **Backend Optimization**: Creates hardware-specific nodes

The key insight: **The graph is a mutable data structure that evolves through compilation**, not an immutable representation that gets replaced.

    // CPU-specific compilation passes
    pub fn matmulPatternPass(
        backend: CpuBackend,
        graph: *Graph,
        remap: *NodeRemap,
    ) !void {
        // Pattern: Mul([A, 1, K], [1, B, K]) -> SumReduce(2) becomes MatMul2D
        var removed_nodes = std.ArrayList(NodeId).init(backend.allocator);
        defer removed_nodes.deinit();
        
        for (graph.nodes.items) |node| {
            if (node.op_type != .reduce_sum) continue;
            
            // Check if reducing along last dimension
            const reduce_node = node;
            
            // Find the multiply node feeding into this reduce
            const mul_node_id = reduce_node.inputs[0];
            const mul_node = graph.getNode(mul_node_id) orelse continue;
            if (mul_node.op_type != .multiply) continue;
            
            // Check shapes for matmul pattern [A, 1, K] * [1, B, K]
            const lhs_tracker = graph.getTracker(mul_node.inputs[0]);
            const rhs_tracker = graph.getTracker(mul_node.inputs[1]);
            
            if (isMatmulPattern(lhs_tracker, rhs_tracker)) {
                // Replace with optimized MatMul2D
                const matmul_node = try graph.createNode(.custom, mul_node.inputs);
                try graph.setNodeMetadata(matmul_node, NodeMetadata{
                    .name = "cpu_matmul_2d",
                    .backend_data = try backend.allocator.create(MatMul2D),
                });
                
                // Update remapping
                try remap.updateReference(reduce_node.id, matmul_node);
                try removed_nodes.append(mul_node_id);
                try removed_nodes.append(reduce_node.id);
            }
        }
        
        // Remove old nodes
        for (removed_nodes.items) |node_id| {
            graph.removeNode(node_id);
        }
    }
    
    pub fn elementwiseFusionPass(
        backend: CpuBackend,
        graph: *Graph,
        remap: *NodeRemap,
    ) !void {
        // Find chains of elementwise operations that can be fused
        var fusion_chains = std.ArrayList([]NodeId).init(backend.allocator);
        defer {
            for (fusion_chains.items) |chain| {
                backend.allocator.free(chain);
            }
            fusion_chains.deinit();
        }
        
        // Identify fusible chains
        for (graph.nodes.items) |node| {
            if (isElementwiseOp(node.op_type) and !isAlreadyFused(node.id, fusion_chains.items)) {
                const chain = try findElementwiseChain(backend.allocator, graph, node.id);
                if (chain.len > 1) {
                    try fusion_chains.append(chain);
                }
            }
        }
        
        // Create fused operations
        for (fusion_chains.items) |chain| {
            const fused_node = try createFusedElementwise(backend, graph, chain);
            
            // Update remapping for the last node in chain
            try remap.updateReference(chain[chain.len - 1], fused_node);
            
            // Remove intermediate nodes
            for (chain[0..chain.len-1]) |node_id| {
                graph.removeNode(node_id);
            }
        }
    }
    
    pub fn compile(
        allocator: Allocator,
        graph: *Graph,
        outputs: []const NodeId,
        remap: *NodeRemap,
    ) !CpuCompiledGraph {
        // Build execution order
        try graph.toposort();
        const execution_order = try allocator.dupe(ExecutionNode, graph.linearized_graph.?);
        
        // Create memory plan optimized for CPU caching
        const memory_plan = try createCpuMemoryPlan(allocator, graph);
        
        return CpuCompiledGraph{
            .base = CompiledGraph{
                .allocator = allocator,
                .execution_order = execution_order,
                .memory_plan = memory_plan,
                .original_graph = graph,
                .output_nodes = try allocator.dupe(NodeId, outputs),
                .backend = .cpu,
                .compilation_stats = .{}, // Filled during compilation
            },
            .simd_plan = try generateSimdPlan(allocator, execution_order),
            .thread_pool_config = try optimizeThreading(execution_order),
        };
    }
};

pub const CudaBackend = struct {
    allocator: Allocator,
    cuda_context: CudaContext,
    
    // CUDA-specific optimized operations
    pub const FusedKernel = struct {
        node_id: NodeId,
        kernel_source: []const u8,  // Generated CUDA C code
        compiled_kernel: ?CudaKernel = null,  // Runtime compiled kernel
        
        pub fn compile(self: *FusedKernel, cuda_ctx: CudaContext) !void {
            // Runtime compilation using NVRTC
            self.compiled_kernel = try cuda_ctx.compileKernel(self.kernel_source);
        }
        
        pub fn execute(self: FusedKernel, inputs: []const CudaBuffer, output: CudaBuffer) !void {
            const kernel = self.compiled_kernel orelse return error.KernelNotCompiled;
            
            // Launch CUDA kernel
            try kernel.launch(.{
                .grid_size = calculateGridSize(output.len),
                .block_size = 256,
                .args = &[_]*anyopaque{ @ptrCast(&inputs), @ptrCast(&output) },
            });
        }
    };
    
    // CUDA-specific compilation passes
    pub fn kernelFusionPass(
        backend: CudaBackend,
        graph: *Graph,
        remap: *NodeRemap,
    ) !void {
        // More sophisticated fusion than CPU - can fuse across different op types
        var fusion_groups = std.ArrayList([]NodeId).init(backend.allocator);
        defer {
            for (fusion_groups.items) |group| {
                backend.allocator.free(group);
            }
            fusion_groups.deinit();
        }
        
        // Identify fusion opportunities
        try identifyFusionGroups(backend.allocator, graph, &fusion_groups);
        
        for (fusion_groups.items) |group| {
            // Generate CUDA kernel source
            const kernel_source = try generateCudaKernel(backend.allocator, graph, group);
            
            // Create fused kernel node
            const fused_node = try graph.createNode(.custom, getGroupInputs(graph, group));
            const fused_kernel = try backend.allocator.create(FusedKernel);
            fused_kernel.* = FusedKernel{
                .node_id = fused_node,
                .kernel_source = kernel_source,
            };
            
            try graph.setNodeMetadata(fused_node, NodeMetadata{
                .name = "cuda_fused_kernel",
                .backend_data = fused_kernel,
            });
            
            // Update remapping and remove old nodes
            const output_node = group[group.len - 1];
            try remap.updateReference(output_node, fused_node);
            
            for (group) |node_id| {
                graph.removeNode(node_id);
            }
        }
    }
    
    pub fn memoryCoalescingPass(
        backend: CudaBackend,
        graph: *Graph,
        remap: *NodeRemap,
    ) !void {
        // Optimize memory access patterns for GPU
        for (graph.nodes.items) |*node| {
            switch (node.op_type) {
                .transpose => {
                    // Replace with coalesced memory access version
                    try optimizeTransposeForCoalescing(backend, graph, node, remap);
                },
                .slice => {
                    // Optimize slicing for continuous memory access
                    try optimizeSliceForCoalescing(backend, graph, node, remap);
                },
                else => {},
            }
        }
    }
    
    pub fn compile(
        allocator: Allocator,
        graph: *Graph,
        outputs: []const NodeId,
        remap: *NodeRemap,
    ) !CudaCompiledGraph {
        // Build execution order
        try graph.toposort();
        const execution_order = try allocator.dupe(ExecutionNode, graph.linearized_graph.?);
        
        // Create GPU-optimized memory plan
        const memory_plan = try createCudaMemoryPlan(allocator, graph);
        
        // Compile all CUDA kernels
        var kernels = std.ArrayList(CudaKernel).init(allocator);
        for (graph.nodes.items) |node| {
            if (node.metadata) |meta| {
                if (meta.backend_data) |data| {
                    const fused_kernel = @as(*FusedKernel, @ptrCast(@alignCast(data)));
                    try fused_kernel.compile(cuda_context);
                    try kernels.append(fused_kernel.compiled_kernel.?);
                }
            }
        }
        
        return CudaCompiledGraph{
            .base = CompiledGraph{
                .allocator = allocator,
                .execution_order = execution_order,
                .memory_plan = memory_plan,
                .original_graph = graph,
                .output_nodes = try allocator.dupe(NodeId, outputs),
                .backend = .cuda,
                .compilation_stats = .{},
            },
            .kernels = try kernels.toOwnedSlice(),
            .stream_config = try optimizeCudaStreams(execution_order),
            .memory_pools = try createCudaMemoryPools(allocator, memory_plan),
        };
    }
};

pub const CompilationPhase = enum {
    optimization,  // Backend-agnostic: CSE, fusion, dead code elimination
    lowering,      // Backend-specific: convert to backend primitives  
    scheduling,    // Backend-specific: optimize for target hardware
    finalization,  // Create final execution artifact
};

// Final compilation result after ALL passes
pub const CompiledGraph = struct {
    allocator: Allocator,
    
    // Execution plan
    execution_order: []const ExecutionNode,
    memory_plan: MemoryPlan,
    
    // NEW: Owns tensor data storage
    storage: DataStorage,
    
    // Metadata
    original_graph: *const Graph,  // Read-only reference to source
    output_nodes: []const NodeId,
    backend: BackendType,
    compilation_stats: CompilationStats,
    
    // Backend-specific artifacts
    backend_data: ?*anyopaque = null,  // Backend-specific compiled artifacts
    
    pub fn init(allocator: Allocator, graph: *const Graph) CompiledGraph {
        return .{
            .allocator = allocator,
            .execution_order = &.{},
            .memory_plan = .{},
            .storage = DataStorage.init(allocator),
            .original_graph = graph,
            .output_nodes = &.{},
            .backend = .cpu,
            .compilation_stats = .{},
        };
    }
    
    pub fn deinit(self: *CompiledGraph) void {
        self.storage.deinit();  // Free all tensor data first
        self.allocator.free(self.execution_order);
        self.memory_plan.deinit(self.allocator);
        self.allocator.free(self.output_nodes);
        // Backend cleanup handled separately
    }
};

pub const CompilationStats = struct {
    passes_run: u32,
    nodes_eliminated: u32,
    operations_fused: u32,
    memory_saved: usize,
    compile_time_ms: u64,
};

pub const BackendConfig = struct {
    backend_type: BackendType,
    device_config: DeviceConfig,
    optimization_level: OptimizationLevel,
    
    pub const OptimizationLevel = enum {
        debug,      // No optimization, debug info
        balanced,   // Moderate optimization
        aggressive, // Maximum optimization
    };
};

// Example usage of backend-aware compilation (following Luminal's patterns)
pub fn createStandardPipeline(allocator: Allocator, backend: BackendType) !CompilationPipeline {
    var pipeline = CompilationPipeline{
        .allocator = allocator,
        .backend_config = .{
            .backend_type = backend,
            .device_config = getDefaultDeviceConfig(backend),
            .optimization_level = .balanced,
        },
    };
    
    // Phase 1: Generic optimization passes (platform-agnostic)
    try pipeline.addGenericPass(Compiler.GenericPass(){
        .name = "dead_code_elimination",
        .phase = .optimization,
        .context = {},
        .compile_fn = deadCodeEliminationPass,
    });
    
    try pipeline.addGenericPass(Compiler.GenericPass(){
        .name = "common_subexpression_elimination", 
        .phase = .optimization,
        .context = {},
        .compile_fn = csePass,
    });
    
    try pipeline.addGenericPass(Compiler.GenericPass(){
        .name = "constant_folding",
        .phase = .optimization,
        .context = {},
        .compile_fn = constantFoldingPass,
    });
    
    // Phase 2: Backend-specific passes (following Luminal's strategy)
    switch (backend) {
        .cpu => {
            const cpu_backend = CpuBackend{ .allocator = allocator };
            
            // Pattern recognition and replacement passes
            try pipeline.addBackendPass(.{
                .cpu = Compiler.BackendPass(CpuBackend){
                    .name = "matmul_pattern_recognition",
                    .phase = .lowering,
                    .backend_specific = true,
                    .context = cpu_backend,
                    .compile_fn = CpuBackend.matmulPatternPass,
                }
            });
            
            // Elementwise fusion for SIMD
            try pipeline.addBackendPass(.{
                .cpu = Compiler.BackendPass(CpuBackend){
                    .name = "elementwise_fusion",
                    .phase = .lowering,
                    .backend_specific = true,
                    .context = cpu_backend,
                    .compile_fn = CpuBackend.elementwiseFusionPass,
                }
            });
        },
        .cuda => {
            const cuda_backend = CudaBackend{ 
                .allocator = allocator, 
                .cuda_context = try initCudaContext(),
            };
            
            // Advanced kernel fusion
            try pipeline.addBackendPass(.{
                .cuda = Compiler.BackendPass(CudaBackend){
                    .name = "kernel_fusion",
                    .phase = .lowering,
                    .backend_specific = true,
                    .context = cuda_backend,
                    .compile_fn = CudaBackend.kernelFusionPass,
                }
            });
            
            // Memory coalescing optimization
            try pipeline.addBackendPass(.{
                .cuda = Compiler.BackendPass(CudaBackend){
                    .name = "memory_coalescing",
                    .phase = .lowering,
                    .backend_specific = true,
                    .context = cuda_backend,
                    .compile_fn = CudaBackend.memoryCoalescingPass,
                }
            });
        },
        .metal => {
            // Similar structure for Metal backend
            // Metal-specific passes would go here
        },
    }
    
    return pipeline;
}

// Concrete example of backend compilation integration
pub fn compileModelForBackend(
    model: *Model,
    backend_type: BackendType,
) !*anyopaque {
    // Create pipeline with backend-specific optimizations
    var pipeline = try createStandardPipeline(model.allocator, backend_type);
    defer pipeline.deinit();
    
    // Compile through full pipeline
    const compiled = try pipeline.compile(&model.graph, model.output_nodes);
    
    // Backend now has optimized operations specific to hardware
    std.log.info("Compiled for {s} with {} passes", .{ @tagName(backend_type), pipeline.generic_passes.items.len + pipeline.backend_passes.items.len });
    
    return compiled;
}

// Real-world usage showing backend differences
pub fn demonstrateBackendOptimizations() !void {
    var model = try createTransformerModel(allocator);
    defer model.deinit();
    
    // CPU compilation: MatMul → BLAS, elementwise → SIMD
    const cpu_compiled = try compileModelForBackend(&model, .cpu);
    const cpu_result = @as(*CpuCompiledGraph, @ptrCast(@alignCast(cpu_compiled)));
    // Result: MatMul operations replaced with optimized BLAS calls
    //         Elementwise chains fused into SIMD loops
    
    // CUDA compilation: Operations → fused kernels
    const cuda_compiled = try compileModelForBackend(&model, .cuda);
    const cuda_result = @as(*CudaCompiledGraph, @ptrCast(@alignCast(cuda_compiled)));
    // Result: Multiple operations fused into single CUDA kernels
    //         Memory access patterns optimized for coalescing
    
    std.log.info("CPU: {} BLAS ops, {} SIMD ops", .{ cpu_result.simd_plan.blas_ops.len, cpu_result.simd_plan.vectorized_ops.len });
    std.log.info("CUDA: {} fused kernels, {} memory pools", .{ cuda_result.kernels.len, cuda_result.memory_pools.len });
}
```

## **Summary: Backend Compilation Flexibility**

**YES - Backends Can Fully Customize Compilation:**

### **1. Reuse Generic Infrastructure**
```zig
// All backends benefit from shared optimizations
deadCodeElimination() → csePass() → constantFolding()  // Generic passes

// Then add backend-specific optimizations
CpuBackend.matmulPatternPass()     // CPU: Pattern → BLAS
CudaBackend.kernelFusionPass()     // CUDA: Ops → Fused kernels
```

### **2. Define Custom Compilation Passes**
```zig
// Backends implement their own optimization functions
pub fn customOptimization(
    backend: MyBackend,
    graph: *Graph,           // Modify the computation graph
    remap: *NodeRemap,       // Track node transformations
) !void {
    // Custom pattern matching, node replacement, memory optimization
    // Backend has full access to graph manipulation APIs
}
```

### **3. Register Flexible Pass Ordering**
```zig
// Backends control when their passes run
try pipeline.addBackendPass(.{
    .my_backend = Compiler.BackendPass(MyBackend){
        .name = "my_custom_pass",
        .phase = .lowering,     // Or .optimization, .scheduling
        .compile_fn = MyBackend.customOptimization,
    }
});
```

### **4. Access Full Graph APIs**
Backends can use all graph manipulation functions:
- `graph.removeNode()`, `graph.replaceNode()`
- `graph.createNode()`, `graph.moveOutgoingEdges()`
- `graph.setNodeMetadata()` for backend-specific data
- Pattern matching across the entire graph

### **5. Create Backend-Specific Artifacts**
```zig
// Each backend produces optimized execution format
CpuCompiledGraph:  BLAS ops + SIMD plans
CudaCompiledGraph: Fused kernels + memory pools  
MetalCompiledGraph: Metal shaders + command buffers
```

This design gives backends **complete flexibility** to implement sophisticated optimizations while reusing the common compilation infrastructure. It follows Luminal's proven approach where backends are **compilation systems** that transform generic graphs into hardware-optimized execution plans.

**Backend-Specific Compilation Extensions:**
```zig
// Each backend can extend compilation with custom data
pub const CudaCompiledGraph = struct {
    base: CompiledGraph,
    kernels: []const CudaKernel,
    stream_config: CudaStreamConfig,
    memory_pools: []const CudaMemoryPool,
};

pub const CpuCompiledGraph = struct {
    base: CompiledGraph,
    simd_plan: SimdExecutionPlan,
    thread_pool_config: ThreadPoolConfig,
    cache_optimization: CacheOptimizationPlan,
};

### **🎯 Main Compilation Entry Points**

```zig
// MAIN FUNCTION: General graph compilation (what most users call)
pub fn compileGraph(
    allocator: Allocator, 
    graph: *Graph, 
    outputs: []const NodeId,
    backend: BackendType,
) !CompiledGraph {
    const compiled_ptr = try compileForBackend(allocator, graph, outputs, backend);
    
    // Type-safe wrapper around opaque pointer
    return CompiledGraph{
        .backend_data = compiled_ptr,
        .backend_type = backend,
        .output_nodes = try allocator.dupe(NodeId, outputs),
        .allocator = allocator,
    };
}

// Backend-specific compilation (internal implementation)
pub fn compileForBackend(
    allocator: Allocator,
    graph: *Graph,
    outputs: []const NodeId,
    backend: BackendType,
) !*anyopaque {
    var pipeline = try createStandardPipeline(allocator, backend);
    defer pipeline.deinit();
    
    // Multi-phase compilation through pipeline
    const base_compiled = try pipeline.compile(graph, outputs);
    
    // Backend-specific final compilation step
    switch (backend) {
        .cuda => {
            const cuda_compiled = try allocator.create(CudaCompiledGraph);
            cuda_compiled.base = base_compiled;
            cuda_compiled.kernels = try generateCudaKernels(allocator, &base_compiled);
            cuda_compiled.stream_config = try optimizeCudaStreams(&base_compiled);
            return cuda_compiled;
        },
        .cpu => {
            const cpu_compiled = try allocator.create(CpuCompiledGraph);
            cpu_compiled.base = base_compiled;
            cpu_compiled.simd_plan = try generateSimdPlan(allocator, &base_compiled);
            cpu_compiled.thread_pool_config = try optimizeThreading(&base_compiled);
            return cpu_compiled;
        },
        else => return &base_compiled,
    }
}
```

**Practical Usage Patterns:**
```zig
// Example 1: Standard compilation with automatic pipeline
pub fn compileModel(model: *Model, backend: BackendType) !*anyopaque {
    // Create standard optimization pipeline
    var pipeline = try createStandardPipeline(model.allocator, backend);
    defer pipeline.deinit();
    
    // Add model-specific passes if needed
    if (model.has_attention_layers) {
        try pipeline.addPass(.{
            .name = "attention_optimization",
            .phase = .optimization,
            .run_fn = optimizeAttentionPattern,
        });
    }
    
    // Compile through full pipeline
    return try compileForBackend(
        model.allocator,
        &model.graph,
        model.output_nodes,
        backend
    );
}

// Example 2: Custom compilation pipeline
pub fn compileWithCustomPasses(
    allocator: Allocator,
    graph: *Graph,
    outputs: []const NodeId,
    backend: BackendType,
    custom_passes: []const CompilerPass,
) !CompiledGraph {
    var pipeline = CompilationPipeline{
        .allocator = allocator,
        .backend_config = .{
            .backend_type = backend,
            .device_config = getDefaultDeviceConfig(backend),
            .optimization_level = .aggressive,
        },
    };
    
    // Add standard passes first
    try addStandardOptimizationPasses(&pipeline);
    
    // Add custom passes
    for (custom_passes) |pass| {
        try pipeline.addPass(pass);
    }
    
    // Add backend-specific passes last
    try addBackendSpecificPasses(&pipeline, backend);
    
    return try pipeline.compile(graph, outputs);
}

// Example 3: Debug compilation with intermediate inspection
pub fn compileWithDebugging(
    allocator: Allocator,
    graph: *Graph,
    outputs: []const NodeId,
    debug_config: DebugConfig,
) !CompiledGraph {
    var pipeline = try createStandardPipeline(allocator, .cpu);
    
    // Inject debug passes between optimization passes
    if (debug_config.dump_ir) {
        for (pipeline.passes.items, 0..) |_, i| {
            try pipeline.passes.insert(allocator, i * 2 + 1, .{
                .name = "debug_dump",
                .phase = .optimization,
                .run_fn = struct {
                    fn dump(g: *Graph) !void {
                        try debugDumpGraph(g, "after_pass_{}.dot", .{i});
                    }
                }.dump,
            });
        }
    }
    
    return try pipeline.compile(graph, outputs);
}

// Example 4: Multi-backend compilation (experimental)
pub fn compileForMultipleBackends(
    allocator: Allocator,
    graph: *Graph,
    outputs: []const NodeId,
    backends: []const BackendType,
) ![]const *anyopaque {
    var results = try allocator.alloc(*anyopaque, backends.len);
    
    // Run backend-agnostic passes once
    var base_pipeline = CompilationPipeline{
        .allocator = allocator,
        .backend_config = undefined, // Set per backend
    };
    try addStandardOptimizationPasses(&base_pipeline);
    
    // Apply base optimizations to original graph
    for (base_pipeline.passes.items) |pass| {
        if (pass.phase == .optimization and !pass.backend_specific) {
            try pass.run(graph);
        }
    }
    
    // Compile for each backend with backend-specific passes
    for (backends, 0..) |backend, i| {
        // Clone graph for this backend (expensive but necessary)
        var backend_graph = try graph.clone(allocator);
        defer backend_graph.deinit();
        
        results[i] = try compileForBackend(allocator, &backend_graph, outputs, backend);
    }
    
    return results;
}
```

### **📋 Summary: Complete Compilation Flow**

**1. Component Relationships:**
```
User calls compileGraph()
    ↓
Creates CompilationPipeline with backend-specific passes
    ↓
Phase 1: Generic passes (CSE, dead code elimination, constant folding)
    ↓
Phase 2: Backend-specific passes (pattern matching, fusion)
    ↓  
Phase 3: Backend compilation (creates optimized operations like FusedElementwise)
    ↓
Returns CompiledGraph with backend_data containing optimized operations
```

**2. When execute() is called:**
- **Compilation time**: `FusedElementwise` struct is created with operation sequence
- **Execution time**: `FusedElementwise.execute()` is called by runtime with actual tensor data
- **Key insight**: The struct stores the optimization metadata, execute() does the computation

**3. How Luminal abstraction compares:**
- **Luminal**: `cx.compile(backend_tuple)` where backends register passes dynamically  
- **Zing**: `compileGraph(graph, backend)` with static, type-safe compilation pipeline
- **Both**: Multi-pass optimization → pattern matching → backend-specific code generation

**4. Missing pieces for implementation:**
- **Runtime system**: How compiled graphs actually execute (separate from compilation)
- **Memory planning integration**: How compilation coordinates with memory allocation
- **Pattern matching DSL**: How to express complex optimization patterns declaratively

**Memory Management During Multi-Pass Compilation:**
```zig
// Integration with memory solutions from earlier
pub const CompilationPipeline = struct {
    // ... previous fields ...
    
    // Memory management for compilation phases
    temp_arena: ?ArenaAllocator = null,
    
    pub fn compile(self: *CompilationPipeline, graph: *Graph, outputs: []const NodeId) !CompiledGraph {
        // Create temporary arena for compilation metadata
        self.temp_arena = ArenaAllocator.init(self.allocator);
        defer {
            if (self.temp_arena) |*arena| arena.deinit();
            self.temp_arena = null;
        }
        
        // Use Graph's optimization memory management
        try graph.beginOptimization();
        defer graph.finalizeOptimization() catch {};
        
        var stats = CompilationStats{};
        const start_time = std.time.milliTimestamp();
        
        // Phase 1: Backend-agnostic optimization
        for (self.passes.items) |pass| {
            if (pass.phase == .optimization and !pass.backend_specific) {
                const nodes_before = graph.countValidNodes();
                try pass.run(graph);
                const nodes_after = graph.countValidNodes();
                
                stats.nodes_eliminated += @intCast(nodes_before - nodes_after);
                stats.passes_run += 1;
                
                // Optional: Compact memory if fragmentation is high
                if (graph.getFragmentationRatio() > 0.3) {
                    try graph.compactMemory();
                }
            }
        }
        
        // Phase 2: Backend-specific lowering
        for (self.passes.items) |pass| {
            if (pass.phase == .lowering) {
                try pass.run(graph);
                stats.passes_run += 1;
            }
        }
        
        // Phase 3: Scheduling
        for (self.passes.items) |pass| {
            if (pass.phase == .scheduling) {
                try pass.run(graph);
                stats.passes_run += 1;
            }
        }
        
        stats.compile_time_ms = @intCast(std.time.milliTimestamp() - start_time);
        
        // Phase 4: Create final executable artifact
        return try self.finalizeCompilation(graph, outputs, stats);
    }
    
    fn finalizeCompilation(
        self: *CompilationPipeline,
        graph: *Graph,
        outputs: []const NodeId,
        stats: CompilationStats,
    ) !CompiledGraph {
        // Build execution order
        try graph.toposort();
        const execution_order = try self.allocator.dupe(ExecutionNode, graph.linearized_graph.?);
        
        // Create memory plan
        const memory_plan = try createMemoryPlan(self.allocator, graph, execution_order);
        
        return CompiledGraph{
            .allocator = self.allocator,
            .execution_order = execution_order,
            .memory_plan = memory_plan,
            .original_graph = graph,
            .output_nodes = try self.allocator.dupe(NodeId, outputs),
            .backend = self.backend_config.backend_type,
            .compilation_stats = stats,
        };
    }
};
```

// Memory planning for backends
pub const MemoryPlan = struct {
    total_memory: usize,
    allocations: []const BufferAllocation,
    liveness_intervals: []const LivenessInterval,
};
// ↑ Complete memory allocation strategy for the entire graph
// Why needed: Optimal memory usage, prevents out-of-memory errors
// Contains: Total memory required + specific allocation plan
// Used by: Backends to pre-allocate memory pools
// Benefit: Enables memory reuse between tensors with non-overlapping lifetimes

pub const BufferAllocation = struct {
    offset: usize,
    size: usize,
    alignment: usize,
    device: Device,
};
// ↑ Specific memory allocation for a tensor
// Why needed: Precise control over memory layout for performance
// Used by: Memory planning to pack tensors efficiently
// Example: Two tensors with non-overlapping lifetimes can share same buffer

pub const LivenessInterval = struct {
    node_id: NodeId,
    output_idx: u8,
    start: u32,  // Execution step
    end: u32,    // Last use
    size: usize,
};
// ↑ Lifetime information for each tensor output
// Why needed: Determines when tensors can be safely freed/reused
// Algorithm: Built by analyzing when each tensor is produced and last consumed
// Used by: Memory planning to minimize peak memory usage
// Critical for: Large models that would otherwise exceed memory limits

// Graph statistics for analysis
pub const GraphStats = struct {
    node_count: u32,
    edge_count: u32,
    memory_usage: usize,
    max_parallel_nodes: u32,
    critical_path_length: u32,
    op_type_counts: std.EnumArray(OpType, u32),
};
// ↑ Comprehensive analysis of graph characteristics
// Why needed: Performance analysis, debugging, optimization decisions
// Used by: Profiling tools, compiler heuristics, capacity planning
// Example: High critical_path_length suggests pipeline optimization needed

// Source identification for tensor operations
pub const NodeSource = struct {
    node_id: NodeId,
    output_idx: u8,
    shape_tracker: TrackerId,
};
// ↑ Complete identification of a tensor source
// Why needed: Nodes can have multiple outputs, need precise identification
// Used by: Graph traversal, execution planning, shape inference
// Example: Split operation produces multiple outputs, each needs separate tracking

// Device abstraction for backend support
pub const Device = enum {
    cpu,
    cuda,
    metal,
    vulkan,
    custom,
};
// ↑ Target device for computation and memory allocation
// Why needed: Different devices have different capabilities and memory spaces
// Used by: Memory planning, kernel selection, data transfer decisions
// Enables: Heterogeneous computing across multiple device types

// Node metadata for compiler passes
pub const NodeMetadata = struct {
    name: ?[]const u8 = null,
    debug_info: ?DebugInfo = null,
    backend_data: ?*anyopaque = null,  // Backend-specific data
    compile_hints: CompileHints = .{},
};
// ↑ Additional information attached to nodes for optimization and debugging
// Why needed: Compiler passes need extra context, debugging needs source info
// name: Human-readable identifier for debugging
// debug_info: Source location for error reporting
// backend_data: Backend-specific optimization data (e.g., kernel handles)
// compile_hints: User/compiler guidance for optimization decisions

pub const DebugInfo = struct {
    source_file: []const u8,
    line: u32,
    column: u32,
};
// ↑ Source code location for debugging
// Why needed: Error messages can point to original source code
// Used by: Debuggers, error reporting, profiling tools

pub const CompileHints = struct {
    can_fuse: bool = true,
    prefer_memory: bool = false,
    vectorize: bool = true,
    parallelize: bool = true,
};
// ↑ Optimization guidance for compiler passes
// Why needed: User or heuristics can guide optimization decisions
// can_fuse: Whether this node can be fused with others
// prefer_memory: Favor memory efficiency over speed
// vectorize: Enable SIMD optimizations
// parallelize: Allow parallel execution
// Used by: Fusion passes, vectorization, scheduling decisions
```

#### Real-World Usage Examples

**Example 1: Execution with Memory Management**
```zig
// 1. Graph construction creates computation
var graph = try Graph.init(allocator);
const a = try graph.tensor(&.{1024, 768});  // Creates input node
const w = try graph.parameter(&.{768, 512}); // Creates parameter node  
const result = try a.matmul(w);              // Creates matmul → decomposed to primitives

// 2. Mark important tensors
graph.markNoDelete(&.{w});          // Preserve parameter across runs
graph.markToRetrieve(result, 0, result.shape_tracker); // Mark output for retrieval

// 3. Compilation builds execution plan
const compiled = try graph.compile(&.{result});
// - linearized_graph: [input_load, param_load, multiply, reduce_sum]  
// - consumers_map: {(input,0): 1, (param,0): 1, (mult,0): 1}
// - memory_plan: Calculates tensor lifetimes, optimal buffer reuse

// 4. Execution follows the plan
try graph.execute();
// - Walks linearized_graph in order
// - Uses consumers_map to free tensors when ref count hits 0
// - Follows memory_plan for optimal allocation
```

**Example 2: Compiler Pass Using Graph Manipulation**
```zig
// CSE (Common Subexpression Elimination) pass
fn eliminateCommonSubexpressions(graph: *Graph, remap: anytype) !void {
    var seen = std.HashMap(NodeSignature, NodeId).init(allocator);
    
    for (graph.nodes.items) |node| {
        const signature = computeSignature(graph, node.id);
        
        if (seen.get(signature)) |existing| {
            // Found duplicate - use graph manipulation to merge
            graph.moveOutgoingEdges(node.id, existing);  // Transfer consumers
            remap.updateReferences(node.id, existing);    // Update external refs
            graph.safeRemoveNode(node.id, 0);            // Remove duplicate
        } else {
            try seen.put(signature, node.id);
        }
    }
}
// The manipulation functions ensure graph invariants are maintained
```

**Example 3: Backend Memory Planning**
```zig
// Backend uses memory plan for optimal allocation
fn executeWithMemoryPlan(compiled: *CompiledGraph) !void {
    // 1. Pre-allocate memory pool based on plan
    const memory_pool = try allocator.alloc(u8, compiled.memory_plan.total_memory);
    
    // 2. Map each tensor to its buffer
    var tensor_buffers = std.HashMap(NodeId, []u8).init(allocator);
    for (compiled.memory_plan.allocations) |alloc| {
        const buffer = memory_pool[alloc.offset..alloc.offset + alloc.size];
        // Multiple tensors may share same buffer at different times
        try tensor_buffers.put(alloc.node_id, buffer);
    }
    
    // 3. Execute with pre-planned memory
    for (compiled.execution_order) |exec_node| {
        const inputs = gatherInputTensors(exec_node.sources, &tensor_buffers);
        const output_buffer = tensor_buffers.get(exec_node.node_id).?;
        try executeKernel(exec_node.node_id, inputs, output_buffer);
    }
}
```

**Example 4: Dynamic Dimensions in Practice**
```zig
// User code with dynamic batch size
const batch = try graph.symbolic("batch");
const input = try graph.tensor(&.{batch, 768});  // Shape: [batch, 768]
const output = try model.forward(input);         // Shape: [batch, 256]

// At runtime, resolve dynamic dimensions  
graph.setDynamicDim("batch", 32);  // Batch size becomes 32
try graph.resolveDynamicDims(input);
// input shape tracker now resolves to [32, 768]
// All dependent nodes get updated shapes automatically
```

**Example 5: Multiple Data Types and Mixed Precision**
```zig
// Mixed precision training setup
var graph = try Graph.init(allocator);
graph.setDefaultDataType(.f16);  // Use half precision by default

// Create model with different precisions
const input = try graph.createParameter(&.{32, 768}, .f16, "input");
const weights = try graph.createParameter(&.{768, 512}, .f16, "weights");
const bias = try graph.createParameter(&.{512}, .f32, "bias");  // Higher precision for bias

// Matmul in f16 for speed
const matmul_result = try input.matmul(weights);  // Result: f16

// Promote to f32 for bias addition (more accurate)
const promoted = try graph.insertTypeCast(matmul_result, .f32);
const with_bias = try promoted.add(bias);  // Result: f32

// Quantization for inference
const quantized = try graph.createQuantizeNode(with_bias, .i8, 0.1, 128);
// Scale: 0.1, zero_point: 128 for symmetric quantization

// Type promotion happens automatically in operations
const a_f16 = try graph.createConstant(@as(f16, 3.14), .f16);
const b_f32 = try graph.createConstant(@as(f32, 2.71), .f32);
const result = try a_f16.add(b_f32);  // Automatically promotes to f32

// Validation catches incompatible operations
const complex_num = try graph.createConstant(Complex64{.real = 1.0, .imag = 2.0}, .c64);
// This would error: complex + integer not supported
// const invalid = try complex_num.add(try graph.createConstant(5, .i32));
```

**Example 6: Quantization Pipeline**
```zig
// Full precision training → quantized inference pipeline
fn createQuantizedModel(graph: *Graph, trained_weights: NodeId) !NodeId {
    // 1. Trained weights are in f32
    const f32_weights = trained_weights;  // Shape: [1024, 512], dtype: f32
    
    // 2. Calibrate quantization parameters (simplified)
    const scale = computeQuantizationScale(f32_weights);  // e.g., 0.05
    const zero_point = 128;  // Symmetric quantization center
    
    // 3. Quantize weights to int8
    const i8_weights = try graph.createQuantizeNode(f32_weights, .i8, scale, zero_point);
    
    // 4. During inference, operations use quantized arithmetic
    const input_i8 = try graph.createParameter(&.{32, 1024}, .i8, "quantized_input");
    
    // 5. Quantized matrix multiplication (much faster on specialized hardware)
    const qmatmul = try input_i8.qmatmul(i8_weights, scale, zero_point);
    
    // 6. Dequantize only when necessary (e.g., for activation functions)
    const f32_for_activation = try graph.createDequantizeNode(qmatmul, .f32, scale, zero_point);
    const activated = try f32_for_activation.relu();
    
    // 7. Quantize again for next layer
    const output_i8 = try graph.createQuantizeNode(activated, .i8, scale, zero_point);
    
    return output_i8;
}

// Memory savings: i8 uses 4x less memory than f32
// Speed gains: Specialized int8 kernels are much faster
// Accuracy: Careful calibration maintains model quality
```

**Example 7: Data Type Inference and Validation**
```zig
// Type inference in action
fn buildModelWithInference(graph: *Graph) !NodeId {
    // Input data types are explicit
    const input = try graph.createParameter(&.{32, 784}, .f32, "input");
    const weights1 = try graph.createParameter(&.{784, 256}, .f16, "w1");
    
    // Graph infers output types automatically
    const mm1 = try input.matmul(weights1);
    // graph.inferOutputDataType(.matmul, &.{.f32, .f16}) returns .f32 (promotion rule)
    
    const bias1 = try graph.createParameter(&.{256}, .f32, "b1");
    const layer1 = try mm1.add(bias1);  // f32 + f32 = f32
    
    // Validation prevents incompatible operations
    try graph.validateDataTypeCompatibility(.sqrt, &.{.i32});  // Error: sqrt doesn't support integers
    
    // Safe operations are allowed
    const int_input = try graph.createParameter(&.{10}, .i32, "indices");
    const float_weights = try graph.createParameter(&.{100, 64}, .f32, "embeddings");
    const embedded = try int_input.gather(float_weights);  // i32 indices → f32 values (OK)
    
    return layer1;
}
```

#### F. Concurrency and Thread Safety
- **Construction phase**: Single-threaded only
- **Compiled phase**: Read-only access is thread-safe
- **Execution**: Multiple threads can execute different subgraphs
- **No concurrent mutation**: Graph structure is immutable after compilation

#### G. Dependency Graph

```
Graph (owner)
  ├── SymbolicPool (owned)
  ├── ShapeTracker[] (owned via arena)
  ├── TensorData (owned, explicit allocator)
  └── Node[] (owned via arena)
```

#### H. Invariants and Contracts

**Core Graph Invariants:**
- **Acyclic**: Graph must not contain cycles at any time
- **Node ID uniqueness**: Each NodeId maps to exactly one Node
- **Edge consistency**: All edges connect existing nodes
- **Shape tracker validity**: All TrackerIds reference valid ShapeTrackers
- **Input arity**: Node input count matches operation requirements

**Execution Invariants:**
- **Topological ordering**: Execution follows dependency order
- **Tensor lifetime**: Tensors exist when consumers execute
- **Memory consistency**: Buffer allocations don't overlap active tensors
- **Dynamic dimension resolution**: All symbolic dims resolved before execution
- **Multi-output indexing**: Output indices are within node's output count

**Compiler Pass Contracts:**
- **Graph validity preservation**: Passes maintain graph invariants
- **Semantic equivalence**: Optimizations preserve computational semantics
- **Reference integrity**: Node remapping updates all external references
- **No orphaned nodes**: Removed nodes have no remaining consumers
- **Schedule dependency respect**: Explicit ordering constraints maintained

**Backend Contracts:**
- **Device consistency**: Tensors allocated on correct device
- **Memory alignment**: Buffer allocations respect device requirements
- **Kernel compatibility**: Operations supported by target backend
- **Synchronization**: Cross-device transfers properly synchronized

**Thread Safety Contracts:**
- **Construction exclusivity**: Only one thread modifies graph structure
- **Compilation atomicity**: Compilation produces consistent state
- **Execution isolation**: Parallel execution on disjoint subgraphs only
- **Tensor ownership**: Clear ownership semantics for tensor data

#### I. Lifecycle and Teardown

```zig
// 1. Create graph
var graph = try Graph.init(allocator);

// 2. Build computation (mutable phase)
const a = try graph.constant(5.0);
const b = try graph.variable("x");
const c = try graph.add(a, b);

// 3. Compile (transition to immutable)
const compiled = try graph.compile(&.{c});

// 4. Execute (read-only)
const result = try backend.execute(compiled);

// 5. Cleanup
graph.deinit(); // Frees everything
```

---


## Component Integration Patterns (Not Runtime Contracts)

**NOTE**: These are **design documentation patterns**, not runtime interfaces. They document how components should interact to prevent architectural errors during implementation.

### **Why Document These Patterns?**

1. **Prevent Integration Bugs**: Clear component boundaries prevent mixing responsibilities
2. **Guide Implementation**: Shows developers how components should call each other
3. **Maintain Invariants**: Documents the assumptions each component makes
4. **Debug Architecture Issues**: When bugs occur, check if these patterns are followed

### **Integration Pattern 1: Tensor → Graph (High-Level to Primitive Decomposition)**

```zig
// DESIGN PATTERN (not runtime interface):
// Tensor layer decomposes operations immediately to primitives

// Implementation example:
pub fn matmul(self: Tensor, other: Tensor) !Tensor {
    // Tensor layer handles decomposition
    const a_reshaped = try self.reshape(&.{M, 1, K});     // View operation
    const b_reshaped = try other.reshape(&.{1, N, K});    // View operation  
    const multiplied = try a_reshaped.multiply(b_reshaped); // → graph.createNode(.multiply, ...)
    return multiplied.reduce_sum(&.{2});                  // → graph.createNode(.reduce_sum, ...)
}

// ANTI-PATTERN to avoid:
// pub fn matmul(self: Tensor, other: Tensor) !Tensor {
//     return graph.createNode(.matmul, &.{self.node_id, other.node_id}); // ❌ matmul is NOT a primitive!
// }
```

**WHO USES THIS PATTERN:**
- **Implementers** of Tensor API methods
- **Code reviewers** checking for proper decomposition
- **Debuggers** tracking why certain primitives appear in graphs

### **Integration Pattern 2: Graph → ShapeTracker (Node Creation with Shape Inference)**

```zig
// DESIGN PATTERN: Every graph node has a corresponding shape tracker

// Implementation example:
pub fn createNode(self: *Graph, op_type: OpType, inputs: []const NodeId) !NodeId {
    // 1. Validate inputs exist
    for (inputs) |input_id| {
        _ = self.getNode(input_id) orelse return error.NodeNotFound;
    }
    
    // 2. Create shape tracker for this operation
    const input_shapes = try self.arena.child_allocator.alloc(TrackerId, inputs.len);
    for (inputs, 0..) |input_id, i| {
        input_shapes[i] = self.getNode(input_id).?.shape_tracker;
    }
    
    const output_shape_tracker = try self.inferShapeForOp(op_type, input_shapes);
    
    // 3. Create node with shape tracker
    const node_id = self.next_node_id;
    self.next_node_id = self.next_node_id.next();
    
    try self.nodes.append(self.arena.child_allocator, Node{
        .id = node_id,
        .op_type = op_type,
        .inputs = try self.arena.child_allocator.dupe(NodeId, inputs);
        .shape_tracker = output_shape_tracker,
    });
    
    return node_id;
}

// ANTI-PATTERN to avoid:
// const node_id = self.next_node_id;
// try self.nodes.append(self.arena.child_allocator, Node{...}); // ❌ No shape inference!
```

**WHO USES THIS PATTERN:**
- **Graph.createNode() implementation**
- **Shape inference system implementers**
- **Compiler pass writers** who need to create new nodes

### **Integration Pattern 3: Graph → SymbolicPool (Expression Management)**

```zig
// DESIGN PATTERN: All symbolic expressions go through the pool for deduplication

// Implementation example:
pub fn createSymbolicDim(self: *Graph, name: []const u8) !ExprId {
    // Use pool for deduplication and canonicalization
    return self.symbolic_pool.variable(name);
}

pub fn createShapeTracker(self: *Graph, dims: []const i64) !TrackerId {
    var expr_dims = try self.arena.child_allocator.alloc(ExprId, dims.len);
    for (dims, 0..) |dim, i| {
        if (dim == -1) {
            // Dynamic dimension - use symbolic pool
            expr_dims[i] = try self.symbolic_pool.variable('a' + @as(u8, @intCast(i)));
        } else {
            // Concrete dimension - use symbolic pool for consistency
            expr_dims[i] = try self.symbolic_pool.constant(@intCast(dim));
        }
    }
    
    const tracker_id = self.next_tracker_id;
    self.next_tracker_id = self.next_tracker_id.next();
    
    try self.shape_trackers.append(self.arena.child_allocator, ShapeTracker{
        .dims = expr_dims,
        .strides = calculateStrides(expr_dims),
        // ...
    });
    
    return tracker_id;
}

// ANTI-PATTERN to avoid:
// const expr = Expression.variable(name); // ❌ Bypasses pool deduplication!
```

**WHO USES THIS PATTERN:**
- **Shape system implementers**
- **Dynamic dimension handling code**
- **Symbolic expression optimization passes**

### **Integration Pattern 4: Graph → Tensor Storage (Simple and Explicit)**

```zig
// DESIGN PATTERN: Tensor data now stored in CompiledGraph.storage (DataStorage)

// Implementation example:
pub fn createParameter(self: *Graph, shape: []const i64, dtype: DataType, name: ?[]const u8) !TensorHandle {
    // 1. Create graph node
    const node_id = try self.addNode(.function, &.{});  // Parameters use function nodes
    
    // 2. Store shape metadata in the node
    const tracker_id = try self.createShapeTracker(shape);
    self.getNode(node_id).?.shape_tracker = tracker_id;
    
    // 3. Set metadata for debugging and parameter loading
    if (name) |param_name| {
        try self.setNodeMetadata(node_id, NodeMetadata{
            .name = param_name,
            .is_parameter = true,
        });
    }
    
    // 4. Mark as parameter (for training)
    try self.no_delete.put(self.arena.child_allocator, node_id, {});
    
    // Note: Actual tensor data loaded during execution via DataStorage
    return TensorHandle{ .graph = self, .node_id = node_id };
}

// No need for special reset - tensors persist naturally
// Parameters marked in no_delete set remain during optimization

// ANTI-PATTERN to avoid:
// pub fn createParameter(...) !TensorHandle {
//     const node_id = try self.addNode(.variable, &.{});
//     // ❌ Forgot to create tensor data - node has no data!
//     return TensorHandle{ .graph = self, .node_id = node_id };
// }
```

**WHO USES THIS PATTERN:**
- **Parameter creation code**
- **Tensor operation implementations**
- **All code that creates tensors with actual data**

### **When Are These Patterns Actually Used?**

**During Implementation:**
```zig
// Developer implementing new tensor operation
pub fn layerNorm(self: Tensor, epsilon: f32) !Tensor {
    // ✅ Follows Tensor→Graph pattern: decomposes to primitives
    const mean = try self.reduce_mean(&.{-1});
    const variance = try self.sub(mean).pow(2).reduce_mean(&.{-1});
    const std_dev = try variance.add(epsilon).sqrt();
    return self.sub(mean).div(std_dev);
    // Each operation creates primitive graph nodes
}
```

**During Code Review:**
```zig
// ❌ Code reviewer catches this architectural error:
pub fn softmax(self: Tensor, axis: i32) !Tensor {
    return self.graph.createNode(.softmax, &.{self.node_id}); // Wrong! softmax isn't primitive
}

// ✅ Corrected to follow decomposition pattern:
pub fn softmax(self: Tensor, axis: i32) !Tensor {
    const max_vals = try self.reduce_max(&.{axis}, .{ .keep_dims = true });
    const shifted = try self.sub(max_vals);
    const exp_vals = try shifted.exp();
    const sum_exp = try exp_vals.reduce_sum(&.{axis}, .{ .keep_dims = true });
    return exp_vals.div(sum_exp);
}
```

**During Debugging:**
```zig
// When graph has unexpected nodes, check integration patterns
fn debugGraph(graph: *Graph) void {
    for (graph.nodes.items) |node| {
        switch (node.op_type) {
            .matmul, .softmax, .layer_norm => {
                // ❌ These shouldn't appear - they should decompose!
                std.log.err("Non-primitive operation found: {}", .{node.op_type});
            },
            .add, .multiply, .reduce_sum => {
                // ✅ These are correct primitives
            },
            else => {},
        }
    }
}
```

### **Are These Contracts Necessary?**

**YES, for documentation and prevention of architectural errors:**
- **Architecture enforcement**: Prevents mixing component responsibilities
- **Implementation guidance**: Shows correct interaction patterns
- **Bug prevention**: Common errors (like non-primitive operations in graph) are caught early
- **Code review**: Reviewers can check against documented patterns

**NO, they're not runtime interfaces:**
- No generated code or runtime checking
- No performance overhead
- No compile-time enforcement

**Alternative approach**: Could use Zig's compile-time features for enforcement:
```zig
fn ensurePrimitive(comptime op: OpType) void {
    const primitive_ops = .{.add, .multiply, .reduce_sum, .reciprocal, .sqrt, .sin, .exp2, .log2, .negate, .reduce_max, .contiguous, .constant, .variable, .input, .function};
    inline for (primitive_ops) |primitive| {
        if (op == primitive) return;
    }
    @compileError("Operation " ++ @tagName(op) ++ " is not a primitive and should be decomposed");
}
```

The patterns are **design documentation** that prevents architectural mistakes and guides correct implementation!

---

## Use Case Validation

### Use Case 1: Create and Execute Computation

```zig
// 1. Create tensors and operations
var graph = try Graph.init(allocator);
defer graph.deinit();

const a = try graph.tensor(&.{32, 768});
const w = try graph.parameter(&.{768, 256});
const b = try graph.parameter(&.{256});

// 2. Build computation (decomposition happens here)
const y = try a.matmul(w).add(b).relu();

// 3. Compile graph
const compiled = try graph.compile(&.{y});

// 4. Execute with inputs
const inputs = try createInputs();
const outputs = try backend.execute(compiled, inputs);

// Validation:
// ✓ Clean component interaction
// ✓ Memory managed by arena
// ✓ Errors propagated clearly
```

### Use Case 2: Gradient Computation

```zig
// 1. Forward pass
const loss = try model.forward(input, target);

// 2. Backward pass
var autodiff = try AutodiffPass.init(&graph);
try autodiff.differentiate(loss.node_id, model.parameters());

// 3. Access gradients
for (model.parameters()) |param| {
    const grad = graph.data_store.getGradient(param) orelse continue;
    try optimizer.updateParameter(param, grad);
}

// Validation:
// ✓ Gradients stored separately
// ✓ Parameters persist across updates
// ✓ Clean separation of concerns
```

### Use Case 3: Parallel Execution

```zig
// 1. Compile once
const compiled = try graph.compile(outputs);

// 2. Execute in parallel
var threads: [4]std.Thread = undefined;
for (&threads, 0..) |*thread, i| {
    thread.* = try std.Thread.spawn(.{}, executeWorker, .{
        compiled,
        inputs[i],
        &results[i],
    });
}

// 3. Wait for completion
for (threads) |thread| {
    thread.join();
}

// Validation:
// ✓ Compiled graph is thread-safe
// ✓ No data races on read-only access
// ✓ Results isolated per thread
```

---

## Implementation Patterns

### Error Handling Pattern

```zig
pub const GraphError = error{
    NodeNotFound,
    InvalidInput,
    ShapeIncompatible,
    SymbolicResolutionFailed,
    OutOfMemory,
};

pub const ShapeError = error{
    BroadcastIncompatible,
    InvalidReshape,
    DimensionMismatch,
    SymbolicDimensionUnresolved,
};

// Detailed error context
pub fn createNodeWithContext(
    graph: *Graph,
    op: OpType,
    inputs: []const NodeId,
) GraphError!NodeId {
    // Validate inputs exist
    for (inputs, 0..) |input_id, i| {
        _ = graph.getNode(input_id) orelse {
            std.log.err("Input {} (node {}) not found for {} operation", .{i, input_id, @tagName(op)});
            return error.NodeNotFound;
        };
    }
    
    // Validate shapes are compatible
    const shapes = try graph.arena.child_allocator.alloc(TrackerId, inputs.len);
    for (inputs, 0..) |input_id, i| {
        shapes[i] = graph.getNode(input_id).?.shape_tracker;
    }
    
    const output_shape = graph.inferShape(op, shapes) catch |err| {
        std.log.err("Shape inference failed for {}: {}", .{@tagName(op), err});
        return error.ShapeIncompatible;
    };
    
    // Create node
    return graph.createNodeInternal(op, inputs, output_shape);
}
```

### Memory Planning Pattern

```zig
pub fn planMemory(graph: *CompiledGraph) !MemoryPlan {
    var plan = MemoryPlan.init(graph.allocator);
    
    // Build liveness intervals
    for (graph.nodes) |node, idx| {
        const interval = LivenessInterval{
            .node_id = node.id,
            .start = idx,
            .end = findLastUse(graph, node.id),
            .size = calculateTensorSize(node.shape_tracker),
        };
        try plan.intervals.append(interval);
    }
    
    // Greedy allocation with reuse
    var active = std.ArrayList(LivenessInterval).init(graph.allocator);
    var free_list = std.ArrayList(BufferAllocation).init(graph.allocator);
    
    // Sort by start time
    std.sort.heap(LivenessInterval, plan.intervals.items, {}, compareStart);
    
    for (plan.intervals.items) |interval| {
        // Free expired allocations
        var i: usize = 0;
        while (i < active.items.len) {
            if (active.items[i].end < interval.start) {
                try free_list.append(active.items[i].allocation);
                _ = active.swapRemove(i);
            } else {
                i += 1;
            }
        }
        
        // Allocate buffer (reuse if possible)
        const allocation = try allocateBuffer(&free_list, interval.size);
        interval.allocation = allocation;
        try active.append(interval);
    }
    
    return plan;
}
```

### Graph Manipulation Pattern

```zig
// Pattern for node replacement during optimization
pub fn replaceNodePattern(
    graph: *Graph,
    old_node: NodeId,
    new_node: NodeId,
    remap: anytype,
) !void {
    // Transfer all outgoing edges
    graph.moveOutgoingEdges(old_node, new_node);
    
    // Update all external references
    remap.updateReferences(old_node, new_node);
    
    // Transfer metadata
    if (graph.no_delete.contains(old_node)) {
        graph.no_delete.remove(old_node);
        graph.no_delete.insert(new_node);
    }
    
    // Safe removal (only if no remaining references)
    graph.safeRemoveNode(old_node, 0);
}

// Pattern for Common Subexpression Elimination (CSE)
pub fn eliminateDuplicateNode(
    graph: *Graph,
    duplicate: NodeId,
    original: NodeId,
    remap: anytype,
) !void {
    // Redirect all consumers of duplicate to original
    graph.moveOutgoingEdges(duplicate, original);
    
    // Update external tracking
    remap.updateReferences(duplicate, original);
    
    // Remove the duplicate
    graph.removeNode(duplicate);
}

// Pattern for operation fusion
pub fn fuseOperations(
    graph: *Graph,
    nodes: []const NodeId,
    fused_op: OpType,
) !NodeId {
    // Collect all unique inputs
    var inputs = std.ArrayList(NodeId).init(graph.arena.child_allocator);
    for (nodes) |node| {
        const sources = graph.getSources(node);
        for (sources) |src| {
            if (!inputs.contains(src.node_id)) {
                try inputs.append(src.node_id);
            }
        }
    }
    
    // Create fused node
    const fused = try graph.createNode(fused_op, inputs.items);
    
    // Transfer all consumers from original nodes
    for (nodes) |node| {
        graph.moveOutgoingEdges(node, fused);
    }
    
    // Schedule dependency to maintain order
    if (nodes.len > 0) {
        graph.addScheduleDependency(inputs.items[inputs.items.len - 1], fused);
    }
    
    // Remove original nodes
    for (nodes) |node| {
        graph.safeRemoveNode(node, 0);
    }
    
    return fused;
}
```

### Fusion Detection Pattern

```zig
pub fn detectFusionOpportunities(graph: *Graph) ![]FusionCandidate {
    var candidates = std.ArrayList(FusionCandidate).init(graph.allocator);
    
    // Pattern: Elementwise chains (add→multiply→add)
    for (graph.nodes.items) |node| {
        if (!isElementwise(node.op_type)) continue;
        
        var chain = std.ArrayList(NodeId).init(graph.allocator);
        try chain.append(node.id);
        
        // Follow single-use elementwise operations
        var current = node.id;
        while (true) {
            const consumers = graph.getConsumers(current);
            if (consumers.len != 1) break;
            
            const consumer = graph.getNode(consumers[0]) orelse break;
            if (!isElementwise(consumer.op_type)) break;
            if (!canFuseShapes(graph.getTracker(current), graph.getTracker(consumer.id))) break;
            
            try chain.append(consumer.id);
            current = consumer.id;
        }
        
        if (chain.items.len > 1) {
            try candidates.append(.{
                .type = .elementwise_chain,
                .nodes = try chain.toOwnedSlice(),
            });
        }
    }
    
    return candidates.toOwnedSlice();
}
```

---

## Performance Characteristics

### Operation Complexity

| Operation | Time Complexity | Space Complexity | Notes |
|-----------|----------------|------------------|-------|
| Node Creation | O(1) | O(1) | Amortized with arena |
| Edge Addition | O(1) | O(1) | Append to edge list |
| Shape Inference | O(d) | O(d) | d = number of dimensions |
| Symbolic Simplify | O(n) | O(1) | n = expression nodes, with caching |
| CSE Pass | O(n²) | O(n) | n = graph nodes |
| Fusion Detection | O(n·m) | O(n) | m = avg consumers per node |
| Memory Planning | O(n log n) | O(n) | Interval scheduling |
| Execution | O(ops) | O(memory) | Depends on operation types |

### ⚠️ Critical Algorithm Optimizations for Large Graphs

**Problem**: With only ~12 primitive operations, deep learning graphs can grow to millions of nodes. Naive implementations of key algorithms become O(n²) bottlenecks.

**Most Critical Optimizations Needed:**

#### 1. Consumer Tracking: O(n²) → O(1)
```zig
// CRITICAL: Replace edge traversal with adjacency lists
consumer_lists: std.AutoHashMapUnmanaged(NodeId, std.ArrayListUnmanaged(NodeId)) = .{},

pub fn getConsumers(self: *Graph, node: NodeId) []const NodeId {
    return self.consumer_lists.get(node) orelse &.{}; // O(1) vs O(edges)
}
```

#### 2. Incremental Topological Sort: O(V+E) → O(k)
```zig
// CRITICAL: Cache topo sort, only recompute affected regions
pub const IncrementalTopoSort = struct {
    sorted_nodes: std.ArrayListUnmanaged(NodeId) = .{},
    dirty_region: ?struct { start: u32, end: u32 } = null,
    
    pub fn markDirty(self: *IncrementalTopoSort, position: u32) void {
        // O(k) recomputation where k = size of affected region
    }
};
```

#### 3. Structure-of-Arrays for Cache Performance
```zig
// CRITICAL: Improve cache locality for graph traversal
pub const Graph = struct {
    // Hot data (frequently accessed together)
    node_ops: std.ArrayListUnmanaged(OpType) = .{},
    node_input_starts: std.ArrayListUnmanaged(u32) = .{},
    node_input_counts: std.ArrayListUnmanaged(u8) = .{},
    
    // Cold data (metadata, rarely accessed)
    compilation_metadata: struct {
        node_map: std.AutoHashMapUnmanaged(NodeId, u32) = .{},
        debug_names: std.StringHashMapUnmanaged(NodeId) = .{},
    } = .{},
};
```

#### 4. CSE with Incremental Hashing: O(n²) → O(n log n)
```zig
// CRITICAL: Avoid recomputing signatures for unchanged nodes
pub fn csePassIncremental(graph: *Graph) !void {
    var signature_cache = std.HashMap(NodeId, u64).init(graph.arena.child_allocator);
    
    // Only recompute signatures for modified nodes
    for (graph.modified_nodes.items) |node_id| {
        const signature = computeSignatureIncremental(graph, node_id);
        try signature_cache.put(node_id, signature);
    }
}
```

**Target Performance for 1M+ Node Graphs:**
- **Consumer lookup**: O(1) with adjacency lists
- **Topological sort**: O(k) incremental updates where k << n  
- **CSE**: O(n log n) with signature caching
- **Memory planning**: O(n) linear-time greedy allocation
- **Total compilation**: < 2 seconds for 1M nodes

### Memory Layout

```
Graph Memory Layout:
┌────────────────────┐
│   Arena Header     │ 16 bytes
├────────────────────┤
│   Node Array       │ n * 48 bytes (approx)
├────────────────────┤
│   Edge Array       │ e * 8 bytes  
├────────────────────┤
│   Shape Trackers   │ t * 64 bytes (approx)
├────────────────────┤
│   Symbolic Pool    │ Variable size
├────────────────────┤
│   String Data      │ Variable size
└────────────────────┘

Typical memory usage for 1000-node graph: ~200KB
```

### Cache Performance

- **Node traversal**: Sequential access pattern (cache-friendly)
- **Edge lookup**: Random access (potential cache misses)
- **Shape inference**: Sequential access to dimensions
- **Symbolic operations**: Hash table lookups (unpredictable)

---

## Testing Strategy

### Unit Testing

```zig
test "symbolic expression interning" {
    var arena = std.heap.ArenaAllocator.init(testing.allocator);
    defer arena.deinit();
    
    var pool = try SymbolicPool.init(arena.child_allocator);
    
    // Same expressions should return same ID
    const a = try pool.variable("batch_size");
    const b = try pool.constant(32);
    const expr1 = try pool.binary(.multiply, a, b);
    const expr2 = try pool.binary(.multiply, a, b);
    
    try testing.expectEqual(expr1, expr2);
}

test "shape broadcasting rules" {
    var arena = std.heap.ArenaAllocator.init(testing.allocator);
    defer arena.deinit();
    
    var graph = try Graph.init(arena.child_allocator);
    
    // Test NumPy broadcasting rules
    const test_cases = [_]struct {
        a: []const i64,
        b: []const i64,
        expected: []const i64,
    }{
        .{ .a = &.{5, 1}, .b = &.{1, 6}, .expected = &.{5, 6} },
        .{ .a = &.{3, 1, 4}, .b = &.{4}, .expected = &.{3, 1, 4} },
        .{ .a = &.{2, 3, 4}, .b = &.{3, 4}, .expected = &.{2, 3, 4} },
    };
    
    for (test_cases) |tc| {
        const result = try graph.broadcastShapes(tc.a, tc.b);
        try testing.expectEqualSlices(i64, tc.expected, result);
    }
}
```

### Integration Testing

```zig
test "end-to-end matrix multiplication" {
    var arena = std.heap.ArenaAllocator.init(testing.allocator);
    defer arena.deinit();
    
    var graph = try Graph.init(arena.child_allocator);
    
    // Create matrices
    const a = try graph.constant(
        &[_][3]f32{{1, 2, 3}, {4, 5, 6}},
        &.{2, 3}
    );
    const b = try graph.constant(
        &[_][2]f32{{7, 8}, {9, 10}, {11, 12}},
        &.{3, 2}
    );
    
    // Matmul decomposes to primitives
    const c = try a.matmul(b);
    
    // Compile and execute
    const compiled = try compileGraph(&graph, .{ .backend = .cpu });
    const result = try executeGraph(compiled, .{});
    
    // Verify result
    const expected = [_][2]f32{{58, 64}, {139, 154}};
    try testing.expectEqual(expected, result.data);
}
```

### Property-Based Testing

```zig
test "fusion preserves semantics" {
    var arena = std.heap.ArenaAllocator.init(testing.allocator);
    defer arena.deinit();
    
    var prng = std.rand.DefaultPrng.init(42);
    const random = prng.random();
    
    // Generate random graph
    var graph = try Graph.init(arena.child_allocator);
    var nodes = std.ArrayList(NodeId).init(arena.child_allocator);
    
    // Create random operations
    for (0..100) |_| {
        const op = random.enumValue(OpType);
        const num_inputs = getNumInputs(op);
        
        var inputs = try arena.child_allocator.alloc(NodeId, num_inputs);
        for (inputs) |*input| {
            if (nodes.items.len > 0 and random.boolean()) {
                input.* = nodes.items[random.intRangeAtMost(usize, 0, nodes.items.len - 1)];
            } else {
                input.* = try graph.constant(random.float(f32), &.{4, 4});
            }
        }
        
        const node = try graph.createNode(op, inputs);
        try nodes.append(node);
    }
    
    // Execute without optimization
    const result1 = try executeUnoptimized(&graph);
    
    // Execute with fusion
    try fusionPass(&graph, arena.child_allocator);
    const result2 = try executeOptimized(&graph);
    
    // Results should be numerically close
    try testing.expectApproxEqAbs(result1, result2, 1e-5);
}
```

---

## Summary

This architecture provides a clean, efficient design for a deep learning framework that:

1. **Follows proven patterns** from Luminal while leveraging Zig's strengths
2. **Maintains clear component boundaries** with well-defined contracts
3. **Handles memory efficiently** through arena allocation
4. **Provides safety** through compile-time and runtime checks
5. **Scales well** from simple operations to complex networks

Each component has a clear purpose, explicit dependencies, and well-defined lifecycle, making the system maintainable and extensible.

---

## Open Design Decisions

### 1. View Operations and Autograd

**Current Design**: View operations (reshape, transpose, slice, expand, permute) do NOT create graph nodes. They only modify ShapeTracker metadata.

**Autograd Challenge**: Gradients through views need special handling (broadcast-sum for expand, scatter for slice, etc.) but there's no node to attach these rules to.

**Solution**: The backward pass synthesizes temporary gradient transformation nodes when needed:
```zig
// During autograd, if gradient flows through a view:
if (hasViewTransform(forward_shape, grad_shape)) {
    // Create temporary nodes for gradient transformation
    const grad_transform = try createGradientViewTransform(forward_shape, grad_shape);
    const transformed_grad = try graph.createNode(grad_transform.op, &.{grad_node});
}
```

### 2. Memory Ownership Model

**Graph Arena**: Owns all graph structure memory (nodes, edges, shape trackers)
- Freed when Graph.deinit() is called
- No individual deallocation needed

**Tensor Allocator**: Owns actual tensor data
- Separate from arena for persistence across graph modifications
- Tensors can outlive graph transformations

**CompiledGraph**: References graph structure, doesn't duplicate
- Backend-specific data in separate allocator
- Execution plans reference original graph nodes

### 3. Multi-Device Execution

**Current**: Single device per tensor (TensorData.device field)

**Future Design**:
- One MemoryPlan per device
- Implicit copy nodes inserted during device assignment pass
- Backend compilers handle device-specific memory allocation

### 4. Concurrency Model

**Graph Construction**: Single-threaded only
- Arena allocators are not thread-safe
- All graph building happens on one thread

**Graph Execution**: Thread-safe after compilation
- Read-only graph structure
- Backends handle their own parallelism

### 5. Custom Operations

**Registration**: Custom ops must provide:
```zig
pub const CustomOpInterface = struct {
    // Memory planning
    output_shapes: *const fn(inputs: []const ShapeTracker) []const ShapeTracker,
    memory_size: *const fn(inputs: []const ShapeTracker) usize,
    
    // Autograd (optional)
    backward: ?*const fn(grad: GradientInfo, inputs: []const NodeId) []const GradientInfo,
    
    // Execution
    execute: *const fn(inputs: []const TensorData, outputs: []TensorData) void,
};
```

### 6. Packed Data Types

**Current Limitation**: Sub-byte types (i4, i2, i1) are byte-padded
- `sizeInBytes()` returns 1 for all sub-byte types
- 4-bit tensors use 2× more memory than optimal

**Future**: Bit-packing implementation with:
- Packed storage format
- Unpacking during computation
- Special accessors for packed data

### 7. Pattern Matching with Views

**Challenge**: View ops can break adjacency of primitive sequences

**Solution**: Pattern matcher ignores view transformations:
```zig
fn matchesPattern(graph: *Graph, start: NodeId, pattern: Pattern) bool {
    // Skip over view metadata when scanning for patterns
    const actual_op = getActualOp(start); // Looks through views
    return pattern.matches(actual_op);
}
```

### 8. Quantization and Mixed Precision

**Backend Override**: Backends can override dtype promotion rules:
```zig
pub const BackendTypeRules = struct {
    promote_table: [DataType.len][DataType.len]DataType,
    preferred_accumulation: DataType = .f32,
};
```

Front-end inserts explicit casts when backend rules differ from defaults.Symbolic Module – Code-Level Review & Design Feedback

(file docs/SYMBOLIC_DESIGN.md as posted)

⸻


✔︎ = solid	⚠︎ = potential issue	❓ = question


1 — High-level architecture

	Observations	Feedback
✔︎ Clear split of concerns – expression pool + SoA storage + interning + simplifier	Good single-responsibility separation; no domain-specific assumptions leak in.	
✔︎ Immutable expressions & ID handles	Matches SymPy inspiration; enables O(1) equality by ID.	
✔︎ Arena-friendly bulk teardown	deinit() drops all lists/hash-maps at once – idiomatic Zig.	


⸻

2 — Core data structures

	Observations	Feedback / Questions
⚠︎ SoA synchronisation risk – expr_types and expr_data must always grow together.  Currently each creation appends to both, but a future refactor might accidentally violate this.	⟶ Add a private helper appendExpr(type, data) that mutations must go through, or wrap both slices in a struct to keep them in lock-step.	
✔︎ ExprId typed enum	Safer than bare u32; nice.	
⚠︎ ExprKey.eql branches	In the .constant arm you compare b == .constant and av == b.constant, but b.constant is only valid if b is .constant.  Same pattern for other variants.	The and short-circuits so this is technically safe, but it’s easy to mis-read.  Consider pattern-matching both sides explicitly for clarity.
❓ Hash stability – Wyhash is seeded with magic 0/1/2 constants depending on variant.  Collisions for different variants with equal payload are unlikely but possible (e.g. 0x1234 in .variable vs .constant).	Acceptable? If not, prepend discriminant byte to the state rather than seeding with small integer.	


⸻

3 — Memory & lifetime

	Observations	Feedback / Questions
⚠︎ Pool lives in Graph arena (per earlier docs) but compiled artefacts may hold ExprIds after Graph is freed.	Re-confirm the lifetime rule here; either copy pool into CompiledGraph or require Graph to outlive compiled graphs.	
⚠︎ intern_map stores ExprKey by value.  Keys have ExprId members which are indices into this pool; that’s fine – but the map itself is freed in deinit().  Good.	Just be sure no other module stores pointers into the map.	
✔︎ freeze() flag	Cheap thread-safety barrier.	


⸻

4 — API surface & correctness

	Observations	Feedback / Questions
✔︎ Comprehensive helpers (createAdd, createMul, …)	Nice ergonomics.	
⚠︎ variable() string interning – relies on internString() which isn’t shown.  Need to guarantee var_names and string_data stay consistent on OOM rollback.	Wrap both inserts in one errdefer block.	
⚠︎ divide() at creation time forbids const / 0 but allows var / 0.  Later evaluate() re-checks and errors.	Good: avoids premature error for symbolic zeros.	
⚠︎ simplifyBinary() overflows use std.math.add/sub/mul which throw Overflow.  You catch return null, silently disabling simplification for overflowing constants.	Consider folding to an overflow sentinel constant (e.g. keep as symbolic) or surface IntegerOverflow to caller – silent failure hides bugs.	
⚠︎ Recursion depth – evaluation & simplification are recursive. Deeply nested (~10⁴) expressions can blow stack.	Either add an explicit depth limit or refactor to iterative DFS for evaluation.	
⚠︎ isNegationOf() helper referenced but not defined (in simplifyBinary .add).	Add implementation or drop optimisation.	
❓ shrinkAndFree() on ArrayListUnmanaged is not part of upstream std (as of Zig 0.12).  Did you intend shrinkAndFreeRetainingCapacity() or custom helper?		
✔︎ Error sets cover arithmetic faults	Good: DivisionByZero, IntegerOverflow, …	
⚠︎ PoolFrozen error only checked in constant(), not in variable() or binary().	Decide whether any mutation is disallowed after freeze(); if so, guard all creation helpers.	


⸻

5 — Simplifier rules

	Observations	Feedback
✔︎ Includes many common identities (x + 0, x * 1, x + x ⇒ 2x, …)	Good baseline.	
⚠︎ Commutativity & canonical order – For add / multiply you don’t currently reorder operands; intern_map will treat a+b and b+a as different keys.	To achieve a unique “canonical form” you must sort IDs (or hash) for commutative ops before interning.	
⚠︎ Min/max identity checks compare against std.math.maxInt/minInt to eliminate.  Only correct for 64-bit shapes; but shapes are non-negative – maybe use semantic bounds (e.g. INT_MAX_DIM).		
❓ Mod simplifications assume Euclidean % semantics (Zig uses mathematical remainder → same sign as dividend).  Verify identities hold for negatives.		


⸻

6 — Concurrency & thread safety

	Observations	Feedback
✔︎ freeze() conceptually ends write phase; read APIs take *const self.	Clear two-phase model.	
⚠︎ evaluateConcurrent() spawns n_threads but uses allocator.alloc(Thread, n_threads) without bounds-checking CPU count; be careful on >64-core boxes.	Clamp to reasonable limit.	
⚠︎ Uses same Bindings instance from all threads (read-only); okay if caller doesn’t mutate.	Document that bindings must be immutable once evaluation starts.	


⸻

7 — Integration with other components

	Observations	Feedback
✔︎ API names (createConstant, DynamicMap alias) match the architecture doc → easy for ShapeTracker & Compiler to depend on this module.		
⚠︎ Need cheap isConst(expr, k) helper for back-end shape codegen; currently isConstant + getConstantValue requires two calls.	Provide single inline helper to avoid double lookup.	
❓ How will ShapeTracker / Graph obtain the canonical zero/one ExprIds created in init()?  Export const ZERO_ID, ONE_ID globals to avoid each caller creating new ones and missing interning hits.		


⸻

Suggested To-Do List (highest-impact first)
	1.	Guarantee SoA alignment – centralise “append expression” logic.
	2.	Canonicalise commutative ops before interning (add, multiply, and_, or_, eq/ne maybe).
	3.	Define lifetime contract (pool vs compiled graph).
	4.	Audit freeze barrier – guard all mutating builders, or rename to sealConstantsOnly() if partial.
	5.	Implement / remove missing helpers (isNegationOf, shrinkAndFree, string interner functions).
	6.	Depth-safe evaluation (iterative fallback or explicit limit).
	7.	Document modulo semantics and max/min identities wrt negative numbers.

Addressing these points will make the Symbolic module robust, thread-safe, and fully idiomatic Zig while preserving the nice memory and performance characteristics you already have.