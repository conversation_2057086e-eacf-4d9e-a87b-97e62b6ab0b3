# Session API Documentation

## Overview

The Session API is <PERSON>ing's high-level interface for tensor operations, designed to provide a PyTorch-like experience while maintaining <PERSON>ig's explicit memory management philosophy. It automatically manages the graph/compile/execute lifecycle, eliminating boilerplate while preserving control over resource allocation.

## Design Philosophy

The Session API follows these core principles:

1. **Explicit Memory Management**: All allocations go through the user-provided allocator
2. **Method Chaining**: Natural syntax like `tensor.add(b).mul(c).relu()`
3. **Automatic Lifecycle**: Graph compilation and execution handled transparently
4. **Backend Flexibility**: Support for different compute backends (CPU, CUDA, Metal)
5. **Execution Modes**: Both eager (immediate) and lazy (deferred) execution

## Basic Usage

### Creating a Session

```zig
const std = @import("std");
const zing = @import("zing");

// Create a session with default settings (CPU backend, eager mode)
var session = try zing.Session.init(allocator);
defer session.deinit();

// Create with specific backend
const config = zing.BackendConfig{ .device = .cuda };
var gpu_session = try zing.Session.initWithBackend(allocator, config);
defer gpu_session.deinit();
```

### Creating Tensors

```zig
// Create tensors filled with specific values
const zeros = try session.zeros(&.{2, 3}, .f32);
const ones = try session.ones(&.{3, 3}, .f32);

// Create from data
const data = [_]f32{1.0, 2.0, 3.0, 4.0};
const tensor = try session.tensor(&.{2, 2}, .f32, data);

// Create placeholder for later input
const input = try session.placeholder(&.{10, 784}, .f32);
```

### Tensor Operations

All operations return new `SessionTensor` objects and can be chained:

```zig
// Basic arithmetic
const sum = try a.add(b);
const diff = try a.subtract(b);
const product = try a.mul(b);
const quotient = try a.divide(b);

// Matrix multiplication
const result = try input.matmul(weights);

// Activations
const activated = try result.relu();

// Shape operations
const reshaped = try tensor.reshape(&.{4, 1});

// Method chaining
const output = try input.matmul(w1).add(b1).relu().matmul(w2).add(b2);
```

### Accessing Results

```zig
// Get computed data as a slice
const output_data = try result.data(f32);
std.debug.print("First element: {}\n", .{output_data[0]});

// Copy data to your own buffer
const owned_data = try result.copy(f32, allocator);
defer allocator.free(owned_data);
```

### Tensor Properties

```zig
const rank = tensor.rank();       // Number of dimensions
const size = tensor.size();       // Total number of elements
const shape = tensor.shape;       // Slice of dimensions
```

## Execution Modes

### Eager Mode (Default)

Operations execute immediately as they're called:

```zig
var session = try Session.init(allocator);
// Operations execute immediately
const c = try a.add(b);  // Executes now
const d = try c.mul(a);  // Executes now
```

### Lazy Mode

Operations build a graph and execute on demand:

```zig
session.setMode(.lazy);

// Operations just build the graph
const c = try a.add(b);  // No execution
const d = try c.mul(a);  // No execution

// Explicit execution
try session.run();

// Or automatic execution when accessing data
const data = try d.data(f32);  // Triggers execution if needed
```

### Mixed Mode Execution

The Session API elegantly handles mixing eager and lazy execution within the same session:

```zig
// Start in eager mode
const a = try session.ones(&.{2, 3}, .f32);
const b = try a.add(a);  // Executes immediately

// Switch to lazy mode
session.setMode(.lazy);
const c = try b.mul(a);  // Builds graph
const d = try c.relu();  // Builds graph

// Access results - automatically compiles and executes what's needed
const result = try d.data(f32);
```

## Automatic Recompilation

The Session API implements intelligent recompilation to handle graph modifications efficiently:

1. **Graph Version Tracking**: Each modification increments a version counter
2. **Output Tracking**: Separates "compiled" vs "pending" outputs
3. **Incremental Compilation**: Only recompiles when necessary
4. **Node Substitution Handling**: Correctly handles optimization passes that modify the graph

This elegant solution ensures:
- No redundant compilations
- Correct handling of graph optimizations
- Efficient incremental graph building
- Seamless mixing of execution modes

## Broadcasting

The Session API supports NumPy-style broadcasting:

```zig
// Broadcasting examples
const a = try session.ones(&.{2, 1}, .f32);    // [2, 1]
const b = try session.ones(&.{1, 3}, .f32);    // [1, 3]
const c = try a.add(b);                        // [2, 3]

// Scalar broadcasting
const scalar = try session.tensor(&.{}, .f32, @as(f32, 2.0));
const scaled = try tensor.mul(scalar);
```

## Error Handling

The Session API uses Zig's error unions for explicit error handling:

```zig
const SessionError = error{
    GraphCompilationFailed,
    ExecutionFailed,
    InvalidShape,
    InvalidDataType,
    IncompatibleTensors,  // Tensors from different sessions
    NotCompiled,
    NotExecuted,
};
```

## Memory Management

The Session API provides automatic memory management while maintaining explicit control:

1. **Shape Memory**: All tensor shapes are tracked and freed on session cleanup
2. **Graph Memory**: Managed by the internal graph structure
3. **Execution Memory**: Allocated based on memory planning, reused across runs
4. **No Hidden Allocations**: All memory goes through your provided allocator

```zig
// Session tracks all allocated shapes
const tensor = try session.ones(&.{100, 100}, .f32);
// Shape memory automatically freed in session.deinit()
```

## Backend Configuration

Configure different compute backends:

```zig
const BackendConfig = struct {
    device: Device = .cpu,
    // Future options:
    // cuda_device_id: ?u32 = null,
    // metal_device_id: ?u32 = null,
};

// CPU backend (default)
var cpu_session = try Session.init(allocator);

// CUDA backend (falls back to CPU if unavailable)
const cuda_config = BackendConfig{ .device = .cuda };
var gpu_session = try Session.initWithBackend(allocator, cuda_config);
```

## Performance Considerations

1. **Lazy Mode**: Better for complex graphs - allows optimization passes
2. **Eager Mode**: Better for debugging and simple operations
3. **Reuse Sessions**: Creating a session has overhead; reuse when possible
4. **Batch Operations**: The graph compiler can optimize batched operations

## Advanced Example: Neural Network

```zig
pub fn neuralNetwork(session: *Session, input: SessionTensor) !SessionTensor {
    // Layer 1: 784 -> 128
    const w1 = try session.ones(&.{784, 128}, .f32);
    const b1 = try session.zeros(&.{128}, .f32);
    const h1 = try input.matmul(w1).add(b1).relu();
    
    // Layer 2: 128 -> 64
    const w2 = try session.ones(&.{128, 64}, .f32);
    const b2 = try session.zeros(&.{64}, .f32);
    const h2 = try h1.matmul(w2).add(b2).relu();
    
    // Output layer: 64 -> 10
    const w3 = try session.ones(&.{64, 10}, .f32);
    const b3 = try session.zeros(&.{10}, .f32);
    const output = try h2.matmul(w3).add(b3);
    
    return output;
}
```

## Design Rationale

The Session API design balances several goals:

1. **Ease of Use**: Familiar PyTorch-like interface
2. **Performance**: Zero-cost abstractions, efficient execution
3. **Safety**: Explicit error handling, no hidden allocations
4. **Flexibility**: Multiple backends, execution modes
5. **Idiomatic Zig**: Follows Zig patterns and conventions

The automatic recompilation system was designed to handle real-world use cases where users build graphs incrementally, modify them through optimization passes, and mix execution modes - all while maintaining correctness and efficiency.