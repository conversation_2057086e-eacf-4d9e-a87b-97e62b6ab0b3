# C API Documentation

## Overview

The Zing C API provides a pure C interface to <PERSON><PERSON>'s tensor computation capabilities, allowing C and C++ developers to leverage <PERSON>ing's optimized tensor operations without needing to use <PERSON>ig directly. The API follows a session-based design with automatic memory management.

## Design Philosophy

The C API is designed with these principles:

1. **Session-Owned Memory**: All tensors are owned by their session and automatically cleaned up
2. **Opaque Pointers**: Implementation details are hidden behind opaque pointer types
3. **Thread-Local Error Handling**: Errors are stored in thread-local storage for safety
4. **Simple, Intuitive API**: Functions mirror the Zig Session API closely
5. **Zero-Copy Data Access**: Direct access to tensor data without copies

## Basic Usage

### Headers and Linking

```c
#include <zing.h>

// Link with: -lzing_c
// Add include path: -I/path/to/zing/include
```

### Error Handling

All functions that can fail return 0 on success, -1 on error:

```c
zing_session_t* session = zing_session_create();
if (!session) {
    const char* error = zing_get_last_error();
    fprintf(stderr, "Failed to create session: %s\n", error);
    return -1;
}
```

### Session Management

```c
// Create a session (required for all operations)
zing_session_t* session = zing_session_create();

// Use the session...

// Clean up (automatically frees all tensors)
zing_session_destroy(session);
```

### Creating Tensors

```c
// Create tensor filled with zeros
int64_t shape[] = {2, 3};
zing_tensor_t* zeros = zing_zeros(session, shape, 2, ZING_F32);

// Create tensor filled with ones
zing_tensor_t* ones = zing_ones(session, shape, 2, ZING_F32);

// Create tensor from data
float data[] = {1.0f, 2.0f, 3.0f, 4.0f, 5.0f, 6.0f};
zing_tensor_t* tensor = zing_tensor(session, shape, 2, ZING_F32, data);

// Create placeholder (for inputs)
zing_tensor_t* input = zing_placeholder(session, shape, 2, ZING_F32);
```

### Tensor Operations

All operations return new tensors and can be chained:

```c
// Arithmetic operations
zing_tensor_t* sum = zing_add(a, b);
zing_tensor_t* diff = zing_subtract(a, b);
zing_tensor_t* product = zing_multiply(a, b);
zing_tensor_t* quotient = zing_divide(a, b);

// Matrix multiplication
int64_t shape_a[] = {2, 3};
int64_t shape_b[] = {3, 4};
zing_tensor_t* a = zing_ones(session, shape_a, 2, ZING_F32);
zing_tensor_t* b = zing_ones(session, shape_b, 2, ZING_F32);
zing_tensor_t* result = zing_matmul(a, b);  // Result is [2, 4]

// Activation functions
zing_tensor_t* activated = zing_relu(tensor);

// Shape operations
int64_t new_shape[] = {6, 1};
zing_tensor_t* reshaped = zing_reshape(tensor, new_shape, 2);
```

### Accessing Tensor Data

```c
// Get tensor data (triggers computation if needed)
const float* data = (const float*)zing_data(tensor);
if (!data) {
    fprintf(stderr, "Failed to get data: %s\n", zing_get_last_error());
    return -1;
}

// Use the data
for (int i = 0; i < 6; i++) {
    printf("data[%d] = %f\n", i, data[i]);
}
```

### Tensor Properties

```c
// Get tensor properties
size_t rank = zing_rank(tensor);
size_t size = zing_size(tensor);
const int64_t* shape = zing_shape(tensor);
zing_dtype_t dtype = zing_dtype(tensor);

printf("Tensor: rank=%zu, size=%zu, dtype=%d\n", rank, size, dtype);
printf("Shape: [");
for (size_t i = 0; i < rank; i++) {
    printf("%lld%s", shape[i], i < rank-1 ? ", " : "");
}
printf("]\n");
```

### Execution Modes

```c
// Set execution mode
zing_session_set_mode(session, ZING_LAZY);  // Lazy mode
zing_session_set_mode(session, ZING_EAGER); // Eager mode (default)

// Get current mode
zing_exec_mode_t mode = zing_session_get_mode(session);

// In lazy mode, explicitly run computation
if (zing_session_run(session) != 0) {
    fprintf(stderr, "Execution failed: %s\n", zing_get_last_error());
}
```

## Data Types

```c
typedef enum {
    ZING_F32,  // 32-bit float
    ZING_F64,  // 64-bit float
    ZING_I32,  // 32-bit integer
    ZING_I64   // 64-bit integer
} zing_dtype_t;

typedef enum {
    ZING_EAGER,  // Execute operations immediately
    ZING_LAZY    // Build graph, execute on demand
} zing_exec_mode_t;
```

## Complete Example

```c
#include <zing.h>
#include <stdio.h>
#include <stdlib.h>

int main() {
    // Create session
    zing_session_t* session = zing_session_create();
    if (!session) {
        fprintf(stderr, "Failed to create session: %s\n", zing_get_last_error());
        return 1;
    }
    
    // Create input tensors
    int64_t shape[] = {2, 3};
    float data_a[] = {1, 2, 3, 4, 5, 6};
    float data_b[] = {7, 8, 9, 10, 11, 12};
    
    zing_tensor_t* a = zing_tensor(session, shape, 2, ZING_F32, data_a);
    zing_tensor_t* b = zing_tensor(session, shape, 2, ZING_F32, data_b);
    
    if (!a || !b) {
        fprintf(stderr, "Failed to create tensors: %s\n", zing_get_last_error());
        zing_session_destroy(session);
        return 1;
    }
    
    // Perform computation: (a + b) * a
    zing_tensor_t* sum = zing_add(a, b);
    zing_tensor_t* result = zing_multiply(sum, a);
    
    if (!result) {
        fprintf(stderr, "Computation failed: %s\n", zing_get_last_error());
        zing_session_destroy(session);
        return 1;
    }
    
    // Get results
    const float* output = (const float*)zing_data(result);
    if (!output) {
        fprintf(stderr, "Failed to get output: %s\n", zing_get_last_error());
        zing_session_destroy(session);
        return 1;
    }
    
    // Print results
    printf("Result of (a + b) * a:\n");
    for (int i = 0; i < 6; i++) {
        printf("%.1f ", output[i]);
    }
    printf("\n");
    
    // Clean up (all tensors automatically freed)
    zing_session_destroy(session);
    return 0;
}
```

## Advanced Example: Neural Network

```c
typedef struct {
    zing_tensor_t* weights;
    zing_tensor_t* bias;
} Layer;

zing_tensor_t* forward_pass(zing_session_t* session, 
                           zing_tensor_t* input,
                           Layer* layers, 
                           int num_layers) {
    zing_tensor_t* x = input;
    
    for (int i = 0; i < num_layers; i++) {
        // Linear transformation: x @ w + b
        zing_tensor_t* linear = zing_matmul(x, layers[i].weights);
        x = zing_add(linear, layers[i].bias);
        
        // Apply ReLU activation (except last layer)
        if (i < num_layers - 1) {
            x = zing_relu(x);
        }
    }
    
    return x;
}
```

## Memory Management

The C API uses **session-owned memory management**:

1. **Automatic Cleanup**: All tensors created within a session are automatically freed when the session is destroyed
2. **No Manual Free**: Never call `free()` on tensor pointers
3. **Data Lifetime**: Tensor data pointers are valid until the session is destroyed
4. **Thread Safety**: Each thread should use its own session

Example of the memory model:
```c
void process_batch(zing_session_t* session) {
    // Create many temporary tensors
    for (int i = 0; i < 1000; i++) {
        int64_t shape[] = {10, 10};
        zing_tensor_t* temp = zing_ones(session, shape, 2, ZING_F32);
        // No need to free 'temp' - session tracks it
    }
    // All 1000 tensors are freed when session is destroyed
}
```

## Error Handling Best Practices

```c
// Helper macro for error checking
#define CHECK(expr) \
    do { \
        if (!(expr)) { \
            fprintf(stderr, "Error at %s:%d: %s\n", \
                    __FILE__, __LINE__, zing_get_last_error()); \
            goto cleanup; \
        } \
    } while(0)

// Usage example
int process_data() {
    zing_session_t* session = NULL;
    int ret = -1;
    
    session = zing_session_create();
    CHECK(session != NULL);
    
    zing_tensor_t* a = zing_ones(session, shape, 2, ZING_F32);
    CHECK(a != NULL);
    
    // ... more operations ...
    
    ret = 0;  // Success
    
cleanup:
    if (session) {
        zing_session_destroy(session);
    }
    return ret;
}
```

## Performance Tips

1. **Reuse Sessions**: Creating sessions has overhead; reuse them for multiple computations
2. **Batch Operations**: Process multiple inputs together for better performance
3. **Lazy Mode**: Use lazy mode for complex graphs to benefit from optimizations
4. **Data Layout**: Ensure your data is contiguous in memory for best performance

## Integration with C++

The C API works seamlessly with C++. You can create RAII wrappers:

```cpp
#include <zing.h>
#include <memory>
#include <stdexcept>

class ZingSession {
public:
    ZingSession() : session_(zing_session_create()) {
        if (!session_) {
            throw std::runtime_error(zing_get_last_error());
        }
    }
    
    ~ZingSession() {
        if (session_) {
            zing_session_destroy(session_);
        }
    }
    
    // Delete copy operations
    ZingSession(const ZingSession&) = delete;
    ZingSession& operator=(const ZingSession&) = delete;
    
    // Allow move operations
    ZingSession(ZingSession&& other) noexcept 
        : session_(other.session_) {
        other.session_ = nullptr;
    }
    
    operator zing_session_t*() { return session_; }
    
private:
    zing_session_t* session_;
};
```

## Thread Safety

- **Sessions are not thread-safe**: Each thread should create its own session
- **Error handling is thread-local**: Each thread has its own error message
- **Tensors cannot be shared between sessions**: Create tensors in the session that will use them

## Limitations and Future Work

Current limitations:
- No support for custom operations
- Limited to predefined data types
- No gradient computation (planned for future)
- No direct GPU memory management (handled internally)

## Building and Linking

### Static Linking
```bash
gcc -o myapp myapp.c -I/path/to/zing/include -L/path/to/zing/lib -lzing_c -lc
```

### Dynamic Linking
```bash
gcc -o myapp myapp.c -I/path/to/zing/include -L/path/to/zing/lib -lzing_c
export LD_LIBRARY_PATH=/path/to/zing/lib:$LD_LIBRARY_PATH
./myapp
```

### CMake Integration
```cmake
find_package(PkgConfig REQUIRED)
pkg_check_modules(ZING REQUIRED zing)

add_executable(myapp main.c)
target_include_directories(myapp PRIVATE ${ZING_INCLUDE_DIRS})
target_link_libraries(myapp ${ZING_LIBRARIES})
```