# Zing Tensor API Reference

This document provides a comprehensive reference for all tensor operations available in the Zing tensor computation library. All operations are methods on the `TensorHandle` type.

## Table of Contents

1. [Tensor Creation](#tensor-creation)
2. [View Operations](#view-operations)
3. [Arithmetic Operations](#arithmetic-operations)
4. [Comparison Operations](#comparison-operations)
5. [Reduction Operations](#reduction-operations)
6. [Activation Functions](#activation-functions)
7. [Mathematical Functions](#mathematical-functions)
8. [Logical Operations](#logical-operations)
9. [Normalization Operations](#normalization-operations)
10. [Linear Algebra Operations](#linear-algebra-operations)
11. [Indexing Operations](#indexing-operations)

## Tensor Creation

Located in `src/tensor/creation.zig`:

### Basic Creation Functions

```zig
// Create a tensor filled with zeros
pub fn zeros(graph: *Graph, shape: []const i64, dtype: DataType) !TensorHandle

// Create a tensor filled with ones  
pub fn ones(graph: *Graph, shape: []const i64, dtype: DataType) !TensorHandle

// Create a tensor filled with a constant value
pub fn full(graph: *Graph, shape: []const i64, value: f32, dtype: DataType) !TensorHandle

// Create a constant scalar tensor
pub fn constant(graph: *Graph, value: f32, dtype: DataType) !TensorHandle

// Create a placeholder tensor (for inputs)
pub fn placeholder(graph: *Graph, shape: []const i64, dtype: DataType) !TensorHandle

// Create a parameter tensor (for learnable weights)
pub fn parameter(graph: *Graph, name: []const u8, shape: []const i64, dtype: DataType) !TensorHandle
```

### Range and Sequence Functions

```zig
// Create a tensor with values from 0 to n-1
pub fn arange(graph: *Graph, n: i64, dtype: DataType) !TensorHandle

// Create a tensor with evenly spaced values
pub fn linspace(graph: *Graph, start: f32, end: f32, steps: i64, dtype: DataType) !TensorHandle
```

### Random Tensor Creation

```zig
// Create a tensor with random values from uniform distribution [0, 1)
pub fn randUniform(graph: *Graph, shape: []const i64, dtype: DataType) !TensorHandle

// Create a tensor with random values from normal distribution (mean=0, std=1)
pub fn randNormal(graph: *Graph, shape: []const i64, dtype: DataType) !TensorHandle
```

### Special Matrices

```zig
// Create an identity matrix
pub fn eye(graph: *Graph, n: i64, dtype: DataType) !TensorHandle

// Create a diagonal matrix from a 1D tensor
pub fn diag(self: TensorHandle) !TensorHandle
```

## View Operations

Located in `src/tensor/ops/view.zig`. These operations change how data is viewed without copying:

### Shape Manipulation

```zig
// Reshape tensor to new shape (total elements must match)
pub fn reshape(self: TensorHandle, new_shape: []const i64) !TensorHandle

// Remove dimensions of size 1
pub fn squeeze(self: TensorHandle, axis: usize) !TensorHandle

// Add a dimension of size 1
pub fn unsqueeze(self: TensorHandle, axis: usize) !TensorHandle

// Flatten tensor to 1D
pub fn flatten(self: TensorHandle) !TensorHandle

// Flatten specific dimensions
pub fn flattenDims(self: TensorHandle, start_dim: usize, end_dim: usize) !TensorHandle
```

### Dimension Reordering

```zig
// Transpose (swap last two dimensions)
pub fn transpose(self: TensorHandle) !TensorHandle

// Permute dimensions according to given order
pub fn permute(self: TensorHandle, axes: []const usize) !TensorHandle
```

### Slicing and Indexing

```zig
// Slice tensor along dimensions
pub fn slice(self: TensorHandle, ranges: []const [2]i64) !TensorHandle

// Example: tensor.slice(&.{.{0, 10}, .{5, 15}}) // slice [0:10, 5:15]
```

### Broadcasting and Expansion

```zig
// Expand tensor to match target shape (broadcasting rules apply)
pub fn expand(self: TensorHandle, new_shape: []const i64) !TensorHandle

// Expand along specific axis
pub fn expandAxis(self: TensorHandle, axis: usize, size: i64) !TensorHandle

// Explicitly broadcast to shape
pub fn broadcast(self: TensorHandle, target_shape: []const i64) !TensorHandle
```

### Memory Layout

```zig
// Make tensor contiguous in memory
pub fn makeContiguous(self: TensorHandle) !TensorHandle
```

## Arithmetic Operations

Located in `src/tensor/ops/arithmetic.zig` and `src/tensor/ops/primitive.zig`:

### Binary Operations

```zig
// Element-wise addition
pub fn add(self: TensorHandle, other: TensorHandle) !TensorHandle

// Element-wise subtraction  
pub fn subtract(self: TensorHandle, other: TensorHandle) !TensorHandle

// Element-wise multiplication
pub fn mul(self: TensorHandle, other: TensorHandle) !TensorHandle

// Element-wise division
pub fn divide(self: TensorHandle, other: TensorHandle) !TensorHandle

// Element-wise modulo
pub fn mod(self: TensorHandle, other: TensorHandle) !TensorHandle

// Element-wise power
pub fn pow(self: TensorHandle, other: TensorHandle) !TensorHandle

// Floor division
pub fn floorDiv(self: TensorHandle, other: TensorHandle) !TensorHandle
```

### Unary Operations

```zig
// Negation
pub fn neg(self: TensorHandle) !TensorHandle

// Absolute value
pub fn abs(self: TensorHandle) !TensorHandle

// Square
pub fn square(self: TensorHandle) !TensorHandle

// Cube
pub fn cube(self: TensorHandle) !TensorHandle

// Reciprocal (1/x)
pub fn recip(self: TensorHandle) !TensorHandle

// Square root
pub fn sqrt(self: TensorHandle) !TensorHandle

// Sign (-1, 0, or 1)
pub fn sign(self: TensorHandle) !TensorHandle
```

### Ternary Operations

```zig
// Fused multiply-add: a * b + c
pub fn fma(a: TensorHandle, b: TensorHandle, c: TensorHandle) !TensorHandle
```

### Clamping

```zig
// Clamp values between min and max
pub fn clamp(self: TensorHandle, min: f32, max: f32) !TensorHandle
```

## Comparison Operations

Located in `src/tensor/ops/comparison.zig`:

### Element-wise Comparisons

```zig
// Less than (returns 0.0 or 1.0)
pub fn lessThan(self: TensorHandle, other: TensorHandle) !TensorHandle

// Less than or equal
pub fn lessThanOrEqual(self: TensorHandle, other: TensorHandle) !TensorHandle

// Greater than
pub fn greaterThan(self: TensorHandle, other: TensorHandle) !TensorHandle

// Greater than or equal  
pub fn greaterThanOrEqual(self: TensorHandle, other: TensorHandle) !TensorHandle

// Equal
pub fn equal(self: TensorHandle, other: TensorHandle) !TensorHandle

// Not equal
pub fn notEqual(self: TensorHandle, other: TensorHandle) !TensorHandle
```

### Element-wise Min/Max

```zig
// Element-wise maximum
pub fn maximum(self: TensorHandle, other: TensorHandle) !TensorHandle

// Element-wise minimum
pub fn minimum(self: TensorHandle, other: TensorHandle) !TensorHandle
```

## Reduction Operations

Located in `src/tensor/ops/reduction.zig`. All reduction operations now take a `keepdims` parameter:

### Basic Reductions

```zig
// Sum along axis
pub fn sum(self: TensorHandle, axis: usize, keepdims: bool) !TensorHandle

// Maximum along axis
pub fn max(self: TensorHandle, axis: usize, keepdims: bool) !TensorHandle

// Minimum along axis
pub fn min(self: TensorHandle, axis: usize, keepdims: bool) !TensorHandle

// Mean along axis
pub fn mean(self: TensorHandle, axis: usize, keepdims: bool) !TensorHandle

// Product along axis
pub fn prod(self: TensorHandle, axis: usize, keepdims: bool) !TensorHandle
```

### Statistical Reductions

```zig
// Variance along axis
pub fn variance(self: TensorHandle, axis: usize, keepdims: bool) !TensorHandle

// Standard deviation along axis
pub fn stddev(self: TensorHandle, axis: usize, keepdims: bool) !TensorHandle
```

### All-Element Reductions

```zig
// Sum all elements (returns scalar)
pub fn sumAll(self: TensorHandle) !TensorHandle

// Maximum of all elements
pub fn maxAll(self: TensorHandle) !TensorHandle

// Minimum of all elements
pub fn minAll(self: TensorHandle) !TensorHandle

// Mean of all elements
pub fn meanAll(self: TensorHandle) !TensorHandle

// Product of all elements
pub fn prodAll(self: TensorHandle) !TensorHandle
```

### Norm Reductions

```zig
// L1 norm (sum of absolute values)
pub fn norm1(self: TensorHandle) !TensorHandle

// L2 norm (Euclidean norm)
pub fn norm2(self: TensorHandle) !TensorHandle

// Frobenius norm (for matrices)
pub fn normFro(self: TensorHandle) !TensorHandle
```

### Other Reductions

```zig
// Count non-zero elements
pub fn countNonzero(self: TensorHandle) !TensorHandle

// Argmax (indices of maximum values) - NOT YET IMPLEMENTED
pub fn argmax(self: TensorHandle, axis: usize) !TensorHandle

// Argmin (indices of minimum values) - NOT YET IMPLEMENTED
pub fn argmin(self: TensorHandle, axis: usize) !TensorHandle

// Cumulative sum along last dimension
pub fn cumsumLastDim(self: TensorHandle) !TensorHandle
```

## Activation Functions

Located in `src/tensor/ops/activation.zig`:

### Common Activations

```zig
// Rectified Linear Unit (max(0, x))
pub fn relu(self: TensorHandle) !TensorHandle

// Sigmoid (1 / (1 + exp(-x)))
pub fn sigmoid(self: TensorHandle) !TensorHandle

// Hyperbolic tangent
pub fn tanh(self: TensorHandle) !TensorHandle

// Gaussian Error Linear Unit
pub fn gelu(self: TensorHandle) !TensorHandle

// Softplus (log(1 + exp(x)))
pub fn softplus(self: TensorHandle) !TensorHandle

// Leaky ReLU (max(alpha * x, x))
pub fn leakyRelu(self: TensorHandle, alpha: f32) !TensorHandle

// Swish (x * sigmoid(x))
pub fn swish(self: TensorHandle) !TensorHandle

// SiLU (Sigmoid Linear Unit, same as swish)
pub fn silu(self: TensorHandle) !TensorHandle
```

### Softmax Functions

```zig
// Softmax along last dimension
pub fn softmax(self: TensorHandle) !TensorHandle

// Log softmax along last dimension
pub fn logSoftmax(self: TensorHandle) !TensorHandle
```

## Mathematical Functions

Located in `src/tensor/ops/math.zig`:

### Exponential and Logarithmic

```zig
// Natural exponential (e^x)
pub fn exp(self: TensorHandle) !TensorHandle

// Base-2 exponential (2^x)
pub fn exp2(self: TensorHandle) !TensorHandle

// Natural logarithm
pub fn log(self: TensorHandle) !TensorHandle
pub fn ln(self: TensorHandle) !TensorHandle  // Alias for log

// Base-2 logarithm
pub fn log2(self: TensorHandle) !TensorHandle
```

### Trigonometric Functions

```zig
// Sine
pub fn sin(self: TensorHandle) !TensorHandle

// Cosine
pub fn cos(self: TensorHandle) !TensorHandle

// Tangent
pub fn tan(self: TensorHandle) !TensorHandle
```

### Hyperbolic Functions

```zig
// Hyperbolic sine
pub fn sinh(self: TensorHandle) !TensorHandle

// Hyperbolic cosine
pub fn cosh(self: TensorHandle) !TensorHandle
```

## Logical Operations

Located in `src/tensor/ops/logical.zig`:

```zig
// Logical NOT (for boolean tensors)
pub fn logicalNot(self: TensorHandle) !TensorHandle

// Logical AND
pub fn logicalAnd(self: TensorHandle, other: TensorHandle) !TensorHandle

// Logical OR
pub fn logicalOr(self: TensorHandle, other: TensorHandle) !TensorHandle

// Conditional selection (where condition ? true_val : false_val)
pub fn select(condition: TensorHandle, true_val: TensorHandle, false_val: TensorHandle) !TensorHandle
```

## Normalization Operations

Located in `src/tensor/ops/normalization.zig`:

```zig
// Layer normalization
pub fn layerNorm(self: TensorHandle, eps: f32) !TensorHandle

// Normalize by range to [0, 1]
pub fn normalizeByRange(self: TensorHandle) !TensorHandle

// Batch normalization
pub fn batchNorm(self: TensorHandle, gamma: TensorHandle, beta: TensorHandle, eps: f32) !TensorHandle

// Instance normalization  
pub fn instanceNorm(self: TensorHandle, eps: f32) !TensorHandle

// Group normalization
pub fn groupNorm(self: TensorHandle, num_groups: usize, eps: f32) !TensorHandle
```

## Linear Algebra Operations

Located in `src/tensor/ops/linalg.zig`:

```zig
// Matrix multiplication (supports batched operations)
// For 2D: [M, K] @ [K, N] -> [M, N]
// For 3D: [B, M, K] @ [B, K, N] -> [B, M, N]
// For 4D/5D: uses reshape + 3D matmul + reshape
pub fn matmul(self: TensorHandle, other: TensorHandle) !TensorHandle
```

## Indexing Operations

Located in `src/tensor/ops/indexing.zig`:

```zig
// Gather elements along an axis using indices
pub fn gather(self: TensorHandle, indices: TensorHandle, axis: usize) !TensorHandle
```

## Usage Examples

### Basic Arithmetic
```zig
const a = try tensor.ones(&graph, &.{2, 3}, .f32);
const b = try tensor.full(&graph, &.{2, 3}, 2.0, .f32);
const c = try a.add(b);  // Element-wise addition
const d = try c.mul(a);  // Element-wise multiplication
```

### Reductions
```zig
const x = try tensor.arange(&graph, 12, .f32);
const reshaped = try x.reshape(&.{3, 4});

// Sum along axis 0, keeping dimensions
const sum0 = try reshaped.sum(0, true);  // Shape: [1, 4]

// Sum along axis 1, removing dimension  
const sum1 = try reshaped.sum(1, false); // Shape: [3]

// Mean of all elements
const mean_all = try reshaped.meanAll();  // Shape: []
```

### Broadcasting
```zig
const a = try tensor.ones(&graph, &.{3, 1}, .f32);
const b = try tensor.ones(&graph, &.{1, 4}, .f32);
const c = try a.add(b);  // Broadcasting: [3, 1] + [1, 4] -> [3, 4]
```

### Matrix Operations
```zig
const a = try tensor.randNormal(&graph, &.{2, 3, 4}, .f32);
const b = try tensor.randNormal(&graph, &.{2, 4, 5}, .f32);
const c = try a.matmul(b);  // Batched matmul: [2, 3, 4] @ [2, 4, 5] -> [2, 3, 5]
```

### Activation Functions
```zig
const x = try tensor.randNormal(&graph, &.{10, 20}, .f32);
const activated = try x.relu();
const probs = try activated.softmax();  // Softmax along last dimension
```

## Error Handling

All tensor operations return error unions. Common errors include:

- `InvalidShape`: Shape mismatch or invalid dimensions
- `InvalidAxis`: Axis out of bounds
- `InvalidBroadcast`: Shapes cannot be broadcast together
- `DimensionMismatch`: Incompatible dimensions for operation
- `InvalidMatmulDimensions`: Invalid shapes for matrix multiplication
- `DynamicDimensionNotSupported`: Operation doesn't support symbolic dimensions

Example:
```zig
const result = tensor.add(other) catch |err| {
    switch (err) {
        error.InvalidBroadcast => std.log.err("Cannot broadcast shapes", .{}),
        else => return err,
    }
};
```