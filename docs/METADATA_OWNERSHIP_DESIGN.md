# Metadata Ownership Design Document

## Overview

This document outlines the design for clear metadata ownership in the Zing tensor computation library. The goal is to establish clear boundaries between what different components can set in node metadata, preventing conflicts and making the system more robust.

## Current Problems

1. **Dual Ownership**: Both TensorHandle operations and the shape inference compiler pass attempt to manage node metadata, leading to conflicts
2. **Overwriting Issues**: Shape inference may overwrite metadata carefully set by operations (e.g., keepdims flag)
3. **Unclear Responsibilities**: No clear rules about who owns which metadata fields
4. **Timing Conflicts**: Metadata set during node creation can be lost during compilation

## Design Principles

### 1. Separation of Concerns

- **TensorHandle Operations**: Own SEMANTIC metadata (what the operation does)
- **Shape Inference Pass**: Own SHAPE metadata (tensor dimensions and sizes)
- **Memory Planning Pass**: Own EXECUTION metadata (buffer allocation details)

### 2. Read-Only Cross-Boundary Access

- Shape inference READS semantic metadata but never modifies it
- Memory planning READS shape metadata but never modifies it
- Each component only writes to its designated fields

### 3. Single Source of Truth

- After compilation, node metadata is the authoritative source
- TensorHandle shape tracking is for API convenience only

## Metadata Field Ownership

```zig
pub const NodeMetadata = struct {
    // ===== SEMANTIC METADATA =====
    // Owner: TensorHandle operations
    // Set during: Node creation
    // Purpose: Describe what the operation does
    
    // For reduction operations
    reduction_axis: ?i64 = null,
    keepdims: bool = false,
    
    // For reshape operations  
    reshape_target: ?[]const i64 = null,
    
    // For transpose operations
    transpose_axes: ?[]const usize = null,
    
    // For slice operations
    slice_ranges: ?[]const [2]i64 = null,
    
    // For constant nodes
    constant_value: ?f32 = null,
    
    // ===== SHAPE METADATA =====
    // Owner: Shape Inference Pass
    // Set during: Compilation (shape inference phase)
    // Purpose: Track tensor dimensions for memory planning
    
    output_shape: ?ShapeTracker = null,
    input_shapes: ?[]ShapeTracker = null,
    
    // ===== EXECUTION METADATA =====
    // Owner: Memory Planning Pass
    // Set during: Compilation (memory planning phase)
    // Purpose: Guide runtime execution
    
    buffer_size: ?usize = null,
    alignment: ?usize = null,
    buffer_offset: ?usize = null,
};
```

## Implementation Rules

### Rule 1: TensorHandle Operations Set Only Semantic Metadata

```zig
// CORRECT: Set semantic metadata only
pub fn sumReduce(self: TensorHandle, axis: usize, keepdims: bool) !TensorHandle {
    const result_id = try self.graph.createNode(.{ .compute = .sum_reduce }, &.{self.node_id}, self.dtype);
    
    // Set semantic metadata
    try self.graph.setReductionAxis(result_id, @intCast(axis));
    try self.graph.setReductionKeepdims(result_id, keepdims);
    
    // DON'T set shape metadata - that's shape inference's job
    // Local shape tracking for API convenience only
    const result_shape = try shape_mod.inferReduceShape(&self.shape, axis, keepdims, ...);
    
    return TensorHandle{
        .graph = self.graph,
        .node_id = result_id,
        .shape = result_shape,  // Local tracking, not authoritative
        .dtype = self.dtype,
    };
}

// WRONG: Don't set shape metadata
pub fn badExample(self: TensorHandle) !TensorHandle {
    // ... create node ...
    
    // DON'T DO THIS - shape inference owns output_shape
    node.metadata.?.output_shape = some_shape;
}
```

### Rule 2: Shape Inference Reads Semantic Metadata

```zig
// Shape inference respects semantic metadata
fn inferReductionShape(node: *const Node, input_shape: ShapeTracker, ...) !ShapeTracker {
    // READ semantic metadata
    const axis = if (node.metadata) |meta| meta.reduction_axis else null;
    const keepdims = if (node.metadata) |meta| meta.keepdims else false;
    
    // Use semantic metadata to compute shape
    if (axis) |reduction_axis| {
        // Compute shape based on the operation's semantics
        return computeReducedShape(input_shape, reduction_axis, keepdims, ...);
    }
    
    // Default behavior if no semantic metadata
    return defaultShapeInference(input_shape);
}
```

### Rule 3: Graph Methods Enforce Ownership

```zig
// Graph provides type-safe methods for setting semantic metadata
pub fn setReductionAxis(self: *Graph, node_id: NodeId, axis: i64) !void {
    const node = self.getNodeMut(node_id) orelse return error.InvalidNodeId;
    
    if (node.metadata == null) {
        node.metadata = try self.arena.allocator().create(NodeMetadata);
        node.metadata.?.* = .{};
    }
    
    // Only set semantic field
    node.metadata.?.reduction_axis = axis;
}

pub fn setReductionKeepdims(self: *Graph, node_id: NodeId, keepdims: bool) !void {
    const node = self.getNodeMut(node_id) orelse return error.InvalidNodeId;
    
    if (node.metadata == null) {
        node.metadata = try self.arena.allocator().create(NodeMetadata);
        node.metadata.?.* = .{};
    }
    
    // Only set semantic field
    node.metadata.?.keepdims = keepdims;
}

// Shape metadata is set differently - only by compiler passes
pub fn setNodeShapeMetadata(self: *Graph, node_id: NodeId, input_shapes: []const ShapeTracker, output_shape: ShapeTracker) !void {
    // This method should only be called by shape inference pass
    // Could add debug assertion to verify caller
}
```

## Migration Plan

### Phase 1: Add New Semantic Metadata Methods
1. Add `setReductionKeepdims` method to Graph
2. Add similar methods for other semantic metadata
3. Update NodeMetadata struct with clear field groupings

### Phase 2: Update TensorHandle Operations
1. Remove all `output_shape` and `input_shapes` setting from operations
2. Use only semantic metadata setters
3. Keep local shape tracking for API convenience

### Phase 3: Update Shape Inference
1. Ensure it reads semantic metadata
2. Remove any code that might overwrite semantic fields
3. Add validation for consistency

### Phase 4: Add Validation
1. Add post-shape-inference validation pass
2. Verify TensorHandle shapes match inferred shapes
3. Log warnings for mismatches during development

## Benefits

1. **Clear Ownership**: No ambiguity about who sets what
2. **No Overwrites**: Components can't step on each other
3. **Easier Debugging**: Clear which phase set each field
4. **Maintainability**: New developers understand boundaries
5. **Robustness**: Less chance of subtle timing bugs

## Testing Strategy

1. **Unit Tests**: Each component tested in isolation
2. **Integration Tests**: Verify metadata flows correctly through compilation
3. **Regression Tests**: Ensure existing functionality preserved
4. **Debug Mode**: Add ownership assertions in debug builds

## Future Considerations

1. **Metadata Versioning**: If we need to evolve metadata format
2. **Custom Operations**: How external operations specify semantic metadata
3. **Serialization**: Saving/loading graphs with metadata
4. **Performance**: Ensure metadata separation doesn't impact speed

## Conclusion

This design provides clear ownership boundaries while preserving the existing architecture. By separating semantic, shape, and execution metadata, we eliminate conflicts and make the system more maintainable. The implementation can be done incrementally without breaking existing code.