/// Zing Compiler - Pure Interface Module
/// 
/// This module provides only type definitions and interfaces for the compiler.
/// NO IMPLEMENTATIONS OR RE-EXPORTS - users import specific compiler modules directly.
/// This design ensures consistency with backends.zig and clean separation of concerns.
///
/// The compiler consists of:
/// - backend_interface.zig: Comptime backend selection and validation
/// - compile.zig: Main compilation entry point
/// - unified_pipeline.zig: Template-based optimization pipeline
/// - pass_templates.zig: Compile-time pass creation templates
/// - patterns.zig: Pattern matching system
/// - transforms.zig: Safe graph transformations
/// - passes/: Individual optimization passes

const std = @import("std");
const Allocator = std.mem.Allocator;

// Import shared types
const types = @import("types");
const NodeId = types.NodeId;
const DataType = types.DataType;

// ===== Compiler-Specific Types =====

/// Custom operation representation for AOT fusion
pub const CustomOp = struct {
    /// Unique identifier for the fused operation pattern
    id: u64,
    /// Backend that will execute this operation
    backend_name: []const u8,
    /// Fusion pattern information for AOT kernel selection
    fusion_pattern: types.FusionPattern,
    /// Human-readable name for debugging
    debug_name: []const u8,
};

/// Compilation options for fine-grained control
pub const CompilationOptions = struct {
    optimization_level: OptimizationLevel = .balanced,
    enable_fusion: bool = true,
    enable_memory_optimization: bool = true,
    enable_pattern_recognition: bool = true,
    enable_shape_inference: bool = true,
    debug_passes: bool = false,
};

/// Optimization level presets
pub const OptimizationLevel = enum {
    none,      // No optimizations
    quick,     // Fast compilation with basic optimizations
    balanced,  // Default - good balance of compile time and performance
    aggressive,// Maximum optimizations, longer compile time
};

/// Pipeline configuration for pass orchestration
pub const PipelineConfig = struct {
    passes: []const PassConfig = &.{},
    max_iterations: u32 = 3,
    convergence_threshold: f32 = 0.01,
};

/// Individual pass configuration
pub const PassConfig = struct {
    name: []const u8,
    enabled: bool = true,
    priority: u32 = 50,
    max_iterations: u32 = 1,
};

/// Pass execution context
pub const PassContext = struct {
    graph: *@import("graph").Graph,
    allocator: Allocator,
    backend_capabilities: ?@import("backends").BackendCapabilities = null,
    pass_data: ?*anyopaque = null,
    memory_plan: ?*anyopaque = null,
};

/// Pass type enumeration
pub const PassType = enum {
    traversal,
    analysis,
    pattern,
    transformation,
};

/// Pattern match result
pub const Match = struct {
    root: NodeId,
    nodes: []const NodeId,
    pattern_id: u32,
};

/// Pattern definition
pub const Pattern = struct {
    id: u32,
    name: []const u8,
    ops: []const types.ComputeOp,
    constraints: []const PatternConstraint = &.{},
};

/// Pattern matching constraints
pub const PatternConstraint = enum {
    same_dtype,
    same_shape,
    scalar_only,
    elementwise_only,
    reduction_only,
};

/// Compiler error types
pub const CompilerError = error{
    // Graph validation errors
    InvalidGraph,
    CyclicDependency,
    MissingInputs,
    TypeMismatch,
    ShapeMismatch,
    
    // Optimization errors
    OptimizationFailed,
    PatternMatchFailed,
    TransformationFailed,
    
    // Backend errors
    BackendNotSupported,
    CompilationFailed,
    
    // Resource errors
    OutOfMemory,
    AllocationFailed,
};

// ===== Backend Interface Types =====

/// Compiled program result
pub const CompiledProgram = struct {
    graph: @import("backends").CompiledGraph,
    backend_name: []const u8,
    
    pub fn deinit(self: *CompiledProgram, allocator: Allocator) void {
        self.graph.deinit(allocator);
    }
};

// ===== Main Compilation Interface =====

/// Main compilation functionality
pub const compile = @import("compiler/compile.zig");

// ===== Interface Documentation =====
//
// Users can either import the main compile functionality through this module:
//
// const compiler = @import("compiler");
// compiler.compile.compileCpu(graph, allocator);
//
// Or import specific modules directly:
//
// const compile = @import("compiler/compile.zig");
// const unified_pipeline = @import("compiler/unified_pipeline.zig");
// const pass_templates = @import("compiler/pass_templates.zig");
// const patterns = @import("compiler/patterns.zig");
// const transforms = @import("compiler/transforms.zig");
//
// Individual passes:
// const ConstantFoldingPass = @import("compiler/passes/constant_folding.zig").ConstantFoldingPass;
// const DeadCodeEliminationPass = @import("compiler/passes/dce.zig").DeadCodeEliminationPass;
// etc.
//
// This design ensures:
// - No circular dependencies
// - Clear separation of concerns
// - Compile-time optimization
// - Zero runtime overhead

// ===== Unit Tests =====

test "Compiler types" {
    const testing = std.testing;
    
    // Test that types are properly defined
    const custom_op = CustomOp{
        .id = 1,
        .backend_name = "cpu",
        .fusion_pattern = .elementwise_binary,
        .debug_name = "add_mul_fusion",
    };
    
    try testing.expectEqual(@as(u64, 1), custom_op.id);
    try testing.expectEqualStrings("cpu", custom_op.backend_name);
    
    // Test compilation options
    const options = CompilationOptions{
        .optimization_level = .aggressive,
        .enable_fusion = false,
    };
    
    try testing.expectEqual(OptimizationLevel.aggressive, options.optimization_level);
    try testing.expectEqual(false, options.enable_fusion);
}

test "Pattern types" {
    const testing = std.testing;
    
    const pattern = Pattern{
        .id = 1,
        .name = "matmul_add",
        .ops = &[_]types.ComputeOp{ .matmul, .add },
        .constraints = &[_]PatternConstraint{ .same_dtype },
    };
    
    try testing.expectEqual(@as(u32, 1), pattern.id);
    try testing.expectEqualStrings("matmul_add", pattern.name);
    try testing.expectEqual(@as(usize, 2), pattern.ops.len);
}