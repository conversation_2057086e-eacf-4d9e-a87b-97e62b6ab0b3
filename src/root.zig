//! Zing: A high-performance tensor computation library
//! 
//! This is the main entry point for the Zing library.
//! Import this module to access all public APIs.
//!
//! Module Architecture:
//! - Core types (types.zig): Foundation types used everywhere
//! - Backend types (backend_types.zig): Types for backend implementations
//! - Backends (backends/*.zig): Self-contained backend implementations  
//! - Compiler (compiler/*.zig): Graph optimization and compilation
//! - Application (tensor.zig, execution.zig): User-facing APIs
//!
//! See MODULE_ARCHITECTURE.md for detailed module hierarchy and import rules.

// Core types and data structures
pub const types = @import("types");
pub const DataType = types.DataType;
pub const Device = types.Device;
pub const NodeId = types.NodeId;
pub const ComputeOp = types.ComputeOp;

// Backend types (for backend implementers)
pub const backend_types = @import("backend_types");

// Tensor operations and graph building
pub const tensor = @import("tensor");
pub const TensorHandle = tensor.TensorHandle;

// Graph representation and manipulation
pub const graph = @import("graph");
pub const Graph = graph.Graph;

// Shape inference and symbolic dimensions
pub const shape = @import("shape");
pub const Shape = shape.Shape;
pub const symbolic = @import("symbolic");

// Compilation and optimization
pub const compiler = @import("compiler");
pub const backends = @import("backends");

// Execution
pub const execution = @import("execution");
pub const Executor = execution.Executor;

// Session API - High-level tensor operations with automatic lifecycle management
pub const session = @import("session");
pub const Session = session.Session;
pub const SessionTensor = session.SessionTensor;

// Storage management
pub const storage = @import("storage");

// Convenience re-exports for common functions
pub const placeholder = tensor.placeholder;
pub const constant = tensor.constant;
pub const zeros = tensor.zeros;
pub const ones = tensor.ones;
pub const matmul = tensor.matmul;
pub const add = tensor.add;
pub const multiply = tensor.multiply;

// Backend compilation convenience functions
pub const compileCpu = compiler.compile.compileCpu;
pub const compileCuda = compiler.compile.compileCuda;