/// Training loop and utilities
///
/// This module provides the main training infrastructure that combines
/// models, optimizers, loss functions, and schedulers.

const std = @import("std");
const Allocator = std.mem.Allocator;

const Graph = @import("graph").Graph;
const tensor = @import("tensor");
const TensorHandle = tensor.TensorHandle;
const types = @import("types");
const NodeId = types.NodeId;
const shape = @import("shape");

const compiler = @import("compiler");
const execution = @import("execution");
const CompiledGraph = execution.CompiledGraph;
const Executor = execution.Executor;

const storage = @import("storage");
const ParameterStore = storage.ParameterStore;
const StateId = storage.StateId;

const optimizer = @import("optimizer.zig");
const scheduler = @import("scheduler.zig");

/// Training configuration
pub const TrainingConfig = struct {
    /// Number of training epochs
    epochs: u32 = 10,
    
    /// Batch size for training
    batch_size: u32 = 32,
    
    /// Log training metrics every N steps
    log_interval: u32 = 100,
    
    /// Evaluate on validation set every N steps
    eval_interval: u32 = 1000,
    
    /// Save checkpoint every N steps
    checkpoint_interval: u32 = 5000,
    
    /// Early stopping patience (0 = disabled)
    early_stopping_patience: u32 = 0,
    
    /// Gradient clipping value (0 = disabled)
    gradient_clip: f32 = 0,
    
    /// Random seed for reproducibility
    seed: ?u64 = null,
};

/// Training metrics
pub const TrainingMetrics = struct {
    /// Current epoch
    epoch: u32 = 0,
    
    /// Current step within epoch
    step: u32 = 0,
    
    /// Total steps across all epochs
    total_steps: u32 = 0,
    
    /// Current loss value
    loss: f32 = 0,
    
    /// Exponential moving average of loss
    loss_ema: f32 = 0,
    
    /// Current learning rate
    learning_rate: f32 = 0,
    
    /// Validation loss (if evaluated)
    val_loss: ?f32 = null,
    
    /// Best validation loss seen so far
    best_val_loss: ?f32 = null,
    
    /// Steps since best validation loss
    steps_since_best: u32 = 0,
    
    const ema_beta: f32 = 0.99;
    
    pub fn updateLoss(self: *TrainingMetrics, loss: f32) void {
        self.loss = loss;
        if (self.total_steps == 0) {
            self.loss_ema = loss;
        } else {
            self.loss_ema = ema_beta * self.loss_ema + (1 - ema_beta) * loss;
        }
    }
};

/// Main trainer for supervised learning
pub const Trainer = struct {
    allocator: Allocator,
    graph: *Graph,
    config: TrainingConfig,
    metrics: TrainingMetrics,
    
    // Model parameters
    parameters: []NodeId,
    
    // Parameter storage for weight persistence
    param_store: ?*ParameterStore,
    
    // Mapping from NodeId to StateId in ParameterStore
    param_mapping: std.AutoHashMap(NodeId, StateId),
    
    // Optimizer state
    optimizer_type: OptimizerType,
    optimizer_state: ?*anyopaque,
    
    // Learning rate scheduler
    lr_scheduler: ?*scheduler.Scheduler,
    
    // Compiled graphs
    forward_compiled: ?*CompiledGraph,
    backward_compiled: ?*CompiledGraph,
    optimizer_compiled: ?*CompiledGraph,
    
    // Executors
    forward_executor: ?*Executor,
    backward_executor: ?*Executor,
    optimizer_executor: ?*Executor,
    
    pub const OptimizerType = enum {
        sgd,
        sgd_momentum,
        adam,
        adamw,
    };
    
    /// Initialize a new trainer
    pub fn init(
        allocator: Allocator,
        graph: *Graph,
        parameters: []const NodeId,
        optimizer_type: OptimizerType,
        config: TrainingConfig,
    ) !Trainer {
        const trainer = Trainer{
            .allocator = allocator,
            .graph = graph,
            .config = config,
            .metrics = .{},
            .parameters = try allocator.dupe(NodeId, parameters),
            .param_store = null,
            .param_mapping = std.AutoHashMap(NodeId, StateId).init(allocator),
            .optimizer_type = optimizer_type,
            .optimizer_state = null,
            .lr_scheduler = null,
            .forward_compiled = null,
            .backward_compiled = null,
            .optimizer_compiled = null,
            .forward_executor = null,
            .backward_executor = null,
            .optimizer_executor = null,
        };
        
        // Initialize random seed if provided
        if (config.seed) |seed| {
            // TODO: Set random seed for parameter initialization
            _ = seed;
        }
        
        return trainer;
    }
    
    pub fn deinit(self: *Trainer) void {
        self.allocator.free(self.parameters);
        self.param_mapping.deinit();
        
        if (self.forward_executor) |exec| {
            exec.deinit();
            self.allocator.destroy(exec);
        }
        if (self.backward_executor) |exec| {
            exec.deinit();
            self.allocator.destroy(exec);
        }
        if (self.optimizer_executor) |exec| {
            exec.deinit();
            self.allocator.destroy(exec);
        }
        
        if (self.forward_compiled) |comp| {
            comp.deinit(self.allocator);
            self.allocator.destroy(comp);
        }
        if (self.backward_compiled) |comp| {
            comp.deinit(self.allocator);
            self.allocator.destroy(comp);
        }
        if (self.optimizer_compiled) |comp| {
            comp.deinit(self.allocator);
            self.allocator.destroy(comp);
        }
        
        // Clean up optimizer state based on type
        if (self.optimizer_state) |state| {
            switch (self.optimizer_type) {
                .sgd => self.allocator.destroy(@as(*optimizer.SGD, @ptrCast(@alignCast(state)))),
                .sgd_momentum => {
                    const sgd_m = @as(*optimizer.SGDMomentum, @ptrCast(@alignCast(state)));
                    sgd_m.deinit();
                    self.allocator.destroy(sgd_m);
                },
                .adam => {
                    const adam = @as(*optimizer.Adam, @ptrCast(@alignCast(state)));
                    adam.deinit();
                    self.allocator.destroy(adam);
                },
                .adamw => {
                    const adamw = @as(*optimizer.AdamW, @ptrCast(@alignCast(state)));
                    adamw.deinit();
                    self.allocator.destroy(adamw);
                },
            }
        }
    }
    
    /// Set the learning rate scheduler
    pub fn setScheduler(self: *Trainer, sched: *scheduler.Scheduler) void {
        self.lr_scheduler = sched;
    }
    
    /// Set the parameter store for weight persistence
    pub fn setParameterStore(self: *Trainer, store: *ParameterStore) !void {
        self.param_store = store;
        
        // Create mapping from NodeId to StateId
        // This assumes parameters have been registered in the store
        // In a full implementation, we'd need a way to associate graph nodes with store IDs
    }
    
    /// Compile the training graphs
    pub fn compile(
        self: *Trainer,
        model_output: NodeId,
        loss_node: NodeId,
        initial_lr: f32,
    ) !void {
        // Compile forward pass
        try self.graph.markOutput(model_output);
        try self.graph.markOutput(loss_node);
        
        const forward_comp = try self.allocator.create(CompiledGraph);
        forward_comp.* = try compiler.compile.compileCpu(self.graph, self.allocator);
        self.forward_compiled = forward_comp;
        
        self.forward_executor = try self.allocator.create(Executor);
        self.forward_executor.?.* = try Executor.init(self.allocator, forward_comp, null);
        
        // Autograd is now handled automatically during compilation
        // Just verify gradients were created
        if (!self.graph.hasGradientsEnabled()) {
            return error.GradientsNotEnabled;
        }
        
        // Compile backward pass (includes gradient computation)
        const backward_comp = try self.allocator.create(CompiledGraph);
        backward_comp.* = try compiler.compile.compileCpu(self.graph, self.allocator);
        self.backward_compiled = backward_comp;
        
        self.backward_executor = try self.allocator.create(Executor);
        self.backward_executor.?.* = try Executor.init(self.allocator, backward_comp, null);
        
        // Initialize optimizer based on type
        switch (self.optimizer_type) {
            .sgd => {
                const sgd = try self.allocator.create(optimizer.SGD);
                sgd.* = try optimizer.SGD.init(self.graph, initial_lr);
                self.optimizer_state = sgd;
            },
            .sgd_momentum => {
                const sgd_m = try self.allocator.create(optimizer.SGDMomentum);
                sgd_m.* = try optimizer.SGDMomentum.init(self.graph, initial_lr, 0.9);
                self.optimizer_state = sgd_m;
            },
            .adam => {
                const adam = try self.allocator.create(optimizer.Adam);
                adam.* = try optimizer.Adam.init(self.graph, initial_lr, 0.9, 0.999, 1e-8);
                self.optimizer_state = adam;
            },
            .adamw => {
                const adamw = try self.allocator.create(optimizer.AdamW);
                adamw.* = try optimizer.AdamW.init(self.graph, initial_lr, 0.9, 0.999, 1e-8, 0.01);
                self.optimizer_state = adamw;
            },
        }
        
        // TODO: Compile optimizer graph for efficient weight updates
    }
    
    /// Run a single training step
    pub fn trainStep(
        self: *Trainer,
        input_data: []const u8,
        target_data: []const u8,
        input_node: NodeId,
        target_node: NodeId,
        loss_node: NodeId,
    ) !f32 {
        if (self.forward_executor == null or self.backward_executor == null) {
            return error.NotCompiled;
        }
        
        // Update learning rate if scheduler is set
        if (self.lr_scheduler) |sched| {
            const lr = sched.getLearningRate(self.metrics.total_steps);
            self.metrics.learning_rate = lr;
            
            // Update optimizer learning rate
            switch (self.optimizer_type) {
                .sgd => {
                    const sgd = @as(*optimizer.SGD, @ptrCast(@alignCast(self.optimizer_state.?)));
                    try sgd.setLearningRate(lr);
                },
                .sgd_momentum => {
                    const sgd_m = @as(*optimizer.SGDMomentum, @ptrCast(@alignCast(self.optimizer_state.?)));
                    sgd_m.learning_rate = try tensor.constant(self.graph, lr, .f32);
                },
                .adam => {
                    const adam = @as(*optimizer.Adam, @ptrCast(@alignCast(self.optimizer_state.?)));
                    adam.learning_rate = lr;
                },
                .adamw => {
                    const adamw = @as(*optimizer.AdamW, @ptrCast(@alignCast(self.optimizer_state.?)));
                    adamw.adam.learning_rate = lr;
                },
            }
        }
        
        // Set inputs - use graph API to get shape info
        const input_shape = self.graph.getNodeShape(input_node) orelse return error.ShapeNotFound;
        const target_shape = self.graph.getNodeShape(target_node) orelse return error.ShapeNotFound;
        
        // Convert SymbolicDim to i64 for setInput
        var input_dims: [8]i64 = undefined;
        var target_dims: [8]i64 = undefined;
        
        const shape_utils = @import("shape").utils;
        try shape_utils.symbolicDimsToConcreteArray(input_shape.dims, &input_dims);
        try shape_utils.symbolicDimsToConcreteArray(target_shape.dims, &target_dims);
        
        try self.forward_executor.?.setInput(input_node, input_data, input_dims[0..input_shape.dims.len], .f32);
        try self.forward_executor.?.setInput(target_node, target_data, target_dims[0..target_shape.dims.len], .f32);
        
        // Forward pass
        try self.forward_executor.?.run();
        
        // Get loss value
        const loss_output = try self.forward_executor.?.getOutput(loss_node);
        const loss_value = @as(*const f32, @ptrCast(@alignCast(loss_output.data.ptr))).*;
        
        // Backward pass (compute gradients)
        try self.backward_executor.?.run();
        
        // Get gradients and current weights
        const grad_map = self.graph.getGradientMap() orelse return error.NoGradients;
        var gradients = try self.allocator.alloc(optimizer.GradientInfo, self.parameters.len);
        defer self.allocator.free(gradients);
        
        var old_weights = try self.allocator.alloc(TensorHandle, self.parameters.len);
        defer self.allocator.free(old_weights);
        
        for (self.parameters, 0..) |param_id, i| {
            const grad_id = grad_map.get(param_id) orelse return error.GradientNotFound;
            const grad_shape = self.graph.getNodeShape(grad_id) orelse return error.ShapeNotFound;
            
            gradients[i] = .{
                .node_id = grad_id,
                .shape = grad_shape,
            };
            
            old_weights[i] = try TensorHandle.fromNode(self.graph, param_id);
        }
        
        // Apply gradient clipping if enabled
        if (self.config.gradient_clip > 0) {
            try self.clipGradients(gradients, self.config.gradient_clip);
        }
        
        // Update weights
        const new_weights = switch (self.optimizer_type) {
            .sgd => blk: {
                const sgd = @as(*optimizer.SGD, @ptrCast(@alignCast(self.optimizer_state.?)));
                break :blk try sgd.step(old_weights, gradients);
            },
            .sgd_momentum => blk: {
                const sgd_m = @as(*optimizer.SGDMomentum, @ptrCast(@alignCast(self.optimizer_state.?)));
                break :blk try sgd_m.step(old_weights, gradients);
            },
            .adam => blk: {
                const adam = @as(*optimizer.Adam, @ptrCast(@alignCast(self.optimizer_state.?)));
                break :blk try adam.step(old_weights, gradients);
            },
            .adamw => blk: {
                const adamw = @as(*optimizer.AdamW, @ptrCast(@alignCast(self.optimizer_state.?)));
                break :blk try adamw.step(old_weights, gradients);
            },
        };
        defer self.allocator.free(new_weights);
        
        // Apply weight updates by executing optimizer computation and updating graph nodes
        // The optimizer returns new weight tensors, we need to execute them to get values
        // and update the constant values in the parameter nodes
        
        // Update parameters in the graph
        // We need to execute the optimizer computation to get the actual new parameter values
        // and then update the constant values in the graph nodes
        for (self.parameters, new_weights) |param_node_id, new_weight_handle| {
            // Mark the new weight as an output so we can execute and get its value
            try self.graph.markOutput(new_weight_handle.node_id);
            
            // Get the current parameter node to check it's a constant
            const param_node = self.graph.getNode(param_node_id) orelse {
                std.log.err("trainStep: parameter node {} not found", .{param_node_id});
                return error.NodeNotFound;
            };
            
            // Verify it's a constant node (parameters should be constants)
            if (param_node.spec != .data or param_node.spec.data != .constant) {
                std.log.err("trainStep: parameter node {} is not a constant", .{param_node_id});
                return error.NotAConstant;
            }
            
            // Execute the backward graph to compute the new parameter value
            try self.backward_executor.?.run();
            
            // Get the computed new parameter value
            const new_param_output = self.backward_executor.?.getOutput(new_weight_handle.node_id) catch |err| {
                std.log.err("trainStep: failed to get output for new weight node {}: {}", .{ new_weight_handle.node_id, err });
                return err;
            };
            
            // Extract the scalar value (parameters are scalars in our current tests)
            if (new_param_output.data.len < @sizeOf(f32)) {
                std.log.err("trainStep: new parameter output too small ({} bytes)", .{new_param_output.data.len});
                return error.InvalidParameterSize;
            }
            
            const new_value = @as(*const f32, @ptrCast(@alignCast(new_param_output.data.ptr))).*;
            
            // Update the constant value in the graph
            self.graph.updateConstantValue(param_node_id, new_value) catch |err| {
                std.log.err("trainStep: failed to update constant value for parameter {}: {}", .{ param_node_id, err });
                return err;
            };
            
            std.log.debug("Updated parameter {} from old value to {:.6}", .{ param_node_id, new_value });
        }
        
        // Update metrics
        self.metrics.updateLoss(loss_value);
        self.metrics.step += 1;
        self.metrics.total_steps += 1;
        
        // Log if needed
        if (self.metrics.total_steps % self.config.log_interval == 0) {
            std.log.info("Step {}: loss = {:.4}, lr = {:.6}", .{
                self.metrics.total_steps,
                self.metrics.loss_ema,
                self.metrics.learning_rate,
            });
        }
        
        return loss_value;
    }
    
    /// Clip gradients by global norm
    fn clipGradients(self: *Trainer, gradients: []optimizer.GradientInfo, max_norm: f32) !void {
        _ = self;
        
        // Compute global norm
        const global_norm_sq: f32 = 0;
        for (gradients) |grad_info| {
            // TODO: Compute L2 norm of gradient tensor
            _ = grad_info;
        }
        
        const global_norm = @sqrt(global_norm_sq);
        if (global_norm > max_norm) {
            const scale = max_norm / global_norm;
            
            // Scale all gradients
            for (gradients) |grad_info| {
                // TODO: Scale gradient tensor by scale factor
                _ = grad_info;
                _ = scale;
            }
        }
    }
};

/// Convenience function to create an SGD trainer
pub fn sgdTrainer(
    allocator: Allocator,
    graph: *Graph,
    parameters: []const NodeId,
    config: TrainingConfig,
) !*Trainer {
    const trainer = try allocator.create(Trainer);
    trainer.* = try Trainer.init(allocator, graph, parameters, .sgd, config);
    return trainer;
}

/// Convenience function to create an Adam trainer
pub fn adamTrainer(
    allocator: Allocator,
    graph: *Graph,
    parameters: []const NodeId,
    config: TrainingConfig,
) !*Trainer {
    const trainer = try allocator.create(Trainer);
    trainer.* = try Trainer.init(allocator, graph, parameters, .adam, config);
    return trainer;
}

// ===== Tests =====

test "trainer initialization" {
    const testing = std.testing;
    
    var graph = try Graph.init(testing.allocator);
    defer graph.deinit();
    
    // Create some dummy parameters
    const param1 = try graph.addConstant(0.5);
    const param2 = try graph.addConstant(-0.3);
    const params = [_]NodeId{ param1, param2 };
    
    var trainer = try Trainer.init(
        testing.allocator,
        &graph,
        &params,
        .sgd,
        .{},
    );
    defer trainer.deinit();
    
    try testing.expectEqual(@as(u32, 0), trainer.metrics.epoch);
    try testing.expectEqual(@as(u32, 0), trainer.metrics.total_steps);
    try testing.expectEqual(@as(usize, 2), trainer.parameters.len);
}

test "training metrics update" {
    const testing = std.testing;
    
    var metrics = TrainingMetrics{};
    
    metrics.updateLoss(1.0);
    try testing.expectEqual(@as(f32, 1.0), metrics.loss);
    try testing.expectEqual(@as(f32, 1.0), metrics.loss_ema);
    
    metrics.total_steps = 1;
    metrics.updateLoss(0.5);
    try testing.expectEqual(@as(f32, 0.5), metrics.loss);
    try testing.expectApproxEqAbs(@as(f32, 0.995), metrics.loss_ema, 0.001);
}

test "training config defaults" {
    const testing = std.testing;
    
    const config = TrainingConfig{};
    try testing.expectEqual(@as(u32, 10), config.epochs);
    try testing.expectEqual(@as(u32, 32), config.batch_size);
    try testing.expectEqual(@as(u32, 100), config.log_interval);
    try testing.expectEqual(@as(u32, 1000), config.eval_interval);
    try testing.expectEqual(@as(u32, 5000), config.checkpoint_interval);
    try testing.expectEqual(@as(u32, 0), config.early_stopping_patience);
    try testing.expectEqual(@as(f32, 0), config.gradient_clip);
    try testing.expect(config.seed == null);
}

test "trainer with different optimizers" {
    const testing = std.testing;
    
    var graph = try Graph.init(testing.allocator);
    defer graph.deinit();
    
    const param = try graph.addConstant(0.5);
    const params = [_]NodeId{param};
    
    // Test each optimizer type
    const optimizer_types = [_]Trainer.OptimizerType{
        .sgd,
        .sgd_momentum,
        .adam,
        .adamw,
    };
    
    for (optimizer_types) |opt_type| {
        var trainer = try Trainer.init(
            testing.allocator,
            &graph,
            &params,
            opt_type,
            .{},
        );
        defer trainer.deinit();
        
        try testing.expectEqual(opt_type, trainer.optimizer_type);
        try testing.expectEqual(@as(usize, 1), trainer.parameters.len);
    }
}

test "trainer lifecycle" {
    const testing = std.testing;
    
    var graph = try Graph.init(testing.allocator);
    defer graph.deinit();
    
    // Create a simple model: y = w * x + b
    const w = try graph.addConstant(0.5);
    const b = try graph.addConstant(0.1);
    const x = try graph.addPlaceholder(&.{1}, .f32);
    const wx = try graph.addNode(.mul, &.{ w, x }, .f32);
    _ = try graph.addNode(.add, &.{ wx, b }, .f32);
    
    // Create trainer
    var trainer = try Trainer.init(
        testing.allocator,
        &graph,
        &[_]NodeId{ w, b },
        .sgd,
        .{ .epochs = 1, .log_interval = 1 },
    );
    defer trainer.deinit();
    
    // Set scheduler
    var sched = scheduler.constant(0.01);
    trainer.setScheduler(&sched);
    
    // Verify compilation hasn't happened yet
    try testing.expect(trainer.forward_compiled == null);
    try testing.expect(trainer.backward_compiled == null);
    
    // In a full test, we would:
    // 1. Create loss node
    // 2. Call trainer.compile()
    // 3. Run trainer.trainStep() with data
    // 4. Verify metrics update
}

test "convenience trainer constructors" {
    const testing = std.testing;
    
    var graph = try Graph.init(testing.allocator);
    defer graph.deinit();
    
    const param = try graph.addConstant(1.0);
    const params = [_]NodeId{param};
    
    // Test SGD trainer
    const sgd_trainer = try sgdTrainer(
        testing.allocator,
        &graph,
        &params,
        .{},
    );
    defer {
        sgd_trainer.deinit();
        testing.allocator.destroy(sgd_trainer);
    }
    try testing.expectEqual(Trainer.OptimizerType.sgd, sgd_trainer.optimizer_type);
    
    // Test Adam trainer
    const adam_trainer = try adamTrainer(
        testing.allocator,
        &graph,
        &params,
        .{},
    );
    defer {
        adam_trainer.deinit();
        testing.allocator.destroy(adam_trainer);
    }
    try testing.expectEqual(Trainer.OptimizerType.adam, adam_trainer.optimizer_type);
}

test "metrics tracking" {
    const testing = std.testing;
    
    var metrics = TrainingMetrics{};
    
    // Test loss EMA calculation
    const losses = [_]f32{ 1.0, 0.8, 0.6, 0.5, 0.4 };
    for (losses, 0..) |loss, i| {
        metrics.total_steps = @intCast(i);
        metrics.updateLoss(loss);
    }
    
    // EMA should be smoother than raw loss
    try testing.expect(metrics.loss_ema > metrics.loss);
    try testing.expect(metrics.loss_ema < losses[0]);
    
    // Test validation tracking
    metrics.val_loss = 0.3;
    try testing.expectEqual(@as(?f32, 0.3), metrics.val_loss);
    
    // Test best validation tracking
    metrics.best_val_loss = 0.25;
    metrics.steps_since_best = 100;
    try testing.expectEqual(@as(?f32, 0.25), metrics.best_val_loss);
    try testing.expectEqual(@as(u32, 100), metrics.steps_since_best);
}

test "parameter store integration" {
    const testing = std.testing;
    
    var graph = try Graph.init(testing.allocator);
    defer graph.deinit();
    
    const param = try graph.addConstant(1.0);
    const params = [_]NodeId{param};
    
    var trainer = try Trainer.init(
        testing.allocator,
        &graph,
        &params,
        .adam,
        .{},
    );
    defer trainer.deinit();
    
    // Initially no parameter store
    try testing.expect(trainer.param_store == null);
    
    // Create and set parameter store
    var store = ParameterStore.init(testing.allocator);
    defer store.deinit();
    
    try trainer.setParameterStore(&store);
    try testing.expect(trainer.param_store != null);
    try testing.expectEqual(&store, trainer.param_store.?);
}

test "gradient clipping placeholder" {
    const testing = std.testing;
    
    var graph = try Graph.init(testing.allocator);
    defer graph.deinit();
    
    const param = try graph.addConstant(1.0);
    const params = [_]NodeId{param};
    
    // Create trainer with gradient clipping enabled
    var trainer = try Trainer.init(
        testing.allocator,
        &graph,
        &params,
        .sgd,
        .{ .gradient_clip = 1.0 },
    );
    defer trainer.deinit();
    
    try testing.expectEqual(@as(f32, 1.0), trainer.config.gradient_clip);
    
    // Test gradient clipping function (placeholder implementation)
    const grad_info = [_]optimizer.GradientInfo{
        .{ .node_id = param, .shape = try shape.ShapeTracker.init(&.{}, testing.allocator) },
    };
    
    // This is a placeholder test - actual implementation would clip gradients
    try trainer.clipGradients(&grad_info, 1.0);
}

test "training configuration with seed" {
    const testing = std.testing;
    
    var graph = try Graph.init(testing.allocator);
    defer graph.deinit();
    
    const param = try graph.addConstant(1.0);
    const params = [_]NodeId{param};
    
    // Create trainer with seed for reproducibility
    var trainer = try Trainer.init(
        testing.allocator,
        &graph,
        &params,
        .adam,
        .{ .seed = 42 },
    );
    defer trainer.deinit();
    
    try testing.expectEqual(@as(?u64, 42), trainer.config.seed);
}

test "early stopping configuration" {
    const testing = std.testing;
    
    const config = TrainingConfig{
        .early_stopping_patience = 5,
    };
    
    try testing.expectEqual(@as(u32, 5), config.early_stopping_patience);
    
    // Test metrics for early stopping
    var metrics = TrainingMetrics{};
    metrics.best_val_loss = 0.5;
    metrics.val_loss = 0.6; // Worse than best
    metrics.steps_since_best = 3;
    
    // In practice, trainer would check:
    // if (metrics.steps_since_best >= config.early_stopping_patience) { stop training }
    try testing.expect(metrics.steps_since_best < config.early_stopping_patience);
}