/// Fixed autograd implementation that properly handles view operations
/// This implements the solution from the hypothesis - each VJP reverses its own input views
const std = @import("std");
const Allocator = std.mem.Allocator;

// Import types and core infrastructure
const types = @import("types");
const NodeId = types.NodeId;
const ComputeOp = types.ComputeOp;
const DataType = types.DataType;
const DataSource = types.DataSource;
const ShapeTracker = @import("shape").ShapeTracker;

const Graph = @import("graph").Graph;
const Node = @import("graph").Node;
const NodeMetadata = @import("graph").NodeMetadata;
const NodeSpec = types.NodeSpec;

const tensor = @import("tensor");
const TensorHandle = tensor.TensorHandle;

const shape = @import("shape");
const SymbolicDim = shape.SymbolicDim;

// Import PassContext from compiler module
const compiler = @import("compiler");
pub const PassContext = compiler.PassContext;

// ===== Gradient Map Type =====

/// Maps a parameter's NodeId to its gradient information
pub const GradientMap = std.AutoHashMapUnmanaged(NodeId, GradientNode);

pub const GradientNode = struct {
    node_id: NodeId, // Gradient computation node
    accumulated: bool, // Whether multiple gradients were summed
};

// ===== Autograd Implementation =====

/// Autograd context for a single backward pass
const AutogradContext = struct {
    ctx: *PassContext,
    gradients: GradientMap, // Forward node → gradient node mapping
    reachable_set: std.AutoHashMapUnmanaged(NodeId, void), // Nodes reachable from params+loss
    graph: *Graph, // Graph being transformed
    allocator: Allocator,

    pub fn deinit(self: *AutogradContext) void {
        self.gradients.deinit(self.allocator);
        self.reachable_set.deinit(self.allocator);
    }
};

/// Apply autograd starting from a loss node with parameters to differentiate
pub fn applyAutograd(ctx: *PassContext, parameters: []const NodeId, loss_node_id: NodeId) !void {
    // Check if gradients are supported at compile time
    if (!ctx.graph.hasGradientSupport()) {
        std.log.err("computeGradientsPass: gradients not supported in this build", .{});
        return error.GradientsNotSupported;
    }

    // Initialize gradient support in graph if needed
    try ctx.graph.initGradients();

    var autograd_ctx = AutogradContext{
        .ctx = ctx,
        .gradients = .{},
        .reachable_set = .{},
        .graph = ctx.graph,
        .allocator = ctx.allocator,
    };
    defer autograd_ctx.deinit();

    // Step 1: Compute reachable set (intersection of forward and backward reachable)
    try computeReachableSet(&autograd_ctx, parameters, loss_node_id);

    // Step 2: Get topological order and traverse in reverse
    const topo_order = try ctx.graph.topologicalSort();

    // Step 3: Initialize loss gradient (scalar 1.0)
    try initializeLossGradient(&autograd_ctx, loss_node_id);

    // Step 4: Traverse in reverse topological order and compute gradients
    var i = topo_order.len;
    while (i > 0) {
        i -= 1;
        const node_id = topo_order[i];

        if (!autograd_ctx.reachable_set.contains(node_id)) continue;

        const node = ctx.graph.getNode(node_id) orelse continue;

        // Skip if no gradient computed for this node's output
        const output_grad_node = autograd_ctx.gradients.get(node_id) orelse continue;

        // Apply gradient rule for this operation
        try applyGradientRule(&autograd_ctx, node_id, node, output_grad_node.node_id);
    }

    // Step 5: Create gradient map for parameters only
    var param_gradient_map = std.AutoHashMap(NodeId, NodeId).init(ctx.graph.allocator);
    errdefer param_gradient_map.deinit();

    for (parameters) |param_id| {
        if (autograd_ctx.gradients.get(param_id)) |grad_node| {
            try param_gradient_map.put(param_id, grad_node.node_id);
        }
    }

    // Store gradient map in graph for backend compilation
    try ctx.graph.setGradientMap(param_gradient_map);
}

/// NEW HELPER: Undo input views (transpose, expand, etc.) based on the input shape metadata
/// This is the key fix - each VJP must call this before adding gradients
fn undoInputViews(
    ctx: *AutogradContext,
    grad_handle: TensorHandle,
    input_shape: *const ShapeTracker,
) !TensorHandle {
    var result = grad_handle;
    const allocator = ctx.allocator;

    const enable_debug = @import("build_options").enable_debug_logs;
    if (enable_debug) {
        std.log.debug("undoInputViews: reversing views for gradient", .{});
        std.log.debug("  grad shape: dims={}, indexes=[{any}]", .{ result.shape.dims.len, result.shape.indexes });
        std.log.debug("  input shape: dims={}, indexes=[{any}]", .{ input_shape.dims.len, input_shape.indexes });
    }

    // STEP 1: Undo permutes by reversing index mapping
    if (input_shape.indexes.len > 0) {
        // Check if input has non-identity permutation
        var has_permutation = false;
        for (input_shape.indexes, 0..) |idx, i| {
            if (idx != i) {
                has_permutation = true;
                break;
            }
        }

        if (has_permutation and result.shape.dims.len == input_shape.dims.len) {
            if (enable_debug) {
                std.log.debug("  Undoing permutation...", .{});
            }

            // Create inverse permutation axes
            var inverse_axes = try allocator.alloc(usize, input_shape.indexes.len);
            defer allocator.free(inverse_axes);

            // Initialize array first
            for (0..inverse_axes.len) |i| {
                inverse_axes[i] = i;
            }

            for (input_shape.indexes, 0..) |target_idx, logical_idx| {
                inverse_axes[target_idx] = logical_idx;
            }

            // Apply inverse transpose to gradient
            result = try result.transpose(inverse_axes);
        }
    }

    // STEP 2: Undo expands (sum reduce broadcasted dimensions)
    if (input_shape.fake.len > 0) {
        // Iterate through dimensions in reverse order
        var i = input_shape.fake.len;
        while (i > 0) {
            i -= 1;

            if (input_shape.fake[i]) {
                if (enable_debug) {
                    std.log.debug("  Undoing broadcast on dimension {}...", .{i});
                }

                // This dimension was broadcasted - sum reduce it
                result = try result.sumReduce(i, false);
            }
        }
    }

    // STEP 3: Final shape adjustment if needed
    result = try reduceGradientToShape(ctx, result, input_shape);

    return result;
}

/// Compute the reachable set (intersection of forward and backward reachable nodes)
fn computeReachableSet(ctx: *AutogradContext, parameters: []const NodeId, loss_node: NodeId) !void {
    var forward_reachable = std.AutoHashMapUnmanaged(NodeId, void){};
    defer forward_reachable.deinit(ctx.allocator);

    var backward_reachable = std.AutoHashMapUnmanaged(NodeId, void){};
    defer backward_reachable.deinit(ctx.allocator);

    // Forward reachability from parameters
    var forward_stack = std.ArrayList(NodeId).init(ctx.allocator);
    defer forward_stack.deinit();

    for (parameters) |param| {
        try forward_stack.append(param);
        try forward_reachable.put(ctx.allocator, param, {});
    }

    while (forward_stack.items.len > 0) {
        const current = forward_stack.pop() orelse unreachable;
        const consumers = ctx.graph.iterateConsumers(current) orelse continue;

        for (consumers) |consumer| {
            if (!forward_reachable.contains(consumer)) {
                try forward_reachable.put(ctx.allocator, consumer, {});
                try forward_stack.append(consumer);
            }
        }
    }

    // Backward reachability from loss
    var backward_stack = std.ArrayList(NodeId).init(ctx.allocator);
    defer backward_stack.deinit();

    try backward_stack.append(loss_node);
    try backward_reachable.put(ctx.allocator, loss_node, {});

    while (backward_stack.items.len > 0) {
        const current = backward_stack.pop() orelse unreachable;
        const node = ctx.graph.getNode(current) orelse continue;

        if (node.spec == .compute) {
            for (node.inputs) |input_id| {
                if (!backward_reachable.contains(input_id)) {
                    try backward_reachable.put(ctx.allocator, input_id, {});
                    try backward_stack.append(input_id);
                }
            }
        }
    }

    // Intersection = nodes reachable from both parameters and loss
    var forward_iter = forward_reachable.iterator();
    while (forward_iter.next()) |entry| {
        if (backward_reachable.contains(entry.key_ptr.*)) {
            try ctx.reachable_set.put(ctx.allocator, entry.key_ptr.*, {});
        }
    }
}

/// Initialize loss gradient as scalar 1.0
fn initializeLossGradient(ctx: *AutogradContext, loss_node: NodeId) !void {
    const one_tensor = try tensor.constant(ctx.graph, 1.0, .f32);

    try ctx.gradients.put(ctx.allocator, loss_node, GradientNode{
        .node_id = one_tensor.node_id,
        .accumulated = false,
    });
}

/// Apply gradient rule for a specific node
fn applyGradientRule(ctx: *AutogradContext, node_id: NodeId, node: *const Node, output_grad: NodeId) !void {
    switch (node.spec) {
        .data => {},
        .compute => |op| {
            try computeVJP(ctx, node_id, node, op, output_grad);
        },
    }
}

/// Compute Vector-Jacobian Product for an operation
fn computeVJP(
    ctx: *AutogradContext,
    node_id: NodeId,
    node: *const Node,
    op: ComputeOp,
    output_grad: NodeId,
) !void {
    const graph = ctx.graph;

    switch (op) {
        .add => {
            // Gradient of add: ∂(a+b)/∂a = 1, ∂(a+b)/∂b = 1
            const a = node.inputs[0];
            const b = node.inputs[1];
            const output_grad_handle = try TensorHandle.fromNode(graph, output_grad);

            // Get the input shapes used by this operation
            if (node.metadata) |meta| {
                if (meta.input_shapes) |shapes| {
                    // Reverse any views on the inputs before adding gradients
                    const grad_a = try undoInputViews(ctx, output_grad_handle, &shapes[0]);
                    const grad_b = try undoInputViews(ctx, output_grad_handle, &shapes[1]);

                    try addGradToNode(ctx, a, grad_a.node_id);
                    try addGradToNode(ctx, b, grad_b.node_id);
                    return;
                }
            }

            // Fallback for nodes without metadata
            try addGradToNode(ctx, a, output_grad_handle.node_id);
            try addGradToNode(ctx, b, output_grad_handle.node_id);
        },
        .mul => {
            // Gradient of mul: ∂(a*b)/∂a = b, ∂(a*b)/∂b = a
            const a = node.inputs[0];
            const b = node.inputs[1];
            const output_grad_handle = try TensorHandle.fromNode(graph, output_grad);

            // Get the input shapes used by this operation
            if (node.metadata) |meta| {
                if (meta.input_shapes) |shapes| {
                    // Create handles with the shapes used during forward pass
                    var a_handle = try TensorHandle.fromNode(graph, a);
                    a_handle.shape = try shapes[0].clone(ctx.allocator);

                    var b_handle = try TensorHandle.fromNode(graph, b);
                    b_handle.shape = try shapes[1].clone(ctx.allocator);

                    // Compute gradients using the forward shapes
                    const grad_a_expanded = try output_grad_handle.mul(b_handle);
                    const grad_b_expanded = try output_grad_handle.mul(a_handle);

                    // Undo any views before adding gradients
                    const grad_a = try undoInputViews(ctx, grad_a_expanded, &shapes[0]);
                    const grad_b = try undoInputViews(ctx, grad_b_expanded, &shapes[1]);

                    try addGradToNode(ctx, a, grad_a.node_id);
                    try addGradToNode(ctx, b, grad_b.node_id);
                    return;
                }
            }

            // Fallback for nodes without metadata
            const a_handle = try TensorHandle.fromNode(graph, a);
            const b_handle = try TensorHandle.fromNode(graph, b);
            const grad_a = try output_grad_handle.mul(b_handle);
            const grad_b = try output_grad_handle.mul(a_handle);
            try addGradToNode(ctx, a, grad_a.node_id);
            try addGradToNode(ctx, b, grad_b.node_id);
        },
        .recip => {
            // Gradient of 1/x is -1/x^2
            const x = node.inputs[0];
            const x_handle = try TensorHandle.fromNode(graph, x);
            const output_grad_handle = try TensorHandle.fromNode(graph, output_grad);

            const neg_one = try tensor.constant(graph, -1.0, output_grad_handle.dtype);
            const x_squared = try x_handle.mul(x_handle);
            const grad = try neg_one.divide(x_squared);
            const final_grad = try output_grad_handle.mul(grad);

            try addGradToNode(ctx, x, final_grad.node_id);
        },
        .sqrt => {
            // Gradient of sqrt(x) is 1 / (2 * sqrt(x))
            const x = node.inputs[0];
            const output_grad_handle = try TensorHandle.fromNode(graph, output_grad);

            const two = try tensor.constant(graph, 2.0, output_grad_handle.dtype);
            const sqrt_x = try TensorHandle.fromNode(graph, node_id);
            const grad = try two.mul(sqrt_x);
            const final_grad = try output_grad_handle.divide(grad);

            try addGradToNode(ctx, x, final_grad.node_id);
        },
        .exp2 => {
            // Gradient of exp2(x) is ln(2) * exp2(x)
            const x = node.inputs[0];
            const output_grad_handle = try TensorHandle.fromNode(graph, output_grad);

            const ln2 = try tensor.constant(graph, std.math.ln2, output_grad_handle.dtype);
            const exp2_x = try TensorHandle.fromNode(graph, node_id);
            const grad = try ln2.mul(exp2_x);
            const final_grad = try output_grad_handle.mul(grad);

            try addGradToNode(ctx, x, final_grad.node_id);
        },
        .log2 => {
            // Gradient of log2(x) is 1 / (x * ln(2))
            const x = node.inputs[0];
            const x_handle = try TensorHandle.fromNode(graph, x);
            const output_grad_handle = try TensorHandle.fromNode(graph, output_grad);

            const ln2 = try tensor.constant(graph, std.math.ln2, output_grad_handle.dtype);
            const denom = try x_handle.mul(ln2);
            const final_grad = try output_grad_handle.divide(denom);

            try addGradToNode(ctx, x, final_grad.node_id);
        },
        .sin => {
            // Gradient of sin(x) is cos(x)
            const x = node.inputs[0];
            const x_handle = try TensorHandle.fromNode(graph, x);
            const output_grad_handle = try TensorHandle.fromNode(graph, output_grad);

            const grad = try x_handle.cos();
            const final_grad = try output_grad_handle.mul(grad);

            try addGradToNode(ctx, x, final_grad.node_id);
        },
        .sum_reduce => {
            // Gradient of sum is 1.0 everywhere, so just expand the output gradient
            const input = node.inputs[0];
            const input_shape = graph.getNodeShape(input) orelse return error.ShapeNotFound;
            var grad_handle = try TensorHandle.fromNode(graph, output_grad);

            // Reshape output gradient to match input shape
            const axis = node.metadata.?.reduction_axis orelse return error.MetadataNotFound;
            if (!node.metadata.?.keepdims) {
                // Expand gradient to match input shape
                if (grad_handle.shape.dims.len > @as(usize, @intCast(axis))) {
                    const original_dim_size = switch (input_shape.dims[@intCast(axis)]) {
                        .concrete => |v| v,
                        .dynamic => return error.DynamicDimensionNotSupported,
                    };
                    grad_handle = try grad_handle.expandDim(@intCast(axis), original_dim_size);
                } else {
                    // Expand from scalar/lower rank
                    for (input_shape.dims, 0..) |dim, dim_idx| {
                        const target_dim_size = switch (dim) {
                            .concrete => |v| v,
                            .dynamic => return error.DynamicDimensionNotSupported,
                        };

                        if (grad_handle.shape.dims.len <= dim_idx) {
                            grad_handle = try grad_handle.expandDim(grad_handle.shape.dims.len, target_dim_size);
                        }
                    }
                }
            }

            try addGradToNode(ctx, input, grad_handle.node_id);
        },
        .max_reduce => {
            // Gradient of max is 1.0 only where the max occurred, 0.0 elsewhere
            // Ideally we'd store argmax indices during forward pass
            // For now, we compute it: grad = (input == max_output) * output_grad
            const input = node.inputs[0];
            const input_handle = try TensorHandle.fromNode(graph, input);
            var output_handle = try TensorHandle.fromNode(graph, node_id);
            var grad_handle = try TensorHandle.fromNode(graph, output_grad);

            const axis = node.metadata.?.reduction_axis orelse return error.MetadataNotFound;
            const keepdims = node.metadata.?.keepdims;

            // If not keepdims, expand output and grad to match input shape
            if (!keepdims) {
                if (output_handle.shape.dims.len > @as(usize, @intCast(axis))) {
                    const input_shape = graph.getNodeShape(input) orelse return error.ShapeNotFound;
                    const original_dim_size = switch (input_shape.dims[@intCast(axis)]) {
                        .concrete => |v| v,
                        .dynamic => return error.DynamicDimensionNotSupported,
                    };
                    output_handle = try output_handle.expandDim(@intCast(axis), original_dim_size);
                    grad_handle = try grad_handle.expandDim(@intCast(axis), original_dim_size);
                } else {
                    // Expand from scalar/lower rank
                    const input_shape = graph.getNodeShape(input) orelse return error.ShapeNotFound;
                    for (input_shape.dims, 0..) |dim, dim_idx| {
                        const target_dim_size = switch (dim) {
                            .concrete => |v| v,
                            .dynamic => return error.DynamicDimensionNotSupported,
                        };

                        if (output_handle.shape.dims.len <= dim_idx) {
                            output_handle = try output_handle.expandDim(output_handle.shape.dims.len, target_dim_size);
                            grad_handle = try grad_handle.expandDim(grad_handle.shape.dims.len, target_dim_size);
                        }
                    }
                }
            }

            // Create mask: where input == max_output
            const is_max = try input_handle.equal(output_handle);

            // Multiply gradient by mask
            const masked_grad = try grad_handle.mul(is_max);

            // Handle the case where multiple elements have the max value
            // We need to normalize by the count of max elements
            // For now, we'll just pass the masked gradient
            // TODO: Proper normalization when multiple elements are max

            try addGradToNode(ctx, input, masked_grad.node_id);
        },
        .mod => {
            std.log.err("modulo operation is not differentiable", .{});
            return error.NonDifferentiableOperation;
        },
        .less_than => {
            // Comparison operations have zero gradient
            const zero = try tensor.constant(graph, 0.0, .f32);
            try addGradToNode(ctx, node.inputs[0], zero.node_id);
            try addGradToNode(ctx, node.inputs[1], zero.node_id);
        },
        .contiguous => {
            // Contiguous operation: gradient flows through
            const input = node.inputs[0];

            // Get input shape from metadata if available
            if (node.metadata) |meta| {
                if (meta.input_shapes) |shapes| {
                    // Use undoInputViews to handle any transpose in the contiguous operation
                    const output_grad_handle = try TensorHandle.fromNode(graph, output_grad);
                    const grad = try undoInputViews(ctx, output_grad_handle, &shapes[0]);
                    try addGradToNode(ctx, input, grad.node_id);
                    return;
                }
            }

            // Fallback - pass gradient through directly
            try addGradToNode(ctx, input, output_grad);
        },
        else => {
            std.log.warn("gradient for op '{s}' not implemented", .{@tagName(op)});
        },
    }
}

/// Reduce gradient to match original shape (handling broadcasting)
fn reduceGradientToShape(
    ctx: *AutogradContext,
    grad_handle: TensorHandle,
    target_shape: *const ShapeTracker,
) !TensorHandle {
    var result = grad_handle;

    // Step 1: Align ranks
    while (result.shape.dims.len > target_shape.dims.len) {
        const old_result_node_id = result.node_id;
        result = try result.sumReduce(0, false);

        // The new `sumReduce` node's shape must be explicitly set in the graph.
        const old_shape = ctx.graph.getNode(old_result_node_id).?.metadata.?.output_shape orelse return error.ShapeNotFound;
        var new_shape = try old_shape.clone(ctx.allocator);
        new_shape.dims = new_shape.dims[1..];

        // Get the new node and set its metadata
        const new_node = ctx.graph.getNodeMut(result.node_id) orelse return error.NodeNotFound;
        if (new_node.metadata == null) {
            new_node.metadata = try ctx.graph.arena.allocator().create(NodeMetadata);
            new_node.metadata.?.* = .{}; // Initialize to default values
        }
        new_node.metadata.?.output_shape = new_shape;
    }

    while (result.shape.dims.len < target_shape.dims.len) {
        const target_dim_size = switch (target_shape.dims[result.shape.dims.len]) {
            .concrete => |v| v,
            .dynamic => return error.DynamicDimensionNotSupported,
        };
        result = try result.expandDim(@intCast(result.shape.dims.len), target_dim_size);
    }

    if (result.shape.dims.len != target_shape.dims.len) {
        return error.RankMismatch;
    }

    // Step 2: Handle dimension reduction
    for (target_shape.dims, 0..) |target_dim, axis| {
        if (axis >= result.shape.dims.len) break;

        const target_size = switch (target_dim) {
            .concrete => |v| v,
            .dynamic => continue,
        };

        const current_size = switch (result.shape.dims[axis]) {
            .concrete => |v| v,
            .dynamic => continue,
        };

        if (target_size == 1 and current_size > 1) {
            const old_result_node_id = result.node_id;
            result = try result.sumReduce(axis, true);

            // The new `sumReduce` node's shape must be explicitly set in the graph.
            const old_shape = ctx.graph.getNode(old_result_node_id).?.metadata.?.output_shape orelse return error.ShapeNotFound;
            var new_shape = try old_shape.clone(ctx.allocator);
            new_shape.dims[axis] = .{ .concrete = 1 };

            // Get the new node and set its metadata
            const new_node = ctx.graph.getNodeMut(result.node_id) orelse return error.NodeNotFound;
            if (new_node.metadata == null) {
                new_node.metadata = try ctx.graph.arena.allocator().create(NodeMetadata);
                new_node.metadata.?.* = .{}; // Initialize to default values
            }
            new_node.metadata.?.output_shape = new_shape;
        } else if (target_size != current_size) {
            std.log.err("reduceGradientToShape: dimension {} incompatible - current: {}, target: {}", .{ axis, current_size, target_size });
            return error.IncompatibleGradientShape;
        }
    }

    return result;
}

/// SIMPLIFIED: Add gradient to a node (only handles accumulation)
fn addGradToNode(ctx: *AutogradContext, target_node: NodeId, new_grad: NodeId) !void {
    // Get the new gradient handle
    const grad_handle = try TensorHandle.fromNode(ctx.graph, new_grad);

    // Accumulate gradients if this node already has gradients
    if (ctx.gradients.get(target_node)) |existing_grad| {
        const existing_handle = try TensorHandle.fromNode(ctx.graph, existing_grad.node_id);
        const accumulated_handle = try existing_handle.add(grad_handle);

        try ctx.gradients.put(ctx.allocator, target_node, GradientNode{
            .node_id = accumulated_handle.node_id,
            .accumulated = true,
        });
    } else {
        // First gradient for this node
        try ctx.gradients.put(ctx.allocator, target_node, GradientNode{
            .node_id = grad_handle.node_id,
            .accumulated = false,
        });
    }
}
