/// Loss functions for training
///
/// This module provides common loss functions used in machine learning.
/// Following Luminal's design, loss functions work with TensorHandle (mid-level API)
/// and return TensorHandle directly for easy composition.

const std = @import("std");
const tensor = @import("tensor");
const TensorHandle = tensor.TensorHandle;
const Graph = @import("graph").Graph;

/// Mean Squared Error loss: (prediction - target).square().mean()
pub fn mseLoss(prediction: TensorHandle, target: TensorHandle) !TensorHandle {
    const diff = try prediction.subtract(target);
    const squared = try diff.mul(diff);
    return try mean(squared);
}

/// Root Mean Squared Error loss: sqrt(mseLoss(prediction, target))
pub fn rmseLoss(prediction: TensorHandle, target: TensorHandle) !TensorHandle {
    const mse = try mseLoss(prediction, target);
    return try mse.sqrt();
}

/// Mean Absolute Error loss: (prediction - target).abs().mean()
pub fn maeLoss(prediction: Tensor<PERSON>and<PERSON>, target: TensorHandle) !TensorHandle {
    const diff = try prediction.subtract(target);
    const abs_diff = try diff.abs();
    return try mean(abs_diff);
}

/// Huber loss (smooth L1 loss)
/// Uses absolute error when error > delta, squared error when error <= delta
/// - if |x - y| < delta: 0.5 * (x - y)^2
/// - otherwise: delta * (|x - y| - 0.5 * delta)
pub fn huberLoss(prediction: TensorHandle, target: TensorHandle, delta: f32) !TensorHandle {
    const graph = prediction.graph;
    const abs_error = try (try prediction.subtract(target)).abs();
    const delta_tensor = try tensor.constant(graph, delta, prediction.dtype);
    
    // Quadratic part: 0.5 * error^2
    const half = try tensor.constant(graph, 0.5, prediction.dtype);
    const err = try prediction.subtract(target);
    const quadratic = try half.mul(try err.mul(err));
    
    // Linear part: delta * (|error| - 0.5 * delta)
    const half_delta = try half.mul(delta_tensor);
    const linear_term = try abs_error.subtract(half_delta);
    const linear = try delta_tensor.mul(linear_term);
    
    // Select based on condition: |error| < delta
    const condition = try abs_error.lessThan(delta_tensor);
    const loss_values = try where(condition, quadratic, linear);
    
    return try mean(loss_values);
}

/// Smooth L1 loss: huberLoss / delta
pub fn smoothL1Loss(prediction: TensorHandle, target: TensorHandle, delta: f32) !TensorHandle {
    const graph = prediction.graph;
    const huber = try huberLoss(prediction, target, delta);
    const delta_tensor = try tensor.constant(graph, delta, prediction.dtype);
    return try huber.divide(delta_tensor);
}

/// Binary Cross-Entropy loss for probabilities
/// Expects sigmoid outputs (probabilities), NOT logits
/// Computes: -(target * log(prediction) + (1 - target) * log(1 - prediction))
pub fn binaryCrossEntropyLoss(prediction: TensorHandle, target: TensorHandle) !TensorHandle {
    const graph = prediction.graph;
    const one = try tensor.constant(graph, 1.0, prediction.dtype);
    
    // Clamp predictions to avoid log(0)
    const pred_clamped = try prediction.clamp(1e-7, 1.0 - 1e-7);
    
    // target * log(prediction)
    const log_pred = try pred_clamped.log();
    const first_term = try target.mul(log_pred);
    
    // (1 - target) * log(1 - prediction)
    const one_minus_target = try one.subtract(target);
    const one_minus_pred = try one.subtract(pred_clamped);
    const log_one_minus_pred = try one_minus_pred.log();
    const second_term = try one_minus_target.mul(log_one_minus_pred);
    
    // BCE = -(first_term + second_term)
    const bce_sum = try first_term.add(second_term);
    const bce = try bce_sum.neg();
    
    return try mean(bce);
}

/// Binary Cross-Entropy with logits (numerically stable)
/// Expects raw logits, NOT sigmoid outputs
/// Computes: (1 - target) * logits + log(1 + exp(-logits))
pub fn binaryCrossEntropyWithLogitsLoss(logits: TensorHandle, target: TensorHandle) !TensorHandle {
    const graph = logits.graph;
    const one = try tensor.constant(graph, 1.0, logits.dtype);
    const one_minus_target = try one.subtract(target);
    
    // (1 - target) * logits
    const first_term = try one_minus_target.mul(logits);
    
    // log(1 + exp(-logits))
    const neg_logits = try logits.neg();
    const exp_neg_logits = try neg_logits.exp();
    const one_plus_exp = try one.add(exp_neg_logits);
    const second_term = try one_plus_exp.log();
    
    const bce = try first_term.add(second_term);
    return try mean(bce);
}

/// Cross-Entropy loss with logits for multi-class classification
/// Expects raw logits (NOT softmax outputs) and target probabilities (one-hot)
/// Computes: -(logits.log_softmax() * target_probs).sum(-1).mean()
pub fn crossEntropyWithLogitsLoss(logits: TensorHandle, target_probs: TensorHandle) !TensorHandle {
    // Apply log_softmax to logits along last axis
    const last_axis_usize = @as(usize, @intCast(logits.rank() - 1));
    const log_probs = try logits.logSoftmax(last_axis_usize);
    
    // Element-wise multiply with target probabilities and sum over classes
    const weighted = try log_probs.mul(target_probs);
    const neg_weighted = try weighted.neg();
    
    // Sum over last dimension (classes) then mean over batch
    const last_axis = @as(i32, @intCast(logits.rank() - 1));
    const loss_per_sample = try neg_weighted.sumReduce(@intCast(last_axis), true);
    
    return try mean(loss_per_sample);
}

/// KL Divergence loss
/// Computes: (target_probs * (target_probs.log() - logits.log_softmax())).sum(-1).mean()
pub fn klDivWithLogitsLoss(logits: TensorHandle, target_probs: TensorHandle) !TensorHandle {
    const last_axis_usize = @as(usize, @intCast(logits.rank() - 1));
    const log_probs = try logits.logSoftmax(last_axis_usize);
    const target_log_probs = try target_probs.log();
    
    // target_probs * (target_log_probs - log_probs)
    const diff = try target_log_probs.subtract(log_probs);
    const weighted = try target_probs.mul(diff);
    
    // Sum over last dimension then mean
    const last_axis = @as(i32, @intCast(logits.rank() - 1));
    const loss_per_sample = try weighted.sumReduce(@intCast(last_axis), true);
    
    return try mean(loss_per_sample);
}

// ===== Helper functions =====

/// Compute mean of all elements in a tensor
fn mean(t: TensorHandle) !TensorHandle {
    return try t.meanAll();
}

/// Conditional selection: where(condition, true_val, false_val)
fn where(condition: TensorHandle, true_val: TensorHandle, false_val: TensorHandle) !TensorHandle {
    return try condition.select(true_val, false_val);
}

// ===== Tests =====

test "MSE loss" {
    const testing = std.testing;
    
    var graph = try Graph.init(testing.allocator);
    defer graph.deinit();
    
    // Create test data
    const pred_data = [_]f32{1.0, 2.0, 3.0, 4.0};
    const target_data = [_]f32{1.5, 2.5, 2.5, 3.5};
    
    const pred = try tensor.constant(&graph, pred_data[0], .f32);
    const target = try tensor.constant(&graph, target_data[0], .f32);
    
    // For now, just test that loss function compiles and returns a handle
    const loss = try mseLoss(pred, target);
    try testing.expect(loss.node_id != 0);
}

test "MAE loss" {
    const testing = std.testing;
    
    var graph = try Graph.init(testing.allocator);
    defer graph.deinit();
    
    const pred = try tensor.constant(&graph, 2.0, .f32);
    const target = try tensor.constant(&graph, 1.5, .f32);
    
    const loss = try maeLoss(pred, target);
    try testing.expect(loss.node_id != 0);
}

test "Huber loss" {
    const testing = std.testing;
    
    var graph = try Graph.init(testing.allocator);
    defer graph.deinit();
    
    const pred = try tensor.constant(&graph, 5.0, .f32);
    const target = try tensor.constant(&graph, 3.0, .f32);
    
    const loss = try huberLoss(pred, target, 1.0);
    try testing.expect(loss.node_id != 0);
}

test "RMSE loss" {
    const testing = std.testing;
    
    var graph = try Graph.init(testing.allocator);
    defer graph.deinit();
    
    const pred = try tensor.constant(&graph, 4.0, .f32);
    const target = try tensor.constant(&graph, 1.0, .f32);
    
    // RMSE of (4-1)^2 = sqrt(9) = 3
    const loss = try rmseLoss(pred, target);
    try testing.expect(loss.node_id != 0);
}

test "Smooth L1 loss" {
    const testing = std.testing;
    
    var graph = try Graph.init(testing.allocator);
    defer graph.deinit();
    
    const pred = try tensor.constant(&graph, 2.5, .f32);
    const target = try tensor.constant(&graph, 2.0, .f32);
    
    const loss = try smoothL1Loss(pred, target, 1.0);
    try testing.expect(loss.node_id != 0);
}

test "Binary cross-entropy with logits loss" {
    const testing = std.testing;
    
    var graph = try Graph.init(testing.allocator);
    defer graph.deinit();
    
    const logits = try tensor.constant(&graph, 2.0, .f32); // Raw logit
    const target = try tensor.constant(&graph, 1.0, .f32); // Binary target
    
    const loss = try binaryCrossEntropyWithLogitsLoss(logits, target);
    try testing.expect(loss.node_id != 0);
}

test "Cross-entropy with logits loss" {
    const testing = std.testing;
    
    var graph = try Graph.init(testing.allocator);
    defer graph.deinit();
    
    // Create logits for 3 classes
    const logits = try tensor.zeros(&graph, &.{ 1, 3 }, .f32);
    const target_probs = try tensor.ones(&graph, &.{ 1, 3 }, .f32); // One-hot encoded
    
    const loss = try crossEntropyWithLogitsLoss(logits, target_probs);
    try testing.expect(loss.node_id != 0);
}

test "KL divergence with logits loss" {
    const testing = std.testing;
    
    var graph = try Graph.init(testing.allocator);
    defer graph.deinit();
    
    const logits = try tensor.zeros(&graph, &.{ 1, 3 }, .f32);
    const target_probs = try tensor.ones(&graph, &.{ 1, 3 }, .f32);
    
    const loss = try klDivWithLogitsLoss(logits, target_probs);
    try testing.expect(loss.node_id != 0);
}

test "Loss functions with vector inputs" {
    const testing = std.testing;
    
    var graph = try Graph.init(testing.allocator);
    defer graph.deinit();
    
    // Test with vector inputs instead of scalars
    const pred = try tensor.zeros(&graph, &.{4}, .f32);
    const target = try tensor.ones(&graph, &.{4}, .f32);
    
    // Test all loss functions
    const mse = try mseLoss(pred, target);
    try testing.expect(mse.node_id != 0);
    
    const rmse = try rmseLoss(pred, target);
    try testing.expect(rmse.node_id != 0);
    
    const mae = try maeLoss(pred, target);
    try testing.expect(mae.node_id != 0);
    
    const huber = try huberLoss(pred, target, 0.5);
    try testing.expect(huber.node_id != 0);
}

test "Loss functions with matrix inputs" {
    const testing = std.testing;
    
    var graph = try Graph.init(testing.allocator);
    defer graph.deinit();
    
    // Test with matrix inputs
    const pred = try tensor.zeros(&graph, &.{ 3, 4 }, .f32);
    const target = try tensor.ones(&graph, &.{ 3, 4 }, .f32);
    
    const mse = try mseLoss(pred, target);
    try testing.expect(mse.node_id != 0);
    
    // MSE should produce a scalar output
    const shape = graph.getShape(mse.node_id);
    try testing.expect(shape != null);
}

test "Helper functions" {
    const testing = std.testing;
    
    var graph = try Graph.init(testing.allocator);
    defer graph.deinit();
    
    // Test abs function
    const neg_val = try tensor.constant(&graph, -3.14, .f32);
    const abs_val = try neg_val.abs();
    try testing.expect(abs_val.node_id != 0);
    
    // Test negate function
    const pos_val = try tensor.constant(&graph, 2.5, .f32);
    const neg_result = try pos_val.neg();
    try testing.expect(neg_result.node_id != 0);
    
    // Test where function
    const cond = try tensor.constant(&graph, 1.0, .f32); // True
    const true_val = try tensor.constant(&graph, 10.0, .f32);
    const false_val = try tensor.constant(&graph, 20.0, .f32);
    const result = try where(cond, true_val, false_val);
    try testing.expect(result.node_id != 0);
}

test "Mean computation" {
    const testing = std.testing;
    
    var graph = try Graph.init(testing.allocator);
    defer graph.deinit();
    
    // Test mean of a vector
    const vec = try tensor.ones(&graph, &.{5}, .f32);
    const m = try mean(vec);
    try testing.expect(m.node_id != 0);
    
    // Test mean of a matrix
    const mat = try tensor.ones(&graph, &.{ 3, 4 }, .f32);
    const m2 = try mean(mat);
    try testing.expect(m2.node_id != 0);
}

test "Sum all computation" {
    const testing = std.testing;
    
    var graph = try Graph.init(testing.allocator);
    defer graph.deinit();
    
    // Test sum of various shapes
    const scalar = try tensor.constant(&graph, 5.0, .f32);
    const s1 = try scalar.sumAll();
    try testing.expect(s1.node_id != 0);
    
    const vec = try tensor.ones(&graph, &.{10}, .f32);
    const s2 = try vec.sumAll();
    try testing.expect(s2.node_id != 0);
    
    const tensor3d = try tensor.ones(&graph, &.{ 2, 3, 4 }, .f32);
    const s3 = try tensor3d.sumAll();
    try testing.expect(s3.node_id != 0);
}