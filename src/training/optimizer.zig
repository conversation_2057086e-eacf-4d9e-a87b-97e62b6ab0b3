/// Optimizers for training neural networks
///
/// This module provides optimization algorithms following Luminal's design.
/// Optimizers work with the Tensor API and return new weight tensors.

const std = @import("std");
const Allocator = std.mem.Allocator;

const types = @import("types");
const NodeId = types.NodeId;
const DataType = types.DataType;

const Graph = @import("graph").Graph;
const tensor = @import("tensor");
const TensorHandle = tensor.TensorHandle;
const shape_mod = @import("shape");
const ShapeTracker = shape_mod.ShapeTracker;

/// Gradient information for a parameter
pub const GradientInfo = struct {
    node_id: NodeId,
    shape: ShapeTracker,
};

/// Stochastic Gradient Descent (SGD)
/// new_weight = old_weight - (gradient * learning_rate)
pub const SGD = struct {
    graph: *Graph,
    learning_rate: TensorHandle,
    
    /// Initialize SGD optimizer with a learning rate
    pub fn init(graph: *Graph, learning_rate: f32) !SGD {
        const lr = try tensor.constant(graph, learning_rate, .f32);
        return SGD{
            .graph = graph,
            .learning_rate = lr,
        };
    }
    
    /// Apply SGD update to parameters
    /// Returns new weight tensors in the same order as old_weights
    pub fn step(
        self: *SGD,
        old_weights: []const TensorHandle,
        gradients: []const GradientInfo,
    ) ![]TensorHandle {
        const allocator = self.graph.allocator;
        var new_weights = try allocator.alloc(TensorHandle, old_weights.len);
        
        for (old_weights, gradients, 0..) |old_weight, grad_info, i| {
            // Get gradient tensor
            const gradient = try TensorHandle.fromNode(self.graph, grad_info.node_id);
            
            // Expand learning rate to match gradient shape if needed
            const lr_expanded = try self.learning_rate.expandToShape(grad_info.shape);
            
            // SGD update: new_weight = old_weight - (gradient * learning_rate)
            const scaled_grad = try gradient.mul(lr_expanded);
            new_weights[i] = try old_weight.subtract(scaled_grad);
        }
        
        return new_weights;
    }
    
    /// Set the learning rate
    pub fn setLearningRate(self: *SGD, new_lr: f32) !void {
        self.learning_rate = try tensor.constant(self.graph, new_lr, .f32);
    }
};

/// SGD with Momentum
/// velocity = momentum * velocity - learning_rate * gradient
/// new_weight = old_weight + velocity
pub const SGDMomentum = struct {
    graph: *Graph,
    learning_rate: TensorHandle,
    momentum: f32,
    velocities: std.ArrayList(TensorHandle),
    
    pub fn init(graph: *Graph, learning_rate: f32, momentum: f32) !SGDMomentum {
        const lr = try tensor.constant(graph, learning_rate, .f32);
        return SGDMomentum{
            .graph = graph,
            .learning_rate = lr,
            .momentum = momentum,
            .velocities = std.ArrayList(TensorHandle).init(graph.allocator),
        };
    }
    
    pub fn deinit(self: *SGDMomentum) void {
        self.velocities.deinit();
    }
    
    pub fn step(
        self: *SGDMomentum,
        old_weights: []const TensorHandle,
        gradients: []const GradientInfo,
    ) ![]TensorHandle {
        const allocator = self.graph.allocator;
        var new_weights = try allocator.alloc(TensorHandle, old_weights.len);
        
        // Initialize velocities on first step
        if (self.velocities.items.len == 0) {
            for (old_weights) |weight| {
                const shape = self.graph.getNodeShape(weight.node_id) orelse {
                    std.log.err("optimizer: shape not found for weight node {} during initialization", .{weight.node_id});
                    return error.ShapeNotFound;
                };
                // Convert SymbolicDim to i64 array
                var dims: [8]i64 = undefined;
                const shape_utils = @import("shape").utils;
                try shape_utils.symbolicDimsToConcreteArray(shape.dims, dims[0..shape.dims.len]);
                const zeros = try tensor.zeros(self.graph, dims[0..shape.dims.len], weight.dtype);
                try self.velocities.append(zeros);
            }
        }
        
        const momentum_tensor = try tensor.constant(self.graph, self.momentum, .f32);
        
        for (old_weights, gradients, 0..) |old_weight, grad_info, i| {
            const gradient = try TensorHandle.fromNode(self.graph, grad_info.node_id);
            const velocity = self.velocities.items[i];
            
            // Expand scalars to match shape
            const lr_expanded = try self.learning_rate.expandToShape(grad_info.shape);
            const momentum_expanded = try momentum_tensor.expandToShape(grad_info.shape);
            
            // velocity = momentum * velocity - learning_rate * gradient
            const momentum_term = try velocity.mul(momentum_expanded);
            const grad_term = try gradient.mul(lr_expanded);
            const new_velocity = try momentum_term.subtract(grad_term);
            
            // Update velocity
            self.velocities.items[i] = new_velocity;
            
            // new_weight = old_weight + velocity
            new_weights[i] = try old_weight.add(new_velocity);
        }
        
        return new_weights;
    }
};

/// Adam optimizer
/// Adaptive Moment Estimation with bias correction
pub const Adam = struct {
    graph: *Graph,
    learning_rate: f32,
    beta1: f32,
    beta2: f32,
    epsilon: f32,
    t: u32, // timestep
    m: std.ArrayList(TensorHandle), // first moment
    v: std.ArrayList(TensorHandle), // second moment
    
    pub fn init(
        graph: *Graph,
        learning_rate: f32,
        beta1: f32,
        beta2: f32,
        epsilon: f32,
    ) !Adam {
        return Adam{
            .graph = graph,
            .learning_rate = learning_rate,
            .beta1 = beta1,
            .beta2 = beta2,
            .epsilon = epsilon,
            .t = 0,
            .m = std.ArrayList(TensorHandle).init(graph.allocator),
            .v = std.ArrayList(TensorHandle).init(graph.allocator),
        };
    }
    
    pub fn deinit(self: *Adam) void {
        self.m.deinit();
        self.v.deinit();
    }
    
    pub fn step(
        self: *Adam,
        old_weights: []const TensorHandle,
        gradients: []const GradientInfo,
    ) ![]TensorHandle {
        const allocator = self.graph.allocator;
        var new_weights = try allocator.alloc(TensorHandle, old_weights.len);
        
        // Increment timestep
        self.t += 1;
        const t_f32 = @as(f32, @floatFromInt(self.t));
        
        // Initialize moments on first step
        if (self.m.items.len == 0) {
            for (old_weights) |weight| {
                const shape = self.graph.getNodeShape(weight.node_id) orelse {
                    std.log.err("optimizer: shape not found for weight node {} during initialization", .{weight.node_id});
                    return error.ShapeNotFound;
                };
                // Convert SymbolicDim to i64 array
                var dims: [8]i64 = undefined;
                const shape_utils = @import("shape").utils;
                try shape_utils.symbolicDimsToConcreteArray(shape.dims, dims[0..shape.dims.len]);
                const zeros = try tensor.zeros(self.graph, dims[0..shape.dims.len], weight.dtype);
                try self.m.append(zeros);
                try self.v.append(zeros);
            }
        }
        
        // Create constant tensors
        const beta1_t = try tensor.constant(self.graph, self.beta1, .f32);
        const beta2_t = try tensor.constant(self.graph, self.beta2, .f32);
        const one_minus_beta1 = try tensor.constant(self.graph, 1.0 - self.beta1, .f32);
        const one_minus_beta2 = try tensor.constant(self.graph, 1.0 - self.beta2, .f32);
        const lr_t = try tensor.constant(self.graph, self.learning_rate, .f32);
        const eps_t = try tensor.constant(self.graph, self.epsilon, .f32);
        
        // Bias correction terms
        const bias_correction1 = 1.0 - std.math.pow(f32, self.beta1, t_f32);
        const bias_correction2 = 1.0 - std.math.pow(f32, self.beta2, t_f32);
        const bc1_t = try tensor.constant(self.graph, bias_correction1, .f32);
        const bc2_t = try tensor.constant(self.graph, bias_correction2, .f32);
        
        for (old_weights, gradients, 0..) |old_weight, grad_info, i| {
            const gradient = try TensorHandle.fromNode(self.graph, grad_info.node_id);
            
            // Expand constants to match shape
            const beta1_expanded = try beta1_t.expandToShape(grad_info.shape);
            const beta2_expanded = try beta2_t.expandToShape(grad_info.shape);
            const one_minus_beta1_expanded = try one_minus_beta1.expandToShape(grad_info.shape);
            const one_minus_beta2_expanded = try one_minus_beta2.expandToShape(grad_info.shape);
            
            // Update biased first moment: m = beta1 * m + (1 - beta1) * gradient
            const m_scaled = try self.m.items[i].mul(beta1_expanded);
            const g_scaled = try gradient.mul(one_minus_beta1_expanded);
            const new_m = try m_scaled.add(g_scaled);
            self.m.items[i] = new_m;
            
            // Update biased second moment: v = beta2 * v + (1 - beta2) * gradient^2
            const v_scaled = try self.v.items[i].mul(beta2_expanded);
            const g_squared = try gradient.mul(gradient);
            const g2_scaled = try g_squared.mul(one_minus_beta2_expanded);
            const new_v = try v_scaled.add(g2_scaled);
            self.v.items[i] = new_v;
            
            // Bias correction
            const m_hat = try new_m.divide(bc1_t);
            const v_hat = try new_v.divide(bc2_t);
            
            // Update: w = w - lr * m_hat / (sqrt(v_hat) + epsilon)
            const v_hat_sqrt = try v_hat.sqrt();
            const denominator = try v_hat_sqrt.add(eps_t);
            const update = try m_hat.divide(denominator);
            const lr_update = try update.mul(lr_t);
            
            new_weights[i] = try old_weight.subtract(lr_update);
        }
        
        return new_weights;
    }
};

/// AdamW optimizer (Adam with weight decay)
pub const AdamW = struct {
    adam: Adam,
    weight_decay: f32,
    
    pub fn init(
        graph: *Graph,
        learning_rate: f32,
        beta1: f32,
        beta2: f32,
        epsilon: f32,
        weight_decay: f32,
    ) !AdamW {
        return AdamW{
            .adam = try Adam.init(graph, learning_rate, beta1, beta2, epsilon),
            .weight_decay = weight_decay,
        };
    }
    
    pub fn deinit(self: *AdamW) void {
        self.adam.deinit();
    }
    
    pub fn step(
        self: *AdamW,
        old_weights: []const TensorHandle,
        gradients: []const GradientInfo,
    ) ![]TensorHandle {
        // First apply Adam update
        const new_weights = try self.adam.step(old_weights, gradients);
        
        // Then apply weight decay: w = w - learning_rate * weight_decay * w
        const lr_wd = self.adam.learning_rate * self.weight_decay;
        const decay_factor = try tensor.constant(self.adam.graph, lr_wd, .f32);
        
        for (new_weights) |*weight| {
            const weight_shape = self.adam.graph.getNodeShape(weight.node_id) orelse return error.ShapeNotFound;
            const decay_expanded = try decay_factor.expandToShape(weight_shape);
            const decay_term = try weight.mul(decay_expanded);
            weight.* = try weight.subtract(decay_term);
        }
        
        return new_weights;
    }
};


// ===== Convenience functions inspired by Luminal =====

/// Create an SGD optimizer graph that can be compiled separately
/// Returns: (old_weight_inputs, gradient_inputs, new_weight_outputs, learning_rate_tensor)
pub fn sgdGraph(
    allocator: Allocator,
    param_shapes: []const ShapeTracker,
) !struct {
    graph: *Graph,
    old_weights: []TensorHandle,
    gradients: []TensorHandle, 
    new_weights: []TensorHandle,
    learning_rate: TensorHandle,
} {
    var graph = try Graph.init(allocator);
    
    var old_weights = try allocator.alloc(TensorHandle, param_shapes.len);
    var gradients = try allocator.alloc(TensorHandle, param_shapes.len);
    var new_weights = try allocator.alloc(TensorHandle, param_shapes.len);
    
    // Create learning rate as a named tensor for easy adjustment
    const lr = try tensor.placeholder(&graph, &.{}, .f32);
    
    // Create input placeholders and compute updates
    for (param_shapes, 0..) |shape, i| {
        // Convert SymbolicDim to i64 array
        var dims: [8]i64 = undefined;
        const shape_utils = @import("shape").utils;
        try shape_utils.symbolicDimsToConcreteArray(shape.dims, dims[0..shape.dims.len]);
        
        old_weights[i] = try tensor.placeholder(&graph, dims[0..shape.dims.len], .f32);
        gradients[i] = try tensor.placeholder(&graph, dims[0..shape.dims.len], .f32);
        
        // SGD update: new = old - lr * grad
        const lr_expanded = try lr.expand(dims[0..shape.dims.len]);
        const update = try gradients[i].mul(lr_expanded);
        new_weights[i] = try old_weights[i].subtract(update);
    }
    
    return .{
        .graph = graph,
        .old_weights = old_weights,
        .gradients = gradients,
        .new_weights = new_weights,
        .learning_rate = lr,
    };
}

// ===== Tests =====

test "SGD optimizer" {
    const testing = std.testing;
    
    var graph = try Graph.init(testing.allocator);
    defer graph.deinit();
    
    // Create a simple parameter and gradient
    const param = try tensor.constant(&graph, 1.0, .f32);
    const grad_node = try tensor.constant(&graph, 0.1, .f32);
    
    // Create optimizer
    var sgd = try SGD.init(&graph, 0.01);
    
    // Create gradient info
    const grad_info = [_]GradientInfo{
        .{ .node_id = grad_node.node_id, .shape = try ShapeTracker.init(&.{}, testing.allocator) },
    };
    
    // Apply SGD step
    const new_weights = try sgd.step(&.{param}, &grad_info);
    defer testing.allocator.free(new_weights);
    
    // New weight should be: 1.0 - 0.01 * 0.1 = 0.999
    try testing.expect(new_weights.len == 1);
}

test "SGD optimizer with vectors" {
    const testing = std.testing;
    
    var graph = try Graph.init(testing.allocator);
    defer graph.deinit();
    
    // Create vector parameters and gradients
    const param1 = try tensor.ones(&graph, &.{3}, .f32);
    const param2 = try tensor.zeros(&graph, &.{2, 2}, .f32);
    const grad1 = try tensor.constant(&graph, 0.1, .f32);
    const grad2 = try tensor.constant(&graph, -0.2, .f32);
    
    // Create optimizer
    var sgd = try SGD.init(&graph, 0.1);
    
    // Create gradient info
    const grad_info = [_]GradientInfo{
        .{ .node_id = grad1.node_id, .shape = try ShapeTracker.init(&.{3}, testing.allocator) },
        .{ .node_id = grad2.node_id, .shape = try ShapeTracker.init(&.{2, 2}, testing.allocator) },
    };
    
    // Apply SGD step
    const new_weights = try sgd.step(&.{ param1, param2 }, &grad_info);
    defer testing.allocator.free(new_weights);
    
    try testing.expectEqual(@as(usize, 2), new_weights.len);
}

test "SGD momentum optimizer" {
    const testing = std.testing;
    
    var graph = try Graph.init(testing.allocator);
    defer graph.deinit();
    
    var sgd_m = try SGDMomentum.init(&graph, 0.1, 0.9);
    defer sgd_m.deinit();
    
    const param = try tensor.constant(&graph, 1.0, .f32);
    const grad_node = try tensor.constant(&graph, 0.1, .f32);
    
    const grad_info = [_]GradientInfo{
        .{ .node_id = grad_node.node_id, .shape = try ShapeTracker.init(&.{}, testing.allocator) },
    };
    
    // First step - velocity should be initialized
    const weights1 = try sgd_m.step(&.{param}, &grad_info);
    defer testing.allocator.free(weights1);
    try testing.expectEqual(@as(usize, 1), sgd_m.velocities.items.len);
    
    // Second step - momentum should affect update
    const weights2 = try sgd_m.step(weights1, &grad_info);
    defer testing.allocator.free(weights2);
    try testing.expectEqual(@as(usize, 1), weights2.len);
}

test "Adam optimizer" {
    const testing = std.testing;
    
    var graph = try Graph.init(testing.allocator);
    defer graph.deinit();
    
    var adam = try Adam.init(&graph, 0.001, 0.9, 0.999, 1e-8);
    defer adam.deinit();
    
    try testing.expectEqual(@as(u32, 0), adam.t);
    try testing.expectEqual(@as(f32, 0.001), adam.learning_rate);
    try testing.expectEqual(@as(f32, 0.9), adam.beta1);
    try testing.expectEqual(@as(f32, 0.999), adam.beta2);
    
    // Test step
    const param = try tensor.constant(&graph, 1.0, .f32);
    const grad_node = try tensor.constant(&graph, 0.1, .f32);
    
    const grad_info = [_]GradientInfo{
        .{ .node_id = grad_node.node_id, .shape = try ShapeTracker.init(&.{}, testing.allocator) },
    };
    
    // Apply Adam step
    const new_weights = try adam.step(&.{param}, &grad_info);
    defer testing.allocator.free(new_weights);
    
    try testing.expectEqual(@as(u32, 1), adam.t);
    try testing.expectEqual(@as(usize, 1), adam.m.items.len);
    try testing.expectEqual(@as(usize, 1), adam.v.items.len);
}

test "Adam optimizer multiple steps" {
    const testing = std.testing;
    
    var graph = try Graph.init(testing.allocator);
    defer graph.deinit();
    
    var adam = try Adam.init(&graph, 0.01, 0.9, 0.999, 1e-8);
    defer adam.deinit();
    
    // Create parameters
    const param1 = try tensor.constant(&graph, 0.5, .f32);
    const param2 = try tensor.constant(&graph, -0.3, .f32);
    
    // Gradients that change over steps
    const grads = [_]f32{ 0.1, -0.05, 0.08, -0.12 };
    
    var current_params = [_]TensorHandle{ param1, param2 };
    
    // Run multiple optimization steps
    for (grads, 0..) |grad_val, i| {
        const grad1 = try tensor.constant(&graph, grad_val, .f32);
        const grad2 = try tensor.constant(&graph, -grad_val * 0.5, .f32);
        
        const grad_info = [_]GradientInfo{
            .{ .node_id = grad1.node_id, .shape = try ShapeTracker.init(&.{}, testing.allocator) },
            .{ .node_id = grad2.node_id, .shape = try ShapeTracker.init(&.{}, testing.allocator) },
        };
        
        const new_weights = try adam.step(&current_params, &grad_info);
        if (i > 0) testing.allocator.free(current_params);
        
        current_params = new_weights;
        try testing.expectEqual(@as(u32, @intCast(i + 1)), adam.t);
    }
    
    testing.allocator.free(current_params);
    try testing.expectEqual(@as(u32, 4), adam.t);
}

test "AdamW optimizer" {
    const testing = std.testing;
    
    var graph = try Graph.init(testing.allocator);
    defer graph.deinit();
    
    var adamw = try AdamW.init(&graph, 0.001, 0.9, 0.999, 1e-8, 0.01);
    defer adamw.deinit();
    
    try testing.expectEqual(@as(f32, 0.01), adamw.weight_decay);
    
    // Test that weight decay is applied
    const param = try tensor.constant(&graph, 1.0, .f32);
    const grad_node = try tensor.constant(&graph, 0.0, .f32); // Zero gradient
    
    const grad_info = [_]GradientInfo{
        .{ .node_id = grad_node.node_id, .shape = try ShapeTracker.init(&.{}, testing.allocator) },
    };
    
    // With zero gradient, only weight decay should affect the parameter
    const new_weights = try adamw.step(&.{param}, &grad_info);
    defer testing.allocator.free(new_weights);
    
    try testing.expectEqual(@as(usize, 1), new_weights.len);
}


test "sgdGraph function" {
    const testing = std.testing;
    
    // Create parameter shapes
    const shapes = [_]ShapeTracker{
        try ShapeTracker.init(&.{10}, testing.allocator),
        try ShapeTracker.init(&.{ 5, 5 }, testing.allocator),
    };
    defer for (shapes) |shape| {
        shape.deinit(testing.allocator);
    };
    
    // Create SGD graph
    const sgd_result = try sgdGraph(testing.allocator, &shapes);
    defer {
        sgd_result.graph.deinit();
        testing.allocator.free(sgd_result.old_weights);
        testing.allocator.free(sgd_result.gradients);
        testing.allocator.free(sgd_result.new_weights);
    }
    
    try testing.expectEqual(@as(usize, 2), sgd_result.old_weights.len);
    try testing.expectEqual(@as(usize, 2), sgd_result.gradients.len);
    try testing.expectEqual(@as(usize, 2), sgd_result.new_weights.len);
}

test "learning rate modification" {
    const testing = std.testing;
    
    var graph = try Graph.init(testing.allocator);
    defer graph.deinit();
    
    var sgd = try SGD.init(&graph, 0.1);
    
    // Change learning rate
    try sgd.setLearningRate(0.01);
    
    // Verify it's used in optimization
    const param = try tensor.constant(&graph, 1.0, .f32);
    const grad_node = try tensor.constant(&graph, 1.0, .f32);
    
    const grad_info = [_]GradientInfo{
        .{ .node_id = grad_node.node_id, .shape = try ShapeTracker.init(&.{}, testing.allocator) },
    };
    
    const new_weights = try sgd.step(&.{param}, &grad_info);
    defer testing.allocator.free(new_weights);
    
    // With gradient 1.0 and lr 0.01, update should be -0.01
    try testing.expectEqual(@as(usize, 1), new_weights.len);
}