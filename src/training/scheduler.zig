/// Learning rate schedulers for training
///
/// This module provides various learning rate scheduling strategies
/// to adjust the learning rate during training.

const std = @import("std");

/// Base scheduler interface
pub const Scheduler = union(enum) {
    constant: ConstantScheduler,
    step: StepScheduler,
    exponential: ExponentialScheduler,
    cosine: CosineScheduler,
    linear: LinearScheduler,
    polynomial: PolynomialScheduler,
    warmup: WarmupScheduler,
    
    /// Get the learning rate for the current step
    pub fn getLearningRate(self: *Scheduler, step: u32) f32 {
        return switch (self.*) {
            .constant => |s| s.getLearningRate(step),
            .step => |*s| s.getLearningRate(step),
            .exponential => |s| s.getLearningRate(step),
            .cosine => |s| s.getLearningRate(step),
            .linear => |s| s.getLearningRate(step),
            .polynomial => |s| s.getLearningRate(step),
            .warmup => |*s| s.getLearningRate(step),
        };
    }
};

/// Constant learning rate (no scheduling)
pub const ConstantScheduler = struct {
    base_lr: f32,
    
    pub fn init(learning_rate: f32) ConstantScheduler {
        return .{ .base_lr = learning_rate };
    }
    
    pub fn getLearningRate(self: ConstantScheduler, step: u32) f32 {
        _ = step;
        return self.base_lr;
    }
};

/// Step decay: multiply learning rate by gamma every step_size steps
pub const StepScheduler = struct {
    base_lr: f32,
    step_size: u32,
    gamma: f32,
    
    pub fn init(base_lr: f32, step_size: u32, gamma: f32) StepScheduler {
        return .{
            .base_lr = base_lr,
            .step_size = step_size,
            .gamma = gamma,
        };
    }
    
    pub fn getLearningRate(self: *StepScheduler, step: u32) f32 {
        const num_steps = step / self.step_size;
        return self.base_lr * std.math.pow(f32, self.gamma, @floatFromInt(num_steps));
    }
};

/// Exponential decay: lr = base_lr * gamma^step
pub const ExponentialScheduler = struct {
    base_lr: f32,
    gamma: f32,
    
    pub fn init(base_lr: f32, gamma: f32) ExponentialScheduler {
        return .{
            .base_lr = base_lr,
            .gamma = gamma,
        };
    }
    
    pub fn getLearningRate(self: ExponentialScheduler, step: u32) f32 {
        return self.base_lr * std.math.pow(f32, self.gamma, @floatFromInt(step));
    }
};

/// Cosine annealing: lr follows a cosine curve from base_lr to min_lr
pub const CosineScheduler = struct {
    base_lr: f32,
    min_lr: f32,
    total_steps: u32,
    
    pub fn init(base_lr: f32, min_lr: f32, total_steps: u32) CosineScheduler {
        return .{
            .base_lr = base_lr,
            .min_lr = min_lr,
            .total_steps = total_steps,
        };
    }
    
    pub fn getLearningRate(self: CosineScheduler, step: u32) f32 {
        if (step >= self.total_steps) {
            return self.min_lr;
        }
        
        const progress = @as(f32, @floatFromInt(step)) / @as(f32, @floatFromInt(self.total_steps));
        const cosine_decay = 0.5 * (1.0 + std.math.cos(std.math.pi * progress));
        return self.min_lr + (self.base_lr - self.min_lr) * cosine_decay;
    }
};

/// Linear decay: linearly decrease from base_lr to end_lr over total_steps
pub const LinearScheduler = struct {
    base_lr: f32,
    end_lr: f32,
    total_steps: u32,
    
    pub fn init(base_lr: f32, end_lr: f32, total_steps: u32) LinearScheduler {
        return .{
            .base_lr = base_lr,
            .end_lr = end_lr,
            .total_steps = total_steps,
        };
    }
    
    pub fn getLearningRate(self: LinearScheduler, step: u32) f32 {
        if (step >= self.total_steps) {
            return self.end_lr;
        }
        
        const progress = @as(f32, @floatFromInt(step)) / @as(f32, @floatFromInt(self.total_steps));
        return self.base_lr + (self.end_lr - self.base_lr) * progress;
    }
};

/// Polynomial decay: lr = base_lr * (1 - step/total_steps)^power
pub const PolynomialScheduler = struct {
    base_lr: f32,
    end_lr: f32,
    total_steps: u32,
    power: f32,
    
    pub fn init(base_lr: f32, end_lr: f32, total_steps: u32, power: f32) PolynomialScheduler {
        return .{
            .base_lr = base_lr,
            .end_lr = end_lr,
            .total_steps = total_steps,
            .power = power,
        };
    }
    
    pub fn getLearningRate(self: PolynomialScheduler, step: u32) f32 {
        if (step >= self.total_steps) {
            return self.end_lr;
        }
        
        const progress = @as(f32, @floatFromInt(step)) / @as(f32, @floatFromInt(self.total_steps));
        const decay_factor = std.math.pow(f32, 1.0 - progress, self.power);
        return self.end_lr + (self.base_lr - self.end_lr) * decay_factor;
    }
};

/// Warmup scheduler: linearly increase lr for warmup_steps, then use base scheduler
pub const WarmupScheduler = struct {
    warmup_steps: u32,
    warmup_start_lr: f32,
    base_scheduler: *Scheduler,
    
    pub fn init(warmup_steps: u32, warmup_start_lr: f32, base_scheduler: *Scheduler) WarmupScheduler {
        return .{
            .warmup_steps = warmup_steps,
            .warmup_start_lr = warmup_start_lr,
            .base_scheduler = base_scheduler,
        };
    }
    
    pub fn getLearningRate(self: *WarmupScheduler, step: u32) f32 {
        if (step < self.warmup_steps) {
            // Linear warmup
            const base_lr = self.base_scheduler.getLearningRate(self.warmup_steps);
            const progress = @as(f32, @floatFromInt(step)) / @as(f32, @floatFromInt(self.warmup_steps));
            return self.warmup_start_lr + (base_lr - self.warmup_start_lr) * progress;
        } else {
            // Use base scheduler
            return self.base_scheduler.getLearningRate(step);
        }
    }
};

// ===== Convenience constructors =====

/// Create a constant learning rate scheduler
pub fn constant(lr: f32) Scheduler {
    return .{ .constant = ConstantScheduler.init(lr) };
}

/// Create a step decay scheduler
pub fn stepDecay(base_lr: f32, step_size: u32, gamma: f32) Scheduler {
    return .{ .step = StepScheduler.init(base_lr, step_size, gamma) };
}

/// Create an exponential decay scheduler
pub fn exponentialDecay(base_lr: f32, gamma: f32) Scheduler {
    return .{ .exponential = ExponentialScheduler.init(base_lr, gamma) };
}

/// Create a cosine annealing scheduler
pub fn cosineAnnealing(base_lr: f32, min_lr: f32, total_steps: u32) Scheduler {
    return .{ .cosine = CosineScheduler.init(base_lr, min_lr, total_steps) };
}

/// Create a linear decay scheduler
pub fn linearDecay(base_lr: f32, end_lr: f32, total_steps: u32) Scheduler {
    return .{ .linear = LinearScheduler.init(base_lr, end_lr, total_steps) };
}

/// Create a polynomial decay scheduler
pub fn polynomialDecay(base_lr: f32, end_lr: f32, total_steps: u32, power: f32) Scheduler {
    return .{ .polynomial = PolynomialScheduler.init(base_lr, end_lr, total_steps, power) };
}

/// Create a warmup scheduler wrapping another scheduler
pub fn warmup(warmup_steps: u32, warmup_start_lr: f32, base_scheduler: *Scheduler) Scheduler {
    return .{ .warmup = WarmupScheduler.init(warmup_steps, warmup_start_lr, base_scheduler) };
}

// ===== Tests =====

test "constant scheduler" {
    const testing = std.testing;
    
    var scheduler = constant(0.1);
    try testing.expectEqual(@as(f32, 0.1), scheduler.getLearningRate(0));
    try testing.expectEqual(@as(f32, 0.1), scheduler.getLearningRate(100));
    try testing.expectEqual(@as(f32, 0.1), scheduler.getLearningRate(1000));
}

test "step decay scheduler" {
    const testing = std.testing;
    
    var scheduler = stepDecay(0.1, 10, 0.5);
    try testing.expectEqual(@as(f32, 0.1), scheduler.getLearningRate(0));
    try testing.expectEqual(@as(f32, 0.1), scheduler.getLearningRate(9));
    try testing.expectEqual(@as(f32, 0.05), scheduler.getLearningRate(10));
    try testing.expectEqual(@as(f32, 0.05), scheduler.getLearningRate(19));
    try testing.expectEqual(@as(f32, 0.025), scheduler.getLearningRate(20));
}

test "exponential decay scheduler" {
    const testing = std.testing;
    
    var scheduler = exponentialDecay(0.1, 0.99);
    try testing.expectEqual(@as(f32, 0.1), scheduler.getLearningRate(0));
    try testing.expectApproxEqAbs(@as(f32, 0.099), scheduler.getLearningRate(1), 0.001);
    try testing.expectApproxEqAbs(@as(f32, 0.09801), scheduler.getLearningRate(2), 0.001);
}

test "cosine annealing scheduler" {
    const testing = std.testing;
    
    var scheduler = cosineAnnealing(0.1, 0.01, 100);
    
    // At start
    try testing.expectEqual(@as(f32, 0.1), scheduler.getLearningRate(0));
    
    // At midpoint (should be average of base_lr and min_lr)
    try testing.expectApproxEqAbs(@as(f32, 0.055), scheduler.getLearningRate(50), 0.001);
    
    // At end
    try testing.expectApproxEqAbs(@as(f32, 0.01), scheduler.getLearningRate(100), 0.001);
}

test "linear decay scheduler" {
    const testing = std.testing;
    
    var scheduler = linearDecay(0.1, 0.01, 100);
    
    try testing.expectEqual(@as(f32, 0.1), scheduler.getLearningRate(0));
    try testing.expectApproxEqAbs(@as(f32, 0.055), scheduler.getLearningRate(50), 0.001);
    try testing.expectEqual(@as(f32, 0.01), scheduler.getLearningRate(100));
    try testing.expectEqual(@as(f32, 0.01), scheduler.getLearningRate(200)); // Beyond total_steps
}

test "polynomial decay scheduler" {
    const testing = std.testing;
    
    var scheduler = polynomialDecay(0.1, 0.0, 100, 2.0);
    
    // At start
    try testing.expectEqual(@as(f32, 0.1), scheduler.getLearningRate(0));
    
    // At 25% progress: lr = 0.1 * (1 - 0.25)^2 = 0.1 * 0.5625 = 0.05625
    try testing.expectApproxEqAbs(@as(f32, 0.05625), scheduler.getLearningRate(25), 0.001);
    
    // At end
    try testing.expectEqual(@as(f32, 0.0), scheduler.getLearningRate(100));
}

test "warmup scheduler" {
    const testing = std.testing;
    
    var base = constant(0.1);
    var scheduler = warmup(10, 0.01, &base);
    
    // During warmup
    try testing.expectEqual(@as(f32, 0.01), scheduler.getLearningRate(0));
    try testing.expectApproxEqAbs(@as(f32, 0.055), scheduler.getLearningRate(5), 0.001);
    
    // After warmup
    try testing.expectEqual(@as(f32, 0.1), scheduler.getLearningRate(10));
    try testing.expectEqual(@as(f32, 0.1), scheduler.getLearningRate(100));
}

test "step decay edge cases" {
    const testing = std.testing;
    
    // Test with step_size = 1 (decay every step)
    var scheduler1 = stepDecay(1.0, 1, 0.5);
    try testing.expectEqual(@as(f32, 1.0), scheduler1.getLearningRate(0));
    try testing.expectEqual(@as(f32, 0.5), scheduler1.getLearningRate(1));
    try testing.expectEqual(@as(f32, 0.25), scheduler1.getLearningRate(2));
    try testing.expectEqual(@as(f32, 0.125), scheduler1.getLearningRate(3));
    
    // Test with large step_size
    var scheduler2 = stepDecay(0.1, 1000, 0.1);
    try testing.expectEqual(@as(f32, 0.1), scheduler2.getLearningRate(0));
    try testing.expectEqual(@as(f32, 0.1), scheduler2.getLearningRate(999));
    try testing.expectEqual(@as(f32, 0.01), scheduler2.getLearningRate(1000));
}

test "exponential decay convergence" {
    const testing = std.testing;
    
    // Test that exponential decay approaches zero
    var scheduler = exponentialDecay(1.0, 0.9);
    
    // After many steps, should be very small
    const lr_100 = scheduler.getLearningRate(100);
    try testing.expect(lr_100 < 0.0001);
    try testing.expect(lr_100 > 0); // But not zero
    
    // Test with aggressive decay
    var fast_decay = exponentialDecay(1.0, 0.5);
    try testing.expectEqual(@as(f32, 1.0), fast_decay.getLearningRate(0));
    try testing.expectEqual(@as(f32, 0.5), fast_decay.getLearningRate(1));
    try testing.expectEqual(@as(f32, 0.25), fast_decay.getLearningRate(2));
}

test "cosine annealing properties" {
    const testing = std.testing;
    
    var scheduler = cosineAnnealing(1.0, 0.0, 100);
    
    // Should start at base_lr
    try testing.expectEqual(@as(f32, 1.0), scheduler.getLearningRate(0));
    
    // Should end at min_lr
    try testing.expectApproxEqAbs(@as(f32, 0.0), scheduler.getLearningRate(100), 0.001);
    
    // Should be monotonically decreasing
    var prev_lr = scheduler.getLearningRate(0);
    var step: u32 = 1;
    while (step <= 100) : (step += 1) {
        const curr_lr = scheduler.getLearningRate(step);
        try testing.expect(curr_lr <= prev_lr);
        prev_lr = curr_lr;
    }
    
    // Test with non-zero min_lr
    var scheduler2 = cosineAnnealing(0.1, 0.01, 50);
    try testing.expectEqual(@as(f32, 0.1), scheduler2.getLearningRate(0));
    try testing.expectApproxEqAbs(@as(f32, 0.01), scheduler2.getLearningRate(50), 0.001);
    try testing.expectEqual(@as(f32, 0.01), scheduler2.getLearningRate(100)); // Beyond total_steps
}

test "linear decay properties" {
    const testing = std.testing;
    
    var scheduler = linearDecay(1.0, 0.1, 90);
    
    // Check linearity: lr should decrease by constant amount each step
    const lr_0 = scheduler.getLearningRate(0);
    const lr_30 = scheduler.getLearningRate(30);
    const lr_60 = scheduler.getLearningRate(60);
    const lr_90 = scheduler.getLearningRate(90);
    
    try testing.expectEqual(@as(f32, 1.0), lr_0);
    try testing.expectApproxEqAbs(@as(f32, 0.7), lr_30, 0.001);
    try testing.expectApproxEqAbs(@as(f32, 0.4), lr_60, 0.001);
    try testing.expectEqual(@as(f32, 0.1), lr_90);
    
    // Check equal spacing
    try testing.expectApproxEqAbs(lr_0 - lr_30, lr_30 - lr_60, 0.001);
    try testing.expectApproxEqAbs(lr_30 - lr_60, lr_60 - lr_90, 0.001);
}

test "polynomial decay with different powers" {
    const testing = std.testing;
    
    // Linear decay (power = 1)
    var linear = polynomialDecay(1.0, 0.0, 100, 1.0);
    try testing.expectEqual(@as(f32, 1.0), linear.getLearningRate(0));
    try testing.expectApproxEqAbs(@as(f32, 0.5), linear.getLearningRate(50), 0.001);
    try testing.expectEqual(@as(f32, 0.0), linear.getLearningRate(100));
    
    // Quadratic decay (power = 2)
    var quadratic = polynomialDecay(1.0, 0.0, 100, 2.0);
    try testing.expectEqual(@as(f32, 1.0), quadratic.getLearningRate(0));
    try testing.expectApproxEqAbs(@as(f32, 0.25), quadratic.getLearningRate(50), 0.001);
    try testing.expectEqual(@as(f32, 0.0), quadratic.getLearningRate(100));
    
    // Square root decay (power = 0.5)
    var sqrt_decay = polynomialDecay(1.0, 0.0, 100, 0.5);
    try testing.expectEqual(@as(f32, 1.0), sqrt_decay.getLearningRate(0));
    const lr_50 = sqrt_decay.getLearningRate(50);
    try testing.expect(lr_50 > 0.25); // Should decay slower than quadratic
    try testing.expect(lr_50 < 0.5); // But faster than linear
}

test "warmup with different base schedulers" {
    const testing = std.testing;
    
    // Warmup with step decay
    var step_base = stepDecay(0.1, 20, 0.5);
    var warmup_step = warmup(10, 0.001, &step_base);
    
    // During warmup
    try testing.expectEqual(@as(f32, 0.001), warmup_step.getLearningRate(0));
    
    // At warmup end, should match base scheduler
    try testing.expectEqual(@as(f32, 0.1), warmup_step.getLearningRate(10));
    
    // After warmup, should follow base scheduler
    try testing.expectEqual(@as(f32, 0.1), warmup_step.getLearningRate(19));
    try testing.expectEqual(@as(f32, 0.05), warmup_step.getLearningRate(20));
    
    // Warmup with cosine annealing
    var cosine_base = cosineAnnealing(0.1, 0.01, 100);
    var warmup_cosine = warmup(5, 0.0, &cosine_base);
    
    try testing.expectEqual(@as(f32, 0.0), warmup_cosine.getLearningRate(0));
    const lr_5 = warmup_cosine.getLearningRate(5);
    const lr_6 = warmup_cosine.getLearningRate(6);
    try testing.expect(lr_5 > lr_6); // Should start decreasing after warmup
}

test "scheduler boundary conditions" {
    const testing = std.testing;
    
    // Test with zero total steps
    var zero_steps = cosineAnnealing(1.0, 0.1, 0);
    try testing.expectEqual(@as(f32, 0.1), zero_steps.getLearningRate(0));
    try testing.expectEqual(@as(f32, 0.1), zero_steps.getLearningRate(100));
    
    // Test with very large step numbers
    var scheduler = linearDecay(1.0, 0.0, 100);
    try testing.expectEqual(@as(f32, 0.0), scheduler.getLearningRate(1000000));
    
    // Test with same start and end learning rates
    var constant_linear = linearDecay(0.1, 0.1, 100);
    try testing.expectEqual(@as(f32, 0.1), constant_linear.getLearningRate(0));
    try testing.expectEqual(@as(f32, 0.1), constant_linear.getLearningRate(50));
    try testing.expectEqual(@as(f32, 0.1), constant_linear.getLearningRate(100));
}

test "scheduler composition" {
    const testing = std.testing;
    
    // Create a complex schedule: warmup + cosine with restarts
    var cosine1 = cosineAnnealing(0.1, 0.01, 50);
    var with_warmup = warmup(5, 0.001, &cosine1);
    
    // Verify warmup phase
    try testing.expectEqual(@as(f32, 0.001), with_warmup.getLearningRate(0));
    
    // Verify transition
    const lr_5 = with_warmup.getLearningRate(5);
    try testing.expectApproxEqAbs(@as(f32, 0.1), lr_5, 0.001);
    
    // Verify cosine phase
    const lr_30 = with_warmup.getLearningRate(30);
    try testing.expect(lr_30 < 0.1);
    try testing.expect(lr_30 > 0.01);
}