/// Zing Tensor API - Comprehensive interface for tensor operations
/// 
/// This module provides the core API for building tensor computations.
/// All operations immediately decompose to primitives in the computation graph.
///
/// ARCHITECTURAL DECISION: Tensor component is the SINGLE SOURCE OF TRUTH for shape information.
/// TensorHandle carries ShapeTracker while Graph nodes contain NO shape data.
///
/// DESIGN PRINCIPLE: Provide both function-style and method-style APIs for maximum flexibility.
/// Advanced operations are available in specialized modules (nn.zig, linalg.zig) or as TensorHandle methods.

const std = @import("std");
const Allocator = std.mem.Allocator;

// Import types
const types = @import("types");
const DataType = types.DataType;
const NodeId = types.NodeId;

// Import core components
const Graph = @import("graph").Graph;
const shape_mod = @import("shape");
const ShapeTracker = shape_mod.ShapeTracker;

// Import storage types
const storage = @import("storage");
pub const TensorView = storage.TensorView;
pub const OwnedTensor = storage.OwnedTensor;
pub const BufferView = storage.BufferView;

// ===== Core Type Exports =====

// Import TensorHandle from handle.zig
pub const TensorHandle = @import("tensor/handle.zig").TensorHandle;
pub const TensorError = @import("tensor/handle.zig").TensorError;

// ===== Core Tensor Types =====
// These types are now imported from storage.zig to avoid duplication

// ===== Creation Functions =====
// These require explicit graph context since they create new computation nodes

/// Create a tensor filled with zeros
pub const zeros = @import("tensor/creation.zig").zeros;

/// Create a tensor filled with ones
pub const ones = @import("tensor/creation.zig").ones;

/// Create a placeholder tensor (input to the graph)
pub const placeholder = @import("tensor/creation.zig").placeholder;

/// Create a placeholder tensor with options (e.g., requires_grad)
pub const placeholderWithOptions = @import("tensor/creation.zig").placeholderWithOptions;

/// Create a tensor filled with a specific value
pub const full = @import("tensor/creation.zig").full;

/// Create a constant scalar tensor
pub const constant = @import("tensor/creation.zig").constant;

/// Create a parameter tensor (trainable variable)
pub const parameter = @import("tensor/creation.zig").parameter;

/// Create a parameter tensor with options
pub const parameterWithOptions = @import("tensor/creation.zig").parameterWithOptions;

/// Tensor creation options
pub const TensorOptions = @import("tensor/creation.zig").TensorOptions;

/// Create a range tensor from 0 to n-1
pub const arange = @import("tensor/creation.zig").arange;

// ===== Shape Operations (View Operations) =====
// These are convenience functions - also available as TensorHandle methods

/// Reshape tensor to new shape
pub const reshape = @import("tensor/ops/view.zig").reshape;

/// Transpose tensor dimensions
pub const transpose = @import("tensor/ops/view.zig").transpose;

/// Slice tensor
pub const slice = @import("tensor/ops/view.zig").slice;

/// Remove dimensions of size 1
pub const squeeze = @import("tensor/ops/view.zig").squeeze;

/// Add dimension of size 1
pub const unsqueeze = @import("tensor/ops/view.zig").unsqueeze;

/// Flatten tensor to 1D
pub const flatten = @import("tensor/ops/view.zig").flatten;

/// Flatten tensor starting from a specific dimension
pub const flattenDims = @import("tensor/ops/view.zig").flattenDims;

// ===== Essential High-Level Operations =====
// The most fundamental operations are exported at top level for convenience
// These include basic arithmetic, essential linear algebra, and logical operations

// ===== Essential Arithmetic Operations =====
/// Element-wise addition
pub fn add(a: TensorHandle, b: TensorHandle) !TensorHandle {
    return a.add(b);
}

/// Element-wise multiplication  
pub fn mul(a: TensorHandle, b: TensorHandle) !TensorHandle {
    return a.mul(b);
}

/// Element-wise subtraction
pub fn subtract(a: TensorHandle, b: TensorHandle) !TensorHandle {
    return a.subtract(b);
}

/// Element-wise division
pub fn divide(a: TensorHandle, b: TensorHandle) !TensorHandle {
    return a.divide(b);
}

// ===== Essential Linear Algebra Operations =====
/// Matrix multiplication - the essential linear algebra operation
pub fn matmul(a: TensorHandle, b: TensorHandle) !TensorHandle {
    return a.matmul(b);
}

// ===== Essential Logical Operations =====
/// Conditional selection - select elements based on condition (where operation)
pub fn select(condition: TensorHandle, a: TensorHandle, b: TensorHandle) !TensorHandle {
    return condition.select(a, b);
}



// ===== Advanced Operations Available in Specialized Modules =====
//
// For more advanced operations, import specialized modules:
//
// Neural Network Operations:
//   const nn = @import("nn.zig");
//   const result = try nn.softmax(tensor, axis);
//   const result = try nn.relu(tensor);
//
// Extended Linear Algebra:
//   const linalg = @import("tensor/ops/linalg.zig");
//   const result = try linalg.trace(matrix);
//   const result = try linalg.det(matrix);
//
// Extended Arithmetic:
//   const arithmetic = @import("tensor/ops/arithmetic.zig");
//   const result = try arithmetic.pow(base, exponent);
//   const result = try arithmetic.clamp(tensor, min, max);
//
// Extended Reductions:
//   const reduction = @import("tensor/ops/reduction.zig");
//   const result = try reduction.mean(tensor, axis);
//   const result = try reduction.stddev(tensor, axis);
//
// Extended Comparisons:
//   const comparison = @import("tensor/ops/comparison.zig");
//   const result = try comparison.greaterThan(a, b);
//   const result = try comparison.where(condition, a, b);
//
// Remember: All operations are available as both functions and TensorHandle methods:
//   const result = try tensor.add(a, b);      // function style  
//   const result = try a.add(b);              // method style (recommended)
//   const result = try tensor.matmul(a, b);   // function style
//   const result = try a.matmul(b);           // method style (recommended)
//   const result = try a.relu();              // activation methods
//   const result = try a.sum(axis);           // reduction methods
//   const result = try a.reshape(shape);      // view methods

// ===== Tests =====

// Import submodules to ensure their tests run
test {
    _ = @import("tensor/creation.zig");
    _ = @import("tensor/handle.zig");
    _ = @import("tensor/ops/indexing.zig");
    _ = @import("tensor/ops/view.zig");
    _ = @import("tensor/ops/primitive.zig");
    _ = @import("tensor/ops/arithmetic.zig");
    _ = @import("tensor/ops/comparison.zig");
    _ = @import("tensor/ops/reduction.zig");
    _ = @import("tensor/ops/activation.zig");
    _ = @import("tensor/ops/math.zig");
    _ = @import("tensor/ops/logical.zig");
    _ = @import("tensor/ops/normalization.zig");
    _ = @import("tensor/ops/linalg.zig");
}

test "tensor API - basic usage" {
    const testing = std.testing;
    
    var graph = try Graph.init(testing.allocator);
    defer graph.deinit();
    
    // Create tensors
    const a = try zeros(&graph, &.{2, 3}, .f32);
    const b = try ones(&graph, &.{2, 3}, .f32);
    
    // Perform operations using methods (recommended)
    const c = try a.add(b);
    
    // Verify shapes
    try testing.expectEqual(@as(usize, 2), c.rank());
    try testing.expectEqual(@as(i64, 2), c.shape.dims[0].concrete);
    try testing.expectEqual(@as(i64, 3), c.shape.dims[1].concrete);
}

test "tensor API - shape operations" {
    const testing = std.testing;
    
    var graph = try Graph.init(testing.allocator);
    defer graph.deinit();
    
    // Create tensor
    const x = try placeholder(&graph, &.{2, 3, 4}, .f32);
    
    // Test view operations (both function and method style)
    const transposed = try x.transpose(&.{2, 0, 1});
    try testing.expect(x.node_id == transposed.node_id); // Same node, different view
    
    const reshaped = try reshape(x, &.{6, 4});  // Function style
    try testing.expectEqual(@as(usize, 2), reshaped.rank());
    
    const flattened = try flatten(x);  // Function style
    try testing.expectEqual(@as(i64, 24), flattened.shape.dims[0].concrete);
}

test "tensor API - essential operations" {
    const testing = std.testing;
    
    var graph = try Graph.init(testing.allocator);
    defer graph.deinit();
    
    // Create matrices for matmul
    const a = try placeholder(&graph, &.{3, 4}, .f32);
    const b = try placeholder(&graph, &.{4, 5}, .f32);
    
    // Test essential linear algebra operation
    const c = try matmul(a, b);
    // matmul returns 2D result after squeeze: [3,4] @ [4,5] = [3,5]
    try testing.expectEqual(@as(usize, 2), c.rank());
    try testing.expectEqual(@as(i64, 3), c.shape.dims[0].concrete);
    try testing.expectEqual(@as(i64, 5), c.shape.dims[1].concrete);
    
    // Test method-based arithmetic (preferred style)
    const x = try placeholder(&graph, &.{2, 10}, .f32);
    const y = try x.relu();  // Method style
    try testing.expectEqual(@as(usize, 2), y.rank());
    
    // Test decomposition to primitives
    const z = try x.sigmoid();  // Method style - decomposes to primitives
    try testing.expect(z.node_id != x.node_id); // New node created
}

test "tensor API - arange operation" {
    const testing = std.testing;
    
    var graph = try Graph.init(testing.allocator);
    defer graph.deinit();
    
    // Test basic arange
    const range5 = try arange(&graph, 5, .f32);
    try testing.expectEqual(@as(usize, 1), range5.rank());
    try testing.expectEqual(@as(i64, 5), range5.shape.dims[0].concrete);
    try testing.expectEqual(DataType.f32, range5.dtype);
    
    // Test with different sizes
    const range10 = try arange(&graph, 10, .i32);
    try testing.expectEqual(@as(i64, 10), range10.shape.dims[0].concrete);
    try testing.expectEqual(DataType.i32, range10.dtype);
    
    const range1 = try arange(&graph, 1, .f32);
    try testing.expectEqual(@as(i64, 1), range1.shape.dims[0].concrete);
    
    // Test error cases
    try testing.expectError(error.InvalidSize, arange(&graph, 0, .f32));
    try testing.expectError(error.InvalidSize, arange(&graph, -5, .f32));
}

test "tensor API - gather operation" {
    const testing = std.testing;
    
    var graph = try Graph.init(testing.allocator);
    defer graph.deinit();
    
    // Test basic gather for embeddings
    const vocab_size: i64 = 10;
    const embed_dim: i64 = 4;
    const batch_size: i64 = 3;
    
    // Create embedding matrix [vocab_size, embed_dim]
    const embeddings = try placeholder(&graph, &.{vocab_size, embed_dim}, .f32);
    
    // Create indices [batch_size]
    const indices = try placeholder(&graph, &.{batch_size}, .i32);
    
    // Perform gather
    const gathered = try embeddings.gather(indices);
    
    // Check output shape is [batch_size, embed_dim]
    try testing.expectEqual(@as(usize, 2), gathered.rank());
    try testing.expectEqual(batch_size, gathered.shape.dims[0].concrete);
    try testing.expectEqual(embed_dim, gathered.shape.dims[1].concrete);
    
    // Test with larger batch
    const large_batch_indices = try placeholder(&graph, &.{32}, .i32);
    const large_gathered = try embeddings.gather(large_batch_indices);
    try testing.expectEqual(@as(i64, 32), large_gathered.shape.dims[0].concrete);
    try testing.expectEqual(embed_dim, large_gathered.shape.dims[1].concrete);
    
    // Test error cases - non-2D source
    const tensor_3d = try placeholder(&graph, &.{5, 10, 20}, .f32);
    try testing.expectError(error.UnsupportedDimension, tensor_3d.gather(indices));
    
    // Test error cases - non-1D indices  
    const indices_2d = try placeholder(&graph, &.{3, 2}, .i32);
    try testing.expectError(error.InvalidIndicesDimension, embeddings.gather(indices_2d));
}

test "tensor API - gather decomposition" {
    const testing = std.testing;
    
    var graph = try Graph.init(testing.allocator);
    defer graph.deinit();
    
    // Test that gather properly decomposes into primitives
    const embeddings = try placeholder(&graph, &.{5, 3}, .f32);
    const indices = try placeholder(&graph, &.{2}, .i32);
    
    const initial_node_count = graph.nodes.items.len;
    const result = try embeddings.gather(indices);
    const final_node_count = graph.nodes.items.len;
    
    // Gather should create multiple new nodes (arange, equal, mul, sum, etc.)
    try testing.expect(final_node_count > initial_node_count + 3);
    
    // Result should have correct shape
    try testing.expectEqual(@as(usize, 2), result.rank());
    try testing.expectEqual(@as(i64, 2), result.shape.dims[0].concrete);
    try testing.expectEqual(@as(i64, 3), result.shape.dims[1].concrete);
}

test "tensor API - embedding lookup workflow" {
    const testing = std.testing;
    
    var graph = try Graph.init(testing.allocator);
    defer graph.deinit();
    
    // Simulate a real embedding lookup scenario
    const vocab_size: i64 = 1000;
    const embed_dim: i64 = 128;
    const seq_length: i64 = 50;
    
    // Create embedding table
    const embed_table = try placeholder(&graph, &.{vocab_size, embed_dim}, .f32);
    
    // Create input token ids for a sequence
    const token_ids = try placeholder(&graph, &.{seq_length}, .i32);
    
    // Perform embedding lookup
    const embeddings = try embed_table.gather(token_ids);
    
    // Verify output shape is [seq_length, embed_dim]
    try testing.expectEqual(@as(usize, 2), embeddings.rank());
    try testing.expectEqual(seq_length, embeddings.shape.dims[0].concrete);
    try testing.expectEqual(embed_dim, embeddings.shape.dims[1].concrete);
    
    // Now we can do further operations on embeddings
    // For example, add and multiply (simulating attention scores)
    const scores = try embeddings.add(embeddings);
    try testing.expectEqual(embeddings.rank(), scores.rank());
}

test "tensor API - arange with operations" {
    const testing = std.testing;
    
    var graph = try Graph.init(testing.allocator);
    defer graph.deinit();
    
    // Create a range and use it in computations
    const range = try arange(&graph, 10, .f32);
    
    // Square the range: [0, 1, 4, 9, 16, ...]
    const squared = try range.square();
    try testing.expectEqual(range.rank(), squared.rank());
    try testing.expectEqual(range.shape.dims[0].concrete, squared.shape.dims[0].concrete);
    
    // Scale by constant
    const scaled = try range.mul(try constant(&graph, 2.0, .f32));
    try testing.expectEqual(@as(i64, 10), scaled.shape.dims[0].concrete);
    
    // Use in more complex operations
    const range_2d = try range.unsqueeze(1); // [10, 1]
    const expanded = try range_2d.expand(&.{10, 5}); // Broadcast to [10, 5]
    try testing.expectEqual(@as(usize, 2), expanded.rank());
    try testing.expectEqual(@as(i64, 10), expanded.shape.dims[0].concrete);
    try testing.expectEqual(@as(i64, 5), expanded.shape.dims[1].concrete);
}

test "tensor API - essential arithmetic functions" {
    const testing = std.testing;
    
    var graph = try Graph.init(testing.allocator);
    defer graph.deinit();
    
    // Create test tensors
    const a = try placeholder(&graph, &.{2, 3}, .f32);
    const b = try placeholder(&graph, &.{2, 3}, .f32);
    
    // Test top-level arithmetic functions
    const sum_result = try add(a, b);
    try testing.expectEqual(@as(usize, 2), sum_result.rank());
    try testing.expectEqual(@as(i64, 2), sum_result.shape.dims[0].concrete);
    try testing.expectEqual(@as(i64, 3), sum_result.shape.dims[1].concrete);
    
    const mul_result = try mul(a, b);
    try testing.expectEqual(@as(usize, 2), mul_result.rank());
    
    const sub_result = try subtract(a, b);
    try testing.expectEqual(@as(usize, 2), sub_result.rank());
    
    const div_result = try divide(a, b);
    try testing.expectEqual(@as(usize, 2), div_result.rank());
    
    // Test that function style and method style produce equivalent results
    const method_sum = try a.add(b);
    try testing.expectEqual(sum_result.shape.dims.len, method_sum.shape.dims.len);
    for (sum_result.shape.dims, method_sum.shape.dims) |f_dim, m_dim| {
        try testing.expectEqual(f_dim.concrete, m_dim.concrete);
    }
}