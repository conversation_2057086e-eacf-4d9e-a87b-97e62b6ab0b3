/// Compile-time memory layout optimization
const std = @import("std");
const testing = std.testing;
const types = @import("types");
const DataType = types.DataType;

/// Compile-time optimal memory layout for tensors
pub fn ComptimeLayout(comptime shape: []const i64, comptime dtype: DataType, comptime backend_config: ?@import("../storage.zig").BackendStorageConfig) type {
    return struct {
        pub const dimensions = shape;
        pub const element_type = dtypeToZigType(dtype);
        pub const element_size = @sizeOf(element_type);
        pub const total_elements = comptimeNumElements(shape);
        pub const total_bytes = total_elements * element_size;
        
        // Use backend config or defaults
        pub const config = backend_config orelse @import("../storage.zig").BackendStorageConfig.default(.cpu);
        
        // Cache line optimization
        pub const cache_line_size = config.cache_line_size;
        pub const elements_per_cache_line = cache_line_size / element_size;
        pub const cache_lines_needed = (total_bytes + cache_line_size - 1) / cache_line_size;
        
        // SIMD optimization
        pub const simd_alignment = config.simd_alignment;
        pub const simd_vector_size = @min(config.max_vector_width / element_size, comptimeSIMDVectorSize(dtype));
        pub const can_use_simd = total_elements >= simd_vector_size and total_elements % simd_vector_size == 0;
        
        // Memory padding for alignment
        pub const alignment = @max(simd_alignment, @alignOf(element_type));
        pub const padded_bytes = blk: {
            const unpadded = total_bytes;
            const padding = alignment - (unpadded % alignment);
            break :blk if (padding == alignment) unpadded else unpadded + padding;
        };
        
        // Stride optimization for common access patterns
        pub const optimal_strides = comptimeOptimalStrides(shape, dtype);
        pub const is_contiguous = comptimeIsContiguous(optimal_strides, shape);
        
        /// Generate optimized allocation
        pub fn allocate(allocator: std.mem.Allocator) ![]align(alignment) u8 {
            return try allocator.alignedAlloc(u8, alignment, padded_bytes);
        }
        
        /// Generate optimized copy kernel
        pub fn generateCopyKernel() type {
            return struct {
                pub inline fn copy(dst: []u8, src: []const u8) void {
                    if (comptime can_use_simd) {
                        // SIMD-optimized copy
                        const VecType = @Vector(simd_vector_size, element_type);
                        const src_vecs = @as([*]const VecType, @ptrCast(@alignCast(src.ptr)));
                        const dst_vecs = @as([*]VecType, @ptrCast(@alignCast(dst.ptr)));
                        const vec_count = total_elements / simd_vector_size;
                        
                        for (0..vec_count) |i| {
                            dst_vecs[i] = src_vecs[i];
                        }
                    } else {
                        // Regular copy
                        @memcpy(dst[0..total_bytes], src[0..total_bytes]);
                    }
                }
            };
        }
        
        /// Generate optimized fill kernel
        pub fn generateFillKernel() type {
            return struct {
                pub inline fn fill(buffer: []u8, value: element_type) void {
                    if (comptime can_use_simd) {
                        // SIMD-optimized fill
                        const VecType = @Vector(simd_vector_size, element_type);
                        const vec_value: VecType = @splat(value);
                        const dst_vecs = @as([*]VecType, @ptrCast(@alignCast(buffer.ptr)));
                        const vec_count = total_elements / simd_vector_size;
                        
                        for (0..vec_count) |i| {
                            dst_vecs[i] = vec_value;
                        }
                    } else {
                        // Regular fill
                        const elements = @as([*]element_type, @ptrCast(@alignCast(buffer.ptr)));
                        for (0..total_elements) |i| {
                            elements[i] = value;
                        }
                    }
                }
            };
        }
    };
}

/// Convert DataType to Zig type
fn dtypeToZigType(comptime dtype: DataType) type {
    return switch (dtype) {
        .f32 => f32,
        .f64 => f64,
        .f16 => f16,
        .i32 => i32,
        .i64 => i64,
        .i16 => i16,
        .i8 => i8,
        .u32 => u32,
        .u64 => u64,
        .u16 => u16,
        .u8 => u8,
        .bool => bool,
    };
}

/// Calculate total elements
fn comptimeNumElements(comptime shape: []const i64) i64 {
    var total: i64 = 1;
    for (shape) |dim| {
        total *= dim;
    }
    return total;
}

/// Determine SIMD alignment for data type
fn comptimeSIMDAlignment(comptime dtype: DataType) usize {
    return switch (dtype) {
        .f32, .i32, .u32 => 32, // AVX alignment
        .f64, .i64, .u64 => 32, // AVX alignment
        .f16, .i16, .u16 => 16, // SSE alignment
        .i8, .u8, .bool => 16,  // SSE alignment
    };
}

/// Determine SIMD vector size for data type
fn comptimeSIMDVectorSize(comptime dtype: DataType) usize {
    return switch (dtype) {
        .f32, .i32, .u32 => 8,  // AVX: 256 bits / 32 bits
        .f64, .i64, .u64 => 4,  // AVX: 256 bits / 64 bits
        .f16, .i16, .u16 => 16, // AVX: 256 bits / 16 bits
        .i8, .u8 => 32,         // AVX: 256 bits / 8 bits
        .bool => 32,            // Treat as u8
    };
}

/// Calculate optimal strides for memory access
fn comptimeOptimalStrides(comptime shape: []const i64, comptime dtype: DataType) [shape.len]i64 {
    _ = dtype;
    
    if (shape.len == 0) return .{};
    
    var strides: [shape.len]i64 = undefined;
    strides[shape.len - 1] = 1;
    
    if (shape.len > 1) {
        var i = shape.len - 1;
        while (i > 0) : (i -= 1) {
            strides[i - 1] = strides[i] * shape[i];
        }
    }
    
    return strides;
}

/// Check if layout is contiguous
fn comptimeIsContiguous(comptime strides: []const i64, comptime shape: []const i64) bool {
    if (strides.len != shape.len) return false;
    
    var expected_stride: i64 = 1;
    var i = strides.len;
    while (i > 0) : (i -= 1) {
        if (strides[i - 1] != expected_stride) return false;
        expected_stride *= shape[i - 1];
    }
    
    return true;
}

/// Compile-time memory pool optimization
pub fn ComptimeMemoryPool(comptime configs: []const LayoutConfig, comptime backend_config: ?@import("../storage.zig").BackendStorageConfig) type {
    const total_size = comptime blk: {
        var size: usize = 0;
        for (configs) |config| {
            const Layout = ComptimeLayout(config.shape, config.dtype, backend_config);
            size += Layout.padded_bytes;
        }
        break :blk size;
    };
    
    const offsets = comptime blk: {
        var offs: [configs.len]usize = undefined;
        var current_offset: usize = 0;
        
        for (configs, 0..) |config, i| {
            const Layout = ComptimeLayout(config.shape, config.dtype, backend_config);
            // Align offset
            const alignment = Layout.alignment;
            const padding = alignment - (current_offset % alignment);
            if (padding != alignment) current_offset += padding;
            
            offs[i] = current_offset;
            current_offset += Layout.padded_bytes;
        }
        
        break :blk offs;
    };
    
    return struct {
        pub const pool_size = total_size;
        pub const buffer_offsets = offsets;
        
        buffer: []align(64) u8,
        
        pub fn init(allocator: std.mem.Allocator) !@This() {
            return .{
                .buffer = try allocator.alignedAlloc(u8, 64, pool_size),
            };
        }
        
        pub fn deinit(self: *@This(), allocator: std.mem.Allocator) void {
            allocator.free(self.buffer);
        }
        
        pub fn getBuffer(self: *@This(), comptime idx: usize) []u8 {
            const offset = buffer_offsets[idx];
            const Layout = ComptimeLayout(configs[idx].shape, configs[idx].dtype, backend_config);
            return self.buffer[offset..][0..Layout.total_bytes];
        }
    };
}

pub const LayoutConfig = struct {
    shape: []const i64,
    dtype: DataType,
};

/// Generate optimal tiling for matrix operations
pub fn ComptimeTiling(comptime m: i64, comptime n: i64, comptime k: i64, comptime dtype: DataType, comptime backend_config: ?@import("../storage.zig").BackendStorageConfig) type {
    const config = backend_config orelse @import("../storage.zig").BackendStorageConfig.default(.cpu);
    // Optimize for L1 cache
    const l1_size = config.l1_cache_size;
    const element_size = @sizeOf(dtypeToZigType(dtype));
    const l1_elements = l1_size / element_size;
    
    // Find optimal tile sizes based on backend preferences
    const preferred_tile = @as(i64, @intCast(config.preferred_tile_size));
    
    const tile_m = comptime blk: {
        var tm: i64 = preferred_tile;
        while (tm > 1 and (tm * preferred_tile * 3) > l1_elements) : (tm /= 2) {}
        break :blk @min(tm, m);
    };
    
    const tile_n = comptime blk: {
        var tn: i64 = preferred_tile;
        while (tn > 1 and (tile_m * tn * 3) > l1_elements) : (tn /= 2) {}
        break :blk @min(tn, n);
    };
    
    const tile_k = comptime blk: {
        var tk: i64 = preferred_tile;
        while (tk > 1 and (tile_m * tk + tk * tile_n) > l1_elements) : (tk /= 2) {}
        break :blk @min(tk, k);
    };
    
    return struct {
        pub const M = m;
        pub const N = n;
        pub const K = k;
        pub const TILE_M = tile_m;
        pub const TILE_N = tile_n;
        pub const TILE_K = tile_k;
        
        pub const m_tiles = (M + TILE_M - 1) / TILE_M;
        pub const n_tiles = (N + TILE_N - 1) / TILE_N;
        pub const k_tiles = (K + TILE_K - 1) / TILE_K;
        
        /// Generate tiled iteration
        pub inline fn iterateTiles(comptime func: fn(i: i64, j: i64, k: i64) void) void {
            comptime var i: i64 = 0;
            inline while (i < M) : (i += TILE_M) {
                comptime var j: i64 = 0;
                inline while (j < N) : (j += TILE_N) {
                    comptime var k_idx: i64 = 0;
                    inline while (k_idx < K) : (k_idx += TILE_K) {
                        func(i, j, k_idx);
                    }
                }
            }
        }
    };
}

// ===== Tests =====

test "ComptimeLayout basic properties" {
    const Layout = ComptimeLayout(&.{ 2, 3, 4 }, .f32, null);
    
    try testing.expectEqual(@as(i64, 24), Layout.total_elements);
    try testing.expectEqual(@as(usize, 96), Layout.total_bytes);
    try testing.expectEqual(@as(usize, 32), Layout.simd_alignment);
    try testing.expectEqual(@as(usize, 8), Layout.simd_vector_size);
    try testing.expect(Layout.can_use_simd);
}

test "ComptimeLayout SIMD copy" {
    const Layout = ComptimeLayout(&.{ 4, 8 }, .f32, null);
    const CopyKernel = Layout.generateCopyKernel();
    
    var src align(32) = [_]f32{0} ** 32;
    var dst align(32) = [_]f32{0} ** 32;
    
    // Initialize source
    for (&src, 0..) |*val, i| {
        val.* = @floatFromInt(i);
    }
    
    // Copy using SIMD kernel
    CopyKernel.copy(std.mem.asBytes(&dst), std.mem.asBytes(&src));
    
    // Verify
    for (src, dst) |s, d| {
        try testing.expectEqual(s, d);
    }
}

test "ComptimeMemoryPool" {
    const configs = [_]LayoutConfig{
        .{ .shape = &.{ 10, 10 }, .dtype = .f32 },
        .{ .shape = &.{ 5, 20 }, .dtype = .f64 },
        .{ .shape = &.{ 100 }, .dtype = .i32 },
    };
    
    const Pool = ComptimeMemoryPool(&configs, null);
    
    var pool = try Pool.init(testing.allocator);
    defer pool.deinit(testing.allocator);
    
    // Get buffers
    const buf0 = pool.getBuffer(0);
    const buf1 = pool.getBuffer(1);
    const buf2 = pool.getBuffer(2);
    
    try testing.expectEqual(@as(usize, 400), buf0.len); // 10*10*4
    try testing.expectEqual(@as(usize, 800), buf1.len); // 5*20*8
    try testing.expectEqual(@as(usize, 400), buf2.len); // 100*4
}

test "ComptimeTiling" {
    const Tiling = ComptimeTiling(128, 128, 128, .f32, null);
    
    try testing.expect(Tiling.TILE_M <= 64);
    try testing.expect(Tiling.TILE_N <= 64);
    try testing.expect(Tiling.TILE_K <= 64);
    
    // Test that tiles cover the entire matrix
    try testing.expect(Tiling.m_tiles * Tiling.TILE_M >= Tiling.M);
    try testing.expect(Tiling.n_tiles * Tiling.TILE_N >= Tiling.N);
    try testing.expect(Tiling.k_tiles * Tiling.TILE_K >= Tiling.K);
}

test "Backend-specific configurations" {
    const storage = @import("../storage.zig");
    
    // Test CPU configuration
    const CpuLayout = ComptimeLayout(&.{ 100 }, .f32, storage.BackendStorageConfig.default(.cpu));
    try testing.expectEqual(@as(usize, 32), CpuLayout.simd_alignment);
    
    // Test WASM configuration
    const WasmLayout = ComptimeLayout(&.{ 100 }, .f32, storage.BackendStorageConfig.default(.wasm));
    try testing.expectEqual(@as(usize, 16), WasmLayout.simd_alignment);
    
    // Test CUDA configuration
    const CudaLayout = ComptimeLayout(&.{ 100 }, .f32, storage.BackendStorageConfig.default(.cuda));
    try testing.expectEqual(@as(usize, 256), CudaLayout.simd_alignment);
}