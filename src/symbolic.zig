const std = @import("std");
const Allocator = std.mem.Allocator;
const testing = std.testing;

// Import shared primitive types
const types = @import("types");
const ExprId = types.ExprId;

// Feature flags (will be provided by build system)
const build_options = if (@hasDecl(@import("root"), "build_options")) @import("build_options") else struct {
    pub const enable_symbolic_extended_ops = true;
    pub const enable_symbolic_stats = false;
};
const enable_extended_ops = build_options.enable_symbolic_extended_ops;
const enable_stats = build_options.enable_symbolic_stats;

// ===== Constants =====

/// Maximum number of symbols in expressions
pub const MAX_SYMBOLS = 26; // 'a' through 'z'

// ===== Core Types =====

// Expression types
pub const ExprType = enum(u8) {
    constant,
    variable,
    binary,
};

// Core operations (always available)
pub const CoreBinaryOp = enum(u8) {
    // Essential arithmetic
    add,
    subtract,
    multiply,
    divide,

    // Essential comparison (return 0 or 1)
    eq,
    ne,
    lt,
    lte,
    gt,
    gte,

    // Essential logical (operate on 0/1 values)
    and_,
    or_,

    // Min/Max (useful for broadcasting)
    min,
    max,
};

/// Symbol source for shape tracking
pub const SymbolSource = enum {
    input_shape, // From input tensor dimensions
    broadcast_result, // From broadcasting operations
    reduction_result, // From reduction operations
    derived, // Computed from other symbols
};

/// Map from symbol to value for runtime shape evaluation
pub const SymbolValueMap = std.AutoHashMap(u8, i64);

// Extended operations (compile-time optional)
pub const ExtendedBinaryOp = enum(u8) {
    mod,
    pow,
};

// Unified binary operation type
pub const BinaryOp = if (enable_extended_ops)
    enum(u8) {
        // Core operations
        add,
        subtract,
        multiply,
        divide,
        eq,
        ne,
        lt,
        lte,
        gt,
        gte,
        and_,
        or_,
        min,
        max,
        // Extended operations
        mod,
        pow,
    }
else
    enum(u8) {
        // Core operations only
        add,
        subtract,
        multiply,
        divide,
        eq,
        ne,
        lt,
        lte,
        gt,
        gte,
        and_,
        or_,
        min,
        max,
    };

// Helper functions for BinaryOp
pub fn getBinaryOpNeutral(op: BinaryOp) i64 {
    return switch (op) {
        .add => 0,
        .multiply => 1,
        .max => std.math.minInt(i64),
        .min => std.math.maxInt(i64),
        else => 0, // Default for operations without a clear neutral element
    };
}

pub fn applyBinaryOp(op: BinaryOp, a: i64, b: i64) i64 {
    return switch (op) {
        .add => a + b,
        .subtract => a - b,
        .multiply => a * b,
        .divide => if (b != 0) @divTrunc(a, b) else 0,
        .eq => if (a == b) 1 else 0,
        .ne => if (a != b) 1 else 0,
        .lt => if (a < b) 1 else 0,
        .lte => if (a <= b) 1 else 0,
        .gt => if (a > b) 1 else 0,
        .gte => if (a >= b) 1 else 0,
        .and_ => if (a != 0 and b != 0) 1 else 0,
        .or_ => if (a != 0 or b != 0) 1 else 0,
        .min => @min(a, b),
        .max => @max(a, b),
        .mod => if (enable_extended_ops) @mod(a, b) else 0,
        .pow => if (enable_extended_ops) std.math.pow(i64, a, b) else 0,
    };
}

// Expression data (Structure of Arrays for cache efficiency)
pub const ExprData = union {
    constant: i64,
    variable: u32, // Index into variable name table
    binary: struct {
        op: BinaryOp,
        left: ExprId,
        right: ExprId,
    },
};

// Expression ID is now imported from types.zig

// Key for expression interning
pub const ExprKey = union(enum) {
    constant: i64,
    variable: u32,
    binary: struct {
        op: BinaryOp,
        left: ExprId,
        right: ExprId,
    },

    pub const Context = struct {
        pub fn hash(_: Context, key: ExprKey) u64 {
            var hasher = std.hash.Wyhash.init(0);

            // Hash discriminant first to avoid collisions
            const discriminant: u8 = switch (key) {
                .constant => 0,
                .variable => 1,
                .binary => 2,
            };
            hasher.update(&[_]u8{discriminant});

            // Hash actual data without padding
            switch (key) {
                .constant => |v| hasher.update(std.mem.asBytes(&v)),
                .variable => |v| hasher.update(std.mem.asBytes(&v)),
                .binary => |b| {
                    hasher.update(std.mem.asBytes(&b.op));
                    hasher.update(std.mem.asBytes(&b.left));
                    hasher.update(std.mem.asBytes(&b.right));
                },
            }

            return hasher.final();
        }

        pub fn eql(_: Context, a: ExprKey, b: ExprKey) bool {
            return switch (a) {
                .constant => |av| switch (b) {
                    .constant => |bv| av == bv,
                    else => false,
                },
                .variable => |av| switch (b) {
                    .variable => |bv| av == bv,
                    else => false,
                },
                .binary => |ab| switch (b) {
                    .binary => |bb| ab.op == bb.op and
                        ab.left == bb.left and
                        ab.right == bb.right,
                    else => false,
                },
            };
        }
    };
};

// Statistics for monitoring
pub const PoolStats = struct {
    expressions_created: u64 = 0,
    expressions_interned: u64 = 0,
    constants_folded: u64 = 0,
    peak_expression_count: u32 = 0,
};

// Variable bindings for evaluation
pub const Bindings = std.StringHashMapUnmanaged(i64);
pub const DynamicMap = Bindings; // Alias for compatibility

// Common constant IDs - initialized at runtime by init()
pub var ZERO_ID: ExprId = undefined;
pub var ONE_ID: ExprId = undefined;
pub var NEG_ONE_ID: ExprId = undefined;
pub var TWO_ID: ExprId = undefined;

// ===== Error Types =====

pub const SymbolicError = error{
    // Expression creation errors
    VariableNameTooLong,
    VariableNameEmpty,
    ExpressionPoolFull,
    PoolFrozen,

    // Evaluation errors
    VariableNotBound,
    DivisionByZero,
    IntegerOverflow,
    ModuloByZero,
    RecursionDepthExceeded,
    NotImplemented,

    // Memory errors
    OutOfMemory,
};

// ===== Main Pool Structure =====

pub const SymbolicPool = struct {
    // Private fields - use accessors only
    expr_types: std.ArrayListUnmanaged(ExprType) = .{},
    expr_data: std.ArrayListUnmanaged(ExprData) = .{},
    intern_map: std.HashMapUnmanaged(ExprKey, ExprId, ExprKey.Context, 80) = .{},

    // Variable name management (using indices to avoid invalidation)
    var_names: std.ArrayListUnmanaged([]const u8) = .{},
    var_name_lookup: std.HashMapUnmanaged([]const u8, u32, std.hash_map.StringContext, 80) = .{},

    // Metadata
    allocator: Allocator,
    stats: if (enable_stats) PoolStats else void = if (enable_stats) .{} else {},
    is_frozen: bool = false,

    // Common constant IDs for efficiency
    zero_id: ?ExprId = null,
    one_id: ?ExprId = null,
    neg_one_id: ?ExprId = null,

    // ===== Initialization =====

    pub fn init(allocator: Allocator) !SymbolicPool {
        var pool = SymbolicPool{
            .allocator = allocator,
        };

        // Pre-create common constants for efficiency
        const zero = try pool.constant(0);
        const one = try pool.constant(1);
        const neg_one = try pool.constant(-1);
        const two = try pool.constant(2);
        _ = try pool.constant(10);

        // Store for quick access
        pool.zero_id = zero;
        pool.one_id = one;
        pool.neg_one_id = neg_one;

        // Set global constant IDs
        ZERO_ID = zero;
        ONE_ID = one;
        NEG_ONE_ID = neg_one;
        TWO_ID = two;

        return pool;
    }

    pub fn deinit(self: *SymbolicPool) void {
        self.expr_types.deinit(self.allocator);
        self.expr_data.deinit(self.allocator);
        self.intern_map.deinit(self.allocator);

        // Deinit HashMap first to avoid dangling pointer access in its destructor
        self.var_name_lookup.deinit(self.allocator);

        // Then free all duped variable name strings
        for (self.var_names.items) |name| {
            self.allocator.free(name);
        }
        self.var_names.deinit(self.allocator);
    }

    pub fn freeze(self: *SymbolicPool) void {
        self.is_frozen = true;
    }

    // ===== Expression Creation =====

    pub fn constant(self: *SymbolicPool, value: i64) !ExprId {
        // Policy: Shape algebra uses full int range; ShapeTracker enforces non-negativity
        // Prevent minInt which cannot be negated safely
        if (value == std.math.minInt(i64)) {
            std.log.err("constant: value {} is minInt and cannot be safely negated", .{value});
            return error.IntegerOverflow;
        }

        // Try interning first
        const key = ExprKey{ .constant = value };
        if (self.intern_map.get(key)) |id| {
            if (enable_stats) self.stats.expressions_interned += 1;
            return id;
        }

        // Create new expression using centralized append
        const id = try self.appendExpr(.constant, .{ .constant = value });
        try self.intern_map.put(self.allocator, key, id);

        return id;
    }

    pub fn variable(self: *SymbolicPool, name: []const u8) !ExprId {
        if (self.is_frozen) {
            std.log.err("variable: cannot add variable '{s}' - pool is frozen", .{name});
            return error.PoolFrozen;
        }
        if (name.len == 0) {
            std.log.err("variable: empty variable name", .{});
            return error.VariableNameEmpty;
        }
        if (name.len > 255) {
            std.log.err("variable: name length {} exceeds maximum 255", .{name.len});
            return error.VariableNameTooLong;
        }

        // Intern variable name
        const name_idx = try self.internString(name);

        // Try interning expression
        const key = ExprKey{ .variable = name_idx };
        if (self.intern_map.get(key)) |id| {
            if (enable_stats) self.stats.expressions_interned += 1;
            return id;
        }

        // Create new expression using centralized append
        const id = try self.appendExpr(.variable, .{ .variable = name_idx });
        try self.intern_map.put(self.allocator, key, id);

        return id;
    }

    pub fn binary(self: *SymbolicPool, op: BinaryOp, left: ExprId, right: ExprId) !ExprId {
        if (self.is_frozen) {
            std.log.err("binary: cannot create {} expression - pool is frozen", .{op});
            return error.PoolFrozen;
        }

        // Guard against unimplemented operations
        if (enable_extended_ops and op == .pow) {
            std.log.err("binary: pow operation not yet implemented", .{});
            return error.NotImplemented;
        }

        // Try simplification first (using original order)
        if (try self.simplifyBinary(op, left, right)) |simplified| {
            return simplified;
        }

        // Canonicalize for interning consistency
        var canonical_left = left;
        var canonical_right = right;
        if (isCommutative(op) and left > right) {
            canonical_left = right;
            canonical_right = left;
        }

        // No simplification possible, create new expression
        const key = ExprKey{ .binary = .{ .op = op, .left = canonical_left, .right = canonical_right } };
        if (self.intern_map.get(key)) |existing_id| {
            return existing_id;
        }
        const new_id = try self.appendExpr(.binary, .{ .binary = .{ .op = op, .left = left, .right = right } });
        try self.intern_map.put(self.allocator, key, new_id);
        return new_id;
    }

    // Main operation methods
    pub fn add(self: *SymbolicPool, left: ExprId, right: ExprId) !ExprId {
        return self.binary(.add, left, right);
    }

    pub fn subtract(self: *SymbolicPool, left: ExprId, right: ExprId) !ExprId {
        return self.binary(.subtract, left, right);
    }

    pub fn multiply(self: *SymbolicPool, left: ExprId, right: ExprId) (SymbolicError || Allocator.Error)!ExprId {
        return self.binary(.multiply, left, right);
    }

    pub fn divide(self: *SymbolicPool, left: ExprId, right: ExprId) !ExprId {
        // Check for division by zero at creation time if possible
        if (self.isConstant(right)) {
            const val = self.getConstantValue(right);
            if (val == 0) {
                std.log.err("divide: division by zero", .{});
                return error.DivisionByZero;
            }
        }
        return self.binary(.divide, left, right);
    }

    // ===== Safe Read-Only Accessors =====

    pub fn getType(self: *const SymbolicPool, expr: ExprId) ExprType {
        return self.expr_types.items[expr];
    }

    pub fn getConstantValue(self: *const SymbolicPool, expr: ExprId) i64 {
        std.debug.assert(self.getType(expr) == .constant);
        return self.expr_data.items[expr].constant;
    }

    pub fn getVariableIndex(self: *const SymbolicPool, expr: ExprId) u32 {
        std.debug.assert(self.getType(expr) == .variable);
        return self.expr_data.items[expr].variable;
    }

    pub fn getVariableName(self: *const SymbolicPool, expr: ExprId) []const u8 {
        const var_idx = self.getVariableIndex(expr);
        return self.getStringByIndex(var_idx);
    }

    fn getStringByIndex(self: *const SymbolicPool, idx: u32) []const u8 {
        return self.var_names.items[idx];
    }

    pub fn getBinaryData(self: *const SymbolicPool, expr: ExprId) struct {
        op: BinaryOp,
        left: ExprId,
        right: ExprId,
    } {
        std.debug.assert(self.getType(expr) == .binary);
        const data = self.expr_data.items[expr].binary;
        return .{
            .op = data.op,
            .left = data.left,
            .right = data.right,
        };
    }

    // Query methods
    pub fn isConstant(self: *const SymbolicPool, expr: ExprId) bool {
        return self.getType(expr) == .constant;
    }

    pub fn isVariable(self: *const SymbolicPool, expr: ExprId) bool {
        return self.getType(expr) == .variable;
    }

    pub fn isBinary(self: *const SymbolicPool, expr: ExprId) bool {
        return self.getType(expr) == .binary;
    }

    pub fn isConst(self: *const SymbolicPool, expr: ExprId, value: i64) bool {
        return self.isConstant(expr) and self.getConstantValue(expr) == value;
    }

    // ===== Expression Evaluation =====

    pub fn evaluate(
        self: *const SymbolicPool,
        expr: ExprId,
        bindings: *const Bindings,
    ) !i64 {
        const allocator = self.allocator;

        // Work stack for iterative evaluation
        const WorkItem = struct {
            expr: ExprId,
            phase: enum { pending, left_done, ready },
            left_value: i64 = 0,
        };

        var work_stack = std.ArrayList(WorkItem).init(allocator);
        defer work_stack.deinit();

        var result_stack = std.ArrayList(i64).init(allocator);
        defer result_stack.deinit();

        // Start with the root expression
        try work_stack.append(.{ .expr = expr, .phase = .pending });

        while (work_stack.items.len > 0) {
            var work = &work_stack.items[work_stack.items.len - 1];

            switch (self.getType(work.expr)) {
                .constant => {
                    const value = self.getConstantValue(work.expr);
                    try result_stack.append(value);
                    _ = work_stack.pop();
                },
                .variable => {
                    const name = self.getVariableName(work.expr);
                    const value = bindings.get(name) orelse {
                        std.log.err("evaluate: variable '{s}' not found in bindings", .{name});
                        return error.VariableNotBound;
                    };
                    try result_stack.append(value);
                    _ = work_stack.pop();
                },
                .binary => {
                    const data = self.getBinaryData(work.expr);

                    switch (work.phase) {
                        .pending => {
                            // Push left operand to evaluate
                            work.phase = .left_done;
                            try work_stack.append(.{ .expr = data.left, .phase = .pending });
                        },
                        .left_done => {
                            // Left is evaluated, save result and evaluate right
                            work.left_value = result_stack.getLast();
                            _ = result_stack.pop();
                            work.phase = .ready;
                            try work_stack.append(.{ .expr = data.right, .phase = .pending });
                        },
                        .ready => {
                            // Both operands evaluated, compute result
                            const right_value = result_stack.getLast();
                            _ = result_stack.pop();
                            const left_value = work.left_value;

                            const result = try evaluateBinaryOp(data.op, left_value, right_value);
                            try result_stack.append(result);
                            _ = work_stack.pop();
                        },
                    }
                },
            }
        }

        std.debug.assert(result_stack.items.len == 1);
        return result_stack.items[0];
    }

    // ===== Expression Analysis =====

    pub fn containsVariable(self: *const SymbolicPool, expr: ExprId, var_name: []const u8) bool {
        return switch (self.getType(expr)) {
            .constant => false,
            .variable => std.mem.eql(u8, self.getVariableName(expr), var_name),
            .binary => {
                const data = self.getBinaryData(expr);
                return self.containsVariable(data.left, var_name) or
                    self.containsVariable(data.right, var_name);
            },
        };
    }

    pub fn equal(self: *const SymbolicPool, a: ExprId, b: ExprId) bool {
        // Fast path: same ID
        if (a == b) return true;

        // Check types
        const a_type = self.getType(a);
        const b_type = self.getType(b);
        if (a_type != b_type) return false;

        // Compare by type
        return switch (a_type) {
            .constant => self.getConstantValue(a) == self.getConstantValue(b),
            .variable => self.getVariableIndex(a) == self.getVariableIndex(b),
            .binary => {
                const a_data = self.getBinaryData(a);
                const b_data = self.getBinaryData(b);
                return a_data.op == b_data.op and
                    self.equal(a_data.left, b_data.left) and
                    self.equal(a_data.right, b_data.right);
            },
        };
    }

    // ===== Private Helper Methods =====

    fn appendExpr(self: *SymbolicPool, expr_type: ExprType, expr_data: ExprData) !ExprId {
        if (self.is_frozen) {
            std.log.err("appendExpr: cannot append {} expression - pool is frozen", .{expr_type});
            return error.PoolFrozen;
        }

        // Canonicalize binary operations before storage
        var canonical_data = expr_data;
        if (expr_type == .binary) {
            const bin = expr_data.binary;
            if (isCommutative(bin.op) and bin.left > bin.right) {
                canonical_data = .{ .binary = .{
                    .op = bin.op,
                    .left = bin.right,
                    .right = bin.left,
                } };
            }
        }

        const id: ExprId = @intCast(self.expr_types.items.len);

        // Append to both arrays atomically with proper error handling
        try self.expr_types.append(self.allocator, expr_type);
        errdefer self.expr_types.items.len -= 1;

        try self.expr_data.append(self.allocator, canonical_data);
        errdefer self.expr_data.items.len -= 1;

        // Update stats
        if (enable_stats) self.stats.expressions_created += 1;

        return id;
    }

    fn internString(self: *SymbolicPool, name: []const u8) !u32 {
        if (name.len > 255) {
            std.log.err("internString: variable name length {} exceeds maximum 255", .{name.len});
            return error.VariableNameTooLong;
        }

        // Check if already interned
        if (self.var_name_lookup.get(name)) |idx| {
            return idx;
        }

        // Make owned copy of the string
        const owned_name = try self.allocator.dupe(u8, name);
        errdefer self.allocator.free(owned_name);

        // Store string directly in var_names
        const idx = @as(u32, @intCast(self.var_names.items.len));
        try self.var_names.append(self.allocator, owned_name);

        // Store lookup using the owned copy of the string
        try self.var_name_lookup.put(self.allocator, owned_name, idx);

        return idx;
    }

    fn simplifyBinary(
        self: *SymbolicPool,
        op: BinaryOp,
        left: ExprId,
        right: ExprId,
    ) !?ExprId {
        // Constant folding with proper error handling
        if (self.isConstant(left) and self.isConstant(right)) {
            const l = self.getConstantValue(left);
            const r = self.getConstantValue(right);

            const result = try evaluateBinaryOp(op, l, r);

            if (enable_stats) self.stats.constants_folded += 1;
            return try self.constant(result);
        }

        // Algebraic identities
        switch (op) {
            .add => {
                // x + 0 = x
                if (self.isZero(right)) return left;
                if (self.isZero(left)) return right;

                // x + x = 2x (if x is simple enough)
                if (self.equal(left, right)) {
                    const two = try self.constant(2);
                    return try self.multiply(two, left);
                }

                // x + (-x) = 0
                if (self.isNegationOf(left, right)) {
                    return self.zero_id.?;
                }
            },
            .multiply => {
                // x * 1 = x
                if (self.isOne(right)) return left;
                if (self.isOne(left)) return right;

                // x * 0 = 0
                if (self.isZero(right) or self.isZero(left)) {
                    return self.zero_id.?;
                }

                // x * (-1) = -x
                if (self.isMinusOne(right)) {
                    return try self.negate(left);
                }
                if (self.isMinusOne(left)) {
                    return try self.negate(right);
                }

                // Power of 2 optimization: 2^n * x → x << n
                if (self.isConstant(left)) {
                    const val = self.getConstantValue(left);
                    if (val > 0 and @popCount(val) == 1) {
                        // val is a power of 2
                        const shift = @ctz(val);
                        if (shift > 0 and shift < 63) {
                            // Future: could convert to shift operation
                            // For now, just proceed with multiply
                        }
                    }
                }
            },
            .divide => {
                // x / 1 = x
                if (self.isOne(right)) return left;

                // x / x = 1 (if x != 0)
                if (self.equal(left, right)) {
                    return self.one_id.?;
                }

                // 0 / x = 0 (if x != 0)
                if (self.isZero(left)) {
                    return self.zero_id.?;
                }
            },
            .min => {
                // min(x, x) = x
                if (self.equal(left, right)) return left;

                // min(x, MAX_INT) = x
                if (self.isConstant(right) and
                    self.getConstantValue(right) == std.math.maxInt(i64))
                {
                    return left;
                }
            },
            .max => {
                // max(x, x) = x
                if (self.equal(left, right)) return left;

                // max(x, MIN_INT) = x
                if (self.isConstant(right) and
                    self.getConstantValue(right) == std.math.minInt(i64))
                {
                    return left;
                }
            },
            .subtract => {
                // x - 0 = x
                if (self.isZero(right)) return left;

                // x - x = 0
                if (self.equal(left, right)) {
                    return self.zero_id.?;
                }
            },
            .mod => {
                // x % 1 = 0
                if (self.isOne(right)) {
                    return self.zero_id.?;
                }

                // x % x = 0
                if (self.equal(left, right)) {
                    return self.zero_id.?;
                }
            },
            .pow => {
                // Not implemented yet
            },

            // Comparison simplifications
            .eq => {
                // x == x = 1
                if (self.equal(left, right)) {
                    return self.one_id.?;
                }
            },
            .ne => {
                // x != x = 0
                if (self.equal(left, right)) {
                    return self.zero_id.?;
                }
            },
            .lt, .gt => {
                // x < x = 0, x > x = 0
                if (self.equal(left, right)) {
                    return self.zero_id.?;
                }
            },
            .lte, .gte => {
                // x <= x = 1, x >= x = 1
                if (self.equal(left, right)) {
                    return self.one_id.?;
                }
            },

            // Logical simplifications
            .and_ => {
                // x && 0 = 0
                if (self.isZero(left) or self.isZero(right)) {
                    return self.zero_id.?;
                }
                // 1 && x = x (if x is boolean)
                if (self.isOne(left)) return right;
                if (self.isOne(right)) return left;
            },
            .or_ => {
                // x || 1 = 1
                if (self.isOne(left) or self.isOne(right)) {
                    return self.one_id.?;
                }
                // 0 || x = x
                if (self.isZero(left)) return right;
                if (self.isZero(right)) return left;
            },
        }

        return null; // No simplification found
    }

    // Helper methods for simplification
    fn isZero(self: *const SymbolicPool, expr: ExprId) bool {
        return self.isConstant(expr) and self.getConstantValue(expr) == 0;
    }

    fn isOne(self: *const SymbolicPool, expr: ExprId) bool {
        return self.isConstant(expr) and self.getConstantValue(expr) == 1;
    }

    fn isMinusOne(self: *const SymbolicPool, expr: ExprId) bool {
        return self.isConstant(expr) and self.getConstantValue(expr) == -1;
    }

    fn negate(self: *SymbolicPool, expr: ExprId) !ExprId {
        const minus_one = try self.constant(-1);
        return self.multiply(minus_one, expr);
    }

    fn isNegationOf(self: *const SymbolicPool, a: ExprId, b: ExprId) bool {
        // Check if a = -b (or b = -a)
        // Pattern 1: a is multiply(-1, b)
        if (self.getType(a) == .binary) {
            const data = self.getBinaryData(a);
            if (data.op == .multiply and self.isMinusOne(data.left) and data.right == b) {
                return true;
            }
            if (data.op == .multiply and self.isMinusOne(data.right) and data.left == b) {
                return true;
            }
        }

        // Pattern 2: b is multiply(-1, a)
        if (self.getType(b) == .binary) {
            const data = self.getBinaryData(b);
            if (data.op == .multiply and self.isMinusOne(data.left) and data.right == a) {
                return true;
            }
            if (data.op == .multiply and self.isMinusOne(data.right) and data.left == a) {
                return true;
            }
        }

        // Pattern 3: Both are constants with opposite signs
        if (self.isConstant(a) and self.isConstant(b)) {
            return self.getConstantValue(a) == -self.getConstantValue(b);
        }

        return false;
    }

    pub fn getVariable(self: *const SymbolicPool, name: []const u8) ?ExprId {
        return self.var_name_lookup.get(name);
    }
};

// Helper function for evaluating binary operations
fn evaluateBinaryOp(op: BinaryOp, left: i64, right: i64) !i64 {
    return switch (op) {
        .add => blk: {
            const r = @addWithOverflow(left, right);
            if (r[1] != 0) {
                std.log.err("evaluateBinaryOp: integer overflow in addition: {} + {}", .{ left, right });
                return error.IntegerOverflow;
            }
            break :blk r[0];
        },
        .subtract => blk: {
            const r = @subWithOverflow(left, right);
            if (r[1] != 0) {
                std.log.err("evaluateBinaryOp: integer overflow in subtraction: {} - {}", .{ left, right });
                return error.IntegerOverflow;
            }
            break :blk r[0];
        },
        .multiply => blk: {
            const r = @mulWithOverflow(left, right);
            if (r[1] != 0) {
                std.log.err("evaluateBinaryOp: integer overflow in multiplication: {} * {}", .{ left, right });
                return error.IntegerOverflow;
            }
            break :blk r[0];
        },
        .divide => if (right == 0) {
            std.log.err("evaluateBinaryOp: division by zero: {} / 0", .{left});
            return error.DivisionByZero;
        } else @divFloor(left, right),
        .mod => if (right == 0) {
            std.log.err("evaluateBinaryOp: modulo by zero: {} % 0", .{left});
            return error.ModuloByZero;
        } else blk: {
            // Euclidean modulo: always return non-negative result
            const rem = @rem(left, right);
            break :blk if (rem < 0) rem + @as(i64, @intCast(@abs(right))) else rem;
        },
        .min => @min(left, right),
        .max => @max(left, right),
        .pow => {
            std.log.err("evaluateBinaryOp: pow operation not yet implemented", .{});
            return error.NotImplemented;
        },

        // Comparison operations (return 0 or 1)
        .eq => if (left == right) 1 else 0,
        .ne => if (left != right) 1 else 0,
        .lt => if (left < right) 1 else 0,
        .lte => if (left <= right) 1 else 0,
        .gt => if (left > right) 1 else 0,
        .gte => if (left >= right) 1 else 0,

        // Logical operations (operate on 0/1 values)
        .and_ => if (left != 0 and right != 0) 1 else 0,
        .or_ => if (left != 0 or right != 0) 1 else 0,
    };
}

fn isCommutative(op: BinaryOp) bool {
    return switch (op) {
        .add, .multiply, .min, .max, .eq, .ne, .and_, .or_ => true,
        else => false,
    };
}

// ===== Unit Tests =====

test "expression interning" {
    var arena = std.heap.ArenaAllocator.init(testing.allocator);
    defer arena.deinit();

    var pool = try SymbolicPool.init(arena.child_allocator);
    defer pool.deinit();

    // Same expressions return same ID
    const x = try pool.variable("x");
    const five = try pool.constant(5);

    const expr1 = try pool.add(x, five);
    const expr2 = try pool.add(x, five);

    try testing.expectEqual(expr1, expr2);
}

test "algebraic identities" {
    var arena = std.heap.ArenaAllocator.init(testing.allocator);
    defer arena.deinit();

    var pool = try SymbolicPool.init(arena.child_allocator);
    defer pool.deinit();

    const x = try pool.variable("x");
    const zero = pool.zero_id.?;
    const one = pool.one_id.?;

    // x + 0 = x
    const x_plus_zero = try pool.add(x, zero);
    try testing.expectEqual(x, x_plus_zero);

    // x * 1 = x
    const x_times_one = try pool.multiply(x, one);
    try testing.expectEqual(x, x_times_one);

    // x * 0 = 0
    const x_times_zero = try pool.multiply(x, zero);
    try testing.expectEqual(zero, x_times_zero);
}

test "evaluation" {
    var arena = std.heap.ArenaAllocator.init(testing.allocator);
    defer arena.deinit();

    var pool = try SymbolicPool.init(arena.child_allocator);
    defer pool.deinit();

    // Create expression: 2x + 3y
    const x = try pool.variable("x");
    const y = try pool.variable("y");
    const two = try pool.constant(2);
    const three = try pool.constant(3);

    const expr = try pool.add(try pool.multiply(two, x), try pool.multiply(three, y));

    // Evaluate with x=5, y=7
    var bindings = Bindings{};
    defer bindings.deinit(arena.child_allocator);

    try bindings.put(arena.child_allocator, "x", 5);
    try bindings.put(arena.child_allocator, "y", 7);

    const result = try pool.evaluate(expr, &bindings);
    try testing.expectEqual(@as(i64, 31), result); // 2*5 + 3*7 = 31
}

test "comparison operations" {
    var arena = std.heap.ArenaAllocator.init(testing.allocator);
    defer arena.deinit();

    var pool = try SymbolicPool.init(arena.child_allocator);
    defer pool.deinit();

    const a = try pool.constant(5);
    const b = try pool.constant(3);

    // Test all comparison ops
    const gt = try pool.binary(.gt, a, b);
    const lt = try pool.binary(.lt, a, b);
    const gte = try pool.binary(.gte, a, b);
    const lte = try pool.binary(.lte, a, b);
    const eq = try pool.binary(.eq, a, b);
    const ne = try pool.binary(.ne, a, b);

    var bindings = Bindings{};
    defer bindings.deinit(arena.child_allocator);

    try testing.expectEqual(@as(i64, 1), try pool.evaluate(gt, &bindings)); // 5 > 3
    try testing.expectEqual(@as(i64, 0), try pool.evaluate(lt, &bindings)); // 5 < 3
    try testing.expectEqual(@as(i64, 1), try pool.evaluate(gte, &bindings)); // 5 >= 3
    try testing.expectEqual(@as(i64, 0), try pool.evaluate(lte, &bindings)); // 5 <= 3
    try testing.expectEqual(@as(i64, 0), try pool.evaluate(eq, &bindings)); // 5 == 3
    try testing.expectEqual(@as(i64, 1), try pool.evaluate(ne, &bindings)); // 5 != 3
}

test "logical operations" {
    var arena = std.heap.ArenaAllocator.init(testing.allocator);
    defer arena.deinit();

    var pool = try SymbolicPool.init(arena.child_allocator);
    defer pool.deinit();

    const true_val = try pool.constant(1);
    const false_val = try pool.constant(0);

    // Test AND
    const and_tt = try pool.binary(.and_, true_val, true_val);
    const and_tf = try pool.binary(.and_, true_val, false_val);
    const and_ff = try pool.binary(.and_, false_val, false_val);

    // Test OR
    const or_tt = try pool.binary(.or_, true_val, true_val);
    const or_tf = try pool.binary(.or_, true_val, false_val);
    const or_ff = try pool.binary(.or_, false_val, false_val);

    var bindings = Bindings{};
    defer bindings.deinit(arena.child_allocator);

    // AND truth table
    try testing.expectEqual(@as(i64, 1), try pool.evaluate(and_tt, &bindings));
    try testing.expectEqual(@as(i64, 0), try pool.evaluate(and_tf, &bindings));
    try testing.expectEqual(@as(i64, 0), try pool.evaluate(and_ff, &bindings));

    // OR truth table
    try testing.expectEqual(@as(i64, 1), try pool.evaluate(or_tt, &bindings));
    try testing.expectEqual(@as(i64, 1), try pool.evaluate(or_tf, &bindings));
    try testing.expectEqual(@as(i64, 0), try pool.evaluate(or_ff, &bindings));
}

test "overflow detection" {
    var arena = std.heap.ArenaAllocator.init(testing.allocator);
    defer arena.deinit();

    var pool = try SymbolicPool.init(arena.child_allocator);
    defer pool.deinit();

    const max_val = try pool.constant(std.math.maxInt(i64));
    const one = try pool.constant(1);

    // Overflow should be caught at creation time during constant folding
    try testing.expectError(error.IntegerOverflow, pool.add(max_val, one));

    // Test overflow during evaluation with variables
    const x = try pool.variable("x");
    const y = try pool.variable("y");
    const expr = try pool.add(x, y);

    var bindings = Bindings{};
    defer bindings.deinit(arena.child_allocator);
    try bindings.put(arena.child_allocator, "x", std.math.maxInt(i64));
    try bindings.put(arena.child_allocator, "y", 1);

    // Should return IntegerOverflow error during evaluation
    try testing.expectError(error.IntegerOverflow, pool.evaluate(expr, &bindings));
}

test "division by zero" {
    var arena = std.heap.ArenaAllocator.init(testing.allocator);
    defer arena.deinit();

    var pool = try SymbolicPool.init(arena.child_allocator);
    defer pool.deinit();

    const x = try pool.variable("x");
    const zero = try pool.constant(0);

    // Division by constant zero should fail at creation
    try testing.expectError(error.DivisionByZero, pool.divide(x, zero));

    // Division by variable that evaluates to zero should fail at evaluation
    const y = try pool.variable("y");
    const div_expr = try pool.divide(x, y);

    var bindings = Bindings{};
    defer bindings.deinit(arena.child_allocator);
    try bindings.put(arena.child_allocator, "x", 10);
    try bindings.put(arena.child_allocator, "y", 0);

    try testing.expectError(error.DivisionByZero, pool.evaluate(div_expr, &bindings));
}

test "string interning" {
    var arena = std.heap.ArenaAllocator.init(testing.allocator);
    defer arena.deinit();

    var pool = try SymbolicPool.init(arena.child_allocator);
    defer pool.deinit();

    // Same variable name should produce same variable
    const x1 = try pool.variable("x");
    const x2 = try pool.variable("x");
    try testing.expectEqual(x1, x2);

    // Different names should produce different variables
    const y = try pool.variable("y");
    try testing.expect(x1 != y);

    // Verify names are preserved
    try testing.expectEqualStrings("x", pool.getVariableName(x1));
    try testing.expectEqualStrings("y", pool.getVariableName(y));
}

test "min/max operations" {
    var arena = std.heap.ArenaAllocator.init(testing.allocator);
    defer arena.deinit();

    var pool = try SymbolicPool.init(arena.child_allocator);
    defer pool.deinit();

    const a = try pool.constant(5);
    const b = try pool.constant(3);

    const min_ab = try pool.binary(.min, a, b);
    const max_ab = try pool.binary(.max, a, b);

    var bindings = Bindings{};
    defer bindings.deinit(arena.child_allocator);

    try testing.expectEqual(@as(i64, 3), try pool.evaluate(min_ab, &bindings));
    try testing.expectEqual(@as(i64, 5), try pool.evaluate(max_ab, &bindings));
}

test "freeze pool" {
    var arena = std.heap.ArenaAllocator.init(testing.allocator);
    defer arena.deinit();

    var pool = try SymbolicPool.init(arena.child_allocator);
    defer pool.deinit();

    // Can create expressions before freeze
    const x = try pool.variable("x");

    // Freeze the pool
    pool.freeze();

    // Cannot create new expressions after freeze
    try testing.expectError(error.PoolFrozen, pool.variable("y"));
    try testing.expectError(error.PoolFrozen, pool.binary(.add, x, x));
}

test "complex expression" {
    var arena = std.heap.ArenaAllocator.init(testing.allocator);
    defer arena.deinit();

    var pool = try SymbolicPool.init(arena.child_allocator);
    defer pool.deinit();

    // Create expression: (x + 2) * (y - 3)
    const x = try pool.variable("x");
    const y = try pool.variable("y");
    const two = try pool.constant(2);
    const three = try pool.constant(3);

    const x_plus_2 = try pool.add(x, two);
    const y_minus_3 = try pool.subtract(y, three);
    const expr = try pool.multiply(x_plus_2, y_minus_3);

    var bindings = Bindings{};
    defer bindings.deinit(arena.child_allocator);
    try bindings.put(arena.child_allocator, "x", 5);
    try bindings.put(arena.child_allocator, "y", 7);

    const result = try pool.evaluate(expr, &bindings);
    try testing.expectEqual(@as(i64, 28), result); // (5+2)*(7-3) = 7*4 = 28
}

test "modulo operation" {
    if (!enable_extended_ops) return;

    var arena = std.heap.ArenaAllocator.init(testing.allocator);
    defer arena.deinit();

    var pool = try SymbolicPool.init(arena.child_allocator);
    defer pool.deinit();

    const a = try pool.constant(17);
    const b = try pool.constant(5);

    const mod_expr = try pool.binary(.mod, a, b);

    var bindings = Bindings{};
    defer bindings.deinit(arena.child_allocator);

    try testing.expectEqual(@as(i64, 2), try pool.evaluate(mod_expr, &bindings)); // 17 % 5 = 2
}

test "expression equality" {
    var arena = std.heap.ArenaAllocator.init(testing.allocator);
    defer arena.deinit();

    var pool = try SymbolicPool.init(arena.child_allocator);
    defer pool.deinit();

    const x = try pool.variable("x");
    const y = try pool.variable("y");
    const two = try pool.constant(2);

    // Same structure should be equal
    const expr1 = try pool.add(try pool.multiply(two, x), y);
    const expr2 = try pool.add(try pool.multiply(two, x), y);
    try testing.expect(pool.equal(expr1, expr2));

    // Different structure should not be equal
    const expr3 = try pool.add(x, try pool.multiply(two, y));
    try testing.expect(!pool.equal(expr1, expr3));
}

test "canonicalization of commutative ops" {
    var arena = std.heap.ArenaAllocator.init(testing.allocator);
    defer arena.deinit();

    var pool = try SymbolicPool.init(arena.child_allocator);
    defer pool.deinit();

    const x = try pool.variable("x");
    const y = try pool.variable("y");

    // Both should produce the same expression due to canonicalization
    const add_xy = try pool.add(x, y);
    const add_yx = try pool.add(y, x);
    try testing.expectEqual(add_xy, add_yx);

    const mul_xy = try pool.multiply(x, y);
    const mul_yx = try pool.multiply(y, x);
    try testing.expectEqual(mul_xy, mul_yx);
}

test "convenience methods" {
    var arena = std.heap.ArenaAllocator.init(testing.allocator);
    defer arena.deinit();

    var pool = try SymbolicPool.init(arena.child_allocator);
    defer pool.deinit();

    // Test various operation methods
    const a = try pool.constant(5);
    const b = try pool.constant(3);

    const sum = try pool.add(a, b);
    const diff = try pool.subtract(a, b);
    const prod = try pool.multiply(a, b);
    const quot = try pool.divide(a, b);

    var bindings = Bindings{};
    defer bindings.deinit(arena.child_allocator);

    try testing.expectEqual(@as(i64, 8), try pool.evaluate(sum, &bindings));
    try testing.expectEqual(@as(i64, 2), try pool.evaluate(diff, &bindings));
    try testing.expectEqual(@as(i64, 15), try pool.evaluate(prod, &bindings));
    try testing.expectEqual(@as(i64, 1), try pool.evaluate(quot, &bindings));
}

test "global constant IDs" {
    var arena = std.heap.ArenaAllocator.init(testing.allocator);
    defer arena.deinit();

    var pool = try SymbolicPool.init(arena.child_allocator);
    defer pool.deinit();

    // Verify global constants are set correctly
    try testing.expectEqual(ZERO_ID, pool.zero_id.?);
    try testing.expectEqual(ONE_ID, pool.one_id.?);
    try testing.expectEqual(NEG_ONE_ID, pool.neg_one_id.?);

    // Verify they have the expected values
    try testing.expectEqual(@as(i64, 0), pool.getConstantValue(ZERO_ID));
    try testing.expectEqual(@as(i64, 1), pool.getConstantValue(ONE_ID));
    try testing.expectEqual(@as(i64, -1), pool.getConstantValue(NEG_ONE_ID));
    try testing.expectEqual(@as(i64, 2), pool.getConstantValue(TWO_ID));
}

test "variable not bound error" {
    var arena = std.heap.ArenaAllocator.init(testing.allocator);
    defer arena.deinit();

    var pool = try SymbolicPool.init(arena.child_allocator);
    defer pool.deinit();

    const x = try pool.variable("x");
    const y = try pool.variable("y");
    const expr = try pool.add(x, y);

    // Empty bindings - both variables unbound
    var bindings = Bindings{};
    defer bindings.deinit(arena.child_allocator);

    try testing.expectError(error.VariableNotBound, pool.evaluate(expr, &bindings));

    // Partial bindings - y still unbound
    try bindings.put(arena.child_allocator, "x", 5);
    try testing.expectError(error.VariableNotBound, pool.evaluate(expr, &bindings));

    // All bound - should work
    try bindings.put(arena.child_allocator, "y", 3);
    try testing.expectEqual(@as(i64, 8), try pool.evaluate(expr, &bindings));
}

test "subtract overflow" {
    var arena = std.heap.ArenaAllocator.init(testing.allocator);
    defer arena.deinit();

    var pool = try SymbolicPool.init(arena.child_allocator);
    defer pool.deinit();

    // Test minInt cannot be created as constant
    try testing.expectError(error.IntegerOverflow, pool.constant(std.math.minInt(i64)));

    // Test overflow during evaluation with variables
    const x = try pool.variable("x");
    const y = try pool.variable("y");
    const expr = try pool.subtract(x, y);

    var bindings = Bindings{};
    defer bindings.deinit(arena.child_allocator);
    try bindings.put(arena.child_allocator, "x", std.math.minInt(i64));
    try bindings.put(arena.child_allocator, "y", 1);

    try testing.expectError(error.IntegerOverflow, pool.evaluate(expr, &bindings));

    // Test another case: -maxInt - 2 should overflow
    const large_neg = try pool.constant(-std.math.maxInt(i64));
    const two = try pool.constant(2);
    try testing.expectError(error.IntegerOverflow, pool.subtract(large_neg, two));
}

test "multiply overflow" {
    var arena = std.heap.ArenaAllocator.init(testing.allocator);
    defer arena.deinit();

    var pool = try SymbolicPool.init(arena.child_allocator);
    defer pool.deinit();

    const large = try pool.constant(std.math.maxInt(i64) / 2 + 1);
    const two = try pool.constant(2);

    // Should overflow during constant folding
    try testing.expectError(error.IntegerOverflow, pool.multiply(large, two));

    // Test with variables
    const x = try pool.variable("x");
    const y = try pool.variable("y");
    const expr = try pool.multiply(x, y);

    var bindings = Bindings{};
    defer bindings.deinit(arena.child_allocator);
    try bindings.put(arena.child_allocator, "x", std.math.maxInt(i64) / 2 + 1);
    try bindings.put(arena.child_allocator, "y", 2);

    try testing.expectError(error.IntegerOverflow, pool.evaluate(expr, &bindings));
}

test "pow not implemented" {
    if (!enable_extended_ops) return;

    var arena = std.heap.ArenaAllocator.init(testing.allocator);
    defer arena.deinit();

    var pool = try SymbolicPool.init(arena.child_allocator);
    defer pool.deinit();

    const x = try pool.variable("x");
    const y = try pool.variable("y");

    // Should fail at creation time
    try testing.expectError(error.NotImplemented, pool.binary(.pow, x, y));
}

test "deep expression evaluation" {
    var arena = std.heap.ArenaAllocator.init(testing.allocator);
    defer arena.deinit();

    var pool = try SymbolicPool.init(arena.child_allocator);
    defer pool.deinit();

    // Create a very deep expression tree
    var expr = try pool.constant(1);
    var i: usize = 0;
    while (i < 1000) : (i += 1) {
        expr = try pool.add(expr, try pool.constant(1));
    }

    var bindings = Bindings{};
    defer bindings.deinit(arena.child_allocator);

    // Should handle deep recursion via iterative evaluation
    const result = try pool.evaluate(expr, &bindings);
    try testing.expectEqual(@as(i64, 1001), result);
}

test "evaluate after freeze" {
    var arena = std.heap.ArenaAllocator.init(testing.allocator);
    defer arena.deinit();

    var pool = try SymbolicPool.init(arena.child_allocator);
    defer pool.deinit();

    const x = try pool.variable("x");
    const y = try pool.variable("y");
    const expr = try pool.add(x, y);

    // Freeze the pool
    pool.freeze();

    // Evaluation should still work after freeze
    var bindings = Bindings{};
    defer bindings.deinit(arena.child_allocator);
    try bindings.put(arena.child_allocator, "x", 5);
    try bindings.put(arena.child_allocator, "y", 3);

    const result = try pool.evaluate(expr, &bindings);
    try testing.expectEqual(@as(i64, 8), result);
}

test "euclidean modulo semantics" {
    if (!enable_extended_ops) return;

    var arena = std.heap.ArenaAllocator.init(testing.allocator);
    defer arena.deinit();

    var pool = try SymbolicPool.init(arena.child_allocator);
    defer pool.deinit();

    // Test Euclidean modulo vs @rem
    // Euclidean: -5 mod 3 = 1 (always non-negative)
    // @rem:      -5 rem 3 = -2 (sign of dividend)

    const neg5 = try pool.constant(-5);
    const pos3 = try pool.constant(3);
    const neg3 = try pool.constant(-3);
    const pos5 = try pool.constant(5);

    var bindings = Bindings{};
    defer bindings.deinit(arena.child_allocator);

    // -5 mod 3 should be 1 (Euclidean)
    const expr1 = try pool.binary(.mod, neg5, pos3);
    try testing.expectEqual(@as(i64, 1), try pool.evaluate(expr1, &bindings));

    // 5 mod -3 should be 2 (Euclidean)
    const expr2 = try pool.binary(.mod, pos5, neg3);
    try testing.expectEqual(@as(i64, 2), try pool.evaluate(expr2, &bindings));

    // -5 mod -3 should be 1 (Euclidean)
    const expr3 = try pool.binary(.mod, neg5, neg3);
    try testing.expectEqual(@as(i64, 1), try pool.evaluate(expr3, &bindings));

    // 5 mod 3 should be 2 (same as @rem for positive operands)
    const expr4 = try pool.binary(.mod, pos5, pos3);
    try testing.expectEqual(@as(i64, 2), try pool.evaluate(expr4, &bindings));
}

test "non-commutative operation order" {
    var arena = std.heap.ArenaAllocator.init(testing.allocator);
    defer arena.deinit();

    var pool = try SymbolicPool.init(arena.child_allocator);
    defer pool.deinit();

    const x = try pool.variable("x");
    const y = try pool.variable("y");

    // Subtract is non-commutative: x - y != y - x
    const sub_xy = try pool.subtract(x, y);
    const sub_yx = try pool.subtract(y, x);

    // Should be different expressions
    try testing.expect(sub_xy != sub_yx);

    var bindings = Bindings{};
    defer bindings.deinit(arena.child_allocator);
    try bindings.put(arena.child_allocator, "x", 10);
    try bindings.put(arena.child_allocator, "y", 3);

    // x - y = 7, y - x = -7
    try testing.expectEqual(@as(i64, 7), try pool.evaluate(sub_xy, &bindings));
    try testing.expectEqual(@as(i64, -7), try pool.evaluate(sub_yx, &bindings));

    // Divide is also non-commutative
    const div_xy = try pool.divide(x, y);
    const div_yx = try pool.divide(y, x);

    try testing.expect(div_xy != div_yx);
    try testing.expectEqual(@as(i64, 3), try pool.evaluate(div_xy, &bindings));
    try testing.expectEqual(@as(i64, 0), try pool.evaluate(div_yx, &bindings));
}

test "stats tracking when enabled" {
    // Only run if stats are enabled
    if (!enable_stats) return;

    var arena = std.heap.ArenaAllocator.init(testing.allocator);
    defer arena.deinit();

    var pool = try SymbolicPool.init(arena.child_allocator);
    defer pool.deinit();

    // Initial stats should be non-zero due to pre-created constants
    try testing.expect(pool.stats.expressions_created > 0);

    const initial_created = pool.stats.expressions_created;
    const initial_peak = pool.stats.peak_expression_count;

    // Create new expressions
    const x = try pool.variable("x");
    const y = try pool.variable("y");
    _ = try pool.add(x, y);

    // Stats should have increased
    try testing.expect(pool.stats.expressions_created > initial_created);
    try testing.expect(pool.stats.peak_expression_count >= initial_peak);

    // Test constant folding stats
    const initial_folded = pool.stats.constants_folded;
    const five = try pool.constant(5);
    const three = try pool.constant(3);
    _ = try pool.add(five, three); // Should be folded to 8

    try testing.expect(pool.stats.constants_folded > initial_folded);

    // Test interning stats
    const initial_interned = pool.stats.expressions_interned;
    const duplicate_x = try pool.variable("x"); // Should be interned
    try testing.expectEqual(x, duplicate_x);
    try testing.expect(pool.stats.expressions_interned > initial_interned);
}
