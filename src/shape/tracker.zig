const std = @import("std");
const Allocator = std.mem.Allocator;

// Import types
const shape_types = @import("types.zig");
const SymbolicDim = shape_types.SymbolicDim;
const MAX_RANK = shape_types.MAX_RANK;
const MAX_TENSOR_SIZE = shape_types.MAX_TENSOR_SIZE;

// Import symbolic operations
const symbolic = @import("symbolic.zig");
const symbolicAdd = symbolic.symbolicAdd;
const symbolicSub = symbolic.symbolicSub;
const symbolicMax = symbolic.symbolicMax;
const symbolicMin = symbolic.symbolicMin;

// Import from other components
const types = @import("types");
const ExprId = types.ExprId;
const SymbolicPool = @import("symbolic").SymbolicPool;

// ============================================================================
// ShapeTracker - Core Data Structure
// ============================================================================

/// ShapeTracker embedded in TensorHandle (not in Node)
/// Tracks tensor dimensions and view transformations without moving data
/// 
/// OWNERSHIP RULES:
/// - ShapeTracker ALWAYS owns all its arrays (dims, strides, indexes, fake, mask, padding)
/// - All arrays are allocated with the same allocator
/// - For arena-allocated ShapeTrackers (in TensorHandle), no explicit cleanup needed
/// - For non-arena ShapeTrackers, call deinit() to free all arrays
pub const ShapeTracker = struct {
    dims: []const SymbolicDim, // Physical dimensions (owned by this tracker)
    strides: []const SymbolicDim, // Memory strides (owned by this tracker)
    offset: SymbolicDim, // Starting offset in buffer
    indexes: []const u8, // Permutation of dimensions (owned by this tracker)
    fake: []const bool, // Broadcast flags (owned by this tracker)
    mask: ?[]const [2]SymbolicDim, // Optional slicing bounds (owned by this tracker)
    padding: ?[]const [2]SymbolicDim, // Optional padding (owned by this tracker)

    // ===== Core Methods =====

    /// Cleanup for ShapeTracker allocations
    /// Only call this for ShapeTrackers NOT managed by arena allocators
    /// This frees ALL arrays owned by the tracker
    pub fn deinit(self: *const ShapeTracker, allocator: Allocator) void {
        // Free all owned arrays
        allocator.free(self.dims);
        allocator.free(self.strides);
        allocator.free(self.indexes);
        allocator.free(self.fake);
        
        // Free optional mask and padding if present
        if (self.mask) |mask| {
            allocator.free(mask);
        }
        if (self.padding) |padding| {
            allocator.free(padding);
        }
    }

    /// Create a deep copy of the ShapeTracker with independent allocations
    /// This is useful when you need a ShapeTracker that outlives the original data
    pub fn clone(self: *const ShapeTracker, allocator: Allocator) !ShapeTracker {
        // Deep copy all slices
        const dims_copy = try allocator.dupe(SymbolicDim, self.dims);
        errdefer allocator.free(dims_copy);
        
        const strides_copy = try allocator.dupe(SymbolicDim, self.strides);
        errdefer allocator.free(strides_copy);
        
        const indexes_copy = try allocator.dupe(u8, self.indexes);
        errdefer allocator.free(indexes_copy);
        
        const fake_copy = try allocator.dupe(bool, self.fake);
        errdefer allocator.free(fake_copy);
        
        // Copy optional mask and padding
        const mask_copy = if (self.mask) |mask| blk: {
            const copy = try allocator.dupe([2]SymbolicDim, mask);
            break :blk copy;
        } else null;
        errdefer if (mask_copy) |mask| allocator.free(mask);
        
        const padding_copy = if (self.padding) |padding| blk: {
            const copy = try allocator.dupe([2]SymbolicDim, padding);
            break :blk copy;
        } else null;
        errdefer if (padding_copy) |padding| allocator.free(padding);
        
        return ShapeTracker{
            .dims = dims_copy,
            .strides = strides_copy,
            .offset = self.offset,
            .indexes = indexes_copy,
            .fake = fake_copy,
            .mask = mask_copy,
            .padding = padding_copy,
        };
    }

    pub fn isContiguous(self: ShapeTracker) bool {
        // Following Luminal's is_reshaped() logic:
        // Any virtual transformation means non-contiguous

        // Check permutations (transpose)
        for (self.indexes, 0..) |idx, i| {
            if (idx != i) return false;
        }

        // Check broadcasting (expand)
        for (self.fake) |is_fake| {
            if (is_fake) return false;
        }

        // Check slicing and padding
        if (self.mask != null or self.padding != null) return false;

        // Check offset
        if (self.offset != .concrete or self.offset.concrete != 0) return false;

        // Check strides match C-contiguous layout
        var expected_stride: i64 = 1;
        var i = self.dims.len;
        while (i > 0) : (i -= 1) {
            const stride = self.strides[i - 1];
            const dim = self.dims[i - 1];

            // Only handle concrete case - symbolic dims assumed non-contiguous
            if (stride != .concrete or dim != .concrete) return false;
            if (stride.concrete != expected_stride) return false;

            expected_stride *= dim.concrete;
        }

        return true;
    }

    pub fn isReshaped(self: ShapeTracker) bool {
        // Luminal's terminology: "reshaped" means has virtual transformations
        return !self.isContiguous();
    }

    pub fn numElements(self: ShapeTracker, pool: *SymbolicPool) !ExprId {
        var total = try pool.constant(1);
        for (self.dims) |dim| {
            const dim_expr = switch (dim) {
                .concrete => |val| try pool.constant(val),
                .dynamic => |expr| expr,
            };
            total = try pool.multiply(total, dim_expr);
        }
        return total;
    }

    // Convenience method for concrete-only shapes
    pub fn numElementsStatic(self: ShapeTracker) ?i64 {
        var total: i64 = 1;
        for (self.dims) |dim| {
            switch (dim) {
                .concrete => |val| {
                    // Check for overflow
                    const result = @mulWithOverflow(total, val);
                    if (result[1] != 0) return null; // Overflow occurred
                    total = result[0];
                },
                .dynamic => return null,
            }
        }
        return total;
    }

    pub fn rank(self: ShapeTracker) usize {
        return self.dims.len;
    }

    /// Get logical dimensions after applying all transformations
    pub fn logicalDims(self: ShapeTracker, allocator: Allocator, pool: *SymbolicPool) ![]SymbolicDim {
        // Performance optimization: use stack allocation for small ranks
        var stack_buffer: [MAX_RANK]SymbolicDim = undefined;
        const result = if (self.dims.len <= MAX_RANK) 
            stack_buffer[0..self.dims.len]
        else
            try allocator.alloc(SymbolicDim, self.dims.len);

        // Apply transformations in order
        for (self.indexes, 0..) |physical_idx, logical_idx| {
            var dim = self.dims[physical_idx];

            // Apply mask if present
            if (self.mask) |mask| {
                const bounds = mask[physical_idx];
                dim = try symbolicSub(pool, bounds[1], bounds[0]);
            }

            // Apply padding if present
            if (self.padding) |padding| {
                const pads = padding[physical_idx];
                const pad_sum = try symbolicAdd(pool, pads[0], pads[1]);
                dim = try symbolicAdd(pool, dim, pad_sum);
            }

            result[logical_idx] = dim;
        }

        return result;
    }

    /// Create a ShapeTracker from dimensions with C-contiguous layout
    pub fn fromDims(dims: []const SymbolicDim, allocator: Allocator, pool: *SymbolicPool) !ShapeTracker {
        // Validate rank
        if (dims.len > MAX_RANK) {
            std.log.err("fromDims: rank {} exceeds MAX_RANK {}", .{ dims.len, MAX_RANK });
            return error.RankTooHigh;
        }

        // Validate dimensions
        for (dims, 0..) |dim, i| {
            if (dim == .concrete) {
                if (dim.concrete < 0) {
                    std.log.err("fromDims: negative dimension {} at index {}", .{ dim.concrete, i });
                    return error.NegativeDimension;
                }
                if (dim.concrete == 0) {
                    std.log.err("fromDims: zero dimension at index {}", .{i});
                    return error.ZeroDimension;
                }
                if (dim.concrete > MAX_TENSOR_SIZE) {
                    std.log.err("fromDims: dimension {} at index {} exceeds MAX_TENSOR_SIZE", .{ dim.concrete, i });
                    return error.DimensionTooLarge;
                }
            }
            // For dynamic dimensions, we can't validate at creation time
            // but we can at least ensure the expression is valid
        }

        // Create strides for C-contiguous layout
        const strides = try allocator.alloc(SymbolicDim, dims.len);
        errdefer allocator.free(strides);
        try computeStrides(dims, strides, pool);

        // Create identity permutation
        const indexes = try allocator.alloc(u8, dims.len);
        errdefer allocator.free(indexes);
        for (indexes, 0..) |*idx, i| {
            idx.* = @intCast(i);
        }

        // Create fake array (all false - no broadcasting)
        const fake = try allocator.alloc(bool, dims.len);
        errdefer allocator.free(fake);
        for (fake) |*f| {
            f.* = false;
        }

        // Make a copy of dims so we own it
        const owned_dims = try allocator.dupe(SymbolicDim, dims);
        errdefer allocator.free(owned_dims);
        
        return ShapeTracker{
            .dims = owned_dims,
            .strides = strides,
            .offset = SymbolicDim{ .concrete = 0 },
            .indexes = indexes,
            .fake = fake,
            .mask = null,
            .padding = null,
        };
    }

    // ===== View Operations =====

    /// Create a transposed view by permuting dimensions
    /// IMPORTANT: This modifies the ShapeTracker in-place and frees old arrays.
    /// Only use with non-arena allocated ShapeTrackers or ensure the allocator matches.
    pub fn transpose(self: *ShapeTracker, axes: []const usize, allocator: Allocator) !void {
        if (axes.len != self.dims.len) {
            std.log.err("transpose: axes length {} != tensor rank {}", .{ axes.len, self.dims.len });
            return error.InvalidPermutation;
        }
        if (axes.len > MAX_RANK) {
            std.log.err("transpose: rank {} exceeds MAX_RANK {}", .{ axes.len, MAX_RANK });
            return error.RankTooHigh;
        }

        // Use stack allocation for validation when possible
        var used_buf: [MAX_RANK]bool = undefined;
        const used = if (axes.len <= MAX_RANK)
            used_buf[0..axes.len]
        else
            try allocator.alloc(bool, axes.len);
        defer if (axes.len > MAX_RANK) allocator.free(used);

        for (used) |*u| u.* = false;

        // Validate axes form a valid permutation
        for (axes) |axis| {
            if (axis >= self.dims.len) {
                std.log.err("transpose: axis {} out of bounds for rank {}", .{ axis, self.dims.len });
                return error.AxisOutOfBounds;
            }
            if (used[axis]) {
                std.log.err("transpose: duplicate axis {} in permutation {any}", .{ axis, axes });
                return error.DuplicateAxis;
            }
            used[axis] = true;
        }

        // Create new permuted arrays
        const new_dims = try allocator.alloc(SymbolicDim, self.dims.len);
        const new_strides = try allocator.alloc(SymbolicDim, self.dims.len);
        const new_indexes = try allocator.alloc(u8, self.dims.len);
        const new_fake = try allocator.alloc(bool, self.dims.len);

        for (axes, 0..) |old_axis, new_axis| {
            new_dims[new_axis] = self.dims[old_axis];
            new_strides[new_axis] = self.strides[old_axis];
            new_indexes[new_axis] = self.indexes[old_axis];
            new_fake[new_axis] = self.fake[old_axis];
        }

        // Update mask and padding if present
        var new_mask: ?[]const [2]SymbolicDim = null;
        var new_padding: ?[]const [2]SymbolicDim = null;

        if (self.mask) |mask| {
            const mask_copy = try allocator.alloc([2]SymbolicDim, self.dims.len);
            for (axes, 0..) |old_axis, new_axis| {
                mask_copy[new_axis] = mask[old_axis];
            }
            new_mask = mask_copy;
        }

        if (self.padding) |padding| {
            const padding_copy = try allocator.alloc([2]SymbolicDim, self.dims.len);
            for (axes, 0..) |old_axis, new_axis| {
                padding_copy[new_axis] = padding[old_axis];
            }
            new_padding = padding_copy;
        }

        // Free old arrays before updating
        // IMPORTANT: This assumes the same allocator was used for all arrays
        allocator.free(self.dims);
        allocator.free(self.strides);
        allocator.free(self.indexes);
        allocator.free(self.fake);
        if (self.mask) |old_mask| {
            allocator.free(old_mask);
        }
        if (self.padding) |old_padding| {
            allocator.free(old_padding);
        }
        
        // Update tracker with new arrays
        self.dims = new_dims;
        self.strides = new_strides;
        self.indexes = new_indexes;
        self.fake = new_fake;
        self.mask = new_mask;
        self.padding = new_padding;
    }

    /// Create a sliced view with start and end bounds
    /// IMPORTANT: This modifies the ShapeTracker in-place and may free old mask.
    /// Only use with non-arena allocated ShapeTrackers or ensure the allocator matches.
    pub fn slice(self: *ShapeTracker, starts: []const SymbolicDim, ends: []const SymbolicDim, allocator: Allocator, pool: *SymbolicPool) !void {
        if (starts.len != self.dims.len or ends.len != self.dims.len) {
            std.log.err("slice: dimension count mismatch - starts: {}, ends: {}, dims: {}", 
                       .{ starts.len, ends.len, self.dims.len });
            return error.InvalidSlice;
        }

        // Validate slice bounds
        for (starts, ends, self.dims) |start, end, dim| {
            // Check concrete bounds
            if (start == .concrete and end == .concrete and dim == .concrete) {
                if (start.concrete < 0 or start.concrete > dim.concrete) {
                    std.log.err("slice: invalid start bound {} for dimension of size {}", 
                               .{ start.concrete, dim.concrete });
                    return error.InvalidSlice;
                }
                if (end.concrete < start.concrete or end.concrete > dim.concrete) {
                    std.log.err("slice: invalid end bound {} for dimension of size {} (start: {})", 
                               .{ end.concrete, dim.concrete, start.concrete });
                    return error.InvalidSlice;
                }
            }
        }

        // Create or update mask
        const new_mask = if (self.mask) |existing_mask| blk: {
            // Combine existing mask with new slice bounds
            const combined = try allocator.alloc([2]SymbolicDim, self.dims.len);
            for (existing_mask, starts, ends, 0..) |existing, start, end, i| {
                // New bounds are intersection of existing and new
                combined[i] = [2]SymbolicDim{
                    try symbolicMax(pool, existing[0], start),
                    try symbolicMin(pool, existing[1], end),
                };
            }
            break :blk combined;
        } else blk: {
            // Create new mask from slice bounds
            const new = try allocator.alloc([2]SymbolicDim, self.dims.len);
            for (starts, ends, 0..) |start, end, i| {
                new[i] = [2]SymbolicDim{ start, end };
            }
            break :blk new;
        };

        // Free old mask if present and not arena-allocated
        if (self.mask) |old_mask| {
            allocator.free(old_mask);
        }
        self.mask = new_mask;
    }

    /// Create an expanded view by adding a new broadcasted dimension
    /// IMPORTANT: This modifies the ShapeTracker in-place and frees old arrays.
    /// Only use with non-arena allocated ShapeTrackers or ensure the allocator matches.
    pub fn expand(self: *ShapeTracker, axis: usize, size: SymbolicDim, allocator: Allocator) !void {
        if (axis > self.dims.len) {
            std.log.err("expand: invalid axis {} for tensor with {} dimensions", 
                       .{ axis, self.dims.len });
            return error.InvalidAxis;
        }

        const new_rank = self.dims.len + 1;
        if (new_rank > MAX_RANK) {
            std.log.err("expand: new rank {} exceeds MAX_RANK {}", .{ new_rank, MAX_RANK });
            return error.RankTooHigh;
        }

        // Validate size
        if (size == .concrete) {
            if (size.concrete <= 0) {
                std.log.err("expand: invalid dimension size {} (must be positive)", .{size.concrete});
                return error.InvalidDimension;
            }
        }

        // Create new arrays with expanded size
        const new_dims = try allocator.alloc(SymbolicDim, new_rank);
        const new_strides = try allocator.alloc(SymbolicDim, new_rank);
        const new_indexes = try allocator.alloc(u8, new_rank);
        const new_fake = try allocator.alloc(bool, new_rank);

        // Copy existing dimensions, inserting new one at specified axis
        var src_idx: usize = 0;
        for (0..new_rank) |i| {
            if (i == axis) {
                // Insert new broadcasted dimension
                new_dims[i] = size;
                new_strides[i] = SymbolicDim{ .concrete = 0 }; // Broadcast stride
                new_indexes[i] = @intCast(i);
                new_fake[i] = true; // Mark as broadcasted
            } else {
                // Copy existing dimension
                new_dims[i] = self.dims[src_idx];
                new_strides[i] = self.strides[src_idx];
                new_indexes[i] = if (src_idx < self.indexes.len) self.indexes[src_idx] else @intCast(src_idx);
                new_fake[i] = if (src_idx < self.fake.len) self.fake[src_idx] else false;
                src_idx += 1;
            }
        }

        // Update mask and padding if present
        var new_mask: ?[]const [2]SymbolicDim = null;
        var new_padding: ?[]const [2]SymbolicDim = null;

        if (self.mask) |mask| {
            const mask_copy = try allocator.alloc([2]SymbolicDim, new_rank);
            src_idx = 0;
            for (0..new_rank) |i| {
                if (i == axis) {
                    // New dimension has no mask
                    mask_copy[i] = [2]SymbolicDim{
                        SymbolicDim{ .concrete = 0 },
                        size,
                    };
                } else {
                    mask_copy[i] = mask[src_idx];
                    src_idx += 1;
                }
            }
            new_mask = mask_copy;
        }

        if (self.padding) |padding| {
            const padding_copy = try allocator.alloc([2]SymbolicDim, new_rank);
            src_idx = 0;
            for (0..new_rank) |i| {
                if (i == axis) {
                    // New dimension has no padding
                    padding_copy[i] = [2]SymbolicDim{
                        SymbolicDim{ .concrete = 0 },
                        SymbolicDim{ .concrete = 0 },
                    };
                } else {
                    padding_copy[i] = padding[src_idx];
                    src_idx += 1;
                }
            }
            new_padding = padding_copy;
        }

        // Free old arrays before updating
        // IMPORTANT: This assumes the same allocator was used for all arrays
        allocator.free(self.dims);
        allocator.free(self.strides);
        allocator.free(self.indexes);
        allocator.free(self.fake);
        if (self.mask) |old_mask| {
            allocator.free(old_mask);
        }
        if (self.padding) |old_padding| {
            allocator.free(old_padding);
        }
        
        // Update tracker with new arrays
        self.dims = new_dims;
        self.strides = new_strides;
        self.indexes = new_indexes;
        self.fake = new_fake;
        self.mask = new_mask;
        self.padding = new_padding;
    }

    /// Create a padded view
    /// IMPORTANT: This modifies the ShapeTracker in-place and may free old padding.
    /// Only use with non-arena allocated ShapeTrackers or ensure the allocator matches.
    pub fn pad(self: *ShapeTracker, pad_widths: []const [2]SymbolicDim, allocator: Allocator, pool: *SymbolicPool) !void {
        if (pad_widths.len != self.dims.len) {
            std.log.err("pad: padding dimensions {} don't match tensor dimensions {}", 
                       .{ pad_widths.len, self.dims.len });
            return error.InvalidPadding;
        }

        // Validate padding amounts
        for (pad_widths, 0..) |pad_width, i| {
            if (pad_width[0] == .concrete and pad_width[0].concrete < 0) {
                std.log.err("pad: negative padding {} at dimension {} (left side)", 
                           .{ pad_width[0].concrete, i });
                return error.InvalidPadding;
            }
            if (pad_width[1] == .concrete and pad_width[1].concrete < 0) {
                std.log.err("pad: negative padding {} at dimension {} (right side)", 
                           .{ pad_width[1].concrete, i });
                return error.InvalidPadding;
            }
        }

        // Create or update padding
        const new_padding = if (self.padding) |existing_padding| blk: {
            // Combine existing padding with new padding (cumulative)
            const combined = try allocator.alloc([2]SymbolicDim, self.dims.len);
            for (existing_padding, pad_widths, 0..) |existing, new, i| {
                combined[i] = [2]SymbolicDim{
                    try symbolicAdd(pool, existing[0], new[0]),
                    try symbolicAdd(pool, existing[1], new[1]),
                };
            }
            break :blk combined;
        } else blk: {
            // Must copy pad_widths since we need to own it
            const copy = try allocator.dupe([2]SymbolicDim, pad_widths);
            break :blk copy;
        };

        // Free old padding if present and not arena-allocated
        if (self.padding) |old_padding| {
            allocator.free(old_padding);
        }
        self.padding = new_padding;
    }
};

// ============================================================================
// Internal Helpers
// ============================================================================

fn computeStrides(dims: []const SymbolicDim, strides: []SymbolicDim, pool: *SymbolicPool) !void {
    // Row-major (C-style) layout: rightmost dimension has stride 1
    var stride_expr = try pool.constant(1);
    var concrete_stride: i64 = 1; // Track concrete stride for overflow checking
    
    var i = dims.len;
    while (i > 0) : (i -= 1) {
        strides[i - 1] = SymbolicDim{ .dynamic = stride_expr };

        // Multiply stride by current dimension for next iteration
        const dim_expr = switch (dims[i - 1]) {
            .concrete => |val| blk: {
                // Critical: Check for overflow before multiplying
                if (val > 0) {
                    const overflow_check = @mulWithOverflow(concrete_stride, val);
                    if (overflow_check[1] != 0 or overflow_check[0] > MAX_TENSOR_SIZE) {
                        std.log.err("Stride computation overflow: {} * {} would exceed MAX_TENSOR_SIZE", .{ concrete_stride, val });
                        return error.StrideOverflow;
                    }
                    concrete_stride = overflow_check[0];
                }
                break :blk try pool.constant(val);
            },
            .dynamic => |expr| blk: {
                // For symbolic dims, we can't check overflow at compile time
                // Mark that we now have symbolic strides
                concrete_stride = -1; // Sentinel value indicating symbolic
                break :blk expr;
            },
        };
        stride_expr = try pool.multiply(stride_expr, dim_expr);
    }

    // Optimize: convert concrete expressions back to concrete SymbolicDim
    for (strides) |*stride| {
        if (stride.* == .dynamic) {
            if (pool.isConstant(stride.dynamic)) {
                const val = pool.getConstantValue(stride.dynamic);
                // Additional safety check on final stride values
                if (val > MAX_TENSOR_SIZE) {
                    std.log.err("Computed stride {} exceeds MAX_TENSOR_SIZE", .{val});
                    return error.StrideOverflow;
                }
                stride.* = SymbolicDim{ .concrete = val };
            }
        }
    }
}