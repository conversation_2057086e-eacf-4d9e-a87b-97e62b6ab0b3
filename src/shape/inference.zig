const std = @import("std");
const Allocator = std.mem.Allocator;

// Import shape types
const shape_types = @import("types.zig");
const SymbolicDim = shape_types.SymbolicDim;
const MAX_RANK = shape_types.MAX_RANK;

// Import tracker
const ShapeTracker = @import("tracker.zig").ShapeTracker;

// Import symbolic operations
const symbolic = @import("symbolic.zig");
const symbolicAdd = symbolic.symbolicAdd;

// Import from other components
const SymbolicPool = @import("symbolic").SymbolicPool;

// Import broadcasting types
const types = @import("types");
const BroadcastPlan = types.BroadcastPlan;
const BroadcastPattern = types.BroadcastPattern;
const BroadcastStrategy = types.BroadcastStrategy;
const InputTransform = types.InputTransform;

// ============================================================================
// Shape Inference Functions
// ============================================================================

/// Infer broadcast shape for element-wise operations
pub fn inferBroadcastShape(a: *const ShapeTracker, b: *const ShapeTracker, allocator: Allocator, pool: *SymbolicPool) !ShapeTracker {
    const a_dims = a.dims;
    const b_dims = b.dims;
    const result_rank = @max(a_dims.len, b_dims.len);

    if (result_rank > MAX_RANK) {
        std.log.err("inferBroadcastShape: result rank {} exceeds MAX_RANK {}", .{ result_rank, MAX_RANK });
        return error.RankTooHigh;
    }

    var result_dims = try allocator.alloc(SymbolicDim, result_rank);
    defer allocator.free(result_dims);

    // Align dimensions from the right and compute broadcast shape
    var i: usize = result_rank;
    while (i > 0) : (i -= 1) {
        const idx = i - 1;
        const a_idx = if (a_dims.len + idx >= result_rank) a_dims[a_dims.len + idx - result_rank] else SymbolicDim{ .concrete = 1 };
        const b_idx = if (b_dims.len + idx >= result_rank) b_dims[b_dims.len + idx - result_rank] else SymbolicDim{ .concrete = 1 };

        // Broadcasting rule: dims must be equal or one must be 1
        result_dims[idx] = try resolveBroadcastDim(a_idx, b_idx, pool);
    }

    return ShapeTracker.fromDims(result_dims, allocator, pool);
}

/// Infer shape after reduction operation with keepdims option
pub fn inferReduceShape(input: *const ShapeTracker, axis: usize, keepdims: bool, allocator: Allocator, pool: *SymbolicPool) !ShapeTracker {
    if (axis >= input.dims.len) {
        std.log.err("inferReduceShapeEx: invalid axis {} for tensor with {} dimensions", 
                   .{ axis, input.dims.len });
        return error.InvalidReduceDimension;
    }

    if (keepdims) {
        // Keep dimension with size 1
        var result_dims = try allocator.alloc(SymbolicDim, input.dims.len);
        defer allocator.free(result_dims);

        // Copy all dimensions, setting reduced dimension to 1
        for (input.dims, 0..) |input_dim, i| {
            if (i == axis) {
                result_dims[i] = SymbolicDim{ .concrete = 1 };
            } else {
                result_dims[i] = input_dim;
            }
        }

        return ShapeTracker.fromDims(result_dims, allocator, pool);
    } else {
        // Remove the reduced dimension entirely
        const new_rank = input.dims.len - 1;
        if (new_rank == 0) {
            // Reducing to scalar - return shape []
            return ShapeTracker.fromDims(&.{}, allocator, pool);
        }
        
        var result_dims = try allocator.alloc(SymbolicDim, new_rank);
        defer allocator.free(result_dims);
        
        // Copy all dimensions except the reduced one
        var j: usize = 0;
        for (input.dims, 0..) |input_dim, i| {
            if (i != axis) {
                result_dims[j] = input_dim;
                j += 1;
            }
        }
        
        return ShapeTracker.fromDims(result_dims, allocator, pool);
    }
}

/// Infer shape for matrix multiplication
pub fn inferMatmulShape(a: *const ShapeTracker, b: *const ShapeTracker, allocator: Allocator, pool: *SymbolicPool) !ShapeTracker {
    // Validate inputs are at least 2D
    if (a.dims.len < 2 or b.dims.len < 2) {
        std.log.err("inferMatmulShape: invalid dimensions for matmul - a: {}D, b: {}D (both must be at least 2D)", 
                   .{ a.dims.len, b.dims.len });
        return error.InvalidMatmulDimensions;
    }

    // Check inner dimensions match
    const a_inner = a.dims[a.dims.len - 1];
    const b_inner = b.dims[b.dims.len - 2];

    // For concrete dimensions, check compatibility
    if (a_inner == .concrete and b_inner == .concrete) {
        if (a_inner.concrete != b_inner.concrete) {
            std.log.err("inferMatmulShape: incompatible inner dimensions - a: {}, b: {} (must match)", 
                       .{ a_inner.concrete, b_inner.concrete });
            return error.InvalidMatmulDimensions;
        }
    }

    // Result shape: [...batch_dims..., m, n]
    const batch_rank = @max(a.dims.len - 2, b.dims.len - 2);
    const result_rank = batch_rank + 2;

    if (result_rank > MAX_RANK) {
        std.log.err("inferMatmulShape: result rank {} exceeds MAX_RANK {}", .{ result_rank, MAX_RANK });
        return error.RankTooHigh;
    }

    var result_dims = try allocator.alloc(SymbolicDim, result_rank);
    defer allocator.free(result_dims);

    // Broadcast batch dimensions
    for (0..batch_rank) |i| {
        const a_batch_idx = if (i < a.dims.len - 2) i else std.math.maxInt(usize);
        const b_batch_idx = if (i < b.dims.len - 2) i else std.math.maxInt(usize);

        const a_dim = if (a_batch_idx < a.dims.len - 2) a.dims[a_batch_idx] else SymbolicDim{ .concrete = 1 };
        const b_dim = if (b_batch_idx < b.dims.len - 2) b.dims[b_batch_idx] else SymbolicDim{ .concrete = 1 };

        result_dims[i] = try resolveBroadcastDim(a_dim, b_dim, pool);
    }

    // Output dimensions
    result_dims[result_rank - 2] = a.dims[a.dims.len - 2]; // m
    result_dims[result_rank - 1] = b.dims[b.dims.len - 1]; // n

    return ShapeTracker.fromDims(result_dims, allocator, pool);
}

/// Infer shape for concatenation
pub fn inferConcatShape(tensors: []const ShapeTracker, axis: usize, allocator: Allocator, pool: *SymbolicPool) !ShapeTracker {
    if (tensors.len == 0) {
        std.log.err("inferConcatShape: cannot concatenate empty tensor list", .{});
        return error.InvalidOperation;
    }

    const first = &tensors[0];
    if (axis >= first.dims.len) {
        std.log.err("inferConcatShape: invalid axis {} for tensor with {} dimensions", 
                   .{ axis, first.dims.len });
        return error.InvalidAxis;
    }

    // Validate all tensors have same rank
    for (tensors[1..], 1..) |tensor, idx| {
        if (tensor.dims.len != first.dims.len) {
            std.log.err("inferConcatShape: rank mismatch - tensor[0]: {}D, tensor[{}]: {}D", 
                       .{ first.dims.len, idx, tensor.dims.len });
            return error.ShapeMismatch;
        }
    }

    // Validate non-concat dimensions match
    for (tensors[1..]) |tensor| {
        for (0..first.dims.len) |dim_idx| {
            if (dim_idx == axis) continue; // Skip concat dimension

            const first_dim = first.dims[dim_idx];
            const tensor_dim = tensor.dims[dim_idx];

            // Check concrete dimensions
            if (first_dim == .concrete and tensor_dim == .concrete) {
                if (first_dim.concrete != tensor_dim.concrete) {
                    std.log.err("inferConcatShape: dimension {} mismatch - first: {}, current: {}", 
                               .{ dim_idx, first_dim.concrete, tensor_dim.concrete });
                    return error.ShapeMismatch;
                }
            }
        }
    }

    // Build result shape
    var result_dims = try allocator.alloc(SymbolicDim, first.dims.len);
    defer allocator.free(result_dims);

    // Copy non-concat dimensions
    for (0..first.dims.len) |i| {
        if (i != axis) {
            result_dims[i] = first.dims[i];
        }
    }

    // Sum concat dimension
    var concat_size = try pool.constant(0);
    for (tensors) |tensor| {
        const dim = tensor.dims[axis];
        const dim_expr = switch (dim) {
            .concrete => |val| try pool.constant(val),
            .dynamic => |expr| expr,
        };
        concat_size = try pool.add(concat_size, dim_expr);
    }

    // Set concat dimension
    if (pool.isConstant(concat_size)) {
        result_dims[axis] = SymbolicDim{ .concrete = pool.getConstantValue(concat_size) };
    } else {
        result_dims[axis] = SymbolicDim{ .dynamic = concat_size };
    }

    return ShapeTracker.fromDims(result_dims, allocator, pool);
}

// ============================================================================
// Internal Helpers
// ============================================================================

fn resolveBroadcastDim(a: SymbolicDim, b: SymbolicDim, pool: *SymbolicPool) !SymbolicDim {
    // Broadcasting rules (NumPy-compatible):
    // 1. If dimensions are equal, use that dimension
    // 2. If one dimension is 1, broadcast to the other dimension
    // 3. Otherwise, dimensions are incompatible

    // If both concrete, apply broadcasting rules
    if (a == .concrete and b == .concrete) {
        const a_val = a.concrete;
        const b_val = b.concrete;
        if (a_val == 1) return b; // Broadcast a to b's size
        if (b_val == 1) return a; // Broadcast b to a's size
        if (a_val == b_val) return a; // Same size, no broadcast

        // Incompatible dimensions
        std.log.err("broadcast: incompatible concrete dimensions {} and {}", .{ a_val, b_val });
        return error.IncompatibleBroadcast;
    }

    // If one is concrete 1, always broadcast to the other
    if (a == .concrete and a.concrete == 1) return b;
    if (b == .concrete and b.concrete == 1) return a;

    // For symbolic dims, we need runtime checks
    // For now, optimistically assume they're compatible and create max expression
    const a_expr = switch (a) {
        .concrete => |val| try pool.constant(val),
        .dynamic => |expr| expr,
    };
    const b_expr = switch (b) {
        .concrete => |val| try pool.constant(val),
        .dynamic => |expr| expr,
    };

    // max(a, b) works for the common case where one might be 1
    return SymbolicDim{ .dynamic = try pool.binary(.max, a_expr, b_expr) };
}

// ============================================================================
// Enhanced Broadcasting with Planning
// ============================================================================

/// Result of enhanced shape inference with broadcast planning
pub const InferenceResult = struct {
    shape: ShapeTracker,
    broadcast_plan: ?BroadcastPlan = null,
    
    pub fn deinit(self: *InferenceResult) void {
        if (self.broadcast_plan) |*plan| {
            plan.deinit();
        }
    }
};

/// Enhanced broadcast shape inference that creates execution plan
pub fn inferBinaryOpWithBroadcast(
    a: *const ShapeTracker, 
    b: *const ShapeTracker,
    allocator: Allocator, 
    pool: *SymbolicPool
) !InferenceResult {
    // First, compute the broadcast-compatible result shape
    const result_shape = try inferBroadcastShape(a, b, allocator, pool);
    
    // Check if broadcasting is actually needed
    const needs_broadcast = !shapesEqual(a, b);
    
    if (!needs_broadcast) {
        return InferenceResult{
            .shape = result_shape,
            .broadcast_plan = null, // No broadcasting needed
        };
    }
    
    // Create broadcast plan
    const broadcast_plan = try createBroadcastPlan(a, b, &result_shape, allocator);
    
    return InferenceResult{
        .shape = result_shape,
        .broadcast_plan = broadcast_plan,
    };
}

/// Create a detailed plan for broadcasting two shapes
fn createBroadcastPlan(
    a: *const ShapeTracker,
    b: *const ShapeTracker, 
    result: *const ShapeTracker,
    allocator: Allocator
) !BroadcastPlan {
    // Convert result shape to concrete for planning (symbolic shapes handled at runtime)
    const result_concrete = try shapeTrackerToConcrete(result, allocator);
    defer allocator.free(result_concrete);
    
    // Analyze each input
    var input_transforms = try allocator.alloc(InputTransform, 2);
    
    // Transform for input A
    const a_concrete = try shapeTrackerToConcrete(a, allocator);
    defer allocator.free(a_concrete);
    
    input_transforms[0] = InputTransform{
        .needs_broadcast = !std.mem.eql(i64, a_concrete, result_concrete),
        .original_shape = try allocator.dupe(i64, a_concrete),
        .target_shape = try allocator.dupe(i64, result_concrete),
        .pattern = classifyBroadcastPattern(a_concrete, result_concrete),
        .strategy = selectBroadcastStrategy(a_concrete, result_concrete),
    };
    
    // Transform for input B
    const b_concrete = try shapeTrackerToConcrete(b, allocator);
    defer allocator.free(b_concrete);
    
    input_transforms[1] = InputTransform{
        .needs_broadcast = !std.mem.eql(i64, b_concrete, result_concrete),
        .original_shape = try allocator.dupe(i64, b_concrete),
        .target_shape = try allocator.dupe(i64, result_concrete),
        .pattern = classifyBroadcastPattern(b_concrete, result_concrete),
        .strategy = selectBroadcastStrategy(b_concrete, result_concrete),
    };
    
    // Classify overall pattern
    const overall_pattern = classifyOverallPattern(input_transforms);
    
    return BroadcastPlan{
        .result_shape = try allocator.dupe(i64, result_concrete),
        .input_transforms = input_transforms,
        .overall_pattern = overall_pattern,
        .allocator = allocator,
    };
}

/// Check if two ShapeTrackers have identical shapes
fn shapesEqual(a: *const ShapeTracker, b: *const ShapeTracker) bool {
    if (a.dims.len != b.dims.len) return false;
    
    for (a.dims, b.dims) |a_dim, b_dim| {
        // For concrete dimensions, check exact equality
        if (a_dim == .concrete and b_dim == .concrete) {
            if (a_dim.concrete != b_dim.concrete) return false;
        }
        // For dynamic dimensions, assume different (conservative)
        else if (a_dim == .dynamic or b_dim == .dynamic) {
            return false;
        }
    }
    
    return true;
}

/// Convert ShapeTracker to concrete shape (resolve symbolic dimensions to 1 for planning)
fn shapeTrackerToConcrete(tracker: *const ShapeTracker, allocator: Allocator) ![]i64 {
    var concrete = try allocator.alloc(i64, tracker.dims.len);
    
    for (tracker.dims, 0..) |dim, i| {
        concrete[i] = switch (dim) {
            .concrete => |val| val,
            .dynamic => 1, // Conservative: treat dynamic dims as 1 for planning
        };
    }
    
    return concrete;
}

/// Classify broadcast pattern for optimization
fn classifyBroadcastPattern(original: []const i64, target: []const i64) BroadcastPattern {
    // No broadcasting if shapes are identical
    if (std.mem.eql(i64, original, target)) {
        return .none;
    }
    
    // Scalar broadcast: [1] -> [n, m, ...]
    if (original.len == 1 and original[0] == 1) {
        return .scalar_to_tensor;
    }
    
    // Vector to matrix: [m] -> [n, m]
    if (original.len == 1 and target.len == 2 and original[0] == target[1]) {
        return .vector_to_matrix;
    }
    
    // Matrix row broadcast: [1, m] -> [n, m]
    if (original.len == 2 and target.len == 2 and 
        original[0] == 1 and original[1] == target[1]) {
        return .matrix_row_broadcast;
    }
    
    // Matrix column broadcast: [n, 1] -> [n, m]
    if (original.len == 2 and target.len == 2 and 
        original[0] == target[0] and original[1] == 1) {
        return .matrix_col_broadcast;
    }
    
    return .general;
}

/// Select optimal strategy for a broadcast transformation
fn selectBroadcastStrategy(original: []const i64, target: []const i64) BroadcastStrategy {
    const pattern = classifyBroadcastPattern(original, target);
    
    return switch (pattern) {
        .none => .no_broadcast,
        .scalar_to_tensor => .scalar_kernel, // Use optimized scalar kernel
        .vector_to_matrix => .vector_kernel, // Use SIMD-friendly vector kernel
        .matrix_row_broadcast, .matrix_col_broadcast => .materialize,
        .general => .materialize,
    };
}

/// Classify the overall broadcast pattern from individual transforms
fn classifyOverallPattern(transforms: []const InputTransform) BroadcastPattern {
    // If no transforms need broadcasting, no overall pattern
    var needs_any_broadcast = false;
    for (transforms) |transform| {
        if (transform.needs_broadcast) {
            needs_any_broadcast = true;
            break;
        }
    }
    
    if (!needs_any_broadcast) return .none;
    
    // If any transform is scalar broadcast, overall is scalar
    for (transforms) |transform| {
        if (transform.pattern == .scalar_to_tensor) {
            return .scalar_to_tensor;
        }
    }
    
    // If any transform is vector broadcast, overall is vector
    for (transforms) |transform| {
        if (transform.pattern == .vector_to_matrix) {
            return .vector_to_matrix;
        }
    }
    
    // Otherwise, it's a general/complex broadcast
    return .general;
}