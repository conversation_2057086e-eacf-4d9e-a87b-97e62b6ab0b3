const std = @import("std");
const Allocator = std.mem.Allocator;

// Import shape types
const shape_types = @import("types.zig");
const SymbolicDim = shape_types.SymbolicDim;
const MAX_TENSOR_SIZE = shape_types.MAX_TENSOR_SIZE;

// Import tracker
const ShapeTracker = @import("tracker.zig").ShapeTracker;

// Import for shape collection
const TensorHandle = @import("tensor").TensorHandle;

// ============================================================================
// Utility Functions
// ============================================================================

/// Check if two shapes can be broadcast together
pub fn canBroadcastTo(from: *const ShapeTracker, to: *const ShapeTracker) bool {
    // Can always broadcast to higher rank
    if (from.dims.len > to.dims.len) return false;

    // Check dimension compatibility from right to left
    var i: usize = 0;
    while (i < from.dims.len) : (i += 1) {
        const from_idx = from.dims.len - 1 - i;
        const to_idx = to.dims.len - 1 - i;

        const from_dim = from.dims[from_idx];
        const to_dim = to.dims[to_idx];

        // Check concrete dimensions
        if (from_dim == .concrete and to_dim == .concrete) {
            const from_val = from_dim.concrete;
            const to_val = to_dim.concrete;

            // Broadcasting rule: dimensions must be equal or from must be 1
            if (from_val != to_val and from_val != 1) {
                return false;
            }
        }
        // For symbolic dimensions, assume they could be compatible
    }

    return true;
}

/// Check if shapes are broadcastable (simple i64 version for convenience)
pub fn areBroadcastable(shape1: []const i64, shape2: []const i64) bool {
    const min_rank = @min(shape1.len, shape2.len);

    // Check dimensions from right to left
    var i: usize = 0;
    while (i < min_rank) : (i += 1) {
        const dim1 = shape1[shape1.len - 1 - i];
        const dim2 = shape2[shape2.len - 1 - i];
        if (dim1 != dim2 and dim1 != 1 and dim2 != 1) {
            return false;
        }
    }

    return true;
}

/// Compute broadcast output shape (simple i64 version for convenience)
pub fn broadcastShape(shape1: []const i64, shape2: []const i64, allocator: Allocator) ![]i64 {
    if (!areBroadcastable(shape1, shape2)) {
        std.log.err("broadcastShape: incompatible shapes for broadcasting", .{});
        return error.IncompatibleShapes;
    }

    const out_rank = @max(shape1.len, shape2.len);
    const out_shape = try allocator.alloc(i64, out_rank);

    // Fill from right to left
    var i: usize = 0;
    while (i < out_rank) : (i += 1) {
        const dim1 = if (i < shape1.len) shape1[shape1.len - 1 - i] else 1;
        const dim2 = if (i < shape2.len) shape2[shape2.len - 1 - i] else 1;
        out_shape[out_rank - 1 - i] = @max(dim1, dim2);
    }

    return out_shape;
}

/// Create a static shape array (compile-time)
pub fn createStaticShape(comptime dims: []const i64) [dims.len]SymbolicDim {
    var result: [dims.len]SymbolicDim = undefined;
    inline for (dims, 0..) |dim, i| {
        comptime {
            if (dim <= 0) @compileError("Dimension must be positive");
            if (dim > MAX_TENSOR_SIZE) @compileError("Dimension too large");
        }
        result[i] = SymbolicDim{ .concrete = dim };
    }
    return result;
}

// ============================================================================
// Contiguous Memory Layout Utilities
// ============================================================================

/// Check if a shape tracker represents a contiguous tensor
pub fn isContiguous(tracker: *const ShapeTracker) bool {
    // Already implemented as a method on ShapeTracker
    return tracker.isContiguous();
}

/// Determine if a node operation requires contiguous memory layout
pub fn requiresContiguous(op: @import("types").ComputeOp) bool {
    return switch (op) {
        // Operations that typically require contiguous memory
        .sum_reduce, .max_reduce => true,
        // Most elementwise operations can work with non-contiguous
        .add, .mul, .recip, .sqrt, .sin, .exp2, .log2, .less_than, .mod => false,
        // Special case - depends on backend
        .contiguous => false, // Already makes things contiguous
        .custom => true, // Conservative: assume custom ops need contiguous
    };
}

/// Check if an operation can handle non-contiguous inputs
pub fn canHandleNonContiguous(op: @import("types").ComputeOp) bool {
    return switch (op) {
        .contiguous => true, // Already a contiguous operation
        // Most operations require contiguous inputs in V1
        else => false,
    };
}

// ============================================================================
// Shape-Based Pattern Detection
// ============================================================================

/// Detect if an operation is effectively a reshape
pub fn isReshapePattern(
    graph: *const @import("graph").Graph,
    node_id: @import("types").NodeId,
    _: ?*const anyopaque, // Unused, kept for compatibility
) bool {
    _ = graph;
    _ = node_id;
    // TODO: Implement reshape detection
    // Look for patterns like:
    // - sum_reduce that doesn't change element count
    // - transpose followed by contiguous
    return false;
}

/// Get the broadcasted shape of two tensors (for ShapeTracker)
pub fn getBroadcastShapeTracker(
    allocator: Allocator,
    shape_a: []const SymbolicDim,
    shape_b: []const SymbolicDim,
) ![]SymbolicDim {
    const max_len = @max(shape_a.len, shape_b.len);
    var result = try allocator.alloc(SymbolicDim, max_len);
    
    // Fill from right to left
    var i: usize = 0;
    while (i < max_len) : (i += 1) {
        const dim_a = if (i < shape_a.len) shape_a[shape_a.len - 1 - i] else SymbolicDim{ .concrete = 1 };
        const dim_b = if (i < shape_b.len) shape_b[shape_b.len - 1 - i] else SymbolicDim{ .concrete = 1 };
        
        // Handle symbolic dimensions
        result[max_len - 1 - i] = switch (dim_a) {
            .concrete => |val_a| switch (dim_b) {
                .concrete => |val_b| blk: {
                    if (val_a == val_b) {
                        break :blk SymbolicDim{ .concrete = val_a };
                    } else if (val_a == 1) {
                        break :blk SymbolicDim{ .concrete = val_b };
                    } else if (val_b == 1) {
                        break :blk SymbolicDim{ .concrete = val_a };
                    } else {
                        allocator.free(result);
                        std.log.err("getBroadcastShapeTracker: incompatible dimensions {} and {} at position {}", 
                                   .{ val_a, val_b, i });
                        return error.IncompatibleShapes;
                    }
                },
                .dynamic => |_| dim_b, // Dynamic dimension wins
            },
            .dynamic => |_| dim_a, // Dynamic dimension wins
        };
    }
    
    return result;
}

// ============================================================================
// Memory Layout Optimization
// ============================================================================

/// Estimate memory bandwidth requirement for an operation
pub fn estimateMemoryBandwidth(
    op: @import("types").ComputeOp,
    input_shapes: []const []const i64,
    output_shape: []const i64,
    dtype: @import("types").DataType,
) usize {
    var total_bytes: usize = 0;
    
    // Count input bytes
    for (input_shapes) |shape| {
        var elements: usize = 1;
        for (shape) |dim| {
            elements *= @intCast(dim);
        }
        total_bytes += elements * dtype.size();
    }
    
    // Count output bytes
    var output_elements: usize = 1;
    for (output_shape) |dim| {
        output_elements *= @intCast(dim);
    }
    total_bytes += output_elements * dtype.size();
    
    // Some operations read inputs multiple times
    return switch (op) {
        .sum_reduce, .max_reduce => total_bytes * 2, // May need multiple passes
        else => total_bytes,
    };
}

// ============================================================================
// Shape Collection from TensorHandles - DEPRECATED
// ============================================================================
// Shape information is now stored directly in node metadata.
// These functions are no longer needed.

// ============================================================================
// Shape Knowledge Classification for Dual-Mode System
// ============================================================================

/// Classification of shape knowledge for optimization decisions
pub const ShapeKnowledge = enum {
    /// All dimensions known at compile time - enables comptime optimization
    static,
    /// Some/all dimensions unknown at runtime - requires runtime flexibility  
    dynamic,
    /// Mixed known/unknown dimensions - enables selective optimization
    hybrid,
};

/// Maximum dimension size that's practical for compile-time optimization
pub const MAX_COMPTIME_DIM: i64 = 2048;

/// Maximum total elements for comptime optimization (to avoid compile-time memory issues)
pub const MAX_COMPTIME_ELEMENTS: i64 = 1024 * 1024; // 1M elements

/// Classify a ShapeTracker's optimization potential
pub fn classifyShapeTracker(tracker: *const ShapeTracker) ShapeKnowledge {
    var total_elements: i64 = 1;
    var has_dynamic = false;
    
    for (tracker.dims) |dim| {
        switch (dim) {
            .concrete => |val| {
                if (val > MAX_COMPTIME_DIM) return .dynamic;
                total_elements *= val;
                if (total_elements > MAX_COMPTIME_ELEMENTS) return .dynamic;
            },
            .dynamic => {
                has_dynamic = true;
            },
        }
    }
    
    return if (has_dynamic) .hybrid else .static;
}

/// Classify a concrete shape array's optimization potential
pub fn classifyShapeArray(shape: []const i64) ShapeKnowledge {
    var total_elements: i64 = 1;
    
    for (shape) |dim| {
        if (dim > MAX_COMPTIME_DIM) return .dynamic;
        total_elements *= dim;
        if (total_elements > MAX_COMPTIME_ELEMENTS) return .dynamic;
    }
    
    return .static;
}

/// Maximum rank (number of dimensions) suitable for comptime optimization
pub const MAX_COMPTIME_RANK: usize = 8;

/// Check if a shape is suitable for comptime optimization based on size characteristics
pub fn isSuitableForComptimeOptimization(shape: []const i64) bool {
    // Pure shape-based heuristics, no model knowledge
    
    // Too many dimensions make comptime compilation slow
    if (shape.len > MAX_COMPTIME_RANK) return false;
    
    // Already checked in classifyShapeArray, but double-check
    var total_elements: i64 = 1;
    for (shape) |dim| {
        if (dim <= 0) return false;  // Invalid dimension
        if (dim > MAX_COMPTIME_DIM) return false;  // Too large for comptime
        total_elements *= dim;
        if (total_elements > MAX_COMPTIME_ELEMENTS) return false;  // Too many elements
    }
    
    // Small tensors benefit less from comptime optimization due to overhead
    if (total_elements < 8) return false;
    
    return true;
}

/// Feature flag for comptime optimization (enabled for testing)
pub const ENABLE_COMPTIME_OPTIMIZATION = true;

/// Decide whether to use comptime optimization for a given shape
/// Based purely on shape characteristics, not model knowledge
pub fn shouldUseComptimeOptimization(
    shape: []const i64,
    comptime force_enable: bool
) bool {
    if (!ENABLE_COMPTIME_OPTIMIZATION and !force_enable) return false;
    
    const knowledge = classifyShapeArray(shape);
    return (knowledge == .static) and isSuitableForComptimeOptimization(shape);
}

// ============================================================================
// Coordinate and Index Utilities - Canonical Implementation
// ============================================================================

/// Convert flat index to multi-dimensional coordinates
/// This is the canonical implementation used throughout the codebase
/// for consistent coordinate calculation
pub fn flatToCoords(flat_idx: usize, dims: []const i64, coords: []usize) void {
    std.debug.assert(coords.len >= dims.len);
    
    var remaining = flat_idx;
    var i: usize = dims.len;
    while (i > 0) {
        i -= 1;
        const dim_size = @as(usize, @intCast(dims[i]));
        coords[i] = remaining % dim_size;
        remaining /= dim_size;
    }
}

/// Convert multi-dimensional coordinates to flat index using strides
/// This is the canonical implementation for coordinate-to-index conversion
pub fn coordsToFlat(coords: []const usize, strides: []const i64) usize {
    std.debug.assert(coords.len >= strides.len);
    
    var flat_idx: usize = 0;
    for (0..strides.len) |i| {
        flat_idx += coords[i] * @as(usize, @intCast(strides[i]));
    }
    return flat_idx;
}

/// Map coordinates for broadcasting (handles dimension size = 1)
/// Used when an input tensor has broadcasted dimensions
pub fn mapCoordsForBroadcast(
    output_coords: []const usize,
    input_dims: []const i64,
    output_dims: []const i64,
    mapped_coords: []usize
) void {
    std.debug.assert(mapped_coords.len >= input_dims.len);
    std.debug.assert(output_coords.len >= output_dims.len);
    
    // Handle rank difference by aligning from the right
    const rank_diff = output_dims.len - input_dims.len;
    
    for (0..input_dims.len) |i| {
        const output_coord_idx = i + rank_diff;
        // If input dimension is 1, it's broadcasted, so use coordinate 0
        mapped_coords[i] = if (input_dims[i] == 1) 0 else output_coords[output_coord_idx];
    }
}

/// Complete flat-to-physical index conversion with broadcasting support
/// This combines all coordinate utilities for efficient IndexExpr computation
pub fn flatToPhysicalWithBroadcast(
    logical_idx: usize,
    output_dims: []const i64,
    input_dims: []const i64,
    input_strides: []const i64,
    offset: i64
) usize {
    // Convert logical flat index to output coordinates
    var output_coords: [8]usize = undefined;
    flatToCoords(logical_idx, output_dims, output_coords[0..output_dims.len]);
    
    // Map output coordinates to input coordinates (handle broadcasting)
    var input_coords: [8]usize = undefined;
    mapCoordsForBroadcast(
        output_coords[0..output_dims.len],
        input_dims,
        output_dims,
        input_coords[0..input_dims.len]
    );
    
    // Convert input coordinates to physical index
    const physical_idx = coordsToFlat(input_coords[0..input_dims.len], input_strides);
    
    return physical_idx + @as(usize, @intCast(@max(0, offset)));
}

/// Convert an array of SymbolicDim to concrete i64 values
/// Returns error if any dimension is dynamic
pub fn symbolicDimsToConcreteArray(symbolic_dims: []const SymbolicDim, out_dims: []i64) !void {
    if (out_dims.len < symbolic_dims.len) {
        return error.BufferTooSmall;
    }
    
    for (symbolic_dims, 0..) |dim, i| {
        out_dims[i] = switch (dim) {
            .concrete => |v| v,
            .dynamic => {
                std.log.err("symbolicDimsToConcreteArray: dynamic dimension at index {}", .{i});
                return error.DynamicDimensionNotSupported;
            },
        };
    }
}