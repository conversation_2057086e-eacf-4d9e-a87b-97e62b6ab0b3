const std = @import("std");

// Import shape types
const shape_types = @import("types.zig");
const SymbolicDim = shape_types.SymbolicDim;

// Import tracker
const ShapeTracker = @import("tracker.zig").ShapeTracker;

// Import symbolic operations
const symbolic = @import("symbolic.zig");

// Import from other components
const types = @import("types");
const ExprId = types.ExprId;
const SymbolicPool = @import("symbolic").SymbolicPool;

// Import compile-time index optimization
const comptime_index = @import("comptime_index.zig");
pub const ComptimeIndexExpr = comptime_index.ComptimeIndexExpr;
pub const ComptimeView = comptime_index.ComptimeView;

// ============================================================================
// Index Computation (Advanced)
// ============================================================================

/// Compute the buffer index for given logical coordinates
pub fn computeIndex(tracker: *const ShapeTracker, coords: []const SymbolicDim, pool: *SymbolicPool) !SymbolicDim {
    // Start with offset
    var index_expr = switch (tracker.offset) {
        .concrete => |val| try pool.constant(val),
        .dynamic => |expr| expr,
    };

    // Add contribution from each dimension: index = offset + Σ(coord[i] * stride[i])
    for (coords, tracker.strides, tracker.fake, 0..) |coord, stride, is_fake, i| {
        // Skip fake (broadcasted) dimensions - they don't contribute to the index
        if (is_fake) {
            continue;
        }
        
        // Apply mask bounds if present
        const effective_coord = if (tracker.mask) |mask| blk: {
            // Clamp coordinate to mask bounds
            const start = mask[i][0];
            const end = mask[i][1];
            // coord = max(start, min(coord, end - 1))
            break :blk try clampCoordinate(coord, start, end, pool);
        } else coord;

        // Convert to expression
        const coord_expr = switch (effective_coord) {
            .concrete => |val| try pool.constant(val),
            .dynamic => |expr| expr,
        };

        const stride_expr = switch (stride) {
            .concrete => |val| try pool.constant(val),
            .dynamic => |expr| expr,
        };

        // index += coord * stride
        const contribution = try pool.multiply(coord_expr, stride_expr);
        index_expr = try pool.add(index_expr, contribution);
    }

    // Convert back to SymbolicDim if possible
    if (pool.isConstant(index_expr)) {
        return SymbolicDim{ .concrete = pool.getConstantValue(index_expr) };
    }
    return SymbolicDim{ .dynamic = index_expr };
}

/// Compute total size needed for allocation
pub fn computeSize(dims: []const SymbolicDim, pool: *SymbolicPool) !ExprId {
    var size_expr = try pool.constant(1);
    for (dims) |dim| {
        const dim_expr = switch (dim) {
            .concrete => |val| try pool.constant(val),
            .dynamic => |expr| expr,
        };
        size_expr = try pool.multiply(size_expr, dim_expr);
    }
    return size_expr;
}

/// Check if two size expressions are equal (conservative)
pub fn sizesEqual(a: ExprId, b: ExprId, pool: *SymbolicPool) !bool {
    // For now, only handle concrete comparisons
    const a_is_const = pool.isConstant(a);
    const b_is_const = pool.isConstant(b);
    if (a_is_const and b_is_const) {
        return pool.getConstantValue(a) == pool.getConstantValue(b);
    }
    // If either is symbolic, assume they could be equal
    return true;
}

/// Compute concrete buffer index for given logical indices (for runtime use)
/// This is the function that backends should use to map logical indices to physical memory
pub fn computeConcreteIndex(tracker: *const ShapeTracker, logical_index: usize) usize {
    // Convert flat logical index to coordinates
    var coords: [8]usize = undefined; // Max 8 dimensions
    var remaining = logical_index;
    
    // Convert flat index to multi-dimensional coordinates
    var i: usize = tracker.dims.len;
    while (i > 0) {
        i -= 1;
        const dim_size = switch (tracker.dims[i]) {
            .concrete => |v| @as(usize, @intCast(v)),
            .dynamic => unreachable, // At runtime, all dims must be concrete
        };
        coords[i] = remaining % dim_size;
        remaining /= dim_size;
    }
    
    // Compute physical index considering strides, fake dimensions, and mask bounds
    var physical_index = switch (tracker.offset) {
        .concrete => |v| @as(usize, @intCast(v)),
        .dynamic => unreachable, // At runtime, offset must be concrete
    };
    
    for (0..tracker.dims.len) |dim_idx| {
        // Skip fake (broadcasted) dimensions
        if (tracker.fake[dim_idx]) {
            continue;
        }
        
        // Apply mask bounds if present (for slice operations)
        var effective_coord = coords[dim_idx];
        if (tracker.mask) |mask| {
            const start = switch (mask[dim_idx][0]) {
                .concrete => |v| @as(usize, @intCast(v)),
                .dynamic => unreachable, // At runtime, mask bounds must be concrete
            };
            // Add slice start offset to map logical output coordinate to physical input coordinate
            effective_coord = effective_coord + start;
        }
        
        const stride = switch (tracker.strides[dim_idx]) {
            .concrete => |v| @as(usize, @intCast(v)),
            .dynamic => unreachable, // At runtime, strides must be concrete
        };
        
        physical_index += effective_coord * stride;
    }
    
    return physical_index;
}

/// Get element at logical index from buffer (for backend kernels)
pub fn getElement(comptime T: type, buffer: []const u8, tracker: *const ShapeTracker, logical_index: usize) T {
    const physical_index = computeConcreteIndex(tracker, logical_index);
    const ptr = @as([*]const T, @ptrCast(@alignCast(buffer.ptr)));
    return ptr[physical_index];
}

/// Set element at logical index in buffer (for backend kernels)
pub fn setElement(comptime T: type, buffer: []u8, tracker: *const ShapeTracker, logical_index: usize, value: T) void {
    const physical_index = computeConcreteIndex(tracker, logical_index);
    const ptr = @as([*]T, @ptrCast(@alignCast(buffer.ptr)));
    ptr[physical_index] = value;
}


// ============================================================================
// Internal Helpers
// ============================================================================

fn clampCoordinate(coord: SymbolicDim, start: SymbolicDim, end: SymbolicDim, pool: *SymbolicPool) !SymbolicDim {
    // Full symbolic implementation: clamp(coord, start, end-1) = max(start, min(coord, end-1))

    // Handle all-concrete case for efficiency
    if (coord == .concrete and start == .concrete and end == .concrete) {
        const clamped = @max(start.concrete, @min(coord.concrete, end.concrete - 1));
        return SymbolicDim{ .concrete = clamped };
    }

    // Convert all inputs to expressions
    const coord_expr = switch (coord) {
        .concrete => |val| try pool.constant(val),
        .dynamic => |expr| expr,
    };
    const start_expr = switch (start) {
        .concrete => |val| try pool.constant(val),
        .dynamic => |expr| expr,
    };
    const end_expr = switch (end) {
        .concrete => |val| try pool.constant(val),
        .dynamic => |expr| expr,
    };

    // Compute end - 1
    const one = try pool.constant(1);
    const end_minus_one = try pool.subtract(end_expr, one);

    // Compute min(coord, end-1)
    const coord_clamped_high = try pool.binary(.min, coord_expr, end_minus_one);

    // Compute max(start, min(coord, end-1))
    const result_expr = try pool.binary(.max, start_expr, coord_clamped_high);

    // Optimize back to concrete if possible
    if (pool.isConstant(result_expr)) {
        return SymbolicDim{ .concrete = pool.getConstantValue(result_expr) };
    }
    return SymbolicDim{ .dynamic = result_expr };
}