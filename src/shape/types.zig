const std = @import("std");
const Allocator = std.mem.Allocator;

// Import shared types
const types = @import("types");
const ExprId = types.ExprId;
const NodeId = types.NodeId;
const INVALID_EXPR_ID = types.INVALID_EXPR_ID;

// Import from symbolic component
const SymbolicPool = @import("symbolic").SymbolicPool;

// ============================================================================
// Constants
// ============================================================================

/// Maximum supported tensor rank
pub const MAX_RANK = 8;

/// Maximum supported tensor size (elements)
pub const MAX_TENSOR_SIZE = 1 << 31; // 2^31 elements

// ============================================================================
// Core Types
// ============================================================================

/// Represents a single dimension, which can be a concrete value or a
/// dynamic symbol represented by an expression in the SymbolicPool.
pub const SymbolicDim = union(enum) {
    concrete: i64,
    dynamic: ExprId,

    pub fn isStatic(self: SymbolicDim) bool {
        return switch (self) {
            .concrete => true,
            .dynamic => false,
        };
    }

    pub fn getValue(self: SymbolicDim, pool: *const SymbolicPool) ?i64 {
        return switch (self) {
            .concrete => |val| val,
            .dynamic => |expr| if (pool.isConstant(expr)) pool.getConstantValue(expr) else null,
        };
    }
};

/// Axis specification for reductions
pub const Axis = union(enum) {
    index: usize,
    all,

    pub fn resolve(self: Axis, rank: usize, allocator: Allocator) ![]const usize {
        return switch (self) {
            .index => |idx| blk: {
                if (idx >= rank) {
                    std.log.err("Axis.resolve: index {} out of bounds for rank {}", .{ idx, rank });
                    return error.AxisOutOfBounds;
                }
                const result = try allocator.alloc(usize, 1);
                result[0] = idx;
                break :blk result;
            },
            .all => blk: {
                const axes = try allocator.alloc(usize, rank);
                for (axes, 0..) |*axis, i| {
                    axis.* = i;
                }
                break :blk axes;
            },
        };
    }
};

/// Memory layout types
pub const DataLayout = enum {
    row_major, // C-style, last dimension contiguous
    column_major, // Fortran-style, first dimension contiguous

    pub fn isContiguous(self: DataLayout, strides: []const i64, shape: []const i64) bool {
        return switch (self) {
            .row_major => checkRowMajorContiguous(strides, shape),
            .column_major => checkColumnMajorContiguous(strides, shape),
        };
    }
};

pub const MemoryFormat = enum {
    dense, // Standard dense tensor
    sparse_coo, // Coordinate format sparse
    sparse_csr, // Compressed sparse row
    sparse_csc, // Compressed sparse column
};

/// Shape errors
pub const ShapeError = error{
    IncompatibleShapes,
    InvalidAxis,
    UnboundSymbol,
    ShapeMismatch,
    InvalidBroadcast,
    InvalidReshape,
    InvalidPermutation,
    InvalidReduceDimension,
    IncompatibleBroadcast,
    InvalidSlice,
    InvalidPadding,
    RankTooHigh,
    AxisOutOfBounds,
    DimensionTooLarge,
    NegativeDimension,
    ZeroDimension,
    TensorTooLarge,
    InvalidStride,
    StrideOverflow,
    DuplicateAxis,
    InvalidMatmulDimensions,
    InvalidDimension,
    InvalidOperation,
};

// ============================================================================
// Private Helpers (exported for other shape modules)
// ============================================================================

pub fn checkRowMajorContiguous(strides: []const i64, shape: []const i64) bool {
    if (strides.len != shape.len) return false;

    var expected_stride: i64 = 1;
    var i = strides.len;
    while (i > 0) : (i -= 1) {
        if (strides[i - 1] != expected_stride) return false;
        expected_stride *= shape[i - 1];
    }
    return true;
}

pub fn checkColumnMajorContiguous(strides: []const i64, shape: []const i64) bool {
    if (strides.len != shape.len) return false;

    var expected_stride: i64 = 1;
    for (strides, shape) |stride, dim| {
        if (stride != expected_stride) return false;
        expected_stride *= dim;
    }
    return true;
}