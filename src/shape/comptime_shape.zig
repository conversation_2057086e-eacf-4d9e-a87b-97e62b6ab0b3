/// Compile-time shape operations for static optimization
const std = @import("std");
const testing = std.testing;
const shape_types = @import("types.zig");
const SymbolicDim = shape_types.SymbolicDim;

/// Compile-time shape type for statically known dimensions
pub fn ComptimeShape(comptime dims: []const i64) type {
    // Validate dimensions at compile time
    comptime {
        for (dims) |d| {
            if (d <= 0) @compileError("Shape dimensions must be positive");
        }
    }
    
    return struct {
        pub const dimensions = dims;
        pub const rank = dims.len;
        pub const strides = comptimeStrides(dims);
        pub const num_elements = comptimeNumElements(dims);
        pub const is_contiguous = true; // Always true for freshly created shapes
        
        /// Get dimension at compile time
        pub fn dim(comptime idx: usize) i64 {
            if (idx >= rank) @compileError("Dimension index out of bounds");
            return dimensions[idx];
        }
        
        /// Check if broadcastable with another shape at compile time
        pub fn isBroadcastableWith(comptime other_dims: []const i64) bool {
            return comptimeBroadcastable(dimensions, other_dims);
        }
        
        /// Get broadcast shape at compile time
        pub fn broadcastWith(comptime other_dims: []const i64) ComptimeShape(comptimeBroadcastShape(dimensions, other_dims)) {
            return .{};
        }
    };
}

/// Calculate strides at compile time
pub fn comptimeStrides(comptime shape: []const i64) [shape.len]i64 {
    if (shape.len == 0) return .{};
    
    var strides: [shape.len]i64 = undefined;
    strides[shape.len - 1] = 1;
    
    if (shape.len > 1) {
        comptime var i = shape.len - 1;
        inline while (i > 0) : (i -= 1) {
            strides[i - 1] = strides[i] * shape[i];
        }
    }
    
    return strides;
}

/// Calculate number of elements at compile time
pub fn comptimeNumElements(comptime shape: []const i64) i64 {
    comptime var total: i64 = 1;
    inline for (shape) |dim| {
        total *= dim;
    }
    return total;
}

/// Check if two shapes are broadcastable at compile time
pub fn comptimeBroadcastable(comptime a: []const i64, comptime b: []const i64) bool {
    const max_rank = @max(a.len, b.len);
    const a_offset = max_rank - a.len;
    const b_offset = max_rank - b.len;
    
    comptime var i: usize = 0;
    inline while (i < max_rank) : (i += 1) {
        const a_dim = if (i >= a_offset) a[i - a_offset] else 1;
        const b_dim = if (i >= b_offset) b[i - b_offset] else 1;
        
        if (a_dim != b_dim and a_dim != 1 and b_dim != 1) {
            return false;
        }
    }
    
    return true;
}

/// Calculate broadcast shape at compile time
pub fn comptimeBroadcastShape(comptime a: []const i64, comptime b: []const i64) []const i64 {
    comptime {
        if (!comptimeBroadcastable(a, b)) {
            @compileError("Shapes are not broadcastable");
        }
        
        const max_rank = @max(a.len, b.len);
        var result: [max_rank]i64 = undefined;
        const a_offset = max_rank - a.len;
        const b_offset = max_rank - b.len;
        
        var i: usize = 0;
        while (i < max_rank) : (i += 1) {
            const a_dim = if (i >= a_offset) a[i - a_offset] else 1;
            const b_dim = if (i >= b_offset) b[i - b_offset] else 1;
            result[i] = @max(a_dim, b_dim);
        }
        
        const final = result;
        return &final;
    }
}

/// Compile-time matrix multiplication shape inference
pub fn comptimeMatmulShape(comptime a_shape: []const i64, comptime b_shape: []const i64) [2]i64 {
    comptime {
        if (a_shape.len != 2) @compileError("First matrix must be 2D");
        if (b_shape.len != 2) @compileError("Second matrix must be 2D");
        if (a_shape[1] != b_shape[0]) {
            @compileError(std.fmt.comptimePrint(
                "Matrix dimensions incompatible: ({}, {}) x ({}, {})",
                .{ a_shape[0], a_shape[1], b_shape[0], b_shape[1] }
            ));
        }
    }
    return .{ a_shape[0], b_shape[1] };
}

/// Compile-time reshape validation
pub fn comptimeReshape(comptime old_shape: []const i64, comptime new_shape: []const i64) void {
    comptime {
        const old_elements = comptimeNumElements(old_shape);
        const new_elements = comptimeNumElements(new_shape);
        if (old_elements != new_elements) {
            @compileError(std.fmt.comptimePrint(
                "Cannot reshape from {} elements to {} elements",
                .{ old_elements, new_elements }
            ));
        }
    }
}

/// Compile-time transpose shape
pub fn comptimeTransposeShape(comptime shape: []const i64) [shape.len]i64 {
    comptime {
        var result: [shape.len]i64 = undefined;
        var i: usize = 0;
        while (i < shape.len) : (i += 1) {
            result[i] = shape[shape.len - 1 - i];
        }
        return result;
    }
}

/// Compile-time reduction shape
pub fn comptimeReduceShape(comptime shape: []const i64, comptime axis: i64, comptime keepdims: bool) []const i64 {
    comptime {
        if (axis < 0 or axis >= shape.len) {
            @compileError("Reduction axis out of bounds");
        }
        
        if (keepdims) {
            var result: [shape.len]i64 = undefined;
            for (shape, 0..) |dim, i| {
                result[i] = if (i == axis) 1 else dim;
            }
            const final = result;
            return &final;
        } else {
            var result: [shape.len - 1]i64 = undefined;
            var j: usize = 0;
            for (shape, 0..) |dim, i| {
                if (i != axis) {
                    result[j] = dim;
                    j += 1;
                }
            }
            const final = result;
            return &final;
        }
    }
}

/// Create a validated shape at compile time
pub fn createComptimeShape(comptime dims: []const i64) ComptimeShape(dims) {
    return .{};
}

// ===== Tests =====

test "ComptimeShape basic properties" {
    const Shape2x3 = ComptimeShape(&.{ 2, 3 });
    try testing.expectEqual(@as(usize, 2), Shape2x3.rank);
    try testing.expectEqual(@as(i64, 6), Shape2x3.num_elements);
    try testing.expectEqual([_]i64{ 3, 1 }, Shape2x3.strides);
    
    const Shape4x5x6 = ComptimeShape(&.{ 4, 5, 6 });
    try testing.expectEqual(@as(usize, 3), Shape4x5x6.rank);
    try testing.expectEqual(@as(i64, 120), Shape4x5x6.num_elements);
    try testing.expectEqual([_]i64{ 30, 6, 1 }, Shape4x5x6.strides);
}

test "compile-time broadcast shape" {
    const shape1 = &[_]i64{ 1, 3 };
    const shape2 = &[_]i64{ 2, 1 };
    const broadcast = comptimeBroadcastShape(shape1, shape2);
    try testing.expectEqualSlices(i64, &[_]i64{ 2, 3 }, broadcast);
    
    const shape3 = &[_]i64{ 5, 1, 3 };
    const shape4 = &[_]i64{ 1, 4, 1 };
    const broadcast2 = comptimeBroadcastShape(shape3, shape4);
    try testing.expectEqualSlices(i64, &[_]i64{ 5, 4, 3 }, broadcast2);
}

test "compile-time matmul shape" {
    const a = &[_]i64{ 3, 4 };
    const b = &[_]i64{ 4, 5 };
    const result = comptimeMatmulShape(a, b);
    try testing.expectEqual([_]i64{ 3, 5 }, result);
}

test "compile-time reduce shape" {
    const shape = &[_]i64{ 2, 3, 4 };
    
    // Reduce with keepdims
    const reduced_keepdims = comptimeReduceShape(shape, 1, true);
    try testing.expectEqualSlices(i64, &[_]i64{ 2, 1, 4 }, reduced_keepdims);
    
    // Reduce without keepdims
    const reduced_no_keepdims = comptimeReduceShape(shape, 1, false);
    try testing.expectEqualSlices(i64, &[_]i64{ 2, 4 }, reduced_no_keepdims);
}