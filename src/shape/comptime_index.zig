/// Compile-time index expression optimization
const std = @import("std");
const testing = std.testing;
const IndexExpr = @import("index.zig").IndexExpr;

/// Compile-time index expression for statically known shapes
pub fn ComptimeIndexExpr(comptime dims: []const i64) type {
    const strides = comptimeStrides(dims);
    const ndim = dims.len;
    
    return struct {
        const Self = @This();
        
        pub const dimensions = dims;
        pub const static_strides = strides;
        pub const rank = ndim;
        
        /// Compute linear index at compile time when possible
        pub inline fn compute(coords: [ndim]i64) i64 {
            comptime var index: i64 = 0;
            inline for (coords, static_strides) |coord, stride| {
                index += coord * stride;
            }
            return index;
        }
        
        /// Compute with broadcasting support
        pub inline fn computeBroadcast(linear_idx: i64, comptime broadcast_mask: [ndim]bool) i64 {
            var coords: [ndim]i64 = undefined;
            var remaining = linear_idx;
            
            // Convert linear index to coordinates
            inline for (0..ndim) |i| {
                const dim_idx = ndim - 1 - i;
                if (!broadcast_mask[dim_idx]) {
                    coords[dim_idx] = @mod(remaining, dimensions[dim_idx]);
                    remaining = @divTrunc(remaining, dimensions[dim_idx]);
                } else {
                    coords[dim_idx] = 0; // Broadcast dimension
                }
            }
            
            // Compute final index
            return compute(coords);
        }
        
        /// Optimized strided access pattern
        pub inline fn computeStrided(base_idx: i64, comptime stride_pattern: []const i64) i64 {
            comptime var total_stride: i64 = 0;
            inline for (stride_pattern, static_strides) |pattern_stride, dim_stride| {
                total_stride += pattern_stride * dim_stride;
            }
            return base_idx + total_stride;
        }
    };
}

/// Compile-time strides calculation
fn comptimeStrides(comptime shape: []const i64) [shape.len]i64 {
    if (shape.len == 0) return .{};
    
    var strides: [shape.len]i64 = undefined;
    strides[shape.len - 1] = 1;
    
    if (shape.len > 1) {
        comptime var i = shape.len - 1;
        inline while (i > 0) : (i -= 1) {
            strides[i - 1] = strides[i] * shape[i];
        }
    }
    
    return strides;
}

/// Generate optimized index computation for common patterns
pub fn generateIndexKernel(comptime pattern: enum { contiguous, strided, broadcast, transpose }) type {
    return struct {
        pub fn compute(comptime shape: []const i64) type {
            return struct {
                const IndexComputer = ComptimeIndexExpr(shape);
                
                pub inline fn index(linear_idx: i64) i64 {
                    return switch (pattern) {
                        .contiguous => linear_idx,
                        .strided => IndexComputer.computeStrided(linear_idx, &.{2, 1}), // Example stride
                        .broadcast => IndexComputer.computeBroadcast(linear_idx, .{false, true}), // Example broadcast
                        .transpose => blk: {
                            // Transpose last two dimensions
                            var coords: [shape.len]i64 = undefined;
                            var remaining = linear_idx;
                            
                            inline for (0..shape.len) |i| {
                                const dim_idx = shape.len - 1 - i;
                                coords[dim_idx] = @mod(remaining, shape[dim_idx]);
                                remaining = @divTrunc(remaining, shape[dim_idx]);
                            }
                            
                            // Swap last two coordinates
                            if (shape.len >= 2) {
                                const tmp = coords[shape.len - 1];
                                coords[shape.len - 1] = coords[shape.len - 2];
                                coords[shape.len - 2] = tmp;
                            }
                            
                            break :blk IndexComputer.compute(coords);
                        },
                    };
                }
            };
        }
    };
}

/// Compile-time view operation optimization
pub fn ComptimeView(comptime base_shape: []const i64, comptime view_op: ViewOp) type {
    return struct {
        pub const result_shape = switch (view_op) {
            .reshape => |new_shape| new_shape,
            .transpose => |perm| permuteShape(base_shape, perm),
            .slice => |ranges| sliceShape(base_shape, ranges),
            .expand => |new_shape| new_shape,
        };
        
        pub const base_indexer = ComptimeIndexExpr(base_shape);
        pub const result_indexer = ComptimeIndexExpr(result_shape);
        
        pub inline fn mapIndex(result_idx: i64) i64 {
            return switch (view_op) {
                .reshape => result_idx, // Same linear index
                .transpose => |perm| blk: {
                    // Get result coordinates
                    var result_coords: [result_shape.len]i64 = undefined;
                    var remaining = result_idx;
                    
                    inline for (0..result_shape.len) |i| {
                        const dim_idx = result_shape.len - 1 - i;
                        result_coords[dim_idx] = @mod(remaining, result_shape[dim_idx]);
                        remaining = @divTrunc(remaining, result_shape[dim_idx]);
                    }
                    
                    // Apply inverse permutation
                    var base_coords: [base_shape.len]i64 = undefined;
                    inline for (perm, 0..) |src_dim, dst_dim| {
                        base_coords[src_dim] = result_coords[dst_dim];
                    }
                    
                    break :blk base_indexer.compute(base_coords);
                },
                .slice => |ranges| blk: {
                    // Get result coordinates
                    var result_coords: [result_shape.len]i64 = undefined;
                    var remaining = result_idx;
                    
                    inline for (0..result_shape.len) |i| {
                        const dim_idx = result_shape.len - 1 - i;
                        result_coords[dim_idx] = @mod(remaining, result_shape[dim_idx]);
                        remaining = @divTrunc(remaining, result_shape[dim_idx]);
                    }
                    
                    // Map to base coordinates
                    var base_coords: [base_shape.len]i64 = undefined;
                    inline for (ranges, 0..) |range, i| {
                        base_coords[i] = range.start + result_coords[i];
                    }
                    
                    break :blk base_indexer.compute(base_coords);
                },
                .expand => |_| blk: {
                    // Broadcast logic
                    var result_coords: [result_shape.len]i64 = undefined;
                    var remaining = result_idx;
                    
                    inline for (0..result_shape.len) |i| {
                        const dim_idx = result_shape.len - 1 - i;
                        result_coords[dim_idx] = @mod(remaining, result_shape[dim_idx]);
                        remaining = @divTrunc(remaining, result_shape[dim_idx]);
                    }
                    
                    // Map to base with broadcasting
                    var base_coords: [base_shape.len]i64 = undefined;
                    inline for (base_shape, 0..) |base_dim, i| {
                        if (base_dim == 1) {
                            base_coords[i] = 0; // Broadcast dimension
                        } else {
                            base_coords[i] = result_coords[i];
                        }
                    }
                    
                    break :blk base_indexer.compute(base_coords);
                },
            };
        }
    };
}

pub const ViewOp = union(enum) {
    reshape: []const i64,
    transpose: []const usize,
    slice: []const Range,
    expand: []const i64,
};

pub const Range = struct {
    start: i64,
    end: i64,
};

fn permuteShape(comptime shape: []const i64, comptime perm: []const usize) [perm.len]i64 {
    comptime {
        if (shape.len != perm.len) @compileError("Permutation length must match shape rank");
        
        var result: [perm.len]i64 = undefined;
        for (perm, 0..) |src_dim, dst_dim| {
            result[dst_dim] = shape[src_dim];
        }
        return result;
    }
}

fn sliceShape(comptime shape: []const i64, comptime ranges: []const Range) [ranges.len]i64 {
    comptime {
        if (shape.len != ranges.len) @compileError("Ranges length must match shape rank");
        
        var result: [ranges.len]i64 = undefined;
        for (ranges, 0..) |range, i| {
            result[i] = range.end - range.start;
        }
        return result;
    }
}

// ===== Tests =====

test "ComptimeIndexExpr basic computation" {
    const shape = &[_]i64{ 2, 3, 4 };
    const Indexer = ComptimeIndexExpr(shape);
    
    // Test [0, 0, 0]
    try testing.expectEqual(@as(i64, 0), Indexer.compute(.{ 0, 0, 0 }));
    
    // Test [0, 0, 1]
    try testing.expectEqual(@as(i64, 1), Indexer.compute(.{ 0, 0, 1 }));
    
    // Test [0, 1, 0]
    try testing.expectEqual(@as(i64, 4), Indexer.compute(.{ 0, 1, 0 }));
    
    // Test [1, 0, 0]
    try testing.expectEqual(@as(i64, 12), Indexer.compute(.{ 1, 0, 0 }));
}

test "compile-time broadcast computation" {
    const shape = &[_]i64{ 2, 3, 4 };
    const Indexer = ComptimeIndexExpr(shape);
    
    // Broadcast middle dimension
    const broadcast_mask = [_]bool{ false, true, false };
    
    // Linear index 5 with broadcasting
    const result = Indexer.computeBroadcast(5, broadcast_mask);
    try testing.expectEqual(@as(i64, 1), result); // Maps to [0, 0, 1]
}

test "compile-time view operations" {
    const base_shape = &[_]i64{ 2, 3, 4 };
    
    // Test transpose
    const TransposeView = ComptimeView(base_shape, .{ .transpose = &[_]usize{ 0, 2, 1 } });
    try testing.expectEqualSlices(i64, &[_]i64{ 2, 4, 3 }, &TransposeView.result_shape);
    
    // Test slice
    const SliceView = ComptimeView(base_shape, .{ .slice = &[_]Range{
        .{ .start = 0, .end = 1 },
        .{ .start = 1, .end = 3 },
        .{ .start = 0, .end = 4 },
    } });
    try testing.expectEqualSlices(i64, &[_]i64{ 1, 2, 4 }, &SliceView.result_shape);
}