const std = @import("std");

// Import types
const shape_types = @import("types.zig");
const SymbolicDim = shape_types.SymbolicDim;

// Import from symbolic component
const SymbolicPool = @import("symbolic").SymbolicPool;

// ============================================================================
// Symbolic Operations - Bridge between concrete and dynamic dimensions
// ============================================================================

/// Helper functions for SymbolicDim arithmetic operations
pub fn symbolicAdd(pool: *SymbolicPool, a: SymbolicDim, b: SymbolicDim) !SymbolicDim {
    const a_expr = switch (a) {
        .concrete => |val| try pool.constant(val),
        .dynamic => |expr| expr,
    };
    const b_expr = switch (b) {
        .concrete => |val| try pool.constant(val),
        .dynamic => |expr| expr,
    };
    const result = try pool.add(a_expr, b_expr);
    // Check if result is a constant after simplification
    if (pool.isConstant(result)) {
        return SymbolicDim{ .concrete = pool.getConstantValue(result) };
    }
    return SymbolicDim{ .dynamic = result };
}

pub fn symbolicSub(pool: *SymbolicPool, a: SymbolicDim, b: SymbolicDim) !SymbolicDim {
    const a_expr = switch (a) {
        .concrete => |val| try pool.constant(val),
        .dynamic => |expr| expr,
    };
    const b_expr = switch (b) {
        .concrete => |val| try pool.constant(val),
        .dynamic => |expr| expr,
    };
    const result = try pool.subtract(a_expr, b_expr);
    // Check if result is a constant after simplification
    if (pool.isConstant(result)) {
        return SymbolicDim{ .concrete = pool.getConstantValue(result) };
    }
    return SymbolicDim{ .dynamic = result };
}

pub fn symbolicMul(pool: *SymbolicPool, a: SymbolicDim, b: SymbolicDim) !SymbolicDim {
    const a_expr = switch (a) {
        .concrete => |val| try pool.constant(val),
        .dynamic => |expr| expr,
    };
    const b_expr = switch (b) {
        .concrete => |val| try pool.constant(val),
        .dynamic => |expr| expr,
    };
    const result = try pool.multiply(a_expr, b_expr);
    // Check if result is a constant after simplification
    if (pool.isConstant(result)) {
        return SymbolicDim{ .concrete = pool.getConstantValue(result) };
    }
    return SymbolicDim{ .dynamic = result };
}

pub fn symbolicMax(pool: *SymbolicPool, a: SymbolicDim, b: SymbolicDim) !SymbolicDim {
    const a_expr = switch (a) {
        .concrete => |val| try pool.constant(val),
        .dynamic => |expr| expr,
    };
    const b_expr = switch (b) {
        .concrete => |val| try pool.constant(val),
        .dynamic => |expr| expr,
    };
    const result = try pool.binary(.max, a_expr, b_expr);
    // Check if result is a constant after simplification
    if (pool.isConstant(result)) {
        return SymbolicDim{ .concrete = pool.getConstantValue(result) };
    }
    return SymbolicDim{ .dynamic = result };
}

pub fn symbolicMin(pool: *SymbolicPool, a: SymbolicDim, b: SymbolicDim) !SymbolicDim {
    const a_expr = switch (a) {
        .concrete => |val| try pool.constant(val),
        .dynamic => |expr| expr,
    };
    const b_expr = switch (b) {
        .concrete => |val| try pool.constant(val),
        .dynamic => |expr| expr,
    };
    const result = try pool.binary(.min, a_expr, b_expr);
    // Check if result is a constant after simplification
    if (pool.isConstant(result)) {
        return SymbolicDim{ .concrete = pool.getConstantValue(result) };
    }
    return SymbolicDim{ .dynamic = result };
}