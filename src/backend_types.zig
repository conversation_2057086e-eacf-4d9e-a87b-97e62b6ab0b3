/// Backend Types - Shared type definitions for backend implementations
///
/// This module contains ALL types that backends need to implement their functionality.
/// It serves as the definitive interface contract between the compiler and backends.
///
/// Design principles:
/// 1. This module NEVER imports from compiler or backends
/// 2. Only imports from core modules (types, graph, shape, symbolic)
/// 3. Backends import ONLY from this module and core modules
/// 4. Compiler imports from backends, never the reverse
///
/// This design makes circular dependencies impossible by construction.

const std = @import("std");
const Allocator = std.mem.Allocator;

// Import only core types that have no backend/compiler dependencies
const types = @import("types");
const NodeId = types.NodeId;
const BufferId = types.BufferId;
const DataType = types.DataType;
const ComputeOp = types.ComputeOp;
const ExprId = types.ExprId;

// ============================================================================
// Backend Capability Types
// ============================================================================

/// Hardware and optimization capabilities that backends expose
pub const BackendCapabilities = struct {
    // Hardware capabilities
    name: []const u8,
    target_arch: TargetArch,
    
    // Data type support
    supports_f16: bool = false,
    supports_f32: bool = true,
    supports_f64: bool = false,
    supports_i8: bool = false,
    supports_i16: bool = false,
    supports_i32: bool = true,
    supports_i64: bool = false,
    
    // Memory capabilities
    max_memory_gb: u32 = 8,
    memory_alignment: u32 = 32,
    cache_line_size: u32 = 64,
    
    // Compute capabilities
    max_threads: u32 = 1,
    simd_width: u32 = 1,
    supports_fma: bool = false,
    supports_tensor_cores: bool = false,
    
    // Optimization capabilities
    supports_fusion: bool = true,
    max_fusion_depth: u32 = 4,
    supports_constant_folding: bool = true,
    supports_cse: bool = true,
    supports_memory_optimization: bool = true,
    supports_pattern_recognition: bool = true,
    
    // Memory coalescing
    memory_coalescing_alignment: u32 = 64,
    
    /// Get vectorization hints based on capabilities
    pub fn getVectorizationHints(self: BackendCapabilities) VectorizationHints {
        return .{
            .preferred_width = self.simd_width,
            .supports_fma = self.supports_fma,
            .supports_mixed_precision = self.supports_f16 and self.supports_f32,
        };
    }
    
    /// Get memory optimization hints
    pub fn getMemoryHints(self: BackendCapabilities) MemoryHints {
        return .{
            .preferred_alignment = self.memory_alignment,
            .cache_line_size = self.cache_line_size,
            .coalescing_alignment = self.memory_coalescing_alignment,
        };
    }
};

/// Target architecture enumeration
pub const TargetArch = enum {
    cpu,
    cuda,
    metal,
    vulkan,
    wasm,
    custom,
};

/// Vectorization optimization hints
pub const VectorizationHints = struct {
    preferred_width: u32,
    supports_fma: bool,
    supports_mixed_precision: bool,
};

/// Memory optimization hints
pub const MemoryHints = struct {
    preferred_alignment: u32,
    cache_line_size: u32,
    coalescing_alignment: u32,
};

/// Fusion optimization hints
pub const FusionHints = struct {
    max_fusion_depth: u32,
    preferred_fusion_patterns: []const types.FusionPattern,
};

// ============================================================================
// Pattern Matching Types
// ============================================================================

/// Pattern definition for backend-specific optimizations
pub const Pattern = struct {
    /// Unique identifier for this pattern
    id: u32,
    
    /// Human-readable name
    name: []const u8,
    
    /// Pattern matcher function
    matcher: PatternMatchFn,
    
    /// Cost model for this pattern
    cost_fn: ?PatternCostFn = null,
    
    /// Priority for pattern application (higher = earlier)
    priority: u32 = 50,
};

/// Pattern matching function signature
pub const PatternMatchFn = *const fn (
    graph: *const anyopaque, // Will be *Graph in practice
    root: NodeId,
    context: *const PatternContext,
) ?PatternMatch;

/// Pattern cost estimation function
pub const PatternCostFn = *const fn (
    match: *const PatternMatch,
    context: *const PatternContext,
) f32;

/// Context for pattern matching
pub const PatternContext = struct {
    allocator: Allocator,
    capabilities: BackendCapabilities,
    cost_model: ?*const anyopaque = null,
};

/// Result of a successful pattern match
pub const PatternMatch = struct {
    pattern_id: u32,
    root: NodeId,
    nodes: []const NodeId,
    cost: f32 = 0.0,
    metadata: ?*anyopaque = null,
};

// ============================================================================
// Kernel and Execution Types
// ============================================================================

/// Kernel function signature - the actual compute implementation
pub const KernelFn = *const fn (args: KernelArgs) void;

/// Arguments passed to kernel functions
pub const KernelArgs = struct {
    inputs: []const []const u8,
    outputs: [][]u8,
    work_size: usize = 0,
    custom_data: ?*anyopaque = null,
    
    // Essential metadata for correct kernel execution
    input_shapes: []const types.Shape, // Shape information for each input
    output_shapes: []const types.Shape, // Shape information for each output
    node_metadata: ?*anyopaque = null, // Operation-specific metadata (e.g., reduction axis)
    
    // Optional fields for async backends
    stream: ?*anyopaque = null, // For CUDA/Metal/etc
};

/// Kernel registry for dynamic kernel lookup
pub const KernelRegistry = struct {
    kernels: std.StringHashMap(KernelFn),
    allocator: Allocator,
    
    pub fn init(allocator: Allocator) KernelRegistry {
        return .{
            .kernels = std.StringHashMap(KernelFn).init(allocator),
            .allocator = allocator,
        };
    }
    
    pub fn deinit(self: *KernelRegistry) void {
        self.kernels.deinit();
    }
    
    pub fn register(self: *KernelRegistry, name: []const u8, kernel: KernelFn) !void {
        try self.kernels.put(name, kernel);
    }
    
    pub fn get(self: *const KernelRegistry, name: []const u8) ?KernelFn {
        return self.kernels.get(name);
    }
};

// ============================================================================
// Compilation Output Types
// ============================================================================

/// Backend artifact - compiled kernels and metadata
pub const BackendArtifact = struct {
    /// Compiled kernel functions
    kernels: KernelRegistry,
    
    /// Backend-specific metadata
    metadata: ?*anyopaque = null,
    
    /// Cleanup function for metadata
    cleanup_fn: ?*const fn (*anyopaque, Allocator) void = null,
    
    pub fn deinit(self: *BackendArtifact, allocator: Allocator) void {
        self.kernels.deinit();
        if (self.metadata) |meta| {
            if (self.cleanup_fn) |cleanup| {
                cleanup(meta, allocator);
            }
        }
    }
};

/// Execution step in the compiled program
pub const ExecutionStep = struct {
    /// Node being executed
    node_id: NodeId,
    
    /// Kernel function to execute
    kernel_fn: KernelFn,
    
    /// Input buffer IDs
    input_buffers: []const BufferId,
    
    /// Output buffer IDs
    output_buffers: []const BufferId,
    
    /// Pre-computed work dimensions
    work_size: usize,
    
    /// Kernel-specific metadata
    custom_data: ?*anyopaque = null,
    
    /// Shape info for inputs and outputs
    input_shapes: []const types.Shape,
    output_shapes: []const types.Shape,
    
    /// Operation-specific metadata (e.g., reduction axis)
    node_metadata: ?*anyopaque = null,
};

/// Buffer liveness information for memory planning
pub const LivenessInterval = struct {
    start: u32,
    end: u32,
};

/// Memory allocation resolution
pub const ResolvedAllocation = struct {
    node_id: NodeId,
    output_idx: u8,
    offset: usize,
    size: usize,
    dtype: DataType,
    shape: []const i64,
    alignment: usize,
    lifetime: LivenessInterval,
};

/// Complete memory plan
pub const ResolvedMemoryPlan = struct {
    allocations: []const ResolvedAllocation,
    total_memory: usize,
    arena_size: usize,
    
    pub fn deinit(self: *ResolvedMemoryPlan, allocator: Allocator) void {
        // Free shape arrays within each allocation
        for (self.allocations) |alloc| {
            allocator.free(alloc.shape);
        }
        allocator.free(self.allocations);
    }
};

/// Symbolic memory plan (before resolution)
pub const SymbolicMemoryPlan = struct {
    buffer_expressions: []const ExprId,
    buffer_lifetimes: []const LivenessInterval,
    total_memory_expression: ExprId,
    alignment_requirements: []const usize,
    arena_size_hint: ExprId,
    
    pub fn deinit(self: *SymbolicMemoryPlan, allocator: Allocator) void {
        allocator.free(self.buffer_expressions);
        allocator.free(self.buffer_lifetimes);
        allocator.free(self.alignment_requirements);
    }
};

/// Final compiled graph ready for execution
pub const CompiledGraph = struct {
    /// Execution steps in order
    steps: []const ExecutionStep,
    
    /// Memory plan for buffers
    memory_plan: ResolvedMemoryPlan,
    
    /// Backend artifact with kernels
    artifact: BackendArtifact,
    
    /// Backend name for debugging
    backend_name: []const u8,
    
    pub fn deinit(self: *CompiledGraph, allocator: Allocator) void {
        // Free all arrays within each ExecutionStep
        for (self.steps) |step| {
            allocator.free(step.input_buffers);
            allocator.free(step.output_buffers);
            // Free shape arrays
            for (step.input_shapes) |shape| {
                allocator.free(shape.dims);
                allocator.free(shape.strides);
                allocator.free(shape.index_expr.coeffs);
                allocator.free(shape.index_expr.mods);
            }
            allocator.free(step.input_shapes);
            for (step.output_shapes) |shape| {
                allocator.free(shape.dims);
                allocator.free(shape.strides);
                allocator.free(shape.index_expr.coeffs);
                allocator.free(shape.index_expr.mods);
            }
            allocator.free(step.output_shapes);
        }
        allocator.free(self.steps);
        self.memory_plan.deinit(allocator);
        self.artifact.deinit(allocator);
    }
};

// ============================================================================
// Backend Metadata Types
// ============================================================================

/// Metadata for gather operations
pub const GatherMetadata = struct {
    indices_dtype: DataType,
    axis: i32,
    batch_dims: i32 = 0,
};

/// Metadata for batched matrix multiplication
pub const BatchedMatMulMetadata = struct {
    transpose_a: bool = false,
    transpose_b: bool = false,
    batch_dims: u32 = 1,
};

/// Metadata for fused unary operations
pub const FusedUnaryMetadata = struct {
    ops: []const ComputeOp,
    intermediate_dtype: ?DataType = null,
};

// ============================================================================
// Backend Error Types
// ============================================================================

pub const BackendError = error{
    UnsupportedOperation,
    UnsupportedDataType,
    CompilationFailed,
    KernelNotFound,
    InvalidConfiguration,
    OutOfMemory,
    DeviceError,
};

// ============================================================================
// Backend Interface Requirements
// ============================================================================

// Documentation of required backend interface
//
// Each backend must be a namespace (not a struct) with these declarations:
//
// 1. pub const name: []const u8
//    - Unique identifier for the backend
//
// 2. pub const capabilities: BackendCapabilities
//    - Hardware and optimization capabilities
//
// 3. pub fn isOperationSupported(op: ComputeOp) bool
//    - Check if an operation is supported
//
// 4. pub fn getPatterns() []const Pattern
//    - Return backend-specific optimization patterns
//
// 5. pub fn compile(graph: *Graph, allocator: Allocator) !CompiledGraph
//    - Main compilation entry point
//
// Optional:
// 6. pub fn init(allocator: Allocator) !void
//    - One-time initialization (if needed)
//
// 7. pub fn deinit() void
//    - Cleanup (if needed)
//
// Backends must:
// - Import ONLY from backend_types.zig and core modules (types, graph, shape)
// - NEVER import from compiler modules
// - Be self-contained namespaces
// - Provide compile-time known capabilities

// ============================================================================
// Unit Tests
// ============================================================================

test "Backend capabilities" {
    const testing = std.testing;
    
    const caps = BackendCapabilities{
        .name = "test",
        .target_arch = .cpu,
        .supports_f32 = true,
        .simd_width = 256,
        .supports_fma = true,
    };
    
    try testing.expectEqualStrings("test", caps.name);
    try testing.expectEqual(TargetArch.cpu, caps.target_arch);
    
    const vec_hints = caps.getVectorizationHints();
    try testing.expectEqual(@as(u32, 256), vec_hints.preferred_width);
    try testing.expectEqual(true, vec_hints.supports_fma);
}

test "Kernel registry" {
    const testing = std.testing;
    const allocator = testing.allocator;
    
    var registry = KernelRegistry.init(allocator);
    defer registry.deinit();
    
    const dummy_kernel: KernelFn = struct {
        fn kernel(args: KernelArgs) void {
            _ = args;
        }
    }.kernel;
    
    try registry.register("test_kernel", dummy_kernel);
    
    const found = registry.get("test_kernel");
    try testing.expect(found != null);
    try testing.expectEqual(dummy_kernel, found.?);
    
    const not_found = registry.get("missing_kernel");
    try testing.expect(not_found == null);
}