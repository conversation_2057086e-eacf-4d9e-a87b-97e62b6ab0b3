/// Broadcast-aware kernels for element-wise operations
/// 
/// These kernels properly handle broadcasting by using the shape metadata
/// to correctly index into the input tensors.

const std = @import("std");
const types = @import("types");
const DataType = types.DataType;
const ComputeOp = types.ComputeOp;
const Shape = types.Shape;
const shape_mod = @import("shape");

// Import kernel types from comptime_kernels to avoid duplication
const comptime_kernels = @import("comptime_kernels.zig");
const KernelFn = comptime_kernels.KernelFn;
const KernelArgs = comptime_kernels.KernelArgs;

/// Generate broadcast-aware binary kernel at compile time
pub fn generateBroadcastAwareBinaryKernel(comptime op: ComputeOp, comptime dtype: DataType) type {
    return struct {
        pub fn kernel(args: KernelArgs) void {
            const T = comptime dtypeToZigType(dtype);
            const OpStruct = comptime selectOperation(op, T);
            const op_fn = OpStruct.operation;
            
            // Extract buffers with proper type casting
            const input_a = @as([*]const T, @ptrCast(@alignCast(args.inputs[0].ptr)));
            const input_b = @as([*]const T, @ptrCast(@alignCast(args.inputs[1].ptr)));
            const output = @as([*]T, @ptrCast(@alignCast(args.outputs[0].ptr)));
            
            // Get shapes - these contain the broadcasting information
            const shape_a = args.input_shapes[0];
            const shape_b = args.input_shapes[1];
            const shape_out = args.output_shapes[0];
            
            // Process each output element using canonical coordinate utilities
            const utils = @import("shape").utils;
            for (0..args.work_size) |flat_idx| {
                // Convert flat index to output coordinates using canonical utilities
                var output_coords: [8]usize = undefined;
                utils.flatToCoords(flat_idx, shape_out.dims, output_coords[0..shape_out.dims.len]);
                
                // Map output coordinates to input A coordinates (handle broadcasting)
                var input_a_coords: [8]usize = undefined;
                utils.mapCoordsForBroadcast(
                    output_coords[0..shape_out.dims.len],
                    shape_a.dims,
                    shape_out.dims,
                    input_a_coords[0..shape_a.dims.len]
                );
                
                // Map output coordinates to input B coordinates (handle broadcasting)
                var input_b_coords: [8]usize = undefined;
                utils.mapCoordsForBroadcast(
                    output_coords[0..shape_out.dims.len],
                    shape_b.dims,
                    shape_out.dims,
                    input_b_coords[0..shape_b.dims.len]
                );
                
                // Convert coordinates to physical indices using canonical utilities
                const idx_a = utils.coordsToFlat(input_a_coords[0..shape_a.dims.len], shape_a.strides);
                const idx_b = utils.coordsToFlat(input_b_coords[0..shape_b.dims.len], shape_b.strides);
                
                // Apply operation
                output[flat_idx] = op_fn(input_a[idx_a], input_b[idx_b]);
            }
        }
    };
}

/// Generate broadcast-aware unary kernel at compile time
pub fn generateBroadcastAwareUnaryKernel(comptime op: ComputeOp, comptime dtype: DataType) type {
    return struct {
        pub fn kernel(args: KernelArgs) void {
            const T = comptime dtypeToZigType(dtype);
            const OpStruct = comptime selectOperation(op, T);
            const op_fn = OpStruct.operation;
            
            // Extract buffers with proper type casting
            const input = @as([*]const T, @ptrCast(@alignCast(args.inputs[0].ptr)));
            const output = @as([*]T, @ptrCast(@alignCast(args.outputs[0].ptr)));
            
            // Get shapes
            const shape_in = args.input_shapes[0];
            const shape_out = args.output_shapes[0];
            
            // Process each output element using canonical coordinate utilities
            const utils = @import("shape").utils;
            for (0..args.work_size) |flat_idx| {
                // Convert flat index to output coordinates using canonical utilities
                var output_coords: [8]usize = undefined;
                utils.flatToCoords(flat_idx, shape_out.dims, output_coords[0..shape_out.dims.len]);
                
                // Map output coordinates to input coordinates (handle broadcasting)
                var input_coords: [8]usize = undefined;
                utils.mapCoordsForBroadcast(
                    output_coords[0..shape_out.dims.len],
                    shape_in.dims,
                    shape_out.dims,
                    input_coords[0..shape_in.dims.len]
                );
                
                // Convert coordinates to physical index using canonical utilities
                const idx_in = utils.coordsToFlat(input_coords[0..shape_in.dims.len], shape_in.strides);
                
                // Apply operation
                output[flat_idx] = op_fn(input[idx_in]);
            }
        }
    };
}

/// Convert DataType to Zig type at compile time
fn dtypeToZigType(comptime dtype: DataType) type {
    return switch (dtype) {
        .f16 => f16,
        .f32 => f32,
        .f64 => f64,
        .i8 => i8,
        .i16 => i16,
        .i32 => i32,
        .i64 => i64,
        .u8 => u8,
        .u16 => u16,
        .u32 => u32,
        .u64 => u64,
        .bool => bool,
    };
}

/// Select operation function at compile time
fn selectOperation(comptime op: ComputeOp, comptime T: type) type {
    return switch (op) {
        // Binary operations
        .add => struct {
            fn operation(a: T, b: T) T {
                return a + b;
            }
        },
        .mul => struct {
            fn operation(a: T, b: T) T {
                return a * b;
            }
        },
        .mod => struct {
            fn operation(a: T, b: T) T {
                return @mod(a, b);
            }
        },
        .less_than => struct {
            fn operation(a: T, b: T) T {
                return if (a < b) @as(T, 1) else @as(T, 0);
            }
        },
        
        // Unary operations
        .recip => struct {
            fn operation(a: T) T {
                return @as(T, 1) / a;
            }
        },
        .sqrt => struct {
            fn operation(a: T) T {
                return @sqrt(a);
            }
        },
        .sin => struct {
            fn operation(a: T) T {
                return @sin(a);
            }
        },
        .exp2 => struct {
            fn operation(a: T) T {
                return std.math.exp2(a);
            }
        },
        .log2 => struct {
            fn operation(a: T) T {
                return std.math.log2(a);
            }
        },
        
        else => @compileError("Unsupported operation for broadcast kernel"),
    };
}

/// Get broadcast-aware kernel for binary operations
pub fn getBroadcastAwareBinaryKernel(op: ComputeOp, dtype: DataType) KernelFn {
    // For now, only support f32
    if (dtype != .f32) {
        @panic("Only f32 dtype supported for broadcast kernels currently");
    }
    
    return switch (op) {
        .add => generateBroadcastAwareBinaryKernel(.add, .f32).kernel,
        .mul => generateBroadcastAwareBinaryKernel(.mul, .f32).kernel,
        .less_than => generateBroadcastAwareBinaryKernel(.less_than, .f32).kernel,
        .mod => generateBroadcastAwareBinaryKernel(.mod, .f32).kernel,
        else => @panic("Unsupported operation for broadcast kernel"),
    };
}

/// Get broadcast-aware kernel for unary operations
pub fn getBroadcastAwareUnaryKernel(op: ComputeOp, dtype: DataType) KernelFn {
    // For now, only support f32
    if (dtype != .f32) {
        @panic("Only f32 dtype supported for broadcast kernels currently");
    }
    
    return switch (op) {
        .recip => generateBroadcastAwareUnaryKernel(.recip, .f32).kernel,
        .sqrt => generateBroadcastAwareUnaryKernel(.sqrt, .f32).kernel,
        .sin => generateBroadcastAwareUnaryKernel(.sin, .f32).kernel,
        .exp2 => generateBroadcastAwareUnaryKernel(.exp2, .f32).kernel,
        .log2 => generateBroadcastAwareUnaryKernel(.log2, .f32).kernel,
        else => @panic("Unsupported operation for broadcast kernel"),
    };
}