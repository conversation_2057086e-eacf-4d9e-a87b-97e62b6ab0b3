/// Compile-time SIMD kernel specialization
const std = @import("std");
const builtin = @import("builtin");
const types = @import("types");
const DataType = types.DataType;
const ComputeOp = types.ComputeOp;
const Shape = types.Shape;
const NodeMetadata = @import("graph").NodeMetadata;

// Define kernel types locally to avoid circular dependencies
pub const KernelFn = *const fn (args: KernelArgs) void;
pub const KernelArgs = struct {
    inputs: []const []const u8,
    outputs: [][]u8,
    work_size: usize,
    custom_data: ?*anyopaque = null,
    input_shapes: []const Shape,
    output_shapes: []const Shape,
    node_metadata: ?*const NodeMetadata,
};

/// Detect SIMD capabilities at compile time
pub const SIMDCapabilities = struct {
    pub const has_sse = builtin.cpu.arch == .x86_64 and std.Target.x86.featureSetHas(builtin.cpu.features, .sse);
    pub const has_sse2 = builtin.cpu.arch == .x86_64 and std.Target.x86.featureSetHas(builtin.cpu.features, .sse2);
    pub const has_avx = builtin.cpu.arch == .x86_64 and std.Target.x86.featureSetHas(builtin.cpu.features, .avx);
    pub const has_avx2 = builtin.cpu.arch == .x86_64 and std.Target.x86.featureSetHas(builtin.cpu.features, .avx2);
    pub const has_avx512f = builtin.cpu.arch == .x86_64 and std.Target.x86.featureSetHas(builtin.cpu.features, .avx512f);
    pub const has_neon = builtin.cpu.arch == .aarch64;
    
    pub const vector_size = blk: {
        if (has_avx512f) break :blk 512;
        if (has_avx2 or has_avx) break :blk 256;
        if (has_sse2 or has_sse) break :blk 128;
        if (has_neon) break :blk 128;
        break :blk 64; // Fallback
    };
};

/// Generate SIMD kernel for specific operation and data type
pub fn SIMDKernel(comptime op: ComputeOp, comptime dtype: DataType) type {
    const T = dtypeToZigType(dtype);
    const vector_size = computeVectorSize(dtype);
    const VecType = @Vector(vector_size, T);
    
    return struct {
        pub const operation = op;
        pub const data_type = dtype;
        pub const vec_size = vector_size;
        
        /// Execute SIMD kernel
        pub fn execute(args: KernelArgs) void {
            const work_size = args.work_size;
            const vec_iterations = work_size / vec_size;
            const remainder = work_size % vec_size;
            
            switch (op) {
                .add => executeAdd(args, vec_iterations, remainder),
                .mul => executeMul(args, vec_iterations, remainder),
                .recip => executeRecip(args, vec_iterations, remainder),
                .sqrt => executeSqrt(args, vec_iterations, remainder),
                .sin => executeSin(args, vec_iterations, remainder),
                .exp2 => executeExp2(args, vec_iterations, remainder),
                .log2 => executeLog2(args, vec_iterations, remainder),
                else => executeGeneric(args), // Fallback
            }
        }
        
        fn executeAdd(args: KernelArgs, vec_iterations: usize, remainder: usize) void {
            const a = @as([*]const T, @ptrCast(@alignCast(args.inputs[0].ptr)));
            const b = @as([*]const T, @ptrCast(@alignCast(args.inputs[1].ptr)));
            const output = @as([*]T, @ptrCast(@alignCast(args.outputs[0].ptr)));
            
            // SIMD loop
            var i: usize = 0;
            while (i < vec_iterations) : (i += 1) {
                const offset = i * vec_size;
                const vec_a: VecType = a[offset..][0..vec_size].*;
                const vec_b: VecType = b[offset..][0..vec_size].*;
                const result = vec_a + vec_b;
                output[offset..][0..vec_size].* = result;
            }
            
            // Handle remainder
            if (remainder > 0) {
                const offset = vec_iterations * vec_size;
                for (offset..args.work_size) |idx| {
                    output[idx] = a[idx] + b[idx];
                }
            }
        }
        
        fn executeMul(args: KernelArgs, vec_iterations: usize, remainder: usize) void {
            const a = @as([*]const T, @ptrCast(@alignCast(args.inputs[0].ptr)));
            const b = @as([*]const T, @ptrCast(@alignCast(args.inputs[1].ptr)));
            const output = @as([*]T, @ptrCast(@alignCast(args.outputs[0].ptr)));
            
            // SIMD loop
            var i: usize = 0;
            while (i < vec_iterations) : (i += 1) {
                const offset = i * vec_size;
                const vec_a: VecType = a[offset..][0..vec_size].*;
                const vec_b: VecType = b[offset..][0..vec_size].*;
                const result = vec_a * vec_b;
                output[offset..][0..vec_size].* = result;
            }
            
            // Handle remainder
            if (remainder > 0) {
                const offset = vec_iterations * vec_size;
                for (offset..args.work_size) |idx| {
                    output[idx] = a[idx] * b[idx];
                }
            }
        }
        
        fn executeRecip(args: KernelArgs, vec_iterations: usize, remainder: usize) void {
            const a = @as([*]const T, @ptrCast(@alignCast(args.inputs[0].ptr)));
            const b = @as([*]const T, @ptrCast(@alignCast(args.inputs[1].ptr)));
            const output = @as([*]T, @ptrCast(@alignCast(args.outputs[0].ptr)));
            
            // SIMD loop
            var i: usize = 0;
            while (i < vec_iterations) : (i += 1) {
                const offset = i * vec_size;
                const vec_a: VecType = a[offset..][0..vec_size].*;
                const vec_b: VecType = b[offset..][0..vec_size].*;
                const result = vec_a - vec_b;
                output[offset..][0..vec_size].* = result;
            }
            
            // Handle remainder
            if (remainder > 0) {
                const offset = vec_iterations * vec_size;
                for (offset..args.work_size) |idx| {
                    output[idx] = a[idx] - b[idx];
                }
            }
        }
        
        fn executeSin(args: KernelArgs, vec_iterations: usize, remainder: usize) void {
            const a = @as([*]const T, @ptrCast(@alignCast(args.inputs[0].ptr)));
            const b = @as([*]const T, @ptrCast(@alignCast(args.inputs[1].ptr)));
            const output = @as([*]T, @ptrCast(@alignCast(args.outputs[0].ptr)));
            
            // SIMD loop
            var i: usize = 0;
            while (i < vec_iterations) : (i += 1) {
                const offset = i * vec_size;
                const vec_a: VecType = a[offset..][0..vec_size].*;
                const vec_b: VecType = b[offset..][0..vec_size].*;
                const result = vec_a / vec_b;
                output[offset..][0..vec_size].* = result;
            }
            
            // Handle remainder
            if (remainder > 0) {
                const offset = vec_iterations * vec_size;
                for (offset..args.work_size) |idx| {
                    output[idx] = a[idx] / b[idx];
                }
            }
        }
        
        fn executeExp2(args: KernelArgs, vec_iterations: usize, remainder: usize) void {
            const input = @as([*]const T, @ptrCast(@alignCast(args.inputs[0].ptr)));
            const output = @as([*]T, @ptrCast(@alignCast(args.outputs[0].ptr)));
            
            // SIMD loop
            var i: usize = 0;
            while (i < vec_iterations) : (i += 1) {
                const offset = i * vec_size;
                const vec_in: VecType = input[offset..][0..vec_size].*;
                const result = -vec_in;
                output[offset..][0..vec_size].* = result;
            }
            
            // Handle remainder
            if (remainder > 0) {
                const offset = vec_iterations * vec_size;
                for (offset..args.work_size) |idx| {
                    output[idx] = -input[idx];
                }
            }
        }
        
        fn executeLog2(args: KernelArgs, vec_iterations: usize, remainder: usize) void {
            const input = @as([*]const T, @ptrCast(@alignCast(args.inputs[0].ptr)));
            const output = @as([*]T, @ptrCast(@alignCast(args.outputs[0].ptr)));
            
            // SIMD loop
            var i: usize = 0;
            while (i < vec_iterations) : (i += 1) {
                const offset = i * vec_size;
                const vec_in: VecType = input[offset..][0..vec_size].*;
                const result = @abs(vec_in);
                output[offset..][0..vec_size].* = result;
            }
            
            // Handle remainder
            if (remainder > 0) {
                const offset = vec_iterations * vec_size;
                for (offset..args.work_size) |idx| {
                    output[idx] = @abs(input[idx]);
                }
            }
        }
        
        fn executeSqrt(args: KernelArgs, vec_iterations: usize, remainder: usize) void {
            const input = @as([*]const T, @ptrCast(@alignCast(args.inputs[0].ptr)));
            const output = @as([*]T, @ptrCast(@alignCast(args.outputs[0].ptr)));
            
            // SIMD loop
            var i: usize = 0;
            while (i < vec_iterations) : (i += 1) {
                const offset = i * vec_size;
                const vec_in: VecType = input[offset..][0..vec_size].*;
                const result = @sqrt(vec_in);
                output[offset..][0..vec_size].* = result;
            }
            
            // Handle remainder
            if (remainder > 0) {
                const offset = vec_iterations * vec_size;
                for (offset..args.work_size) |idx| {
                    output[idx] = @sqrt(input[idx]);
                }
            }
        }
        
        fn executeUnused1(args: KernelArgs, vec_iterations: usize, remainder: usize) void {
            const input = @as([*]const T, @ptrCast(@alignCast(args.inputs[0].ptr)));
            const output = @as([*]T, @ptrCast(@alignCast(args.outputs[0].ptr)));
            
            // SIMD loop with manual vectorization for exp
            var i: usize = 0;
            while (i < vec_iterations) : (i += 1) {
                const offset = i * vec_size;
                inline for (0..vec_size) |j| {
                    output[offset + j] = @exp(input[offset + j]);
                }
            }
            
            // Handle remainder
            if (remainder > 0) {
                const offset = vec_iterations * vec_size;
                for (offset..args.work_size) |idx| {
                    output[idx] = @exp(input[idx]);
                }
            }
        }
        
        fn executeUnused2(args: KernelArgs, vec_iterations: usize, remainder: usize) void {
            const input = @as([*]const T, @ptrCast(@alignCast(args.inputs[0].ptr)));
            const output = @as([*]T, @ptrCast(@alignCast(args.outputs[0].ptr)));
            
            // SIMD loop with manual vectorization for log
            var i: usize = 0;
            while (i < vec_iterations) : (i += 1) {
                const offset = i * vec_size;
                inline for (0..vec_size) |j| {
                    output[offset + j] = @log(input[offset + j]);
                }
            }
            
            // Handle remainder
            if (remainder > 0) {
                const offset = vec_iterations * vec_size;
                for (offset..args.work_size) |idx| {
                    output[idx] = @log(input[idx]);
                }
            }
        }
        
        fn executeGeneric(args: KernelArgs) void {
            // Fallback to scalar implementation
            _ = args;
            unreachable; // Should be handled by backend
        }
    };
}

/// Compute optimal vector size for data type
fn computeVectorSize(comptime dtype: DataType) usize {
    const base_size = SIMDCapabilities.vector_size;
    const element_bits = @sizeOf(dtypeToZigType(dtype)) * 8;
    return base_size / element_bits;
}

/// Convert DataType to Zig type
fn dtypeToZigType(comptime dtype: DataType) type {
    return switch (dtype) {
        .f16 => f16,
        .f32 => f32,
        .f64 => f64,
        .i8 => i8,
        .i16 => i16,
        .i32 => i32,
        .i64 => i64,
        .u8 => u8,
        .u16 => u16,
        .u32 => u32,
        .u64 => u64,
        .bool => bool,
    };
}

/// Generate SIMD kernel table at compile time
pub fn generateSIMDKernelTable(comptime ops: []const ComputeOp, comptime dtypes: []const DataType) type {
    _ = dtypes; // Will be used for type-specific tables
    return struct {
        const KernelTable = std.EnumArray(ComputeOp, ?KernelFn);
        
        pub fn getKernel(op: ComputeOp, dtype: DataType) ?KernelFn {
            // Generate kernel table for each data type
            return switch (dtype) {
                .f32 => f32_kernels.get(op),
                .f64 => f64_kernels.get(op),
                .i32 => i32_kernels.get(op),
                .i64 => i64_kernels.get(op),
                .u8 => u8_kernels.get(op),
                // Other types not supported in SIMD
                .f16, .i8, .i16, .u16, .u32, .u64, .bool => null,
            };
        }
        
        const f32_kernels = blk: {
            var table = KernelTable.initFill(null);
            for (ops) |op| {
                if (isSIMDSupported(op)) {
                    const Kernel = SIMDKernel(op, .f32);
                    table.set(op, Kernel.execute);
                }
            }
            break :blk table;
        };
        
        const f64_kernels = blk: {
            var table = KernelTable.initFill(null);
            for (ops) |op| {
                if (isSIMDSupported(op)) {
                    const Kernel = SIMDKernel(op, .f64);
                    table.set(op, Kernel.execute);
                }
            }
            break :blk table;
        };
        
        const i32_kernels = blk: {
            var table = KernelTable.initFill(null);
            for (ops) |op| {
                if (isSIMDSupported(op) and isIntegerOp(op)) {
                    const Kernel = SIMDKernel(op, .i32);
                    table.set(op, Kernel.execute);
                }
            }
            break :blk table;
        };
        
        const i64_kernels = blk: {
            var table = KernelTable.initFill(null);
            for (ops) |op| {
                if (isSIMDSupported(op) and isIntegerOp(op)) {
                    const Kernel = SIMDKernel(op, .i64);
                    table.set(op, Kernel.execute);
                }
            }
            break :blk table;
        };
        
        const u8_kernels = blk: {
            var table = KernelTable.initFill(null);
            for (ops) |op| {
                if (isSIMDSupported(op) and isIntegerOp(op)) {
                    const Kernel = SIMDKernel(op, .u8);
                    table.set(op, Kernel.execute);
                }
            }
            break :blk table;
        };
    };
}

/// Check if operation supports SIMD
fn isSIMDSupported(comptime op: ComputeOp) bool {
    return switch (op) {
        .add, .mul, .recip, .sqrt => true,
        .exp2, .log2 => true, // Manual vectorization
        else => false,
    };
}

/// Check if operation is valid for integer types
fn isIntegerOp(comptime op: ComputeOp) bool {
    return switch (op) {
        .add, .mul, .mod, .less_than => true,
        .recip => false, // Integer reciprocal has different semantics
        .sqrt, .sin, .exp2, .log2 => false, // Float only
        else => false,
    };
}

/// Specialized SIMD patterns for common operations
pub const SIMDPatterns = struct {
    /// FMA (Fused Multiply-Add) pattern
    pub fn FMA(comptime T: type) type {
        const vec_size = computeVectorSize(typeToDataType(T));
        const VecType = @Vector(vec_size, T);
        
        return struct {
            pub inline fn compute(a: VecType, b: VecType, c: VecType) VecType {
                return @mulAdd(VecType, a, b, c);
            }
        };
    }
    
    /// Horizontal sum reduction
    pub fn HorizontalSum(comptime T: type) type {
        const vec_size = computeVectorSize(typeToDataType(T));
        const VecType = @Vector(vec_size, T);
        
        return struct {
            pub inline fn reduce(vec: VecType) T {
                return @reduce(.Add, vec);
            }
        };
    }
    
    /// Horizontal max reduction
    pub fn HorizontalMax(comptime T: type) type {
        const vec_size = computeVectorSize(typeToDataType(T));
        const VecType = @Vector(vec_size, T);
        
        return struct {
            pub inline fn reduce(vec: VecType) T {
                return @reduce(.Max, vec);
            }
        };
    }
};

fn typeToDataType(comptime T: type) DataType {
    return switch (T) {
        f32 => .f32,
        f64 => .f64,
        i32 => .i32,
        i64 => .i64,
        u8 => .u8,
        else => @compileError("Unsupported type"),
    };
}

// ===== Tests =====

const testing = std.testing;

test "SIMD capabilities detection" {
    // Just verify we can access the capabilities
    const has_any_simd = SIMDCapabilities.has_sse or 
                         SIMDCapabilities.has_avx or 
                         SIMDCapabilities.has_neon;
    _ = has_any_simd;
    
    try testing.expect(SIMDCapabilities.vector_size >= 64);
}

test "SIMD kernel generation" {
    const AddKernel = SIMDKernel(.add, .f32);
    try testing.expectEqual(ComputeOp.add, AddKernel.operation);
    try testing.expectEqual(DataType.f32, AddKernel.data_type);
    try testing.expect(AddKernel.vec_size > 0);
}

test "SIMD vector operations" {
    const allocator = testing.allocator;
    
    // Create test data
    const size = 64;
    const a = try allocator.alignedAlloc(f32, 32, size);
    defer allocator.free(a);
    const b = try allocator.alignedAlloc(f32, 32, size);
    defer allocator.free(b);
    const c = try allocator.alignedAlloc(f32, 32, size);
    defer allocator.free(c);
    
    // Initialize
    for (a, 0..) |*val, i| val.* = @floatFromInt(i);
    for (b, 0..) |*val, i| val.* = @floatFromInt(i * 2);
    
    // Create kernel args
    const args = KernelArgs{
        .inputs = &.{
            .{ .ptr = @ptrCast(a), .len = size * @sizeOf(f32) },
            .{ .ptr = @ptrCast(b), .len = size * @sizeOf(f32) },
        },
        .outputs = &.{
            .{ .ptr = @ptrCast(c), .len = size * @sizeOf(f32) },
        },
        .work_size = size,
        .input_shapes = &.{},
        .output_shapes = &.{},
        .node_metadata = null,
        .custom_data = null,
    };
    
    // Execute SIMD add
    const AddKernel = SIMDKernel(.add, .f32);
    AddKernel.execute(args);
    
    // Verify results
    for (c, 0..) |val, i| {
        const expected = @as(f32, @floatFromInt(i)) + @as(f32, @floatFromInt(i * 2));
        try testing.expectApproxEqRel(expected, val, 0.001);
    }
}