// High-performance portable GEMM implementation for Zing
// Based on <PERSON><PERSON>'s algorithm and BLIS design principles
// This version focuses on cache efficiency and instruction-level parallelism

#include <stddef.h>
#include <stdlib.h>
#include <string.h>
#include <stdbool.h>

// Architecture detection
#if defined(__ARM_NEON) || defined(__aarch64__)
    #include <arm_neon.h>
    #define HAS_NEON 1
#endif

#if defined(__AVX2__)
    #include <immintrin.h>
    #define HAS_AVX2 1
#endif

// Optimized blocking parameters (tuned for modern CPUs)
// These match or exceed matrixmultiply's parameters
#define MC  256  // Rows of A in L2 cache block
#define KC  256  // Shared dimension in L1/L2 cache block  
#define NC  256  // Cols of B in L3 cache block
#define MR  8    // Rows in micro-kernel
#define NR  12   // Cols in micro-kernel

// Align for SIMD and cache lines
#define ALIGN_SIZE 64

// Portable aligned allocation
static inline void* aligned_alloc_portable(size_t alignment, size_t size) {
#if defined(_MSC_VER)
    return _aligned_malloc(size, alignment);
#elif defined(__APPLE__)
    void* ptr;
    if (posix_memalign(&ptr, alignment, size) != 0) return NULL;
    return ptr;
#else
    return aligned_alloc(alignment, size);
#endif
}

static inline void aligned_free_portable(void* ptr) {
#if defined(_MSC_VER)
    _aligned_free(ptr);
#else
    free(ptr);
#endif
}

// Pack A into column-major panels (for L1 cache efficiency)
static void pack_a(
    size_t mc, size_t kc,
    const float* restrict a, ptrdiff_t rsa, ptrdiff_t csa,
    float* restrict packed_a
) {
    const size_t mr = MR;
    const size_t mp = (mc + mr - 1) / mr;
    
    for (size_t j = 0; j < kc; j++) {
        for (size_t i = 0; i < mc; i++) {
            size_t panel = i / mr;
            size_t idx = panel * mr * kc + j * mr + (i % mr);
            packed_a[idx] = a[i * rsa + j * csa];
        }
        // Pad to MR
        for (size_t i = mc; i < mp * mr; i++) {
            size_t panel = i / mr;
            size_t idx = panel * mr * kc + j * mr + (i % mr);
            packed_a[idx] = 0.0f;
        }
    }
}

// Pack B into row-major panels (for L1 cache efficiency)
static void pack_b(
    size_t kc, size_t nc,
    const float* restrict b, ptrdiff_t rsb, ptrdiff_t csb,
    float* restrict packed_b
) {
    const size_t nr = NR;
    const size_t np = (nc + nr - 1) / nr;
    
    for (size_t j = 0; j < nc; j++) {
        size_t panel = j / nr;
        for (size_t i = 0; i < kc; i++) {
            size_t idx = panel * nr * kc + i * nr + (j % nr);
            packed_b[idx] = b[i * rsb + j * csb];
        }
    }
    // Pad remaining columns
    for (size_t j = nc; j < np * nr; j++) {
        size_t panel = j / nr;
        for (size_t i = 0; i < kc; i++) {
            size_t idx = panel * nr * kc + i * nr + (j % nr);
            packed_b[idx] = 0.0f;
        }
    }
}

#ifdef HAS_NEON
// Optimized 8x12 micro-kernel for ARM NEON
static void micro_kernel_8x12_neon(
    size_t k,
    const float* restrict a,
    const float* restrict b,
    float* restrict c,
    size_t ldc
) {
    float32x4_t c00 = vdupq_n_f32(0), c01 = vdupq_n_f32(0), c02 = vdupq_n_f32(0);
    float32x4_t c10 = vdupq_n_f32(0), c11 = vdupq_n_f32(0), c12 = vdupq_n_f32(0);
    float32x4_t c20 = vdupq_n_f32(0), c21 = vdupq_n_f32(0), c22 = vdupq_n_f32(0);
    float32x4_t c30 = vdupq_n_f32(0), c31 = vdupq_n_f32(0), c32 = vdupq_n_f32(0);
    float32x4_t c40 = vdupq_n_f32(0), c41 = vdupq_n_f32(0), c42 = vdupq_n_f32(0);
    float32x4_t c50 = vdupq_n_f32(0), c51 = vdupq_n_f32(0), c52 = vdupq_n_f32(0);
    float32x4_t c60 = vdupq_n_f32(0), c61 = vdupq_n_f32(0), c62 = vdupq_n_f32(0);
    float32x4_t c70 = vdupq_n_f32(0), c71 = vdupq_n_f32(0), c72 = vdupq_n_f32(0);
    
    const float* a_ptr = a;
    const float* b_ptr = b;
    
    // Main loop - unroll by 4 for better performance
    size_t k_iter = k / 4;
    size_t k_left = k % 4;
    
    for (size_t p = 0; p < k_iter; p++) {
        // Unrolled iteration 1
        float32x4_t b0 = vld1q_f32(b_ptr); b_ptr += 4;
        float32x4_t b1 = vld1q_f32(b_ptr); b_ptr += 4;
        float32x4_t b2 = vld1q_f32(b_ptr); b_ptr += 4;
        
        float32x4_t a0 = vld1q_f32(a_ptr); a_ptr += 4;
        float32x4_t a1 = vld1q_f32(a_ptr); a_ptr += 4;
        
        c00 = vfmaq_laneq_f32(c00, a0, b0, 0);
        c01 = vfmaq_laneq_f32(c01, a0, b1, 0);
        c02 = vfmaq_laneq_f32(c02, a0, b2, 0);
        c10 = vfmaq_laneq_f32(c10, a1, b0, 0);
        c11 = vfmaq_laneq_f32(c11, a1, b1, 0);
        c12 = vfmaq_laneq_f32(c12, a1, b2, 0);
        
        c20 = vfmaq_laneq_f32(c20, a0, b0, 1);
        c21 = vfmaq_laneq_f32(c21, a0, b1, 1);
        c22 = vfmaq_laneq_f32(c22, a0, b2, 1);
        c30 = vfmaq_laneq_f32(c30, a1, b0, 1);
        c31 = vfmaq_laneq_f32(c31, a1, b1, 1);
        c32 = vfmaq_laneq_f32(c32, a1, b2, 1);
        
        c40 = vfmaq_laneq_f32(c40, a0, b0, 2);
        c41 = vfmaq_laneq_f32(c41, a0, b1, 2);
        c42 = vfmaq_laneq_f32(c42, a0, b2, 2);
        c50 = vfmaq_laneq_f32(c50, a1, b0, 2);
        c51 = vfmaq_laneq_f32(c51, a1, b1, 2);
        c52 = vfmaq_laneq_f32(c52, a1, b2, 2);
        
        c60 = vfmaq_laneq_f32(c60, a0, b0, 3);
        c61 = vfmaq_laneq_f32(c61, a0, b1, 3);
        c62 = vfmaq_laneq_f32(c62, a0, b2, 3);
        c70 = vfmaq_laneq_f32(c70, a1, b0, 3);
        c71 = vfmaq_laneq_f32(c71, a1, b1, 3);
        c72 = vfmaq_laneq_f32(c72, a1, b2, 3);
    }
    
    // Handle remaining iterations
    for (size_t p = 0; p < k_left; p++) {
        float32x4_t a0 = vld1q_f32(a_ptr); a_ptr += 4;
        float32x4_t a1 = vld1q_f32(a_ptr); a_ptr += 4;
        
        float b_vals[12];
        for (int i = 0; i < 12; i++) b_vals[i] = b_ptr[i];
        b_ptr += 12;
        
        c00 = vfmaq_n_f32(c00, a0, b_vals[0]);
        c01 = vfmaq_n_f32(c01, a0, b_vals[4]);
        c02 = vfmaq_n_f32(c02, a0, b_vals[8]);
        c10 = vfmaq_n_f32(c10, a1, b_vals[0]);
        c11 = vfmaq_n_f32(c11, a1, b_vals[4]);
        c12 = vfmaq_n_f32(c12, a1, b_vals[8]);
        
        c20 = vfmaq_n_f32(c20, a0, b_vals[1]);
        c21 = vfmaq_n_f32(c21, a0, b_vals[5]);
        c22 = vfmaq_n_f32(c22, a0, b_vals[9]);
        c30 = vfmaq_n_f32(c30, a1, b_vals[1]);
        c31 = vfmaq_n_f32(c31, a1, b_vals[5]);
        c32 = vfmaq_n_f32(c32, a1, b_vals[9]);
        
        c40 = vfmaq_n_f32(c40, a0, b_vals[2]);
        c41 = vfmaq_n_f32(c41, a0, b_vals[6]);
        c42 = vfmaq_n_f32(c42, a0, b_vals[10]);
        c50 = vfmaq_n_f32(c50, a1, b_vals[2]);
        c51 = vfmaq_n_f32(c51, a1, b_vals[6]);
        c52 = vfmaq_n_f32(c52, a1, b_vals[10]);
        
        c60 = vfmaq_n_f32(c60, a0, b_vals[3]);
        c61 = vfmaq_n_f32(c61, a0, b_vals[7]);
        c62 = vfmaq_n_f32(c62, a0, b_vals[11]);
        c70 = vfmaq_n_f32(c70, a1, b_vals[3]);
        c71 = vfmaq_n_f32(c71, a1, b_vals[7]);
        c72 = vfmaq_n_f32(c72, a1, b_vals[11]);
    }
    
    // Store results - this is an 8x12 kernel, need to store all 8 rows
    // Row 0-3
    vst1q_f32(&c[0 * ldc + 0], vaddq_f32(vld1q_f32(&c[0 * ldc + 0]), c00));
    vst1q_f32(&c[0 * ldc + 4], vaddq_f32(vld1q_f32(&c[0 * ldc + 4]), c01));
    vst1q_f32(&c[0 * ldc + 8], vaddq_f32(vld1q_f32(&c[0 * ldc + 8]), c02));
    
    vst1q_f32(&c[1 * ldc + 0], vaddq_f32(vld1q_f32(&c[1 * ldc + 0]), c10));
    vst1q_f32(&c[1 * ldc + 4], vaddq_f32(vld1q_f32(&c[1 * ldc + 4]), c11));
    vst1q_f32(&c[1 * ldc + 8], vaddq_f32(vld1q_f32(&c[1 * ldc + 8]), c12));
    
    vst1q_f32(&c[2 * ldc + 0], vaddq_f32(vld1q_f32(&c[2 * ldc + 0]), c20));
    vst1q_f32(&c[2 * ldc + 4], vaddq_f32(vld1q_f32(&c[2 * ldc + 4]), c21));
    vst1q_f32(&c[2 * ldc + 8], vaddq_f32(vld1q_f32(&c[2 * ldc + 8]), c22));
    
    vst1q_f32(&c[3 * ldc + 0], vaddq_f32(vld1q_f32(&c[3 * ldc + 0]), c30));
    vst1q_f32(&c[3 * ldc + 4], vaddq_f32(vld1q_f32(&c[3 * ldc + 4]), c31));
    vst1q_f32(&c[3 * ldc + 8], vaddq_f32(vld1q_f32(&c[3 * ldc + 8]), c32));
    
    // Row 4-7
    vst1q_f32(&c[4 * ldc + 0], vaddq_f32(vld1q_f32(&c[4 * ldc + 0]), c40));
    vst1q_f32(&c[4 * ldc + 4], vaddq_f32(vld1q_f32(&c[4 * ldc + 4]), c41));
    vst1q_f32(&c[4 * ldc + 8], vaddq_f32(vld1q_f32(&c[4 * ldc + 8]), c42));
    
    vst1q_f32(&c[5 * ldc + 0], vaddq_f32(vld1q_f32(&c[5 * ldc + 0]), c50));
    vst1q_f32(&c[5 * ldc + 4], vaddq_f32(vld1q_f32(&c[5 * ldc + 4]), c51));
    vst1q_f32(&c[5 * ldc + 8], vaddq_f32(vld1q_f32(&c[5 * ldc + 8]), c52));
    
    vst1q_f32(&c[6 * ldc + 0], vaddq_f32(vld1q_f32(&c[6 * ldc + 0]), c60));
    vst1q_f32(&c[6 * ldc + 4], vaddq_f32(vld1q_f32(&c[6 * ldc + 4]), c61));
    vst1q_f32(&c[6 * ldc + 8], vaddq_f32(vld1q_f32(&c[6 * ldc + 8]), c62));
    
    vst1q_f32(&c[7 * ldc + 0], vaddq_f32(vld1q_f32(&c[7 * ldc + 0]), c70));
    vst1q_f32(&c[7 * ldc + 4], vaddq_f32(vld1q_f32(&c[7 * ldc + 4]), c71));
    vst1q_f32(&c[7 * ldc + 8], vaddq_f32(vld1q_f32(&c[7 * ldc + 8]), c72));
}
#endif

// Portable micro-kernel (fallback)
static void micro_kernel_portable(
    size_t mr, size_t nr, size_t k,
    const float* restrict a,
    const float* restrict b,
    float* restrict c,
    size_t ldc
) {
    // Use 2x4 register blocking for better ILP
    for (size_t i = 0; i < mr; i += 2) {
        for (size_t j = 0; j < nr; j += 4) {
            float c00 = 0, c01 = 0, c02 = 0, c03 = 0;
            float c10 = 0, c11 = 0, c12 = 0, c13 = 0;
            
            if (i + 1 < mr && j + 3 < nr) {
                // Main 2x4 block
                for (size_t p = 0; p < k; p++) {
                    float a0 = a[i * k + p];
                    float a1 = a[(i + 1) * k + p];
                    float b0 = b[p * nr + j];
                    float b1 = b[p * nr + j + 1];
                    float b2 = b[p * nr + j + 2];
                    float b3 = b[p * nr + j + 3];
                    
                    c00 += a0 * b0; c01 += a0 * b1; c02 += a0 * b2; c03 += a0 * b3;
                    c10 += a1 * b0; c11 += a1 * b1; c12 += a1 * b2; c13 += a1 * b3;
                }
                c[i * ldc + j] += c00;
                c[i * ldc + j + 1] += c01;
                c[i * ldc + j + 2] += c02;
                c[i * ldc + j + 3] += c03;
                c[(i + 1) * ldc + j] += c10;
                c[(i + 1) * ldc + j + 1] += c11;
                c[(i + 1) * ldc + j + 2] += c12;
                c[(i + 1) * ldc + j + 3] += c13;
            } else {
                // Edge cases
                for (size_t ii = i; ii < mr && ii < i + 2; ii++) {
                    for (size_t jj = j; jj < nr && jj < j + 4; jj++) {
                        float sum = 0;
                        for (size_t p = 0; p < k; p++) {
                            sum += a[ii * k + p] * b[p * nr + jj];
                        }
                        c[ii * ldc + jj] += sum;
                    }
                }
            }
        }
    }
}

// Macro-kernel that processes mc x nc block
static void macro_kernel(
    size_t mc, size_t nc, size_t kc,
    float alpha,
    const float* restrict packed_a,
    const float* restrict packed_b,
    float beta,
    float* restrict c, ptrdiff_t rsc, ptrdiff_t csc
) {
    const size_t mr = MR;
    const size_t nr = NR;
    const size_t mp = (mc + mr - 1) / mr;
    const size_t np = (nc + nr - 1) / nr;
    
    // Allocate temporary C buffer for micro-kernel results
    float* c_buffer = (float*)aligned_alloc_portable(ALIGN_SIZE, mr * nr * sizeof(float));
    if (!c_buffer) return;
    
    for (size_t jr = 0; jr < np; jr++) {
        size_t nr_cur = (jr != np - 1 || nc % nr == 0) ? nr : (nc % nr);
        
        for (size_t ir = 0; ir < mp; ir++) {
            size_t mr_cur = (ir != mp - 1 || mc % mr == 0) ? mr : (mc % mr);
            
            // Clear C buffer
            memset(c_buffer, 0, mr * nr * sizeof(float));
            
            // Call micro-kernel
            #ifdef HAS_NEON
            if (mr_cur == MR && nr_cur == NR) {
                micro_kernel_8x12_neon(
                    kc,
                    packed_a + ir * mr * kc,
                    packed_b + jr * nr * kc,
                    c_buffer,
                    nr
                );
            } else
            #endif
            {
                micro_kernel_portable(
                    mr_cur, nr_cur, kc,
                    packed_a + ir * mr * kc,
                    packed_b + jr * nr * kc,
                    c_buffer,
                    nr
                );
            }
            
            // Update C matrix
            if (beta == 0.0f) {
                for (size_t i = 0; i < mr_cur; i++) {
                    for (size_t j = 0; j < nr_cur; j++) {
                        c[(ir * mr + i) * rsc + (jr * nr + j) * csc] = 
                            alpha * c_buffer[i * nr + j];
                    }
                }
            } else {
                for (size_t i = 0; i < mr_cur; i++) {
                    for (size_t j = 0; j < nr_cur; j++) {
                        c[(ir * mr + i) * rsc + (jr * nr + j) * csc] = 
                            beta * c[(ir * mr + i) * rsc + (jr * nr + j) * csc] +
                            alpha * c_buffer[i * nr + j];
                    }
                }
            }
        }
    }
    
    aligned_free_portable(c_buffer);
}

// Main SGEMM function with Goto's algorithm
void zing_sgemm(
    size_t m, size_t k, size_t n,
    float alpha,
    const float* a, ptrdiff_t rsa, ptrdiff_t csa,
    const float* b, ptrdiff_t rsb, ptrdiff_t csb,
    float beta,
    float* c, ptrdiff_t rsc, ptrdiff_t csc
) {
    // Quick return if possible
    if (m == 0 || n == 0 || k == 0 || (alpha == 0.0f && beta == 1.0f)) {
        return;
    }
    
    // Handle beta
    if (beta == 0.0f) {
        for (size_t i = 0; i < m; i++) {
            for (size_t j = 0; j < n; j++) {
                c[i * rsc + j * csc] = 0.0f;
            }
        }
    } else if (beta != 1.0f) {
        for (size_t i = 0; i < m; i++) {
            for (size_t j = 0; j < n; j++) {
                c[i * rsc + j * csc] *= beta;
            }
        }
        beta = 1.0f;
    }
    
    // For small matrices, use simple algorithm
    if (m * n * k < 64 * 64 * 64) {
        for (size_t i = 0; i < m; i++) {
            for (size_t j = 0; j < n; j++) {
                float sum = 0.0f;
                for (size_t p = 0; p < k; p++) {
                    sum += a[i * rsa + p * csa] * b[p * rsb + j * csb];
                }
                c[i * rsc + j * csc] += alpha * sum;
            }
        }
        return;
    }
    
    // Allocate packing buffers
    float* packed_a = (float*)aligned_alloc_portable(ALIGN_SIZE, MC * KC * sizeof(float));
    float* packed_b = (float*)aligned_alloc_portable(ALIGN_SIZE, KC * NC * sizeof(float));
    
    if (!packed_a || !packed_b) {
        aligned_free_portable(packed_a);
        aligned_free_portable(packed_b);
        // Fallback to simple implementation
        for (size_t i = 0; i < m; i++) {
            for (size_t j = 0; j < n; j++) {
                float sum = 0.0f;
                for (size_t p = 0; p < k; p++) {
                    sum += a[i * rsa + p * csa] * b[p * rsb + j * csb];
                }
                c[i * rsc + j * csc] += alpha * sum;
            }
        }
        return;
    }
    
    // Main loops following Goto's algorithm
    for (size_t jc = 0; jc < n; jc += NC) {
        size_t nc = (jc + NC <= n) ? NC : (n - jc);
        
        for (size_t pc = 0; pc < k; pc += KC) {
            size_t kc = (pc + KC <= k) ? KC : (k - pc);
            float beta_use = (pc == 0) ? beta : 1.0f;
            
            // Pack B once for all iterations over mc
            pack_b(kc, nc, b + pc * rsb + jc * csb, rsb, csb, packed_b);
            
            for (size_t ic = 0; ic < m; ic += MC) {
                size_t mc = (ic + MC <= m) ? MC : (m - ic);
                
                // Pack A for this block
                pack_a(mc, kc, a + ic * rsa + pc * csa, rsa, csa, packed_a);
                
                // Call macro-kernel
                macro_kernel(
                    mc, nc, kc,
                    alpha,
                    packed_a,
                    packed_b,
                    beta_use,
                    c + ic * rsc + jc * csc,
                    rsc, csc
                );
            }
        }
    }
    
    aligned_free_portable(packed_a);
    aligned_free_portable(packed_b);
}