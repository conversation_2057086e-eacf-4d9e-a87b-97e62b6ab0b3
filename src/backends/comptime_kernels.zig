/// Compile-time kernel specialization for optimal performance
const std = @import("std");
const types = @import("types");
const DataType = types.DataType;
const ComputeOp = types.ComputeOp;
const Shape = types.Shape;
const NodeMetadata = @import("graph").NodeMetadata;

// Define kernel types locally to avoid circular dependencies
pub const KernelFn = *const fn (args: KernelArgs) void;
pub const KernelArgs = struct {
    inputs: []const []const u8,
    outputs: [][]u8,
    work_size: usize,
    custom_data: ?*anyopaque = null,
    input_shapes: []const Shape,
    output_shapes: []const Shape,
    node_metadata: ?*const NodeMetadata,
};

/// Generate specialized kernel at compile time
pub fn generateKernel(comptime op: ComputeOp, comptime dtype: DataType) type {
    return struct {
        pub fn kernel(args: KernelArgs) void {
            const T = comptime dtypeToZigType(dtype);
            const OpStruct = comptime selectOperation(op, T);
            const op_fn = OpStruct.operation;
            
            // Extract buffers with proper type casting
            const input_a = @as([*]const T, @ptrCast(@alignCast(args.inputs[0].ptr)));
            const output = @as([*]T, @ptrCast(@alignCast(args.outputs[0].ptr)));
            
            // Handle different arities
            switch (comptime getOpArity(op)) {
                1 => {
                    // Unary operation
                    for (0..args.work_size) |i| {
                        output[i] = op_fn(input_a[i]);
                    }
                },
                2 => {
                    // Binary operation
                    const input_b = @as([*]const T, @ptrCast(@alignCast(args.inputs[1].ptr)));
                    for (0..args.work_size) |i| {
                        output[i] = op_fn(input_a[i], input_b[i]);
                    }
                },
                else => @compileError("Unsupported operation arity"),
            }
        }
    };
}

/// Convert DataType to Zig type at compile time
fn dtypeToZigType(comptime dtype: DataType) type {
    return switch (dtype) {
        .f16 => f16,
        .f32 => f32,
        .f64 => f64,
        .i8 => i8,
        .i16 => i16,
        .i32 => i32,
        .i64 => i64,
        .u8 => u8,
        .u16 => u16,
        .u32 => u32,
        .u64 => u64,
        .bool => bool,
    };
}

/// Get operation arity at compile time
fn getOpArity(comptime op: ComputeOp) u8 {
    return switch (op) {
        // Unary operations
        .recip, .sqrt, .sin, .exp2, .log2 => 1,
        // Binary operations
        .add, .mul, .mod, .less_than => 2,
        // Special operations - handled differently
        .sum_reduce, .max_reduce, .contiguous, .custom => 1,
    };
}

/// Select operation function at compile time
fn selectOperation(comptime op: ComputeOp, comptime T: type) type {
    return switch (op) {
        // Binary operations
        .add => struct {
            fn operation(a: T, b: T) T {
                return a + b;
            }
        },
        .mul => struct {
            fn operation(a: T, b: T) T {
                return a * b;
            }
        },
        .mod => struct {
            fn operation(a: T, b: T) T {
                return @mod(a, b);
            }
        },
        .less_than => struct {
            fn operation(a: T, b: T) T {
                return if (a < b) @as(T, 1) else @as(T, 0);
            }
        },
        
        // Unary operations
        .recip => struct {
            fn operation(a: T) T {
                return @as(T, 1) / a;
            }
        },
        .sqrt => struct {
            fn operation(a: T) T {
                return @sqrt(a);
            }
        },
        .sin => struct {
            fn operation(a: T) T {
                return @sin(a);
            }
        },
        .exp2 => struct {
            fn operation(a: T) T {
                return std.math.exp2(a);
            }
        },
        .log2 => struct {
            fn operation(a: T) T {
                return std.math.log2(a);
            }
        },
        
        // Reduction operations - these need special handling
        .sum_reduce, .max_reduce => struct {
            fn operation(a: T) T {
                return a; // Placeholder - reductions are handled differently
            }
        },
        
        // Memory operations - these need special handling  
        .contiguous => struct {
            fn operation(a: T) T {
                return a; // Placeholder - memory ops are handled differently
            }
        },
        
        // Custom operations - these need special handling
        .custom => struct {
            fn operation(a: T) T {
                return a; // Placeholder - custom ops are handled differently
            }
        },
    };
}

/// Generate SIMD kernel at compile time
pub fn generateSimdKernel(comptime op: ComputeOp, comptime dtype: DataType, comptime vector_width: u32) type {
    return struct {
        pub fn kernel(args: KernelArgs) void {
            const T = comptime dtypeToZigType(dtype);
            const VecT = @Vector(vector_width, T);
            const OpStruct = comptime selectOperation(op, T);
            const op_fn = OpStruct.operation;
            
            const input_a = @as([*]const T, @ptrCast(@alignCast(args.inputs[0].ptr)));
            const output = @as([*]T, @ptrCast(@alignCast(args.outputs[0].ptr)));
            
            const vec_size = args.work_size / vector_width;
            const remainder = args.work_size % vector_width;
            _ = remainder; // TODO: Handle remainder
            
            switch (comptime getOpArity(op)) {
                1 => {
                    // Vectorized unary operation
                    var i: usize = 0;
                    while (i < vec_size) : (i += 1) {
                        const vec_a: VecT = input_a[i * vector_width..][0..vector_width].*;
                        const vec_result = simdUnaryOp(VecT, op, vec_a);
                        @memcpy(output[i * vector_width..][0..vector_width], &vec_result);
                    }
                    
                    // Handle remainder
                    const base = vec_size * vector_width;
                    for (base..args.work_size) |j| {
                        output[j] = op_fn(input_a[j]);
                    }
                },
                2 => {
                    const input_b = @as([*]const T, @ptrCast(@alignCast(args.inputs[1].ptr)));
                    
                    // Vectorized binary operation
                    var i: usize = 0;
                    while (i < vec_size) : (i += 1) {
                        const vec_a: VecT = input_a[i * vector_width..][0..vector_width].*;
                        const vec_b: VecT = input_b[i * vector_width..][0..vector_width].*;
                        const vec_result = simdBinaryOp(VecT, op, vec_a, vec_b);
                        @memcpy(output[i * vector_width..][0..vector_width], &vec_result);
                    }
                    
                    // Handle remainder
                    const base = vec_size * vector_width;
                    for (base..args.work_size) |j| {
                        output[j] = op_fn(input_a[j], input_b[j]);
                    }
                },
                else => unreachable,
            }
        }
    };
}

/// SIMD unary operation
fn simdUnaryOp(comptime VecT: type, comptime op: ComputeOp, a: VecT) VecT {
    return switch (op) {
        .neg => -a,
        .abs => @abs(a),
        .sqrt => @sqrt(a),
        .exp => @exp(a),
        .log => @log(a),
        else => @compileError("Unsupported SIMD unary operation"),
    };
}

/// SIMD binary operation
fn simdBinaryOp(comptime VecT: type, comptime op: ComputeOp, a: VecT, b: VecT) VecT {
    return switch (op) {
        .add => a + b,
        .sub => a - b,
        .mul => a * b,
        .div => a / b,
        else => @compileError("Unsupported SIMD binary operation"),
    };
}

/// Kernel registry with compile-time specialization
pub const ComptimeKernelRegistry = struct {
    /// Get specialized kernel for operation and dtype
    pub fn getKernel(comptime op: ComputeOp, comptime dtype: DataType) KernelFn {
        const Kernel = generateKernel(op, dtype);
        return Kernel.kernel;
    }
    
    /// Get SIMD kernel if available
    pub fn getSimdKernel(comptime op: ComputeOp, comptime dtype: DataType, comptime vector_width: u32) KernelFn {
        const Kernel = generateSimdKernel(op, dtype, vector_width);
        return Kernel.kernel;
    }
    
    /// Check if operation supports SIMD
    pub fn supportsSimd(comptime op: ComputeOp) bool {
        return switch (op) {
            .add, .sub, .mul, .div, .neg, .abs, .sqrt, .exp, .log => true,
            else => false,
        };
    }
};

/// Generate kernel dispatch table at compile time
pub fn generateKernelTable(comptime ops: []const ComputeOp, comptime dtypes: []const DataType) type {
    return struct {
        const Self = @This();
        
        pub const Entry = struct {
            op: ComputeOp,
            dtype: DataType,
            kernel: KernelFn,
        };
        
        pub const table = blk: {
            var result: [ops.len * dtypes.len]Entry = undefined;
            var idx: usize = 0;
            
            for (ops) |op| {
                for (dtypes) |dtype| {
                    // Skip unsupported combinations
                    if (requiresFloat(op) and !isFloat(dtype)) continue;
                    
                    result[idx] = .{
                        .op = op,
                        .dtype = dtype,
                        .kernel = ComptimeKernelRegistry.getKernel(op, dtype),
                    };
                    idx += 1;
                }
            }
            
            const final_result = result[0..idx];
            break :blk final_result.*;
        };
        
        pub fn getKernel(op: ComputeOp, dtype: DataType) ?KernelFn {
            inline for (&table) |entry| {
                if (entry.op == op and entry.dtype == dtype) {
                    return entry.kernel;
                }
            }
            return null;
        }
    };
}

fn requiresFloat(comptime op: ComputeOp) bool {
    return switch (op) {
        .exp2, .log2, .sin, .sqrt, .recip => true,
        else => false,
    };
}

fn isFloat(comptime dtype: DataType) bool {
    return switch (dtype) {
        .f16, .f32, .f64 => true,
        else => false,
    };
}

// ===== Tests =====

const testing = std.testing;

test "compile-time kernel generation" {
    const AddF32 = generateKernel(.add, .f32);
    const MulI32 = generateKernel(.mul, .i32);
    _ = MulI32; // Used for type checking
    
    // Test add kernel
    var input_a = [_]f32{ 1.0, 2.0, 3.0, 4.0 };
    var input_b = [_]f32{ 5.0, 6.0, 7.0, 8.0 };
    var output = [_]f32{0} ** 4;
    
    const args = KernelArgs{
        .inputs = &.{
            std.mem.sliceAsBytes(&input_a),
            std.mem.sliceAsBytes(&input_b),
        },
        .outputs = &.{std.mem.sliceAsBytes(&output)},
        .work_size = 4,
        .input_shapes = &.{},
        .output_shapes = &.{},
    };
    
    AddF32.kernel(args);
    
    try testing.expectEqual(@as(f32, 6.0), output[0]);
    try testing.expectEqual(@as(f32, 8.0), output[1]);
    try testing.expectEqual(@as(f32, 10.0), output[2]);
    try testing.expectEqual(@as(f32, 12.0), output[3]);
}

test "compile-time kernel table" {
    const ops = [_]ComputeOp{ .add, .mul, .exp };
    const dtypes = [_]DataType{ .f32, .i32 };
    
    const KernelTable = generateKernelTable(&ops, &dtypes);
    
    // Should have kernels for valid combinations
    try testing.expect(KernelTable.getKernel(.add, .f32) != null);
    try testing.expect(KernelTable.getKernel(.add, .i32) != null);
    try testing.expect(KernelTable.getKernel(.mul, .f32) != null);
    try testing.expect(KernelTable.getKernel(.mul, .i32) != null);
    try testing.expect(KernelTable.getKernel(.exp, .f32) != null);
    
    // exp on i32 should not exist
    try testing.expect(KernelTable.getKernel(.exp, .i32) == null);
}