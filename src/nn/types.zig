/// Neural Network specific types
///
/// This module contains types specific to neural network operations,
/// keeping them separate from the core tensor and graph types.

const std = @import("std");

// ===== Activation Types =====

/// Activation function types
pub const ActivationType = enum {
    relu,
    leaky_relu,
    gelu,
    sigmoid,
    tanh,
    swish,
    silu,

    pub fn toString(self: ActivationType) []const u8 {
        return @tagName(self);
    }
};

// ===== Weight Initialization =====

/// Weight initialization methods
pub const WeightInit = enum {
    xavier_uniform,
    xavier_normal,
    kaiming_uniform,
    kaiming_normal,
    normal,
    uniform,

    pub fn getFanIn(shape: []const i64) i64 {
        return if (shape.len >= 2) shape[shape.len - 1] else shape[0];
    }

    pub fn getFanOut(shape: []const i64) i64 {
        return shape[0];
    }
};

// ===== Reduction Types =====

/// Reduction types for operations
pub const ReductionType = enum {
    sum,
    mean,
    max,
    min,
    prod,

    pub fn needsNormalization(self: ReductionType) bool {
        return self == .mean;
    }
};

// ===== Loss Functions =====

/// Loss function types
pub const LossFunction = enum {
    mse,
    cross_entropy,
    binary_cross_entropy,
    l1,
    l2,
    huber,
};

// ===== Pooling Types =====

/// Pooling types
pub const PoolType = enum {
    max,
    avg,

    pub fn toString(self: PoolType) []const u8 {
        return @tagName(self);
    }
};