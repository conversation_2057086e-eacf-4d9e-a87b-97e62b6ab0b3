/// Buffer Pool - Reuses memory buffers across executions
/// 
/// This module provides a simple buffer pool that caches allocated buffers
/// by size to avoid repeated allocations/deallocations.

const std = @import("std");
const Allocator = std.mem.Allocator;

/// Buffer pool for memory reuse
pub const BufferPool = struct {
    allocator: Allocator,
    // Map size -> list of available buffers
    pools: std.AutoHashMap(usize, std.ArrayList([]u8)),
    max_buffers_per_size: usize,
    total_cached_bytes: usize,
    
    const Self = @This();
    
    pub fn init(allocator: Allocator, max_buffers_per_size: usize) Self {
        return .{
            .allocator = allocator,
            .pools = std.AutoHashMap(usize, std.ArrayList([]u8)).init(allocator),
            .max_buffers_per_size = max_buffers_per_size,
            .total_cached_bytes = 0,
        };
    }
    
    pub fn deinit(self: *Self) void {
        var iter = self.pools.iterator();
        while (iter.next()) |entry| {
            // Free all buffers in this size pool
            for (entry.value_ptr.items) |buffer| {
                self.allocator.free(buffer);
            }
            entry.value_ptr.deinit();
        }
        self.pools.deinit();
    }
    
    /// Get a buffer of at least the requested size
    pub fn acquire(self: *Self, size: usize) ![]u8 {
        // Round up to power of 2 for better pooling
        const pool_size = std.math.ceilPowerOfTwo(usize, size) catch size;
        
        if (self.pools.get(pool_size)) |*pool| {
            if (pool.items.len > 0) {
                // Reuse existing buffer
                const buffer = pool.pop();
                self.total_cached_bytes -= buffer.len;
                
                const enable_debug = @import("build_options").enable_debug_logs;
                if (enable_debug) {
                    std.debug.print("BufferPool: Reused {} byte buffer\n", .{buffer.len});
                }
                
                return buffer;
            }
        }
        
        // Allocate new buffer
        const buffer = try self.allocator.alloc(u8, pool_size);
        
        const enable_debug = @import("build_options").enable_debug_logs;
        if (enable_debug) {
            std.debug.print("BufferPool: Allocated new {} byte buffer\n", .{buffer.len});
        }
        
        return buffer;
    }
    
    /// Return a buffer to the pool
    pub fn release(self: *Self, buffer: []u8) !void {
        const size = buffer.len;
        
        // Get or create pool for this size
        const result = try self.pools.getOrPut(size);
        if (!result.found_existing) {
            result.value_ptr.* = std.ArrayList([]u8).init(self.allocator);
        }
        
        // Check if pool is full
        if (result.value_ptr.items.len >= self.max_buffers_per_size) {
            // Pool is full, just free the buffer
            self.allocator.free(buffer);
            
            const enable_debug = @import("build_options").enable_debug_logs;
            if (enable_debug) {
                std.debug.print("BufferPool: Pool full for size {}, freed buffer\n", .{size});
            }
            return;
        }
        
        // Add to pool
        try result.value_ptr.append(buffer);
        self.total_cached_bytes += buffer.len;
        
        const enable_debug = @import("build_options").enable_debug_logs;
        if (enable_debug) {
            std.debug.print("BufferPool: Cached {} byte buffer (total cached: {} bytes)\n", 
                          .{ buffer.len, self.total_cached_bytes });
        }
    }
    
    /// Clear all cached buffers
    pub fn clear(self: *Self) void {
        var iter = self.pools.iterator();
        while (iter.next()) |entry| {
            for (entry.value_ptr.items) |buffer| {
                self.allocator.free(buffer);
            }
            entry.value_ptr.clearRetainingCapacity();
        }
        self.total_cached_bytes = 0;
    }
    
    /// Get statistics about the pool
    pub fn getStats(self: *const Self) PoolStats {
        var buffer_count: usize = 0;
        var size_count: usize = 0;
        
        var iter = self.pools.iterator();
        while (iter.next()) |entry| {
            if (entry.value_ptr.items.len > 0) {
                size_count += 1;
                buffer_count += entry.value_ptr.items.len;
            }
        }
        
        return .{
            .total_cached_bytes = self.total_cached_bytes,
            .buffer_count = buffer_count,
            .size_count = size_count,
        };
    }
};

pub const PoolStats = struct {
    total_cached_bytes: usize,
    buffer_count: usize,
    size_count: usize,
};

// Global buffer pool (optional)
var global_pool: ?BufferPool = null;
var global_pool_mutex = std.Thread.Mutex{};

pub fn getGlobalPool(allocator: Allocator) !*BufferPool {
    global_pool_mutex.lock();
    defer global_pool_mutex.unlock();
    
    if (global_pool == null) {
        global_pool = BufferPool.init(allocator, 16); // Keep up to 16 buffers per size
    }
    
    return &global_pool.?;
}

pub fn deinitGlobalPool() void {
    global_pool_mutex.lock();
    defer global_pool_mutex.unlock();
    
    if (global_pool) |*pool| {
        pool.deinit();
        global_pool = null;
    }
}