/// Comptime Backend Interface - Zero-overhead backend selection
///
/// This replaces the old VTable-based runtime backend system with compile-time
/// backend selection. Backends are now namespaces with comptime-known capabilities.

const std = @import("std");
const Allocator = std.mem.Allocator;

// Import core types
const types = @import("types");
const NodeId = types.NodeId;
const ComputeOp = types.ComputeOp;
const DataType = types.DataType;

const Graph = @import("graph").Graph;
const Node = @import("graph").Node;

// Import backend types
const backend_types = @import("backend_types");

// Re-export backend types for convenience
pub const BackendCapabilities = backend_types.BackendCapabilities;
pub const Pattern = backend_types.Pattern;
pub const CompiledGraph = backend_types.CompiledGraph;

// ============================================================================
// Comptime Backend Interface
// ============================================================================

/// Backend validation function - ensures backend has required declarations
pub fn validateBackend(comptime Backend: type) void {
    // Required constants
    if (!@hasDecl(Backend, "name")) {
        @compileError("Backend must have 'name: []const u8' declaration");
    }
    
    // Required capabilities
    if (!@hasDecl(Backend, "capabilities")) {
        @compileError("Backend must have 'capabilities: BackendCapabilities' declaration");
    }
    
    // Required functions
    if (!@hasDecl(Backend, "isOperationSupported")) {
        @compileError("Backend must have 'isOperationSupported(ComputeOp) bool' function");
    }
    if (!@hasDecl(Backend, "getPatterns")) {
        @compileError("Backend must have 'getPatterns() []const Pattern' function");
    }
    if (!@hasDecl(Backend, "compile")) {
        @compileError("Backend must have 'compile(*Graph, Allocator) anyerror!CompiledGraph' function");
    }
}

// Re-export backend types for convenience
pub const TargetArch = backend_types.TargetArch;

// ============================================================================
// Comptime Backend Selection
// ============================================================================

/// Compile with backend selection at compile time
pub fn compileWithBackend(
    comptime Backend: type,
    graph: *Graph, 
    allocator: Allocator
) !CompiledGraph {
    // Validate backend at compile time
    comptime validateBackend(Backend);
    
    // Compile-time backend information logging
    comptime {
        std.log.info("Compiling with backend: {s}", .{Backend.name});
        std.log.info("Target architecture: {s}", .{@tagName(Backend.target_arch)});
    }
    
    // Use backend's compile function
    return Backend.compile(graph, allocator);
}

/// Get backend patterns at compile time
pub fn getBackendPatterns(comptime Backend: type) []const Pattern {
    comptime validateBackend(Backend);
    return Backend.getPatterns();
}

/// Check operation support at compile time
pub fn isOperationSupportedByBackend(comptime Backend: type, op: ComputeOp) bool {
    comptime validateBackend(Backend);
    return Backend.isOperationSupported(op);
}

/// Get backend capabilities at compile time
pub fn getBackendCapabilities(comptime Backend: type) BackendCapabilities {
    comptime validateBackend(Backend);
    return Backend.capabilities;
}

// ============================================================================
// Utility Functions
// ============================================================================

/// Create a backend capabilities struct with common defaults
pub fn defaultCapabilities() BackendCapabilities {
    return BackendCapabilities{
        .name = "default",
        .target_arch = .cpu,
    };
}

/// CPU-specific capabilities
pub fn cpuCapabilities() BackendCapabilities {
    return BackendCapabilities{
        .name = "cpu",
        .target_arch = .cpu,
        .supports_f32 = true,
        .supports_f64 = true,
        .supports_i32 = true,
        .supports_i64 = true,
        .max_memory_gb = 32,
        .memory_alignment = 64,
        .cache_line_size = 64,
        .max_threads = @intCast(std.Thread.getCpuCount() catch 8),
        .simd_width = 8, // Assume AVX2
        .supports_fma = true,
        .supports_fusion = true,
        .max_fusion_depth = 8,
    };
}

/// GPU-style capabilities template
pub fn gpuCapabilities(comptime max_threads: u32, comptime simd_width: u32) BackendCapabilities {
    return BackendCapabilities{
        .supports_f16 = true,
        .supports_f32 = true,
        .max_memory_gb = 16,
        .memory_alignment = 128,
        .cache_line_size = 128,
        .max_threads = max_threads,
        .simd_width = simd_width,
        .supports_fma = true,
        .supports_tensor_cores = true,
        .supports_fusion = true,
        .max_fusion_depth = 16,
    };
}

// ============================================================================
// Unit Tests
// ============================================================================

test "backend validation" {
    const testing = std.testing;
    
    // Valid backend
    const ValidBackend = struct {
        pub const name = "test_backend";
        pub const target_arch = TargetArch.cpu;
        pub const capabilities = defaultCapabilities();
        
        pub fn isOperationSupported(op: ComputeOp) bool {
            return switch (op) {
                .add, .mul => true,
                else => false,
            };
        }
        
        pub fn getPatterns() []const Pattern {
            return &.{};
        }
        
        pub fn compile(graph: *Graph, allocator: Allocator) !CompiledGraph {
            _ = graph;
            _ = allocator;
            return error.NotImplemented;
        }
    };
    
    // Should validate without error
    comptime validateBackend(ValidBackend);
    
    // Test capabilities
    const caps = getBackendCapabilities(ValidBackend);
    try testing.expect(caps.supports_f32);
    try testing.expect(caps.supports_fusion);
    
    // Test operation support
    try testing.expect(isOperationSupportedByBackend(ValidBackend, .add));
    try testing.expect(!isOperationSupportedByBackend(ValidBackend, .recip));
}

test "capability helpers" {
    const testing = std.testing;
    
    const default_caps = defaultCapabilities();
    try testing.expect(default_caps.supports_f32);
    try testing.expectEqual(@as(u32, 1), default_caps.simd_width);
    
    const cpu_caps = cpuCapabilities();
    try testing.expect(cpu_caps.supports_f64);
    try testing.expect(cpu_caps.simd_width > 1);
    
    const gpu_caps = gpuCapabilities(1024, 32);
    try testing.expectEqual(@as(u32, 1024), gpu_caps.max_threads);
    try testing.expectEqual(@as(u32, 32), gpu_caps.simd_width);
    try testing.expect(gpu_caps.supports_tensor_cores);
}

test "comptime backend compilation" {
    const testing = std.testing;
    
    const TestBackend = struct {
        pub const name = "test_compiler";
        pub const target_arch = TargetArch.cpu;
        pub const capabilities = defaultCapabilities();
        
        pub fn isOperationSupported(op: ComputeOp) bool {
            return switch (op) {
                .add, .mul, .recip => true,
                else => false,
            };
        }
        
        pub fn getPatterns() []const Pattern {
            return &.{};
        }
        
        pub fn compile(graph: *Graph, allocator: Allocator) !CompiledGraph {
            _ = graph;
            _ = allocator;
            return error.NotImplemented;
        }
    };
    
    var graph = try Graph.init(testing.allocator);
    defer graph.deinit();
    
    // This should compile successfully
    var program = try compileWithBackend(TestBackend, &graph, testing.allocator);
    defer program.deinit(testing.allocator);
}