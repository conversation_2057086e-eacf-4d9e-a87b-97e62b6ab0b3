/// Graph Transformation and Validation - Unified safe graph modification system
///
/// This module combines GraphRewriter for atomic transformations and validation
/// utilities into a single cohesive system for compiler passes.

const std = @import("std");
const Allocator = std.mem.Allocator;

// Import core types
const types = @import("types");
const NodeId = types.NodeId;
const DataType = types.DataType;
const NodeSpec = types.NodeSpec;
const CustomOp = types.CustomOp;
const ComputeOp = types.ComputeOp;

const Graph = @import("graph").Graph;
const Node = @import("graph").Node;
const NodeMetadata = @import("graph").NodeMetadata;
const GraphSnapshot = Graph.GraphSnapshot;

// ============================================================================
// GraphRewriter - Atomic Graph Transformations
// ============================================================================

/// GraphRewriter provides atomic, safe graph modifications with guaranteed rollback
pub const GraphRewriter = struct {
    allocator: Allocator,
    graph: *Graph, // Store reference to graph for simpler API
    
    // Operation queues
    deletions: std.AutoHashMapUnmanaged(NodeId, void) = .{},
    substitutions: std.AutoHashMapUnmanaged(NodeId, NodeId) = .{},
    additions: std.ArrayListUnmanaged(PendingOp) = .{},
    temp_id_map: std.AutoHashMapUnmanaged(NodeId, NodeId) = .{}, // Maps temp IDs to actual IDs
    
    // Safety tracking
    initial_mod_count: ?u32 = null,
    snapshot: ?GraphSnapshot = null,
    is_applied: bool = false,
    is_validated: bool = false,
    
    pub const PendingOp = struct {
        spec: NodeSpec,
        inputs: []const NodeId,
        dtype: DataType,
        output_id: ?NodeId = null, // Allow pre-assigned IDs
        custom_op: ?types.CustomOp = null, // For custom operations
        metadata: ?*NodeMetadata = null, // For shape and other metadata
    };

    pub const GraphRewriterError = error{
        // Node validation errors
        NodeNotFound,
        OldNodeNotFound,
        NewNodeNotFound,
        InvalidInput,
        InvalidOperation,
        InvalidNodeId,
        
        // Conflict detection errors
        ConflictingOperations,
        CyclicSubstitution,
        SubstitutionWouldCreateCycle,
        
        // Concurrency protection errors
        ConcurrentModification,
        GraphStateCorrupted,
        
        // Transaction state errors
        TransactionAlreadyApplied,
        TransactionNotValidated,
        RollbackFailed,
        
        // Memory errors
        OutOfMemory,
    };

    pub fn init(graph: *Graph, allocator: Allocator) GraphRewriter {
        return GraphRewriter{ 
            .allocator = allocator,
            .graph = graph,
        };
    }

    pub fn deinit(self: *GraphRewriter) void {
        // Clean up snapshot if it exists
        if (self.snapshot) |*snapshot| {
            snapshot.deinit();
        }
        
        // Clean up operation queues
        self.deletions.deinit(self.allocator);
        self.substitutions.deinit(self.allocator);
        self.temp_id_map.deinit(self.allocator);
        
        // Clean up additions (including duplicated inputs)
        for (self.additions.items) |addition| {
            self.allocator.free(addition.inputs);
        }
        self.additions.deinit(self.allocator);
    }

    // ===== SIMPLIFIED API =====
    
    /// Replace a node with another node
    pub fn replaceNode(self: *GraphRewriter, old_id: NodeId, new_id: NodeId) !void {
        try self.markForSubstitution(old_id, new_id);
    }
    
    /// Add a new node with the given operation
    pub fn addNode(self: *GraphRewriter, spec_type: NodeSpec.SpecType, op: anytype, inputs: []const NodeId, dtype: DataType) !NodeId {
        const spec = switch (spec_type) {
            .compute => NodeSpec{ .compute = op },
            .data => NodeSpec{ .data = op },
        };
        
        // Create a temporary node ID
        const temp_node_id = @as(NodeId, @intCast(0x80000000 | self.additions.items.len));
        
        // Duplicate inputs to ensure they remain valid
        const owned_inputs = try self.allocator.dupe(NodeId, inputs);
        errdefer self.allocator.free(owned_inputs);
        
        try self.additions.append(self.allocator, .{
            .spec = spec,
            .inputs = owned_inputs,
            .dtype = dtype,
            .output_id = temp_node_id,
        });
        
        self.is_validated = false;
        return temp_node_id;
    }
    
    /// Add a constant node
    pub fn addConstant(self: *GraphRewriter, value: f32) !NodeId {
        _ = value; // TODO: Store constant value in metadata
        return self.addNode(.data, types.DataOp.constant, &.{}, .f32);
    }
    
    /// Add a custom operation
    pub fn addCustomOp(self: *GraphRewriter, name: []const u8, inputs: []const NodeId) !NodeId {
        const custom_op = CustomOp{
            .id = 0, // Will be assigned during creation
            .backend_name = "generic",
            .debug_name = name,
        };
        return self.addCustomOperation(custom_op, inputs, .f32);
    }

    // ===== OPERATION QUEUEING WITH BULLETPROOF VALIDATION =====
    
    /// Mark a node for deletion with conflict detection
    pub fn markForDeletion(self: *GraphRewriter, node_id: NodeId) !void {
        if (self.is_applied) return GraphRewriterError.TransactionAlreadyApplied;
        
        // Check for conflict with existing substitutions
        if (self.substitutions.contains(node_id)) {
            return GraphRewriterError.ConflictingOperations;
        }
        
        // Check if we're trying to substitute TO a node we're deleting
        var sub_iter = self.substitutions.iterator();
        while (sub_iter.next()) |entry| {
            if (entry.value_ptr.* == node_id) {
                return GraphRewriterError.ConflictingOperations;
            }
        }
        
        try self.deletions.put(self.allocator, node_id, {});
        self.is_validated = false;
    }

    /// Mark a node for substitution with comprehensive conflict and cycle detection
    pub fn markForSubstitution(self: *GraphRewriter, old_id: NodeId, new_id: NodeId) !void {
        if (self.is_applied) return GraphRewriterError.TransactionAlreadyApplied;
        
        // Basic validation
        if (old_id == new_id) return GraphRewriterError.InvalidOperation;
        
        // Check for conflicts with deletions
        if (self.deletions.contains(old_id) or self.deletions.contains(new_id)) {
            return GraphRewriterError.ConflictingOperations;
        }
        
        // Check for immediate cycle
        if (self.substitutions.get(new_id) == old_id) {
            return GraphRewriterError.CyclicSubstitution;
        }
        
        try self.substitutions.put(self.allocator, old_id, new_id);
        
        // Comprehensive cycle detection after addition
        try self.detectSubstitutionCycles();
        self.is_validated = false;
    }

    /// Add operation to queue with input validation
    pub fn addOperation(self: *GraphRewriter, spec: NodeSpec, inputs: []const NodeId, dtype: DataType) !void {
        if (self.is_applied) return GraphRewriterError.TransactionAlreadyApplied;
        
        // Deep copy inputs to ensure transaction owns the memory
        const owned_inputs = try self.allocator.dupe(NodeId, inputs);
        errdefer self.allocator.free(owned_inputs);
        
        try self.additions.append(self.allocator, .{
            .spec = spec,
            .inputs = owned_inputs,
            .dtype = dtype,
        });
        
        self.is_validated = false;
    }
    
    /// Add a custom operation with automatic node creation
    pub fn addCustomOperation(self: *GraphRewriter, custom_op: CustomOp, inputs: []const NodeId, dtype: DataType) !NodeId {
        return self.addCustomOperationWithMetadata(custom_op, inputs, dtype, null);
    }
    
    pub fn addCustomOperationWithMetadata(self: *GraphRewriter, custom_op: CustomOp, inputs: []const NodeId, dtype: DataType, metadata: ?*NodeMetadata) !NodeId {
        if (self.is_applied) return GraphRewriterError.TransactionAlreadyApplied;
        
        // Validate inputs
        for (inputs) |input_id| {
            if (self.deletions.contains(input_id)) {
                return GraphRewriterError.InvalidInput;
            }
        }
        
        // Duplicate inputs to ensure they remain valid
        const owned_inputs = try self.allocator.dupe(NodeId, inputs);
        errdefer self.allocator.free(owned_inputs);
        
        // Create a temporary node ID that will be resolved during apply
        const temp_node_id = @as(NodeId, @intCast(0x80000000 | self.additions.items.len));
        
        try self.additions.append(self.allocator, .{
            .spec = .{ .compute = .custom },
            .inputs = owned_inputs,
            .dtype = dtype,
            .output_id = temp_node_id,
            .custom_op = custom_op,
            .metadata = metadata,
        });
        
        self.is_validated = false;
        return temp_node_id;
    }

    // ===== ATOMIC COMMIT =====
    
    /// Commit all changes atomically (simplified API)
    pub fn commitChanges(self: *GraphRewriter) !void {
        try self.apply();
    }
    
    /// Apply all operations atomically with guaranteed rollback on any failure
    pub fn apply(self: *GraphRewriter) !void {
        if (self.is_applied) return GraphRewriterError.TransactionAlreadyApplied;
        
        // PHASE 1: Capture initial state and create snapshot (if not already captured)
        if (self.initial_mod_count == null) {
            self.initial_mod_count = self.graph.modifications_count;
        }
        self.snapshot = try self.graph.createSnapshot();
        errdefer {
            if (self.snapshot) |*snapshot| {
                snapshot.deinit();
                self.snapshot = null;
            }
        }
        
        // PHASE 2: Comprehensive validation
        try self.validateOperationConsistency();
        self.is_validated = true;
        
        // PHASE 3: Apply operations with automatic rollback on any failure
        // Apply operations in the correct order, handling temporary node IDs
        self.applyDeletions() catch |err| {
            try self.performRollback();
            return err;
        };
        
        // Apply substitutions that don't involve temporary nodes
        self.applyNonTempSubstitutions() catch |err| {
            try self.performRollback();
            return err;
        };
        
        // Apply additions (creates the actual nodes for temporary IDs)
        self.applyAdditions() catch |err| {
            try self.performRollback();
            return err;
        };
        
        // Apply substitutions that involve temporary nodes (now resolved to actual IDs)
        self.applyTempSubstitutions() catch |err| {
            try self.performRollback();
            return err;
        };
        
        // PHASE 4: Final validation and cleanup
        try self.graph.validateIntegrity();
        self.is_applied = true;
        
        // Clean up snapshot now that we've succeeded
        if (self.snapshot) |*snapshot| {
            snapshot.deinit();
            self.snapshot = null;
        }
    }
    
    // ===== COMPREHENSIVE VALIDATION SYSTEM =====
    
    /// Validate all operations for consistency before applying any changes
    fn validateOperationConsistency(self: *GraphRewriter) !void {
        // Check for concurrent modifications - ensure no external changes
        if (self.initial_mod_count) |expected| {
            if (self.graph.modifications_count != expected) {
                return GraphRewriterError.ConcurrentModification;
            }
        }
        
        // Validate deletion targets exist
        var del_iter = self.deletions.iterator();
        while (del_iter.next()) |entry| {
            if (!self.graph.hasNode(entry.key_ptr.*)) {
                return GraphRewriterError.NodeNotFound;
            }
        }
        
        // Validate substitution nodes exist and detect all conflicts
        var sub_iter = self.substitutions.iterator();
        while (sub_iter.next()) |entry| {
            const old_id = entry.key_ptr.*;
            const new_id = entry.value_ptr.*;
            
            if (!self.graph.hasNode(old_id)) {
                return GraphRewriterError.OldNodeNotFound;
            }
            // Check if new_id exists, unless it's a temporary node ID that will be created
            if (!self.graph.hasNode(new_id)) {
                // Check if this is a temporary node ID from addCustomOperation
                const is_temp_id = (new_id & 0x80000000) != 0;
                if (!is_temp_id) {
                    return GraphRewriterError.NewNodeNotFound;
                }
                // Verify the temporary ID corresponds to a pending addition
                const addition_index = new_id & 0x7FFFFFFF;
                if (addition_index >= self.additions.items.len) {
                    return GraphRewriterError.InvalidNodeId;
                }
            }
            
            // Re-check conflicts (could have changed)
            if (self.deletions.contains(old_id) or self.deletions.contains(new_id)) {
                return GraphRewriterError.ConflictingOperations;
            }
            
            // Check for cycles in the actual graph (skip for temporary nodes)
            const is_temp_id = (new_id & 0x80000000) != 0;
            if (!is_temp_id and try self.graph.wouldCreateCycle(old_id, new_id)) {
                return GraphRewriterError.SubstitutionWouldCreateCycle;
            }
        }
        
        // Validate addition inputs exist (accounting for pending operations)
        for (self.additions.items) |pending| {
            for (pending.inputs) |input_id| {
                const resolved_id = self.resolveSubstitutions(input_id);
                
                // Input must exist and not be deleted
                if (self.deletions.contains(resolved_id)) {
                    return GraphRewriterError.InvalidInput;
                }
                if (!self.graph.hasNode(resolved_id)) {
                    return GraphRewriterError.InvalidInput;
                }
            }
        }
        
        // Final comprehensive cycle detection
        try self.detectSubstitutionCycles();
    }
    
    /// Detect cycles in substitution chains using DFS
    fn detectSubstitutionCycles(self: *GraphRewriter) !void {
        var visited = std.AutoHashMapUnmanaged(NodeId, void){};
        defer visited.deinit(self.allocator);
        var recursion_stack = std.AutoHashMapUnmanaged(NodeId, void){};
        defer recursion_stack.deinit(self.allocator);

        var sub_iter = self.substitutions.iterator();
        while (sub_iter.next()) |entry| {
            if (!visited.contains(entry.key_ptr.*)) {
                try self.detectCyclesDFS(entry.key_ptr.*, &visited, &recursion_stack);
            }
        }
    }

    fn detectCyclesDFS(
        self: *GraphRewriter,
        node_id: NodeId,
        visited: *std.AutoHashMapUnmanaged(NodeId, void),
        recursion_stack: *std.AutoHashMapUnmanaged(NodeId, void)
    ) !void {
        try visited.put(self.allocator, node_id, {});
        try recursion_stack.put(self.allocator, node_id, {});

        if (self.substitutions.get(node_id)) |next_id| {
            if (recursion_stack.contains(next_id)) {
                return GraphRewriterError.CyclicSubstitution;
            }
            if (!visited.contains(next_id)) {
                try self.detectCyclesDFS(next_id, visited, recursion_stack);
            }
        }

        _ = recursion_stack.remove(node_id);
    }
    
    /// Resolve substitution chains with cycle protection
    fn resolveSubstitutions(self: *GraphRewriter, node_id: NodeId) NodeId {
        var current = node_id;
        var depth: u32 = 0;
        const max_depth = 1000; // Prevent infinite loops

        while (depth < max_depth) {
            if (self.substitutions.get(current)) |next_id| {
                current = next_id;
                depth += 1;
            } else {
                break;
            }
        }

        return current;
    }

    // ===== ATOMIC OPERATION APPLICATION =====
    
    /// Apply deletions using Graph's safe removal methods
    fn applyDeletions(self: *GraphRewriter) !void {
        var del_iter = self.deletions.iterator();
        while (del_iter.next()) |entry| {
            try self.graph.removeNode(entry.key_ptr.*);
        }
    }

    /// Apply substitutions that don't involve temporary nodes
    fn applyNonTempSubstitutions(self: *GraphRewriter) !void {
        var sub_iter = self.substitutions.iterator();
        while (sub_iter.next()) |entry| {
            const new_id = entry.value_ptr.*;
            const is_temp_id = (new_id & 0x80000000) != 0;
            
            // Skip substitutions involving temporary nodes
            if (is_temp_id) continue;
            
            self.graph.substituteNode(entry.key_ptr.*, new_id) catch |err| {
                // Convert Graph errors to GraphTransaction errors
                return switch (err) {
                    error.SubstitutionWouldCreateCycle => GraphRewriterError.CyclicSubstitution,
                    error.OldNodeNotFound => GraphRewriterError.OldNodeNotFound,
                    error.NewNodeNotFound => GraphRewriterError.NewNodeNotFound,
                    else => err,
                };
            };
        }
    }
    
    /// Apply substitutions that involve temporary nodes (after they've been created)
    fn applyTempSubstitutions(self: *GraphRewriter) !void {
        var sub_iter = self.substitutions.iterator();
        while (sub_iter.next()) |entry| {
            const old_id = entry.key_ptr.*;
            const new_id = entry.value_ptr.*;
            const is_temp_id = (new_id & 0x80000000) != 0;
            
            // Only process substitutions involving temporary nodes
            if (!is_temp_id) continue;
            
            // Resolve temporary ID to actual ID
            const actual_id = self.temp_id_map.get(new_id) orelse {
                return GraphRewriterError.InvalidNodeId;
            };
            
            self.graph.substituteNode(old_id, actual_id) catch |err| {
                // Convert Graph errors to GraphTransaction errors
                return switch (err) {
                    error.SubstitutionWouldCreateCycle => GraphRewriterError.CyclicSubstitution,
                    error.OldNodeNotFound => GraphRewriterError.OldNodeNotFound,
                    error.NewNodeNotFound => GraphRewriterError.NewNodeNotFound,
                    else => err,
                };
            };
        }
    }

    /// Apply additions using Graph's safe creation methods
    fn applyAdditions(self: *GraphRewriter) !void {
        for (self.additions.items) |pending| {
            // Resolve input substitutions
            var resolved_inputs = try self.allocator.alloc(NodeId, pending.inputs.len);
            defer self.allocator.free(resolved_inputs);

            for (pending.inputs, 0..) |input_id, i| {
                resolved_inputs[i] = self.resolveSubstitutions(input_id);
            }

            const id = if (pending.custom_op) |custom_op|
                try self.graph.addCustomNode(custom_op, resolved_inputs, pending.dtype)
            else
                try self.graph.createNode(pending.spec, resolved_inputs, pending.dtype);
                
            // Set metadata if provided
            if (pending.metadata) |metadata| {
                const node = self.graph.getNodeMut(id) orelse return GraphRewriterError.NodeNotFound;
                node.metadata = metadata;
            }
                
            // Track mapping from temporary ID to actual ID
            if (pending.output_id) |temp_id| {
                const is_temp = (temp_id & 0x80000000) != 0;
                if (is_temp) {
                    try self.temp_id_map.put(self.allocator, temp_id, id);
                } else if (id != temp_id) {
                    return GraphRewriterError.InvalidNodeId;
                }
            }
        }
    }
    
    // ===== BULLETPROOF ROLLBACK SYSTEM =====
    
    /// Perform complete rollback to pre-transaction state
    fn performRollback(self: *GraphRewriter) !void {
        if (self.snapshot) |snapshot| {
            try self.graph.restoreFromSnapshot(snapshot);
        } else {
            return GraphRewriterError.RollbackFailed;
        }
    }
    
    /// Manual rollback (for testing/debugging)
    pub fn rollback(self: *GraphRewriter) !void {
        if (!self.is_applied) return; // Nothing to rollback
        
        try self.performRollback();
        self.is_applied = false;
    }
    
    // ===== STATUS QUERIES =====
    
    pub fn isValid(self: *const GraphRewriter) bool {
        return self.is_validated and !self.is_applied;
    }
    
    pub fn hasOperations(self: *const GraphRewriter) bool {
        return self.deletions.count() > 0 or 
               self.substitutions.count() > 0 or 
               self.additions.items.len > 0;
    }
    
    pub fn getOperationCounts(self: *const GraphRewriter) struct { deletions: u32, substitutions: u32, additions: u32 } {
        return .{
            .deletions = @intCast(self.deletions.count()),
            .substitutions = @intCast(self.substitutions.count()),
            .additions = @intCast(self.additions.items.len),
        };
    }
};

// ============================================================================
// Graph Validation
// ============================================================================

/// Current implementation limitations
pub const CompilerError = error{
    // JIT-related errors (not yet implemented)
    JITNotSupported,
    RuntimeKernelGenerationNotSupported,
    DynamicFusionNotSupported,
    // Shape-related errors
    ValueDependentShapeNotSupported,
    ComputedShapeNotSupported,
    // Memory errors
    MemoryRequirementTooLarge,
    // Operation errors
    UnsupportedOperation,
    UnsupportedFusionPattern,
    MissingAOTKernels,
};

/// Validate graph constraints before optimization
pub fn validateGraph(graph: *Graph) !void {
    // Check operations are supported
    for (graph.nodes.items) |node| {
        if (!node.is_valid) continue;
        
        if (node.spec == .compute) {
            const op = node.spec.compute;
            
            if (!isOperationSupported(op)) {
                std.log.err("validateGraph: operation {s} is not supported for node {}", 
                           .{ @tagName(op), node.id });
                return error.UnsupportedOperation;
            }
        }
    }
    
    // Validate V1 constraints
    try validateV1Constraints(graph);
}

/// Validate V1-specific constraints
fn validateV1Constraints(graph: *Graph) !void {
    // V1: No JIT compilation support
    for (graph.nodes.items) |node| {
        if (!node.is_valid) continue;
        
        if (node.spec == .compute and node.spec.compute == .custom) {
            // Custom operations must be AOT-compiled
            std.log.err("validateV1Constraints: custom operations require AOT compilation in V1 for node {}", 
                       .{node.id});
            return error.JITNotSupported;
        }
    }
    
    // V1: No value-dependent shapes
    if (hasValueDependentShapes(graph)) {
        std.log.err("validateV1Constraints: value-dependent shapes not supported in V1", .{});
        return error.ValueDependentShapeNotSupported;
    }
    
    // V1: Memory requirements validation
    const estimated_memory = estimateMemoryRequirement(graph);
    const MAX_V1_MEMORY = 4 * 1024 * 1024 * 1024; // 4GB limit for V1
    if (estimated_memory > MAX_V1_MEMORY) {
        std.log.err("validateV1Constraints: memory requirement {} exceeds V1 limit {}", 
                   .{ estimated_memory, MAX_V1_MEMORY });
        return error.MemoryRequirementTooLarge;
    }
}

/// Check if operation is supported by the current implementation
pub fn isOperationSupported(op: types.ComputeOp) bool {
    return switch (op) {
        // All primitive operations are supported
        .add, .mul, .mod, .less_than => true,
        .recip, .sqrt, .sin, .exp2, .log2 => true,
        .sum_reduce, .max_reduce => true,
        .contiguous => true,
        .custom => false, // Unless pre-registered
    };
}

/// Check if graph has value-dependent shapes (V1 limitation)
fn hasValueDependentShapes(graph: *Graph) bool {
    // Simplified check - in real implementation would analyze symbolic expressions
    _ = graph;
    
    // For V1, we assume no value-dependent shapes for now
    return false;
}

/// Estimate memory requirement for the graph
fn estimateMemoryRequirement(graph: *Graph) usize {
    var total_memory: usize = 0;
    
    for (graph.nodes.items) |node| {
        if (!node.is_valid) continue;
        
        // Simplified memory estimation - would need shape information
        // For now, just count nodes
        total_memory += 1024; // 1KB per node as rough estimate
    }
    
    return total_memory;
}

/// Check if fusion pattern is supported in V1
pub fn isFusionPatternSupported(pattern: []const types.ComputeOp) bool {
    // V1: Only elementwise fusion supported
    for (pattern) |op| {
        if (!op.isElementwise()) {
            return false;
        }
    }
    
    // V1: Maximum fusion length
    const MAX_FUSION_LENGTH = 8;
    return pattern.len <= MAX_FUSION_LENGTH;
}

/// Validate AOT kernel availability
pub fn validateAOTKernels(fusion_patterns: []const []const types.ComputeOp) !void {
    for (fusion_patterns, 0..) |pattern, i| {
        if (!isFusionPatternSupported(pattern)) {
            std.log.err("validateAOTKernels: fusion pattern {} not supported in V1: {any}", 
                       .{ i, pattern });
            return error.UnsupportedFusionPattern;
        }
        
        // Check if AOT kernel exists for this pattern
        if (!hasAOTKernel(pattern)) {
            std.log.err("validateAOTKernels: missing AOT kernel for pattern {}: {any}", 
                       .{ i, pattern });
            return error.MissingAOTKernels;
        }
    }
}

/// Check if AOT kernel exists for pattern (stub implementation)
fn hasAOTKernel(pattern: []const types.ComputeOp) bool {
    // Stub: In real implementation, would check kernel registry
    // For now, assume common patterns are available
    if (pattern.len == 1) return true; // Single operations always available
    if (pattern.len == 2) return true; // Common binary fusions available
    return false; // Longer chains might not be available
}

/// Validate backend compatibility
pub fn validateBackendCompatibility(backend_name: []const u8, operations: []const types.ComputeOp) !void {
    // Check if backend supports all required operations
    for (operations) |op| {
        if (!isOperationSupportedByBackend(backend_name, op)) {
            std.log.err("validateBackendCompatibility: operation {s} not supported by backend {s}", 
                       .{ @tagName(op), backend_name });
            return error.UnsupportedOperation;
        }
    }
}

/// Check if operation is supported by specific backend
fn isOperationSupportedByBackend(backend_name: []const u8, op: types.ComputeOp) bool {
    // Simplified backend capability check
    if (std.mem.eql(u8, backend_name, "cpu")) {
        return isOperationSupported(op); // CPU supports all primitive ops
    } else if (std.mem.eql(u8, backend_name, "cuda")) {
        // CUDA might have different support matrix
        return switch (op) {
            .add, .mul, .recip, .sqrt, .sin, .exp2, .log2 => true,
            .sum_reduce, .max_reduce => true,
            .mod, .less_than => false, // Hypothetical limitation
            .contiguous, .custom => false,
        };
    } else if (std.mem.eql(u8, backend_name, "metal")) {
        // Metal might have its own limitations
        return isOperationSupported(op);
    }
    
    return false; // Unknown backend
}

/// Compile-time validation for optimization passes
pub fn validateOptimizationPass(pass_name: []const u8) !void {
    // Validate that optimization passes follow V1 constraints
    const allowed_passes = [_][]const u8{
        "dead_code_elimination",
        "constant_folding", 
        "algebraic_simplification",
        "common_subexpression_elimination",
        "elementwise_fusion",
        "memory_optimization",
        "shape_validation",
        "insert_contiguous",
    };
    
    for (allowed_passes) |allowed| {
        if (std.mem.eql(u8, pass_name, allowed)) {
            return; // Pass is allowed
        }
    }
    
    std.log.err("validateOptimizationPass: optimization pass {s} not supported in V1", .{pass_name});
    return error.UnsupportedOperation;
}

// ============================================================================
// Unit Tests
// ============================================================================

test "GraphRewriter basic operations" {
    const testing = std.testing;
    
    var graph = try Graph.init(testing.allocator);
    defer graph.deinit();

    var rewriter = GraphRewriter.init(&graph, testing.allocator);
    defer rewriter.deinit();

    // Test operation queueing
    try rewriter.markForDeletion(1);
    try rewriter.markForSubstitution(2, 3);
    try rewriter.addOperation(.{ .compute = .add }, &.{ 4, 5 }, .f32);

    const counts = rewriter.getOperationCounts();
    try testing.expectEqual(@as(u32, 1), counts.deletions);
    try testing.expectEqual(@as(u32, 1), counts.substitutions);
    try testing.expectEqual(@as(u32, 1), counts.additions);
    
    try testing.expect(rewriter.hasOperations());
    try testing.expect(!rewriter.isValid()); // Not validated yet
}

test "GraphRewriter basic conflict detection" {
    const testing = std.testing;
    
    var graph = try Graph.init(testing.allocator);
    defer graph.deinit();

    var rewriter = GraphRewriter.init(&graph, testing.allocator);
    defer rewriter.deinit();

    // Mark for deletion first
    try rewriter.markForDeletion(1);

    // Attempting to substitute the same node should fail
    try testing.expectError(GraphRewriter.GraphRewriterError.ConflictingOperations, 
        rewriter.markForSubstitution(1, 2));
}

test "GraphRewriter simplified API" {
    const testing = std.testing;
    
    var graph = try Graph.init(testing.allocator);
    defer graph.deinit();
    
    // Create initial graph
    const a = try graph.addPlaceholder(.f32);
    const b = try graph.addNode(.sqrt, &.{a}, .f32);
    
    var rewriter = GraphRewriter.init(&graph, testing.allocator);
    defer rewriter.deinit();
    
    // Use simplified API
    const new_b = try rewriter.addNode(.compute, ComputeOp.log2, &.{a}, .f32);
    try rewriter.replaceNode(b, new_b);
    
    // This should work since we're using temp IDs
    try rewriter.commitChanges();
}

test "operation validation" {
    const testing = std.testing;
    
    // Test that all current primitive operations are supported
    try testing.expect(isOperationSupported(.add));
    try testing.expect(isOperationSupported(.mul));
    try testing.expect(isOperationSupported(.recip));
    try testing.expect(isOperationSupported(.sqrt));
    try testing.expect(isOperationSupported(.sum_reduce));
    try testing.expect(!isOperationSupported(.custom));
}

test "fusion pattern validation" {
    const testing = std.testing;
    
    // Test valid fusion patterns
    try testing.expect(isFusionPatternSupported(&.{.add}));
    try testing.expect(isFusionPatternSupported(&.{.add, .mul}));
    try testing.expect(isFusionPatternSupported(&.{.sin, .exp2, .log2}));
    
    // Test invalid patterns
    try testing.expect(!isFusionPatternSupported(&.{.add, .sum_reduce})); // Contains reduction
    try testing.expect(!isFusionPatternSupported(&.{.add, .mul, .sin, .exp2, .log2, .sqrt, .recip, .add, .mul})); // Too long
}

test "graph validation" {
    const testing = std.testing;
    
    var graph = try Graph.init(testing.allocator);
    defer graph.deinit();
    
    // Create graph with only supported operations
    const input = try graph.addPlaceholder(.f32);
    _ = try graph.addNode(.add, &.{input, input}, .f32);
    _ = try graph.addNode(.mul, &.{input, input}, .f32);
    _ = try graph.addNode(.sum_reduce, &.{input}, .f32);
    
    // Validation should pass
    try validateGraph(&graph);
}

test "GraphRewriter transaction semantics" {
    const testing = std.testing;
    
    var graph = try Graph.init(testing.allocator);
    defer graph.deinit();
    
    // Create initial graph
    const a = try graph.addPlaceholder(.f32);
    const b = try graph.addPlaceholder(.f32);
    const add_node = try graph.addNode(.add, &.{a, b}, .f32);
    
    var rewriter = GraphRewriter.init(&graph, testing.allocator);
    defer rewriter.deinit();
    
    // Test transaction batching
    try rewriter.markForDeletion(add_node);
    const new_node = try rewriter.addNode(.compute, ComputeOp.mul, &.{a, b}, .f32);
    try rewriter.replaceNode(add_node, new_node);
    
    // Should be able to query transaction state
    try testing.expect(rewriter.hasOperations());
    try testing.expect(!rewriter.isValid()); // Not validated yet
    
    const counts = rewriter.getOperationCounts();
    try testing.expectEqual(@as(u32, 1), counts.deletions);
    try testing.expectEqual(@as(u32, 1), counts.substitutions);
    try testing.expectEqual(@as(u32, 1), counts.additions);
}

test "GraphRewriter conflict detection" {
    const testing = std.testing;
    
    var graph = try Graph.init(testing.allocator);
    defer graph.deinit();
    
    const a = try graph.addPlaceholder(.f32);
    
    var rewriter = GraphRewriter.init(&graph, testing.allocator);
    defer rewriter.deinit();
    
    // Mark for deletion first
    try rewriter.markForDeletion(a);
    
    // Attempting to substitute the same node should fail
    try testing.expectError(
        GraphRewriter.GraphRewriterError.ConflictingOperations,
        rewriter.markForSubstitution(a, 999)
    );
    
    // Attempting to substitute TO a deleted node should also fail
    const b = try graph.addPlaceholder(.f32);
    try testing.expectError(
        GraphRewriter.GraphRewriterError.ConflictingOperations,
        rewriter.markForSubstitution(b, a)
    );
}

test "GraphRewriter cyclic substitution detection" {
    const testing = std.testing;
    
    var graph = try Graph.init(testing.allocator);
    defer graph.deinit();
    
    const a = try graph.addPlaceholder(.f32);
    const b = try graph.addPlaceholder(.f32);
    const c = try graph.addPlaceholder(.f32);
    
    var rewriter = GraphRewriter.init(&graph, testing.allocator);
    defer rewriter.deinit();
    
    // Create a substitution chain: a -> b -> c
    try rewriter.markForSubstitution(a, b);
    try rewriter.markForSubstitution(b, c);
    
    // Attempting to close the cycle (c -> a) should fail
    try testing.expectError(
        GraphRewriter.GraphRewriterError.CyclicSubstitution,
        rewriter.markForSubstitution(c, a)
    );
}

test "GraphRewriter custom operations" {
    const testing = std.testing;
    
    var graph = try Graph.init(testing.allocator);
    defer graph.deinit();
    
    const a = try graph.addPlaceholder(.f32);
    const b = try graph.addPlaceholder(.f32);
    
    var rewriter = GraphRewriter.init(&graph, testing.allocator);
    defer rewriter.deinit();
    
    // Test custom operation creation
    const custom_id = try rewriter.addCustomOp("test_matmul", &.{a, b});
    
    // Should be a temporary ID until committed
    try testing.expect((custom_id & 0x80000000) != 0);
    
    // Test simplified API
    const add_id = try rewriter.addNode(.compute, ComputeOp.add, &.{a, b}, .f32);
    try rewriter.replaceNode(custom_id, add_id);
    
    try testing.expect(rewriter.hasOperations());
}

test "validation functions comprehensive" {
    const testing = std.testing;
    
    // Test operation support checking
    try testing.expect(isOperationSupported(.add));
    try testing.expect(isOperationSupported(.mul));
    try testing.expect(isOperationSupported(.sum_reduce));
    try testing.expect(!isOperationSupported(.custom));
    
    // Test fusion pattern validation
    try testing.expect(isFusionPatternSupported(&.{.add}));
    try testing.expect(isFusionPatternSupported(&.{.add, .mul}));
    try testing.expect(!isFusionPatternSupported(&.{.add, .sum_reduce})); // Contains reduction
    
    // Test backend compatibility
    try validateBackendCompatibility("cpu", &.{.add, .mul, .recip});
    try testing.expectError(
        error.UnsupportedOperation,
        validateBackendCompatibility("unknown_backend", &.{.add})
    );
    
    // Test optimization pass validation
    try validateOptimizationPass("dead_code_elimination");
    try validateOptimizationPass("constant_folding");
    try testing.expectError(
        error.UnsupportedOperation,
        validateOptimizationPass("unsupported_pass")
    );
}

test "V1 constraint validation" {
    const testing = std.testing;
    
    var graph = try Graph.init(testing.allocator);
    defer graph.deinit();
    
    // Create valid V1 graph
    const a = try graph.addPlaceholder(.f32);
    const b = try graph.addPlaceholder(.f32);
    _ = try graph.addNode(.add, &.{a, b}, .f32);
    _ = try graph.addNode(.mul, &.{a, b}, .f32);
    
    // Should pass V1 validation
    try validateGraph(&graph);
    
    // Test AOT kernel validation
    const valid_patterns = [_][]const ComputeOp{
        &.{.add},
        &.{.add, .mul},
    };
    try validateAOTKernels(&valid_patterns);
    
    // Test invalid patterns
    const invalid_patterns = [_][]const ComputeOp{
        &.{.add, .mul, .sin, .exp2, .log2, .sqrt, .recip, .add, .mul}, // Too long
    };
    try testing.expectError(
        error.UnsupportedFusionPattern,
        validateAOTKernels(&invalid_patterns)
    );
}

test "memory management in GraphRewriter" {
    const testing = std.testing;
    
    var graph = try Graph.init(testing.allocator);
    defer graph.deinit();
    
    const a = try graph.addPlaceholder(.f32);
    const b = try graph.addPlaceholder(.f32);
    
    var rewriter = GraphRewriter.init(&graph, testing.allocator);
    defer rewriter.deinit();
    
    // Test that input arrays are properly owned
    var inputs = [_]NodeId{a, b};
    try rewriter.addOperation(.{ .compute = .add }, &inputs, .f32);
    
    // Modify original array - shouldn't affect the rewriter
    inputs[0] = 999;
    inputs[1] = 888;
    
    // Should still have the original values in the rewriter
    const counts = rewriter.getOperationCounts();
    try testing.expectEqual(@as(u32, 1), counts.additions);
}

test "GraphRewriter rollback functionality" {
    const testing = std.testing;
    
    var graph = try Graph.init(testing.allocator);
    defer graph.deinit();
    
    const original_count = graph.nodes.items.len;
    const a = try graph.addPlaceholder(.f32);
    
    var rewriter = GraphRewriter.init(&graph, testing.allocator);
    defer rewriter.deinit();
    
    // Make some changes but don't commit
    try rewriter.markForDeletion(a);
    try rewriter.addOperation(.{ .compute = .add }, &.{a, a}, .f32);
    
    // Should have operations queued
    try testing.expect(rewriter.hasOperations());
    
    // Don't call apply() - just let rewriter go out of scope
    // Graph should remain unchanged
    try testing.expectEqual(original_count + 1, graph.nodes.items.len);
}

test "error propagation in GraphRewriter" {
    const testing = std.testing;
    
    var graph = try Graph.init(testing.allocator);
    defer graph.deinit();
    
    var rewriter = GraphRewriter.init(&graph, testing.allocator);
    defer rewriter.deinit();
    
    // Test error when trying to operate on applied transaction
    try rewriter.markForDeletion(999); // Non-existent node
    
    // Should fail validation before apply
    try testing.expectError(
        GraphRewriter.GraphRewriterError.NodeNotFound,
        rewriter.apply()
    );
}