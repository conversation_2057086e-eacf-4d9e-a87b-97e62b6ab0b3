/// Main Compilation Entry Point - Comptime Backend Selection
///
/// This replaces the old VTable-based compile function with compile-time
/// backend selection for zero-overhead compilation.

const std = @import("std");
const Allocator = std.mem.Allocator;

// Import backend system
const backend_interface = @import("backend_interface.zig");
const compileWithBackend = backend_interface.compileWithBackend;
const validateBackend = backend_interface.validateBackend;
const CompiledProgram = backend_interface.CompiledProgram;

// Import available backends directly
const CpuBackend = @import("backends").cpu;
// TODO: Add other backends when implemented
// const CudaBackend = @import("../backends/cuda.zig");
// const MetalBackend = @import("../backends/metal.zig");

// Import core types
const types = @import("types");
const NodeId = types.NodeId;

const Graph = @import("graph").Graph;
const Node = @import("graph").Node;

// Import unified compiler pipeline
const pass_templates = @import("pass_templates.zig");
const PassContext = @import("unified_pipeline.zig").PassContext;

// Import passes
const pattern_recognition = @import("passes/patterns.zig");
const ConstantFoldingPass = @import("passes/constant_folding.zig").ConstantFoldingPass;
const DeadCodeEliminationPass = @import("passes/dce.zig").DeadCodeEliminationPass;
const CommonSubexpressionEliminationPass = @import("passes/cse.zig").CommonSubexpressionEliminationPass;
const MemoryPlanningPass = @import("passes/memory_planning.zig").MemoryPlanningPass;
const ShapeInferencePass = @import("passes/shape_inference.zig").ShapeInference;
const AutogradPass = @import("passes/autograd.zig").AutogradPass;

// ============================================================================
// Main Compilation API
// ============================================================================

// Import backend types
const backend_types = @import("backend_types");
const CompiledGraph = backend_types.CompiledGraph;

/// Compile with CPU backend (most common case)
pub fn compileCpu(graph: *Graph, allocator: Allocator) !CompiledGraph {
    if (@import("build_options").enable_debug_logs) {
        std.debug.print("\ncompileCpu: Starting compilation\n", .{});
        std.debug.print("  graph.output_nodes = {any}\n", .{graph.output_nodes.items});
        std.debug.print("  graph has {} nodes, next_node_id={}\n", .{graph.nodes.items.len, graph.next_node_id});
        for (0..graph.next_node_id) |i| {
            const nid: NodeId = @intCast(i);
            std.debug.print("  Node {} is_valid={}\n", .{nid, graph.hasNode(nid)});
        }
    }
    
    // Create PassContext for the compilation pipeline
    var ctx = PassContext.init(graph, allocator);
    defer ctx.deinit(); // Ensure cleanup even on error
    
    // Run optimization passes with the context
    try runCompilerPipelineWithContext(CpuBackend, &ctx);
    
    // Transfer memory plan to CPU compilation context
    var cpu_ctx = CpuBackend.CpuCompilationContext{
        .memory_plan = ctx.takeMemoryPlan(),
    };
    
    // Use CPU backend's compile function with the context
    return CpuBackend.compileWithContext(graph, &cpu_ctx, allocator);
}

/// Generic compile function with comptime backend selection
pub fn compile(comptime Backend: type, graph: *Graph, allocator: Allocator) !CompiledGraph {
    // Validate backend at compile time
    comptime validateBackend(Backend);
    
    // Run the unified compiler pipeline with backend-specific optimizations
    try runCompilerPipeline(Backend, graph, allocator);
    
    // Compile with the backend
    return compileWithBackend(Backend, graph, allocator);
}

/// Run the complete compiler pipeline with backend-aware passes
fn runCompilerPipeline(comptime Backend: type, graph: *Graph, allocator: Allocator) !void {
    var ctx = PassContext.init(graph, allocator);
    defer ctx.deinit(); // Ensure cleanup
    
    try runCompilerPipelineWithContext(Backend, &ctx);
}

/// Run the compiler pipeline with an existing context
fn runCompilerPipelineWithContext(comptime Backend: type, ctx: *PassContext) !void {
    // Phase 1: Shape Inference and Validation
    std.log.debug("Running shape inference pass", .{});
    try ShapeInferencePass.run(ctx);
    std.log.debug("Shape inference pass completed", .{});
    
    // Phase 2: Autograd (if gradients are requested)
    // This runs AFTER shape inference so it has complete shape information
    if (ctx.graph.hasGradientsEnabled()) {
        std.log.debug("Running autograd pass", .{});
        if (ctx.graph.gradient_state) |grad_state| {
            std.log.debug("  Gradient state exists, requires_grad_nodes: {}", .{grad_state.requires_grad_nodes.count()});
            std.log.debug("  Loss node: {}", .{grad_state.config.loss_node});
        }
        try AutogradPass.run(ctx);
        std.log.debug("Autograd pass completed", .{});
        if (ctx.graph.gradient_state) |grad_state| {
            std.log.debug("  Gradient map after autograd: {} entries", .{grad_state.gradient_map.count()});
        }
    }
    
    // Phase 3: Pattern Recognition (backend-specific)
    const PatternRecognitionPass = pattern_recognition.PatternRecognition(Backend);
    try PatternRecognitionPass.run(ctx);
    
    // Phase 4: Algebraic Optimizations
    try ConstantFoldingPass.run(ctx);
    try CommonSubexpressionEliminationPass.run(ctx);
    
    // Phase 5: Dead Code Elimination
    try DeadCodeEliminationPass.run(ctx);
    
    // Phase 6: Memory Planning
    try MemoryPlanningPass.run(ctx);
    
    // TODO: Add other passes as they get migrated:
    // - AlgebraicPass.run(&ctx);
    // - FusionPass.run(&ctx);
    // - etc.
}

// ============================================================================
// Compilation Options and Configuration
// ============================================================================

/// Compilation options
pub const CompilationOptions = struct {
    optimization_level: OptimizationLevel = .balanced,
    enable_fusion: bool = true,
    enable_cse: bool = true,
    enable_dce: bool = true,
    debug_mode: bool = false,
};

/// Optimization levels
pub const OptimizationLevel = enum {
    none,       // No optimizations
    basic,      // Basic optimizations only
    balanced,   // Good balance of compile time vs performance
    aggressive, // Maximum optimizations
};

/// Compile with options
pub fn compileWithOptions(
    comptime Backend: type,
    graph: *Graph,
    options: CompilationOptions,
    allocator: Allocator,
) !CompiledProgram {
    // Store options in context for passes to use
    var ctx = PassContext.init(graph, allocator);
    // TODO: Add options to PassContext
    
    // Run optimizations based on options
    if (options.optimization_level != .none) {
        if (options.enable_cse) {
            try CommonSubexpressionEliminationPass.run(&ctx);
        }
        
        if (options.enable_dce) {
            try DeadCodeEliminationPass.run(&ctx);
        }
        
        // Always run pattern recognition and constant folding
        const PatternRecognitionPass = pattern_recognition.PatternRecognition(Backend);
        try PatternRecognitionPass.run(&ctx);
        try ConstantFoldingPass.run(&ctx);
    }
    
    // Always run memory planning
    try MemoryPlanningPass.run(&ctx);
    
    return compileWithBackend(Backend, graph, allocator);
}

// ============================================================================
// Backend Selection Helpers
// ============================================================================

/// Compile-time backend selection based on target
pub fn compileForTarget(comptime target: std.Target, graph: *Graph, allocator: Allocator) !CompiledProgram {
    return switch (target.cpu.arch) {
        .x86_64, .aarch64 => compileCpu(graph, allocator),
        // TODO: Add GPU backends when implemented
        // .nvptx64 => compile(CudaBackend, graph, allocator),
        else => @compileError("Unsupported target architecture for compilation"),
    };
}

/// Runtime backend selection (for cases where compile-time isn't possible)
pub const RuntimeBackend = enum {
    cpu,
    // TODO: Add when implemented
    // cuda,
    // metal,
    // vulkan,
};

/// Compile with runtime backend selection (less efficient)
pub fn compileRuntime(backend: RuntimeBackend, graph: *Graph, allocator: Allocator) !CompiledProgram {
    return switch (backend) {
        .cpu => compileCpu(graph, allocator),
        // TODO: Add other backends
    };
}

// ============================================================================
// Unit Tests
// ============================================================================

test "compile with CPU backend" {
    const testing = std.testing;
    
    var graph = try Graph.init(testing.allocator);
    defer graph.deinit();
    
    // Create a simple graph
    const a = try graph.addPlaceholder(.f32);
    const b = try graph.addPlaceholder(.f32);
    _ = try graph.addNode(.add, &.{a, b}, .f32);
    
    // Compile with CPU backend
    var program = try compileCpu(&graph, testing.allocator);
    defer program.deinit(testing.allocator);
}

test "compile with generic backend" {
    const testing = std.testing;
    
    var graph = try Graph.init(testing.allocator);
    defer graph.deinit();
    
    // Create a simple graph
    const a = try graph.addPlaceholder(.f32);
    const b = try graph.addPlaceholder(.f32);
    _ = try graph.addNode(.mul, &.{a, b}, .f32);
    
    // Compile with explicit backend selection
    var program = try compile(CpuBackend, &graph, testing.allocator);
    defer program.deinit(testing.allocator);
}

test "compile with options" {
    const testing = std.testing;
    
    var graph = try Graph.init(testing.allocator);
    defer graph.deinit();
    
    // Create a graph with optimization opportunities
    const a = try graph.addPlaceholder(.f32);
    const zero = try graph.addConstant(0.0);
    const add_zero = try graph.addNode(.add, &.{a, zero}, .f32); // Should be optimized
    _ = try graph.addNode(.mul, &.{add_zero, add_zero}, .f32);
    
    const options = CompilationOptions{
        .optimization_level = .aggressive,
        .enable_fusion = true,
        .enable_cse = true,
        .enable_dce = true,
    };
    
    var program = try compileWithOptions(CpuBackend, &graph, options, testing.allocator);
    defer program.deinit(testing.allocator);
}

test "target-based compilation" {
    const testing = std.testing;
    
    var graph = try Graph.init(testing.allocator);
    defer graph.deinit();
    
    const a = try graph.addPlaceholder(.f32);
    const b = try graph.addPlaceholder(.f32);
    _ = try graph.addNode(.add, &.{a, b}, .f32);
    
    // This should select CPU backend for current target
    var program = try compileForTarget(std.builtin.target, &graph, testing.allocator);
    defer program.deinit(testing.allocator);
}

test "runtime backend selection" {
    const testing = std.testing;
    
    var graph = try Graph.init(testing.allocator);
    defer graph.deinit();
    
    const a = try graph.addPlaceholder(.f32);
    const b = try graph.addPlaceholder(.f32);
    _ = try graph.addNode(.add, &.{a, b}, .f32);
    
    // Runtime selection (less efficient but sometimes necessary)
    var program = try compileRuntime(.cpu, &graph, testing.allocator);
    defer program.deinit(testing.allocator);
}