/// Elementwise Fusion Pass - Template-Based Implementation
///
/// This module implements elegant elementwise fusion using the new template system.
/// It replaces the complex old pattern infrastructure with simple, declarative patterns
/// that are easier to understand, maintain, and extend.

const std = @import("std");
const Allocator = std.mem.Allocator;

// Import types
const types = @import("types");
const NodeId = types.NodeId;
const ComputeOp = types.ComputeOp;
const DataType = types.DataType;
const FusionPattern = types.FusionPattern;
const CustomOp = types.CustomOp;

const Graph = @import("graph").Graph;
const Node = @import("graph").Node;

// Import new template infrastructure
const pass_templates = @import("../pass_templates.zig");
const patterns_mod = @import("../patterns.zig");
const transforms = @import("../transforms.zig");
const PassContext = @import("../unified_pipeline.zig").PassContext;

// ============================================================================
// Fusion Pattern Definitions
// ============================================================================

/// Common elementwise fusion patterns
const fusion_patterns = [_]patterns_mod.Pattern{
    // Pattern 1: (a + b) * c -> fused_add_mul(a, b, c)
    .{
        .name = "add_mul_fusion",
        .match = .{ .op = .{ .op = .mul, .inputs = &.{
            .{ .op = .{ .op = .add, .inputs = &.{ 
                .{ .@"var" = "a" }, 
                .{ .@"var" = "b" } 
            }}},
            .{ .@"var" = "c" },
        }}},
        .replace = .{ .custom = .{ .name = "fused_add_mul", .inputs = &.{ "a", "b", "c" } } },
        .priority = 80,
    },
    
    // Pattern 2: (a * b) + c -> fused_mul_add(a, b, c) 
    .{
        .name = "mul_add_fusion",
        .match = .{ .op = .{ .op = .add, .inputs = &.{
            .{ .op = .{ .op = .mul, .inputs = &.{ 
                .{ .@"var" = "a" }, 
                .{ .@"var" = "b" } 
            }}},
            .{ .@"var" = "c" },
        }}},
        .replace = .{ .custom = .{ .name = "fused_mul_add", .inputs = &.{ "a", "b", "c" } } },
        .priority = 80,
    },
    
    // Pattern 3: a * (b + c) -> fused_mul_add_broadcast(a, b, c)
    .{
        .name = "mul_add_broadcast_fusion",
        .match = .{ .op = .{ .op = .mul, .inputs = &.{
            .{ .@"var" = "a" },
            .{ .op = .{ .op = .add, .inputs = &.{ 
                .{ .@"var" = "b" }, 
                .{ .@"var" = "c" } 
            }}},
        }}},
        .replace = .{ .custom = .{ .name = "fused_mul_add_broadcast", .inputs = &.{ "a", "b", "c" } } },
        .priority = 75,
    },
    
    // Pattern 4: sqrt(a * b) -> fused_mul_sqrt(a, b)
    .{
        .name = "mul_sqrt_fusion",
        .match = .{ .op = .{ .op = .sqrt, .inputs = &.{
            .{ .op = .{ .op = .mul, .inputs = &.{ 
                .{ .@"var" = "a" }, 
                .{ .@"var" = "b" } 
            }}},
        }}},
        .replace = .{ .custom = .{ .name = "fused_mul_sqrt", .inputs = &.{ "a", "b" } } },
        .priority = 75,
    },
    
    // Pattern 5: exp2(a + b) -> fused_add_exp2(a, b)
    .{
        .name = "add_exp2_fusion",
        .match = .{ .op = .{ .op = .exp2, .inputs = &.{
            .{ .op = .{ .op = .add, .inputs = &.{ 
                .{ .@"var" = "a" }, 
                .{ .@"var" = "b" } 
            }}},
        }}},
        .replace = .{ .custom = .{ .name = "fused_add_exp2", .inputs = &.{ "a", "b" } } },
        .priority = 75,
    },
    
    // Pattern 6: recip(sqrt(a)) -> fused_sqrt_recip(a)
    .{
        .name = "sqrt_recip_fusion",
        .match = .{ .op = .{ .op = .recip, .inputs = &.{
            .{ .op = .{ .op = .sqrt, .inputs = &.{ 
                .{ .@"var" = "a" } 
            }}},
        }}},
        .replace = .{ .custom = .{ .name = "fused_sqrt_recip", .inputs = &.{ "a" } } },
        .priority = 75,
    },
    
    // Pattern 7: sum_reduce(a * b) -> fused_mul_sum(a, b) - common in ML
    .{
        .name = "mul_sum_fusion",
        .match = .{ .op = .{ .op = .sum_reduce, .inputs = &.{
            .{ .op = .{ .op = .mul, .inputs = &.{ 
                .{ .@"var" = "a" }, 
                .{ .@"var" = "b" } 
            }}},
        }}},
        .replace = .{ .custom = .{ .name = "fused_mul_sum", .inputs = &.{ "a", "b" } } },
        .priority = 85, // Higher priority for reductions
    },
    
    // Pattern 8: sum_reduce(exp2(a)) -> fused_exp2_sum(a) - common in softmax
    .{
        .name = "exp2_sum_fusion",
        .match = .{ .op = .{ .op = .sum_reduce, .inputs = &.{
            .{ .op = .{ .op = .exp2, .inputs = &.{ 
                .{ .@"var" = "a" } 
            }}},
        }}},
        .replace = .{ .custom = .{ .name = "fused_exp2_sum", .inputs = &.{ "a" } } },
        .priority = 85, // Higher priority for reductions
    },
};

// ============================================================================
// Backend-Specific Fusion Extensions
// ============================================================================

/// Get fusion patterns for a specific backend
pub fn getFusionPatterns(comptime Backend: type) []const patterns_mod.Pattern {
    // Start with common patterns
    var all_patterns = fusion_patterns;
    
    // Add backend-specific patterns if available
    if (@hasDecl(Backend, "fusion_patterns")) {
        all_patterns = all_patterns ++ Backend.fusion_patterns;
    }
    
    return &all_patterns;
}

// ============================================================================
// Fusion Utilities
// ============================================================================

/// Check if an operation is elementwise and fuseable
fn isElementwiseFuseable(op: ComputeOp) bool {
    return switch (op) {
        // Basic arithmetic
        .add, .mul, .sub, .div => true,
        
        // Math functions
        .sin, .cos, .sqrt, .exp2, .log2, .recip => true,
        
        // Comparison (elementwise)
        .less_than => true,
        
        // Reductions can be endpoints of fusion
        .sum_reduce, .max_reduce, .min_reduce, .mean_reduce => true,
        
        // Non-fuseable operations
        .gather, .transpose => false,
        else => false,
    };
}

/// Custom pattern filter for fusion-specific logic
fn fusionPatternFilter(pattern: *const patterns_mod.Pattern) bool {
    // Accept all fusion patterns by default
    // Could add more sophisticated filtering based on:
    // - Backend capabilities
    // - Graph size/complexity
    // - Performance heuristics
    _ = pattern;
    return true;
}

/// Pre-conditions: ensure operations are fuseable
fn fusionPreConditions(ctx: *PassContext) bool {
    _ = ctx;
    // Could check:
    // - Graph has elementwise operations
    // - Backend supports fusion
    // - Memory constraints
    return true;
}

// ============================================================================
// Template-Based Pass Definition  
// ============================================================================

/// Create fusion pass for a specific backend
pub fn ElementwiseFusion(comptime Backend: type) type {
    const backend_patterns = getFusionPatterns(Backend);
    
    return pass_templates.createPatternPass(.{
        .name = "elementwise_fusion",
        .priority = 70,
        .patterns = backend_patterns,
        .max_iterations = 3, // Allow multiple rounds of fusion
        .pattern_filter = fusionPatternFilter,
        .pre_conditions = fusionPreConditions,
    });
}

/// Default fusion pass (backend-agnostic)
pub const DefaultElementwiseFusion = ElementwiseFusion(struct {});

// ============================================================================
// AOT Kernel Integration  
// ============================================================================

/// Compute hash for fusion pattern (for AOT kernel lookup)
pub fn computePatternHash(pattern: FusionPattern) u64 {
    var hasher = std.hash.Wyhash.init(0);
    
    // Hash the operations in the pattern
    for (pattern.ops) |op| {
        hasher.update(std.mem.asBytes(&op));
    }
    
    return hasher.final();
}

/// Check if a custom operation has an AOT kernel
pub fn hasAOTKernel(custom_op: CustomOp) bool {
    // TODO: Integrate with actual AOT kernel registry
    // For now, assume common fusion patterns have kernels
    const common_kernels = [_][]const u8{
        "fused_add_mul",
        "fused_mul_add", 
        "fused_mul_sqrt",
        "fused_add_exp2",
        "fused_sqrt_recip",
        "fused_mul_sum",
        "fused_exp2_sum",
    };
    
    for (common_kernels) |kernel_name| {
        if (std.mem.eql(u8, custom_op.debug_name, kernel_name)) {
            return true;
        }
    }
    
    return false;
}

/// Handle missing AOT kernel (fallback strategy)
pub fn handleMissingAOTKernel(ctx: *PassContext) !void {
    // TODO: Implement fallback strategies:
    // 1. Unfuse the operation back to individual ops
    // 2. Generate kernel at compile time
    // 3. Use generic interpretation
    _ = ctx;
}

// ============================================================================
// Legacy Function (Remove when migration complete)
// ============================================================================

pub fn elementwiseFusion(ctx: *PassContext) !void {
    return DefaultElementwiseFusion.run(ctx);
}

pub fn operatorFusion(ctx: *PassContext) !void {
    // Operator fusion is just elementwise fusion with a different name
    return elementwiseFusion(ctx);
}

// ============================================================================
// Unit Tests
// ============================================================================

test "fusion pattern definitions" {
    const testing = std.testing;
    
    // Test that patterns are well-formed
    try testing.expect(fusion_patterns.len > 0);
    
    for (fusion_patterns) |pattern| {
        try testing.expect(pattern.name.len > 0);
        try testing.expect(pattern.priority > 0);
    }
}

test "elementwise fusion pass creation" {
    const testing = std.testing;
    
    // Test default fusion pass
    try testing.expectEqualStrings("elementwise_fusion", DefaultElementwiseFusion.name);
    try testing.expectEqual(@as(u32, 70), DefaultElementwiseFusion.priority);
    try testing.expectEqual(@as(u32, 3), DefaultElementwiseFusion.max_iterations);
    
    // Test backend-specific fusion pass
    const TestBackend = struct {
        pub const fusion_patterns = [_]patterns_mod.Pattern{
            .{
                .name = "test_fusion",
                .match = .{ .@"var" = "x" },
                .replace = .{ .@"var" = "x" },
                .priority = 50,
            },
        };
    };
    
    const BackendFusion = ElementwiseFusion(TestBackend);
    try testing.expectEqualStrings("elementwise_fusion", BackendFusion.name);
}

test "fusion utilities" {
    const testing = std.testing;
    
    // Test elementwise detection
    try testing.expect(isElementwiseFuseable(.add));
    try testing.expect(isElementwiseFuseable(.mul));
    try testing.expect(isElementwiseFuseable(.sqrt));
    try testing.expect(isElementwiseFuseable(.sum_reduce)); // Endpoint
    
    try testing.expect(!isElementwiseFuseable(.gather));
    try testing.expect(!isElementwiseFuseable(.transpose));
}

test "AOT kernel integration" {
    const testing = std.testing;
    
    // Test pattern hashing
    const pattern = FusionPattern{
        .ops = &[_]ComputeOp{ .add, .mul },
        .connections = &[_]types.Connection{},
    };
    
    const hash1 = computePatternHash(pattern);
    const hash2 = computePatternHash(pattern);
    try testing.expectEqual(hash1, hash2); // Deterministic
    
    // Test AOT kernel lookup
    const custom_op = CustomOp{
        .id = 1,
        .backend_name = "cpu",
        .fusion_pattern = pattern,
        .debug_name = "fused_add_mul",
    };
    
    try testing.expect(hasAOTKernel(custom_op));
    
    const unknown_op = CustomOp{
        .id = 2,
        .backend_name = "cpu", 
        .fusion_pattern = pattern,
        .debug_name = "unknown_fusion",
    };
    
    try testing.expect(!hasAOTKernel(unknown_op));
}

test "fusion pass execution" {
    const testing = std.testing;
    
    var graph = try Graph.init(testing.allocator);
    defer graph.deinit();
    
    // Create a pattern that should be fused: (a + b) * c
    const a = try graph.addPlaceholder(.f32);
    const b = try graph.addPlaceholder(.f32);
    const c = try graph.addPlaceholder(.f32);
    
    const add_result = try graph.addNode(.add, &.{a, b}, .f32);
    const mul_result = try graph.addNode(.mul, &.{add_result, c}, .f32);
    
    var ctx = PassContext.init(&graph, testing.allocator);
    try DefaultElementwiseFusion.run(&ctx);
    
    // Graph should still be valid after fusion
    try testing.expect(graph.nodes.items.len >= 4);
    _ = mul_result; // Suppress unused warning
}

test "backend-specific fusion patterns" {
    const testing = std.testing;
    
    const TestBackend = struct {
        pub const fusion_patterns = [_]patterns_mod.Pattern{
            .{
                .name = "backend_specific_fusion",
                .match = .{ .op = .{ .op = .add, .inputs = &.{
                    .{ .@"var" = "x" },
                    .{ .@"var" = "y" },
                }}},
                .replace = .{ .custom = .{ .name = "backend_add", .inputs = &.{ "x", "y" } } },
                .priority = 60,
            },
        };
    };
    
    const patterns = getFusionPatterns(TestBackend);
    
    // Should include both common and backend-specific patterns
    try testing.expect(patterns.len > fusion_patterns.len);
    
    // Find the backend-specific pattern
    var found_backend_pattern = false;
    for (patterns) |pattern| {
        if (std.mem.eql(u8, pattern.name, "backend_specific_fusion")) {
            found_backend_pattern = true;
            break;
        }
    }
    try testing.expect(found_backend_pattern);
}