/// Shape Inference Pass - Validates and completes shape metadata
/// 
/// DESIGN PHILOSOPHY: Smart inference that respects existing metadata
/// 
/// This pass traverses the graph and ensures all nodes have valid shape metadata.
/// It respects shapes already set by operations during node creation and only
/// computes shapes when they're missing. This approach:
/// 
/// 1. Allows operations to set complete metadata (best knowledge at creation time)
/// 2. Provides a safety net for any missing shapes
/// 3. Validates shape consistency across the graph
/// 4. Respects semantic metadata (like keepdims) set by operations
/// 
/// The pass reads semantic metadata (reduction_axis, keepdims, etc.) to
/// correctly infer shapes for operations that transform dimensions.

const std = @import("std");
const Allocator = std.mem.Allocator;

// Import types
const types = @import("types");
const NodeId = types.NodeId;
const ComputeOp = types.ComputeOp;

// Import graph components
const Graph = @import("graph").Graph;
const Node = @import("graph").Node;
const NodeMetadata = @import("graph").NodeMetadata;

// Import shape components
const shape_mod = @import("shape");
const ShapeTracker = shape_mod.ShapeTracker;
const SymbolicDim = shape_mod.SymbolicDim;

// Import symbolic components
const symbolic = @import("symbolic");
const SymbolicPool = symbolic.SymbolicPool;

// Import pipeline and template infrastructure
const pipeline_mod = @import("../unified_pipeline.zig");
const PassContext = pipeline_mod.PassContext;
const pass_templates = @import("../pass_templates.zig");

// ============================================================================
// Shape Inference Pass using New Template System
// ============================================================================

/// Shape inference pass using the template system
pub const ShapeInference = pass_templates.createTraversalPass(.{
    .name = "shape_inference",
    .priority = 100, // Must run early
    .visitor = inferNodeShapeVisitor,
    .pre_conditions = null, // No special pre-conditions
    .post_conditions = validateShapeConsistency,
});

/// Main entry point for shape inference (wrapper for compatibility)
pub fn inferShapes(ctx: *PassContext) !void {
    try ShapeInference.run(ctx);
}

/// Visitor function for shape inference
/// 
/// DESIGN: This function respects existing metadata. If a node already has
/// valid shape metadata (set by operations), we trust it and skip inference.
/// This allows operations to set complete metadata while still providing
/// a safety net for any missing shapes.
fn inferNodeShapeVisitor(ctx: *PassContext, node_id: NodeId, node: *const Node) !void {
    const graph = ctx.graph;
    
    if (@import("build_options").enable_debug_logs) {
        std.debug.print("Shape inference visiting node {} ({})\n", .{node_id, node.spec});
        // Debug output_nodes corruption
        if (node_id < 3) {
            std.debug.print("  output_nodes at shape inference: {any}\n", .{graph.output_nodes.items});
        }
    }
    
    // Get mutable node for updating metadata
    const node_mut = graph.getNodeMut(node_id) orelse return;
    
    // SMART INFERENCE: Skip if node already has valid shape metadata
    // This respects shapes set by operations during node creation
    const needs_output_shape = node_mut.metadata == null or node_mut.metadata.?.output_shape == null;
    
    var output_shape: ShapeTracker = undefined;
    if (needs_output_shape) {
        // Only infer if shape is missing
        output_shape = try inferNodeShape(graph, node, ctx.allocator);
        
        // Store in metadata
        if (node_mut.metadata == null) {
            node_mut.metadata = try graph.arena.allocator().create(NodeMetadata);
            node_mut.metadata.?.* = .{};
        }
        node_mut.metadata.?.output_shape = output_shape;
    } else {
        // Trust existing shape metadata set by operations
        output_shape = node_mut.metadata.?.output_shape.?;
        if (@import("build_options").enable_debug_logs) {
            std.debug.print("  Node {} already has shape metadata, skipping inference\n", .{node_id});
        }
    }
    
    // Store input shapes for operations that need them
    if (node.inputs.len > 0) {
        // For elementwise operations with broadcasting, we need to create broadcasted input shapes
        if (node.spec == .compute and node.spec.compute.isElementwise() and node.inputs.len > 1) {
            const enable_debug = @import("build_options").enable_debug_logs;
            if (enable_debug) {
                std.debug.print("Shape inference: Creating broadcasted shapes for node {}\n", .{node_id});
            }
            // Get original input shapes
            const original_shapes = try ctx.allocator.alloc(ShapeTracker, node.inputs.len);
            defer ctx.allocator.free(original_shapes);
            
            // Check if we have pre-stored input shapes from view operations
            const has_prestored = node_mut.metadata != null and 
                                node_mut.metadata.?.input_shapes != null and
                                node_mut.metadata.?.input_shapes.?.len == node.inputs.len;
            
            if (has_prestored) {
                // Use the pre-stored shapes from view operations
                if (enable_debug) {
                    std.debug.print("  Using pre-stored input shapes from view operations\n", .{});
                }
                for (node_mut.metadata.?.input_shapes.?, original_shapes) |stored_shape, *orig_shape| {
                    orig_shape.* = stored_shape;
                    if (enable_debug) {
                        std.debug.print("  Pre-stored shape: [", .{});
                        for (orig_shape.*.dims) |d| std.debug.print("{} ", .{d.concrete});
                        std.debug.print("]\n", .{});
                    }
                }
            } else {
                // Get shapes from input nodes  
                for (node.inputs, original_shapes) |input_id, *orig_shape| {
                    const input_node = graph.getNode(input_id) orelse {
                        std.log.err("Shape inference: input node {} not found", .{input_id});
                        return error.InvalidInput;
                    };
                    if (input_node.metadata == null or input_node.metadata.?.output_shape == null) {
                        std.log.err("Shape inference: input node {} has no shape metadata", .{input_id});
                        return error.MissingInputShape;
                    }
                    orig_shape.* = input_node.metadata.?.output_shape.?;
                    if (enable_debug) {
                        std.debug.print("  Input node {} has shape: [", .{input_id});
                        for (orig_shape.*.dims) |d| std.debug.print("{} ", .{d.concrete});
                        std.debug.print("]\n", .{});
                    }
                }
            }
            
            // Create broadcasted versions of input shapes
            const input_shapes = try graph.arena.allocator().alloc(ShapeTracker, node.inputs.len);
            if (enable_debug) {
                std.debug.print("  Output shape has {} dims\n", .{output_shape.dims.len});
            }
            for (original_shapes, input_shapes, 0..) |orig_shape, *input_shape, i| {
                if (enable_debug) {
                    std.debug.print("  Input {} original shape: [", .{i});
                    for (orig_shape.dims) |d| std.debug.print("{} ", .{d.concrete});
                    std.debug.print("]\n", .{});
                }
                input_shape.* = try createBroadcastedShape(orig_shape, output_shape, graph.arena.allocator(), &graph.symbolic_pool);
                if (enable_debug) {
                    std.debug.print("  Input {} broadcasted shape: [", .{i});
                    for (input_shape.dims) |d| std.debug.print("{} ", .{d.concrete});
                    std.debug.print("], strides=[", .{});
                    for (input_shape.strides) |s| std.debug.print("{} ", .{s.concrete});
                    std.debug.print("], fake=[", .{});
                    for (input_shape.fake) |f| std.debug.print("{} ", .{f});
                    std.debug.print("]\n", .{});
                }
            }
            
            // Always update input shapes for elementwise operations (overwrite pre-stored)
            if (node_mut.metadata == null) {
                node_mut.metadata = try graph.arena.allocator().create(NodeMetadata);
                node_mut.metadata.?.* = .{};
            }
            node_mut.metadata.?.input_shapes = input_shapes;
        } else if (node_mut.metadata == null or node_mut.metadata.?.input_shapes == null) {
            // For non-elementwise ops or single input, just clone the shapes
            const input_shapes = try graph.arena.allocator().alloc(ShapeTracker, node.inputs.len);
            for (node.inputs, input_shapes) |input_id, *input_shape| {
                const input_node = graph.getNode(input_id) orelse {
                    std.log.err("Shape inference: input node {} not found", .{input_id});
                    return error.InvalidInput;
                };
                if (input_node.metadata == null or input_node.metadata.?.output_shape == null) {
                    std.log.err("Shape inference: input node {} has no shape metadata", .{input_id});
                    return error.MissingInputShape;
                }
                input_shape.* = try input_node.metadata.?.output_shape.?.clone(graph.arena.allocator());
            }
            
            if (node_mut.metadata == null) {
                node_mut.metadata = try graph.arena.allocator().create(NodeMetadata);
                node_mut.metadata.?.* = .{};
            }
            node_mut.metadata.?.input_shapes = input_shapes;
        }
    }
}

/// Post-condition: validate shape consistency
fn validateShapeConsistency(ctx: *PassContext) !void {
    const topo_order = try ctx.graph.topologicalSort();
    
    for (topo_order) |node_id| {
        const node = ctx.graph.getNode(node_id) orelse continue;
        
        // All compute nodes must have output shapes
        if (node.spec == .compute) {
            if (node.metadata == null or node.metadata.?.output_shape == null) {
                std.log.err("Post-condition failed: compute node {} has no output shape", .{node_id});
                return error.MissingOutputShape;
            }
        }
    }
}

/// Infer output shape for a single node
fn inferNodeShape(graph: *Graph, node: *const Node, allocator: Allocator) !ShapeTracker {
    switch (node.spec) {
        .data => |data_type| switch (data_type) {
            .constant => {
                // Constants are scalars by default
                var empty_dims = [_]SymbolicDim{};
                return ShapeTracker.fromDims(&empty_dims, allocator, &graph.symbolic_pool);
            },
            .placeholder => {
                // Placeholders need shape to be set externally
                // Check if it's already been set (e.g., from TensorHandle)
                if (node.metadata) |metadata| {
                    if (metadata.output_shape) |shape| {
                        return shape;
                    }
                }
                std.log.err("Shape inference: placeholder node {} needs external shape", .{node.id});
                return error.PlaceholderNeedsShape;
            },
            .parameter => {
                // Parameters need shape from parameter store
                std.log.err("Shape inference: parameter node {} needs external shape", .{node.id});
                return error.ParameterNeedsShape;
            },
        },
        .compute => |op| {
            // Get input shapes
            if (node.inputs.len == 0) {
                std.log.err("Shape inference: compute node {} has no inputs", .{node.id});
                return error.NoInputs;
            }
            
            var input_shapes = try allocator.alloc(ShapeTracker, node.inputs.len);
            defer allocator.free(input_shapes);
            
            for (node.inputs, input_shapes) |input_id, *input_shape| {
                const input_node = graph.getNode(input_id) orelse {
                    std.log.err("Shape inference: input {} not found for node {}", .{input_id, node.id});
                    return error.InputNotFound;
                };
                if (input_node.metadata == null or input_node.metadata.?.output_shape == null) {
                    std.log.err("Shape inference: input {} has no shape for node {}", .{input_id, node.id});
                    return error.MissingInputShape;
                }
                input_shape.* = input_node.metadata.?.output_shape.?;
            }
            
            // Infer based on operation type
            return switch (op) {
                // Elementwise operations preserve shape
                .add, .mul, .mod, .less_than => try inferElementwiseShape(input_shapes, graph.arena.allocator(), &graph.symbolic_pool),
                
                // Unary operations preserve shape
                .recip, .sqrt, .sin, .exp2, .log2 => input_shapes[0].clone(graph.arena.allocator()),
                
                // Reductions change shape
                .sum_reduce, .max_reduce => try inferReductionShape(node, input_shapes[0], graph.arena.allocator(), &graph.symbolic_pool),
                
                // Memory operations preserve shape
                .contiguous => input_shapes[0].clone(graph.arena.allocator()),
                
                // Custom operations need backend-specific handling
                .custom => {
                    std.log.warn("Shape inference: custom op for node {}, using input shape", .{node.id});
                    return input_shapes[0].clone(graph.arena.allocator());
                },
            };
        },
    }
}

/// Infer shape for elementwise operations (with broadcasting)
fn inferElementwiseShape(input_shapes: []const ShapeTracker, allocator: Allocator, pool: *SymbolicPool) !ShapeTracker {
    if (input_shapes.len == 0) return error.NoInputs;
    if (input_shapes.len == 1) return input_shapes[0].clone(allocator);
    
    // Start with first shape
    var result = try input_shapes[0].clone(allocator);
    
    // Broadcast with remaining shapes
    for (input_shapes[1..]) |shape| {
        result = try broadcastShapes(result, shape, allocator, pool);
    }
    
    return result;
}

/// Create a broadcasted version of an input shape to match the target shape
/// This is used to create input shapes with proper strides for broadcasting
fn createBroadcastedShape(input: ShapeTracker, target: ShapeTracker, allocator: Allocator, pool: *SymbolicPool) !ShapeTracker {
    _ = pool; // TODO: Use pool for symbolic dimension handling
    
    const enable_debug = @import("build_options").enable_debug_logs;
    if (enable_debug) {
        std.debug.print("createBroadcastedShape: input.dims.len={}, target.dims.len={}\n", .{input.dims.len, target.dims.len});
    }
    
    // For scalar inputs (0D), create a fully broadcasted shape
    if (input.dims.len == 0) {
        // Create new shape with all dimensions from target
        var new_dims = try allocator.alloc(SymbolicDim, target.dims.len);
        var new_strides = try allocator.alloc(SymbolicDim, target.dims.len);
        var new_indexes = try allocator.alloc(u8, target.dims.len);
        var new_fake = try allocator.alloc(bool, target.dims.len);
        
        // All dimensions are broadcasted (stride=0, fake=true)
        for (target.dims, 0..) |tgt_dim, i| {
            new_dims[i] = tgt_dim;
            new_strides[i] = SymbolicDim{ .concrete = 0 };
            new_indexes[i] = @intCast(i);
            new_fake[i] = true;
        }
        
        return ShapeTracker{
            .dims = new_dims,
            .strides = new_strides,
            .offset = SymbolicDim{ .concrete = 0 },
            .indexes = new_indexes,
            .fake = new_fake,
            .mask = null,
            .padding = null,
        };
    }
    
    // Start with a clone of the input
    var result = try input.clone(allocator);
    
    // Expand dimensions to match target rank
    const rank_diff = @as(i64, @intCast(target.dims.len)) - @as(i64, @intCast(input.dims.len));
    if (rank_diff > 0) {
        // Need to add dimensions at the front
        for (0..@intCast(rank_diff)) |i| {
            // Add dimension at front with size from target
            try result.expand(0, target.dims[i], allocator);
        }
        if (enable_debug) {
            std.debug.print("    After expansion: dims=[", .{});
            for (result.dims) |d| std.debug.print("{} ", .{d.concrete});
            std.debug.print("], strides=[", .{});
            for (result.strides) |s| std.debug.print("{} ", .{s.concrete});
            std.debug.print("]\n", .{});
        }
    }
    
    // Now adjust dimensions that need broadcasting
    // Since the fields are const, we need to create new arrays
    var new_strides = try allocator.dupe(SymbolicDim, result.strides);
    var new_fake = try allocator.dupe(bool, result.fake);
    var new_dims = try allocator.dupe(SymbolicDim, result.dims);
    
    if (enable_debug) {
        std.debug.print("    Adjusting dimensions: result.dims.len={}, target.dims.len={}\n", .{result.dims.len, target.dims.len});
    }
    
    // Ensure we only iterate up to the minimum length
    const min_len = @min(result.dims.len, target.dims.len);
    for (0..min_len) |i| {
        const res_dim = result.dims[i];
        const tgt_dim = target.dims[i];
        if (res_dim == .concrete and tgt_dim == .concrete) {
            if (res_dim.concrete == 1 and tgt_dim.concrete != 1) {
                // This dimension needs to be broadcast
                // Set stride to 0 and mark as fake
                new_strides[i] = SymbolicDim{ .concrete = 0 };
                new_fake[i] = true;
                new_dims[i] = tgt_dim; // Update dimension to target size
                if (enable_debug) {
                    std.debug.print("      Dimension {} broadcasted from {} to {}\n", .{i, res_dim.concrete, tgt_dim.concrete});
                }
            }
        }
    }
    
    // Free the old arrays and replace with new ones
    allocator.free(result.strides);
    allocator.free(result.fake);
    allocator.free(result.dims);
    
    return ShapeTracker{
        .dims = new_dims,
        .strides = new_strides,
        .offset = result.offset,
        .indexes = result.indexes,
        .fake = new_fake,
        .mask = result.mask,
        .padding = result.padding,
    };
}

/// Broadcast two shapes according to numpy broadcasting rules
fn broadcastShapes(a: ShapeTracker, b: ShapeTracker, allocator: Allocator, pool: *SymbolicPool) !ShapeTracker {
    const a_rank = a.dims.len;
    const b_rank = b.dims.len;
    const result_rank = @max(a_rank, b_rank);
    
    var result_dims = try allocator.alloc(SymbolicDim, result_rank);
    
    // Iterate from the end (rightmost dimensions)
    var i: usize = 0;
    while (i < result_rank) : (i += 1) {
        const a_idx = if (i < a_rank) a_rank - 1 - i else null;
        const b_idx = if (i < b_rank) b_rank - 1 - i else null;
        const result_idx = result_rank - 1 - i;
        
        if (a_idx != null and b_idx != null) {
            // Both have this dimension
            const a_dim = a.dims[a_idx.?];
            const b_dim = b.dims[b_idx.?];
            
            // Check if dimensions are compatible
            const a_size = if (a_dim == .concrete) a_dim.concrete else null;
            const b_size = if (b_dim == .concrete) b_dim.concrete else null;
            
            if (a_size != null and b_size != null) {
                if (a_size.? == b_size.?) {
                    result_dims[result_idx] = a_dim;
                } else if (a_size.? == 1) {
                    result_dims[result_idx] = b_dim;
                } else if (b_size.? == 1) {
                    result_dims[result_idx] = a_dim;
                } else {
                    std.log.err("Broadcasting: incompatible dimensions {} and {}", .{a_size.?, b_size.?});
                    return error.IncompatibleShapes;
                }
            } else {
                // At least one is symbolic - assume they're compatible
                result_dims[result_idx] = if (a_dim == .concrete) a_dim else b_dim;
            }
        } else if (a_idx != null) {
            // Only a has this dimension
            result_dims[result_idx] = a.dims[a_idx.?];
        } else if (b_idx != null) {
            // Only b has this dimension
            result_dims[result_idx] = b.dims[b_idx.?];
        }
    }
    
    return ShapeTracker.fromDims(result_dims, allocator, pool);
}

/// Infer shape for reduction operations
fn inferReductionShape(node: *const Node, input_shape: ShapeTracker, allocator: Allocator, pool: *SymbolicPool) !ShapeTracker {
    const axis = if (node.metadata) |meta| meta.reduction_axis else null;
    const keepdims = if (node.metadata) |meta| meta.keepdims else false;
    
    if (axis == null) {
        // No axis specified - reduce to scalar
        var empty_dims = [_]SymbolicDim{};
        return ShapeTracker.fromDims(&empty_dims, allocator, pool);
    }
    
    const reduction_axis = axis.?;
    const axis_idx: usize = if (reduction_axis >= 0) 
        @intCast(reduction_axis) 
    else 
        @intCast(@as(i64, @intCast(input_shape.dims.len)) + reduction_axis);
    
    if (axis_idx >= input_shape.dims.len) {
        std.log.err("Reduction axis {} out of bounds for shape with {} dims", .{reduction_axis, input_shape.dims.len});
        return error.InvalidAxis;
    }
    
    if (keepdims) {
        // Keep the dimension but set it to 1
        const result_dims = try allocator.alloc(SymbolicDim, input_shape.dims.len);
        for (input_shape.dims, result_dims, 0..) |src_dim, *dst_dim, i| {
            if (i == axis_idx) {
                dst_dim.* = SymbolicDim{ .concrete = 1 };
            } else {
                dst_dim.* = src_dim;
            }
        }
        return ShapeTracker.fromDims(result_dims, allocator, pool);
    } else {
        // Create new shape without the reduced dimension
        var result_dims = try allocator.alloc(SymbolicDim, input_shape.dims.len - 1);
        var j: usize = 0;
        for (input_shape.dims, 0..) |dim, i| {
            if (i != axis_idx) {
                result_dims[j] = dim;
                j += 1;
            }
        }
        
        return ShapeTracker.fromDims(result_dims, allocator, pool);
    }
}

// ===== Unit Tests =====

test "elementwise shape inference" {
    const testing = std.testing;
    const allocator = testing.allocator;
    
    // Create a symbolic pool for testing
    var pool = try SymbolicPool.init(allocator);
    defer pool.deinit();
    
    // Create shapes for testing
    const shape1 = try ShapeTracker.fromDims(&[_]SymbolicDim{
        .{ .concrete = 2 },
        .{ .concrete = 3 },
    }, allocator, &pool);
    defer shape1.deinit(allocator);
    
    const shape2 = try ShapeTracker.fromDims(&[_]SymbolicDim{
        .{ .concrete = 2 },
        .{ .concrete = 3 },
    }, allocator, &pool);
    defer shape2.deinit(allocator);
    
    // Elementwise with same shapes
    const result = try inferElementwiseShape(&[_]ShapeTracker{shape1, shape2}, allocator, &pool);
    defer result.deinit(allocator);
    
    try testing.expectEqual(@as(usize, 2), result.dims.len);
    try testing.expectEqual(@as(i64, 2), result.dims[0].concrete);
    try testing.expectEqual(@as(i64, 3), result.dims[1].concrete);
}

test "broadcast shape inference" {
    const testing = std.testing;
    const allocator = testing.allocator;
    
    // Create a symbolic pool for testing
    var pool = try SymbolicPool.init(allocator);
    defer pool.deinit();
    
    // Test broadcasting [2, 1] with [3]
    const shape1 = try ShapeTracker.fromDims(&[_]SymbolicDim{
        .{ .concrete = 2 },
        .{ .concrete = 1 },
    }, allocator, &pool);
    defer shape1.deinit(allocator);
    
    const shape2 = try ShapeTracker.fromDims(&[_]SymbolicDim{
        .{ .concrete = 3 },
    }, allocator, &pool);
    defer shape2.deinit(allocator);
    
    const result = try broadcastShapes(shape1, shape2, allocator, &pool);
    defer result.deinit(allocator);
    
    // Result should be [2, 3]
    try testing.expectEqual(@as(usize, 2), result.dims.len);
    try testing.expectEqual(@as(i64, 2), result.dims[0].concrete);
    try testing.expectEqual(@as(i64, 3), result.dims[1].concrete);
}

test "reduction shape inference" {
    const testing = std.testing;
    const allocator = testing.allocator;
    
    var arena = std.heap.ArenaAllocator.init(allocator);
    defer arena.deinit();
    
    // Create a symbolic pool for testing
    var pool = try SymbolicPool.init(allocator);
    defer pool.deinit();
    
    // Create a dummy node with reduction metadata
    var node = Node{
        .id = 0,
        .spec = .{ .compute = .sum_reduce },
        .inputs = &.{},
        .outputs = &.{},
        .is_valid = true,
        .metadata = try arena.allocator().create(NodeMetadata),
    };
    node.metadata.?.* = .{ .reduction_axis = 1 };
    
    // Input shape [2, 3, 4]
    const input_shape = try ShapeTracker.fromDims(&[_]SymbolicDim{
        .{ .concrete = 2 },
        .{ .concrete = 3 },
        .{ .concrete = 4 },
    }, allocator, &pool);
    defer input_shape.deinit(allocator);
    
    const result = try inferReductionShape(&node, input_shape, allocator, &pool);
    defer result.deinit(allocator);
    
    // Result should be [2, 4] (axis 1 removed)
    try testing.expectEqual(@as(usize, 2), result.dims.len);
    try testing.expectEqual(@as(i64, 2), result.dims[0].concrete);
    try testing.expectEqual(@as(i64, 4), result.dims[1].concrete);
}

test "shape inference pass template" {
    const testing = std.testing;
    
    // Verify the pass was created correctly
    try testing.expectEqualStrings("shape_inference", ShapeInference.name);
    try testing.expectEqual(@as(u32, 100), ShapeInference.priority);
}