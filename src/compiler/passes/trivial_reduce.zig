/// Trivial Reduction Optimization Pass
/// 
/// Optimizes reduction operations where the dimension being reduced has size 1.
/// These operations are no-ops and can be replaced with their input,
/// improving performance by eliminating unnecessary operations.

const std = @import("std");
const Allocator = std.mem.Allocator;

// Import types
const types = @import("types");
const NodeId = types.NodeId;
const ComputeOp = types.ComputeOp;

const Graph = @import("graph").Graph;
const Node = @import("graph").Node;
const NodeMetadata = @import("graph").NodeMetadata;

// Import new template infrastructure
const pass_templates = @import("../pass_templates.zig");
const transforms = @import("../transforms.zig");
const PassContext = @import("../unified_pipeline.zig").PassContext;

// Import shape module
const shape_mod = @import("shape");
const ShapeTracker = shape_mod.ShapeTracker;

// ============================================================================
// Single Reduction Removal Logic
// ============================================================================

/// Check if a reduction node operates on a dimension of size 1
fn isSingleReduction(node: *const Node) bool {
    // Check if this is a reduction operation
    const is_reduction = switch (node.spec.compute.op) {
        .sum_reduce, .max_reduce, .min_reduce, .mean_reduce => true,
        else => false,
    };
    
    if (!is_reduction) return false;
    
    // If we have metadata with input shapes, check if any reduced dimension has size 1
    if (node.metadata) |metadata| {
        if (metadata.input_shapes) |input_shapes| {
            if (input_shapes.len > 0) {
                const input_shape = input_shapes[0];
                // For this simple implementation, assume any reduction on a 1D tensor of size 1
                // is a single reduction (in practice this would be more sophisticated)
                if (input_shape.dims.len == 1) {
                    if (input_shape.dims[0] == .concrete and input_shape.dims[0].concrete == 1) {
                        return true;
                    }
                }
            }
        }
    }
    
    return false;
}

/// Visitor function for single reduction removal
fn singleReductionVisitor(node_id: NodeId, node: *Node, ctx: *PassContext) !bool {
    if (isSingleReduction(node)) {
        // This reduction operates on a dimension of size 1, so it's a no-op
        // We should replace it with its input
        
        if (node.inputs.len > 0) {
            var rewriter = transforms.GraphRewriter.init(ctx.graph, ctx.allocator);
            defer rewriter.deinit();
            
            // Replace this node with its first input
            try rewriter.markForSubstitution(node_id, node.inputs[0]);
            try rewriter.commit();
            
            return true; // Indicate that we made a change
        }
    }
    
    return false; // No change made
}

// ============================================================================
// Template-Based Pass Definition
// ============================================================================

/// Trivial reduction optimization pass using template system
pub const TrivialReductionPass = pass_templates.createTraversalPass(.{
    .name = "trivial_reduction_optimization",
    .priority = 70,
    .visitor = singleReductionVisitor,
});

// ============================================================================
// Legacy Function (Remove when migration complete)
// ============================================================================

pub fn removeSingleReductions(ctx: *PassContext) !void {
    return TrivialReductionPass.run(ctx);
}

// ============================================================================
// Unit Tests
// ============================================================================

test "single reduction removal detection" {
    const testing = std.testing;
    
    var graph = try Graph.init(testing.allocator);
    defer graph.deinit();
    
    // Create a reduction on a size-1 tensor
    const input = try graph.addPlaceholder(.f32);
    const sum_node = try graph.addNode(.sum_reduce, &.{input}, .f32);
    
    // For testing, we'll manually set up metadata
    var pool = try shape_mod.SymbolicPool.init(testing.allocator);
    defer pool.deinit();
    
    const input_shape = try ShapeTracker.fromDims(&[_]shape_mod.SymbolicDim{
        .{ .concrete = 1 },
    }, testing.allocator, &pool);
    defer input_shape.deinit(testing.allocator);
    
    const node = graph.getNodeMut(sum_node).?;
    node.metadata = try testing.allocator.create(NodeMetadata);
    node.metadata.?.* = .{};
    
    const input_shapes = try testing.allocator.alloc(ShapeTracker, 1);
    input_shapes[0] = try input_shape.clone(testing.allocator);
    node.metadata.?.input_shapes = input_shapes;
    
    defer {
        if (node.metadata.?.input_shapes) |shapes| {
            for (shapes) |shape| {
                shape.deinit(testing.allocator);
            }
            testing.allocator.free(shapes);
        }
        testing.allocator.destroy(node.metadata.?);
    }
    
    // Test detection
    try testing.expect(isSingleReduction(node));
}

test "single reduction removal pass execution" {
    const testing = std.testing;
    
    var graph = try Graph.init(testing.allocator);
    defer graph.deinit();
    
    // Create a simple graph with a placeholder
    const input = try graph.addPlaceholder(.f32);
    const sum_node = try graph.addNode(.sum_reduce, &.{input}, .f32);
    
    var ctx = PassContext.init(&graph, testing.allocator);
    try TrivialReductionPass.run(&ctx);
    
    // Graph should still be valid (basic smoke test)
    try testing.expect(graph.nodes.items.len >= 2);
    _ = sum_node; // Suppress unused warning
}

test "single reduction removal template integration" {
    const testing = std.testing;
    
    // Test that the pass template is correctly configured
    try testing.expectEqualStrings("trivial_reduction_optimization", TrivialReductionPass.name);
    try testing.expectEqual(@as(u32, 70), TrivialReductionPass.priority);
    
    // Test visitor function exists
    try testing.expect(@hasField(@TypeOf(TrivialReductionPass), "visitor"));
}

test "non-reduction nodes are ignored" {
    const testing = std.testing;
    
    var graph = try Graph.init(testing.allocator);
    defer graph.deinit();
    
    // Create a non-reduction node
    const a = try graph.addPlaceholder(.f32);
    const b = try graph.addPlaceholder(.f32);
    const add_node = try graph.addNode(.add, &.{a, b}, .f32);
    
    const node = graph.getNodeMut(add_node).?;
    
    // Should not be detected as a single reduction
    try testing.expect(!isSingleReduction(node));
}