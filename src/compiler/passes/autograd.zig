/// Autograd Pass - Automatic Differentiation
///
/// This pass runs AFTER shape inference, giving it access to complete shape
/// information. It creates gradient computation nodes for all parameters that
/// require gradients, building the backward pass of the computation graph.
///
/// Key design principles:
/// - Runs as part of compilation pipeline, not before it
/// - Has access to complete shape metadata from shape inference
/// - Creates gradient nodes that go through the same optimization pipeline
/// - Zero overhead when gradients are not requested
const std = @import("std");
const Allocator = std.mem.Allocator;

// Import types
const types = @import("types");
const NodeId = types.NodeId;
const ComputeOp = types.ComputeOp;
const DataType = types.DataType;

// Import graph components
const Graph = @import("graph").Graph;
const Node = @import("graph").Node;
const NodeMetadata = @import("graph").NodeMetadata;
const GradientState = @import("graph").GradientState;

// Import shape handling
const shape_mod = @import("shape");
const ShapeTracker = shape_mod.ShapeTracker;
const SymbolicDim = shape_mod.SymbolicDim;

// Import tensor operations for gradient graph construction
const tensor = @import("tensor");
const TensorHandle = tensor.TensorHandle;

// Import compiler infrastructure
const PassContext = @import("../unified_pipeline.zig").PassContext;
const pass_templates = @import("../pass_templates.zig");

// ============================================================================
// Autograd Pass Definition
// ============================================================================

/// Autograd pass using the template system
pub const AutogradPass = pass_templates.createAnalysisPass(.{
    .name = "autograd",
    .priority = 25, // After shape inference (20) but before optimizations (30+)
    .analyze = runAutograd,
    .modifies_graph = true,
    .pre_conditions = checkPreconditions,
    .post_conditions = checkPostconditions,
});

/// Check preconditions for autograd
fn checkPreconditions(ctx: *PassContext) !void {
    // Only run if gradients are requested
    if (!ctx.graph.hasGradientsEnabled()) {
        return;
    }

    // Ensure shape inference has run
    // We check this by verifying that output nodes have shape metadata
    for (ctx.graph.output_nodes.items) |node_id| {
        const node = ctx.graph.getNode(node_id) orelse continue;
        if (node.metadata == null or node.metadata.?.output_shape == null) {
            std.log.err("Autograd: Node {} missing shape metadata - shape inference must run first", .{node_id});
            return error.MissingShapeMetadata;
        }
    }
}

/// Check postconditions after autograd
fn checkPostconditions(ctx: *PassContext) !void {
    // If gradients were requested, ensure gradient map is populated
    if (ctx.graph.hasGradientsEnabled()) {
        const grad_state = ctx.graph.gradient_state.?;

        // Check that all required gradient nodes exist
        var iter = grad_state.requires_grad_nodes.iterator();
        while (iter.next()) |entry| {
            const param_id = entry.key_ptr.*;
            if (!grad_state.gradient_map.contains(param_id)) {
                std.log.err("Autograd: No gradient computed for parameter {}", .{param_id});
                return error.MissingGradient;
            }
        }
    }
}

/// Main autograd implementation
fn runAutograd(ctx: *PassContext) !void {
    const graph = ctx.graph;
    const allocator = ctx.allocator;

    // Skip if no gradients requested
    if (!graph.hasGradientsEnabled()) {
        return;
    }

    const grad_state = graph.gradient_state.?;
    const loss_node = grad_state.config.loss_node;

    // Initialize autograd context
    var autograd_ctx = AutogradContext{
        .pass_ctx = ctx,
        .graph = graph,
        .allocator = allocator,
        .grad_state = grad_state,
        .gradients = .{}, // Temporary gradient tracking during construction
    };
    defer autograd_ctx.gradients.deinit(allocator);

    // Build reachable set (nodes on path from parameters to loss)
    try computeReachableSet(&autograd_ctx);

    // Initialize loss gradient as 1.0
    try initializeLossGradient(&autograd_ctx, loss_node);

    // Traverse backward from loss to parameters
    const topo_order = try graph.topologicalSort();

    // Process nodes in reverse topological order
    var i = topo_order.len;
    while (i > 0) {
        i -= 1;
        const node_id = topo_order[i];

        // Skip if not in reachable set
        if (!autograd_ctx.reachable_set.contains(node_id)) {
            continue;
        }

        // Skip if no gradient exists for this node
        const output_grad_id = autograd_ctx.gradients.get(node_id) orelse continue;

        // Apply gradient rule for this node
        const node = graph.getNode(node_id) orelse continue;
        try applyGradientRule(&autograd_ctx, node_id, node, output_grad_id);
    }

    // Transfer gradients to persistent gradient map
    var grad_iter = autograd_ctx.gradients.iterator();
    while (grad_iter.next()) |entry| {
        const forward_id = entry.key_ptr.*;
        const grad_id = entry.value_ptr.*;

        // Only store gradients for parameters
        if (grad_state.requires_grad_nodes.contains(forward_id)) {
            try grad_state.gradient_map.put(allocator, forward_id, grad_id);
        }
    }
}

// ============================================================================
// Autograd Context and Helpers
// ============================================================================

/// Context for autograd computation
const AutogradContext = struct {
    pass_ctx: *PassContext,
    graph: *Graph,
    allocator: Allocator,
    grad_state: *GradientState,

    // Temporary gradient tracking during construction
    gradients: std.AutoHashMapUnmanaged(NodeId, NodeId),

    // Nodes reachable from both parameters and loss
    reachable_set: std.AutoHashMapUnmanaged(NodeId, void) = .{},
};

/// Compute nodes reachable from both parameters and loss
fn computeReachableSet(ctx: *AutogradContext) !void {
    defer ctx.reachable_set.deinit(ctx.allocator);

    var forward_reachable = std.AutoHashMapUnmanaged(NodeId, void){};
    defer forward_reachable.deinit(ctx.allocator);

    var backward_reachable = std.AutoHashMapUnmanaged(NodeId, void){};
    defer backward_reachable.deinit(ctx.allocator);

    // Forward pass: mark nodes reachable from parameters
    var forward_stack = std.ArrayList(NodeId).init(ctx.allocator);
    defer forward_stack.deinit();

    var param_iter = ctx.grad_state.requires_grad_nodes.iterator();
    while (param_iter.next()) |entry| {
        const param_id = entry.key_ptr.*;
        try forward_stack.append(param_id);
        try forward_reachable.put(ctx.allocator, param_id, {});
    }

    while (forward_stack.items.len > 0) {
        const current_item = forward_stack.items[forward_stack.items.len - 1];
        _ = forward_stack.pop();
        const consumers = ctx.graph.iterateConsumers(current_item) orelse continue;

        for (consumers) |consumer| {
            if (!forward_reachable.contains(consumer)) {
                try forward_reachable.put(ctx.allocator, consumer, {});
                try forward_stack.append(consumer);
            }
        }
    }

    // Backward pass: mark nodes reachable from loss
    var backward_stack = std.ArrayList(NodeId).init(ctx.allocator);
    defer backward_stack.deinit();

    try backward_stack.append(ctx.grad_state.config.loss_node);
    try backward_reachable.put(ctx.allocator, ctx.grad_state.config.loss_node, {});

    while (backward_stack.items.len > 0) {
        const current_item = backward_stack.items[backward_stack.items.len - 1];
        _ = backward_stack.pop();
        const node = ctx.graph.getNode(current_item) orelse continue;

        for (node.inputs) |input_id| {
            if (!backward_reachable.contains(input_id)) {
                try backward_reachable.put(ctx.allocator, input_id, {});
                try backward_stack.append(input_id);
            }
        }
    }

    // Intersection: nodes reachable from both
    ctx.reachable_set = .{};
    var fwd_iter = forward_reachable.iterator();
    while (fwd_iter.next()) |entry| {
        if (backward_reachable.contains(entry.key_ptr.*)) {
            try ctx.reachable_set.put(ctx.allocator, entry.key_ptr.*, {});
        }
    }
}

/// Initialize gradient of loss node as 1.0
fn initializeLossGradient(ctx: *AutogradContext, loss_node: NodeId) !void {
    // Create a constant 1.0 node
    const one = try tensor.constant(ctx.graph, 1.0, .f32);
    try ctx.gradients.put(ctx.allocator, loss_node, one.node_id);
}

/// Apply gradient computation rule for a node
fn applyGradientRule(ctx: *AutogradContext, node_id: NodeId, node: *const Node, output_grad_id: NodeId) !void {
    switch (node.spec) {
        .data => {
            // Data nodes (parameters, placeholders) don't propagate gradients
            // Their gradients are the final result we want
        },
        .compute => |op| {
            try computeVJP(ctx, node_id, node, op, output_grad_id);
        },
    }
}

/// Compute Vector-Jacobian Product for an operation
fn computeVJP(
    ctx: *AutogradContext,
    _: NodeId, // node_id - will be used when we add more operations
    node: *const Node,
    op: ComputeOp,
    output_grad_id: NodeId,
) !void {
    // Get output gradient as tensor handle
    const output_grad = try TensorHandle.fromNode(ctx.graph, output_grad_id);

    switch (op) {
        .add => {
            // Gradient of add: ∂(a+b)/∂a = 1, ∂(a+b)/∂b = 1
            // Both inputs get the same gradient
            const input_a = node.inputs[0];
            const input_b = node.inputs[1];

            // For now, pass gradient through directly
            // TODO: Handle broadcasting by reducing dimensions
            try addGradient(ctx, input_a, output_grad_id);
            try addGradient(ctx, input_b, output_grad_id);
        },

        .mul => {
            // Gradient of mul: ∂(a*b)/∂a = b, ∂(a*b)/∂b = a
            const input_a = node.inputs[0];
            const input_b = node.inputs[1];

            // Create gradient nodes
            const a_handle = try TensorHandle.fromNode(ctx.graph, input_a);
            const b_handle = try TensorHandle.fromNode(ctx.graph, input_b);

            // grad_a = output_grad * b
            const grad_a = try output_grad.mul(b_handle);
            try addGradient(ctx, input_a, grad_a.node_id);

            // grad_b = output_grad * a
            const grad_b = try output_grad.mul(a_handle);
            try addGradient(ctx, input_b, grad_b.node_id);
        },

        .reduce_sum => {
            // Gradient of sum: broadcast the gradient back
            const input = node.inputs[0];

            // Get input shape from metadata
            const input_node = ctx.graph.getNode(input) orelse return;
            _ = input_node; // Will be used when we implement broadcasting

            // Broadcast gradient to input shape
            // TODO: Extract concrete shape and broadcast
            try addGradient(ctx, input, output_grad_id);
        },

        else => {
            // TODO: Implement other operations
            std.log.warn("Autograd: gradient for op '{}' not implemented yet", .{op});
        },
    }
}

/// Add or accumulate gradient for a node
fn addGradient(ctx: *AutogradContext, node_id: NodeId, grad_id: NodeId) !void {
    const result = try ctx.gradients.getOrPut(ctx.allocator, node_id);

    if (result.found_existing) {
        // Accumulate gradients
        const existing_grad = try TensorHandle.fromNode(ctx.graph, result.value_ptr.*);
        const new_grad = try TensorHandle.fromNode(ctx.graph, grad_id);
        const sum = try existing_grad.add(new_grad);
        result.value_ptr.* = sum.node_id;
    } else {
        // First gradient for this node
        result.value_ptr.* = grad_id;
    }
}

// ============================================================================
// Unit Tests
// ============================================================================

test "Autograd pass registration" {
    const testing = std.testing;

    // Verify pass properties
    try testing.expectEqualStrings("autograd", AutogradPass.name);
    try testing.expectEqual(@as(u32, 25), AutogradPass.priority);
    try testing.expectEqual(true, AutogradPass.modifies_graph);
}

test "Autograd skips when gradients not enabled" {
    const testing = std.testing;

    var graph = try Graph.init(testing.allocator);
    defer graph.deinit();

    // Create simple graph without gradients
    const x = try tensor.placeholder(&graph, &.{ 2, 3 }, .f32);
    const y = try x.add(x);
    try graph.markOutput(y.node_id);

    var ctx = PassContext.init(&graph, testing.allocator);
    defer ctx.deinit();

    // Run autograd - should skip
    try AutogradPass.run(&ctx);

    // Verify no gradient state was created
    try testing.expect(graph.gradient_state == null);
}

test "Autograd creates gradients for simple addition" {
    const testing = std.testing;

    var graph = try Graph.init(testing.allocator);
    defer graph.deinit();

    // Create graph with gradients enabled
    const x = try tensor.placeholderWithOptions(&graph, &.{2}, .f32, .{ .requires_grad = true });
    const y = try tensor.placeholderWithOptions(&graph, &.{2}, .f32, .{ .requires_grad = true });
    const z = try x.add(y);

    try graph.markOutput(z.node_id);
    try graph.setLossOutput(z.node_id);

    // First run shape inference (required precondition)
    var ctx = PassContext.init(&graph, testing.allocator);
    defer ctx.deinit();

    // Mock shape inference by setting output shapes
    for (graph.nodes.items) |*node| {
        if (node.metadata == null) {
            node.metadata = try graph.arena.allocator().create(NodeMetadata);
            node.metadata.?.* = .{};
        }
        // Simple mock: all nodes have shape [2]
        const dims = try graph.arena.allocator().alloc(SymbolicDim, 1);
        dims[0] = .{ .concrete = 2 };
        node.metadata.?.output_shape = try ShapeTracker.fromDims(dims, graph.arena.allocator(), &graph.symbolic_pool);
    }

    // Run autograd
    try AutogradPass.run(&ctx);

    // Verify gradients were created
    try testing.expect(graph.gradient_state != null);
    const grad_map = &graph.gradient_state.?.gradient_map;
    try testing.expect(grad_map.count() > 0);

    // Both x and y should have gradients
    try testing.expect(grad_map.contains(x.node_id));
    try testing.expect(grad_map.contains(y.node_id));
}
