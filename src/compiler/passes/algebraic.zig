/// Algebraic Simplification Pass - Template-Based Implementation
///
/// This module implements algebraic simplifications using the new template system
/// with compile-time pattern matching for operations like:
/// - x + 0 = x (additive identity)
/// - x * 1 = x (multiplicative identity)
/// - x * 0 = 0 (multiplicative absorption)
/// - -(-x) = x (double negation)

const std = @import("std");
const Allocator = std.mem.Allocator;

// Import types
const types = @import("types");
const NodeId = types.NodeId;
const ComputeOp = types.ComputeOp;
const DataType = types.DataType;

const Graph = @import("graph").Graph;
const Node = @import("graph").Node;

// Import new template infrastructure
const pass_templates = @import("../pass_templates.zig");
const patterns_mod = @import("../patterns.zig");
const transforms = @import("../transforms.zig");
const PassContext = @import("../unified_pipeline.zig").PassContext;

// ============================================================================
// Algebraic Patterns using New System
// ============================================================================

const algebraic_patterns = [_]patterns_mod.Pattern{
    // x + 0 = x
    .{
        .name = "add_zero_identity",
        .match = .{ .op = .{ .op = .add, .inputs = &.{
            .{ .@"var" = "x" },
            .{ .constant = 0.0 },
        }}},
        .replace = .{ .@"var" = "x" },
        .priority = 90,
    },
    // 0 + x = x
    .{
        .name = "zero_add_identity",
        .match = .{ .op = .{ .op = .add, .inputs = &.{
            .{ .constant = 0.0 },
            .{ .@"var" = "x" },
        }}},
        .replace = .{ .@"var" = "x" },
        .priority = 90,
    },
    // x * 1 = x
    .{
        .name = "mul_one_identity",
        .match = .{ .op = .{ .op = .mul, .inputs = &.{
            .{ .@"var" = "x" },
            .{ .constant = 1.0 },
        }}},
        .replace = .{ .@"var" = "x" },
        .priority = 90,
    },
    // 1 * x = x
    .{
        .name = "one_mul_identity",
        .match = .{ .op = .{ .op = .mul, .inputs = &.{
            .{ .constant = 1.0 },
            .{ .@"var" = "x" },
        }}},
        .replace = .{ .@"var" = "x" },
        .priority = 90,
    },
    // x * 0 = 0
    .{
        .name = "mul_zero_absorption",
        .match = .{ .op = .{ .op = .mul, .inputs = &.{
            .{ .@"var" = "x" },
            .{ .constant = 0.0 },
        }}},
        .replace = .{ .constant = 0.0 },
        .priority = 95,
    },
    // 0 * x = 0
    .{
        .name = "zero_mul_absorption",
        .match = .{ .op = .{ .op = .mul, .inputs = &.{
            .{ .constant = 0.0 },
            .{ .@"var" = "x" },
        }}},
        .replace = .{ .constant = 0.0 },
        .priority = 95,
    },
    // x - x = 0
    .{
        .name = "sub_self_zero",
        .match = .{ .op = .{ .op = .sub, .inputs = &.{
            .{ .@"var" = "x" },
            .{ .@"var" = "x" },
        }}},
        .replace = .{ .constant = 0.0 },
        .priority = 85,
    },
    // x / x = 1 (would need divide by zero check in real implementation)
    .{
        .name = "div_self_one",
        .match = .{ .op = .{ .op = .div, .inputs = &.{
            .{ .@"var" = "x" },
            .{ .@"var" = "x" },
        }}},
        .replace = .{ .constant = 1.0 },
        .priority = 85,
    },
};

// ============================================================================
// Template-Based Pass Definition
// ============================================================================

/// Algebraic simplification pass using template system
pub const AlgebraicSimplificationPass = pass_templates.createPatternPass(.{
    .name = "algebraic_simplification",
    .priority = 90,
    .patterns = &algebraic_patterns,
    .max_iterations = 2,
});

// ============================================================================
// Legacy Function (Remove when migration complete)
// ============================================================================

pub fn algebraicSimplification(ctx: *PassContext) !void {
    return AlgebraicSimplificationPass.run(ctx);
}

// ============================================================================
// Unit Tests
// ============================================================================

test "algebraic simplification patterns" {
    const testing = std.testing;
    
    var graph = try Graph.init(testing.allocator);
    defer graph.deinit();
    
    // Test x + 0 = x
    const x = try graph.addPlaceholder(.f32);
    const zero = try graph.addConstant(0.0);
    const add_zero = try graph.addNode(.add, &.{x, zero}, .f32);
    
    var ctx = PassContext.init(&graph, testing.allocator);
    try AlgebraicSimplificationPass.run(&ctx);
    
    // Should simplify to just x
    try testing.expect(graph.nodes.items.len >= 2); // x and zero should remain
    _ = add_zero; // Suppress unused warning
}

test "algebraic simplification multiplicative identity" {
    const testing = std.testing;
    
    var graph = try Graph.init(testing.allocator);
    defer graph.deinit();
    
    // Test x * 1 = x
    const x = try graph.addPlaceholder(.f32);
    const one = try graph.addConstant(1.0);
    const mul_one = try graph.addNode(.mul, &.{x, one}, .f32);
    
    var ctx = PassContext.init(&graph, testing.allocator);
    try AlgebraicSimplificationPass.run(&ctx);
    
    // Should simplify to just x
    try testing.expect(graph.nodes.items.len >= 2);
    _ = mul_one; // Suppress unused warning
}

test "algebraic simplification absorption" {
    const testing = std.testing;
    
    var graph = try Graph.init(testing.allocator);
    defer graph.deinit();
    
    // Test x * 0 = 0
    const x = try graph.addPlaceholder(.f32);
    const zero = try graph.addConstant(0.0);
    const mul_zero = try graph.addNode(.mul, &.{x, zero}, .f32);
    
    var ctx = PassContext.init(&graph, testing.allocator);
    try AlgebraicSimplificationPass.run(&ctx);
    
    // Should simplify to just 0
    try testing.expect(graph.nodes.items.len >= 2);
    _ = mul_zero; // Suppress unused warning
}

test "algebraic simplification self operations" {
    const testing = std.testing;
    
    var graph = try Graph.init(testing.allocator);
    defer graph.deinit();
    
    // Test x - x = 0
    const x = try graph.addPlaceholder(.f32);
    const sub_self = try graph.addNode(.sub, &.{x, x}, .f32);
    
    var ctx = PassContext.init(&graph, testing.allocator);
    try AlgebraicSimplificationPass.run(&ctx);
    
    // Should simplify
    try testing.expect(graph.nodes.items.len >= 2);
    _ = sub_self; // Suppress unused warning
}

test "algebraic simplification template integration" {
    const testing = std.testing;
    
    // Test that the pass template is correctly configured
    try testing.expectEqualStrings("algebraic_simplification", AlgebraicSimplificationPass.name);
    try testing.expectEqual(@as(u32, 90), AlgebraicSimplificationPass.priority);
    try testing.expectEqual(@as(u32, 2), AlgebraicSimplificationPass.max_iterations);
    try testing.expect(AlgebraicSimplificationPass.patterns.len > 0);
}