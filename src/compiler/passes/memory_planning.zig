/// Memory Planning Pass - Optimized memory allocation with buffer reuse
/// 
/// This pass creates an efficient memory plan by:
/// 1. Analyzing node lifetimes to identify reuse opportunities
/// 2. Computing actual memory requirements from shape metadata
/// 3. Packing buffers to minimize total memory usage
/// 4. Creating a concrete allocation plan for execution
/// 
/// The memory plan is backend-agnostic. Each backend can implement its own
/// memory optimization strategy based on the lifetime information provided.
/// 
/// Uses the unified pass template system for consistency.

const std = @import("std");
const Allocator = std.mem.Allocator;

// Import types
const types = @import("types");
const NodeId = types.NodeId;
const DataType = types.DataType;
// Import graph components
const Graph = @import("graph").Graph;
const Node = @import("graph").Node;

// Import backend types  
const backend_types = @import("backend_types");
const BufferId = backend_types.BufferId;
const LivenessInterval = backend_types.LivenessInterval;

// Import shape module
const shape_mod = @import("shape");

// Import pipeline context and templates
const PassContext = @import("../unified_pipeline.zig").PassContext;
const pass_templates = @import("../pass_templates.zig");

/// Buffer information for memory planning
const BufferInfo = struct {
    node_id: NodeId,
    size: usize,
    alignment: usize,
    lifetime: LivenessInterval,
    dtype: DataType,
    shape: []const i64, // Concrete shape
};

// Use ResolvedAllocation from backend_types instead of local AllocationDecision
const ResolvedAllocation = backend_types.ResolvedAllocation;
const ResolvedMemoryPlan = backend_types.ResolvedMemoryPlan;

// Internal allocation decision for optimization algorithms
const AllocationDecision = struct {
    buffer_id: NodeId,
    offset: usize,
    size: usize,
};

/// Memory usage statistics
pub const MemoryStats = struct {
    total_buffers: usize = 0,
    total_memory: usize = 0,
    peak_memory: usize = 0,
    buffer_reuses: usize = 0,
    bytes_reused: usize = 0,
    
    pub fn print(self: MemoryStats) void {
        std.debug.print("Memory Planning Stats:\n", .{});
        std.debug.print("  Total buffers: {}\n", .{self.total_buffers});
        std.debug.print("  Total memory: {} KB\n", .{self.total_memory / 1024});
        std.debug.print("  Peak memory: {} KB\n", .{self.peak_memory / 1024});
        if (self.total_memory > 0) {
            const savings_pct = @as(f64, @floatFromInt(self.total_memory - self.peak_memory)) / @as(f64, @floatFromInt(self.total_memory)) * 100.0;
            std.debug.print("  Memory savings: {d:.1}%\n", .{savings_pct});
        }
        std.debug.print("  Buffer reuses: {}\n", .{self.buffer_reuses});
        std.debug.print("  Bytes reused: {} KB\n", .{self.bytes_reused / 1024});
    }
};

// ============================================================================
// Memory Planning Pass using Template System
// ============================================================================

/// Memory Planning pass using the template system
pub const MemoryPlanningPass = pass_templates.createAnalysisPass(.{
    .name = "memory_planning",
    .priority = 60,
    .analyze = analyzeAndPlanMemory,
    .modifies_graph = false, // Pure analysis pass
    .pre_conditions = pass_templates.requireShapeMetadata,
});

/// Analyze graph and create memory plan
fn analyzeAndPlanMemory(ctx: *PassContext) !void {
    const graph = ctx.graph;
    const allocator = ctx.allocator;
    
    // Step 1: Compute buffer lifetimes
    const lifetimes = try computeBufferLifetimes(graph, allocator);
    defer {
        // Free individual shape arrays
        for (lifetimes) |lifetime| {
            allocator.free(lifetime.shape);
        }
        allocator.free(lifetimes);
    }
    
    // Step 2: Apply buffer reuse optimization
    const allocations = try optimizeMemoryLayout(lifetimes, allocator);
    defer allocator.free(allocations);
    
    // Step 3: Create memory plan
    const memory_plan = try createMemoryPlan(allocations, lifetimes, allocator);
    
    // Step 4: Compute and log statistics
    const stats = computeMemoryStats(lifetimes, memory_plan);
    if (@import("build_options").enable_debug_logs) {
        stats.print();
    }
    
    // Store directly in context (no heap allocation needed)
    ctx.setMemoryPlan(memory_plan);
}


/// Compute lifetime intervals for all buffers
fn computeBufferLifetimes(graph: *const Graph, allocator: Allocator) ![]BufferInfo {
    var buffers = std.ArrayList(BufferInfo).init(allocator);
    defer buffers.deinit();
    
    // Debug: Check graph state before topological sort
    std.log.debug("Memory planning: graph has {} nodes, next_node_id={}", .{ graph.nodes.items.len, graph.next_node_id });
    if (@import("build_options").enable_debug_logs) {
        std.debug.print("Memory planning: checking all nodes in graph:\n", .{});
        for (0..graph.next_node_id) |i| {
            const node_id: NodeId = @intCast(i);
            if (graph.hasNode(node_id)) {
                std.debug.print("  Node {} exists and is_valid=true\n", .{node_id});
            } else {
                std.debug.print("  Node {} missing or is_valid=false\n", .{node_id});
            }
        }
    }
    
    // Get execution order - use consistent BFS-based topological sort
    // Cast away const since topologicalSortOwned needs mutable access for caching
    const graph_mut = @constCast(graph);
    const topo_order = try graph_mut.topologicalSortOwned(allocator);
    defer allocator.free(topo_order);
    
    std.log.debug("Memory planning: topological sort returned {} nodes", .{topo_order.len});
    if (@import("build_options").enable_debug_logs) {
        std.debug.print("Memory planning: topo_order = {any}\n", .{topo_order});
        std.debug.print("Memory planning: output_nodes.len = {}\n", .{graph.output_nodes.items.len});
        for (graph.output_nodes.items, 0..) |out_id, i| {
            std.debug.print("  output_nodes[{}] = {}\n", .{i, out_id});
        }
    }
    
    // Create step mapping
    var step_map = std.AutoHashMapUnmanaged(NodeId, u32){};
    defer step_map.deinit(allocator);
    
    for (topo_order, 0..) |node_id, step| {
        try step_map.put(allocator, node_id, @intCast(step));
    }
    
    // Analyze each node
    for (topo_order) |node_id| {
        std.log.debug("Memory planning: processing node_id={}", .{node_id});
        if (@import("build_options").enable_debug_logs) {
            std.debug.print("Memory planning: processing node_id={}\n", .{node_id});
        }
        const node = graph.getNode(node_id) orelse {
            std.log.warn("Memory planning: node {} not found in graph", .{node_id});
            if (@import("build_options").enable_debug_logs) {
                std.debug.print("  WARNING: node {} not found in graph\n", .{node_id});
            }
            continue;
        };
        
        std.log.debug("  Node {} spec: {}", .{ node_id, node.spec });
        if (@import("build_options").enable_debug_logs) {
            std.debug.print("  Node {} spec: {}\n", .{ node_id, node.spec });
        }
        
        // For now, allocate buffers for all nodes including constants
        // TODO: Optimize by embedding constant values directly in execution plan
        // if (node.spec == .data and node.spec.data == .constant) {
        //     std.log.debug("Memory planning: skipping constant node {}", .{node_id});
        //     continue;
        // }
        
        // Get shape information from metadata
        const metadata = node.metadata orelse {
            std.log.warn("Memory planning: node {} has no metadata", .{node_id});
            std.log.debug("  Node {} spec: {}, skipping due to no metadata", .{ node_id, node.spec });
            if (@import("build_options").enable_debug_logs) {
                std.debug.print("  WARNING: node {} has no metadata, skipping\n", .{node_id});
            }
            continue;
        };
        
        const shape_tracker = metadata.output_shape orelse {
            std.log.warn("Memory planning: node {} has no shape metadata", .{node_id});
            std.log.debug("  Node {} spec: {}, metadata exists but no output_shape", .{ node_id, node.spec });
            if (@import("build_options").enable_debug_logs) {
                std.debug.print("  WARNING: node {} has no shape metadata, skipping\n", .{node_id});
            }
            continue;
        };
        
        // Convert to concrete shape and compute size
        const concrete_shape = try resolveShapeToI64(&shape_tracker, allocator);
        defer allocator.free(concrete_shape);
        
        const dtype = if (node.outputs.len > 0) node.outputs[0].dtype else .f32;
        const size = calculateBufferSize(concrete_shape, dtype);
        
        // Compute buffer lifetime with correct semantics
        var lifetime = computeBufferLifetime(graph, node_id, &step_map);
        
        if (@import("build_options").enable_debug_logs) {
            std.debug.print("Node {} initial lifetime: start={}, end={}\n", .{node_id, lifetime.start, lifetime.end});
        }
        
        // Only allocate for nodes that produce values
        if (node.spec == .compute or node.spec == .data) {
            // Apply node-specific lifetime adjustments
            if (node.spec == .data) {
                switch (node.spec.data) {
                    .placeholder => {
                        // Placeholders handled in computeBufferLifetime, no adjustment needed
                        if (@import("build_options").enable_debug_logs) {
                            std.debug.print("Placeholder {} lifetime already set to [{},{}]\n", .{ node_id, lifetime.start, lifetime.end });
                        }
                    },
                    .constant => {
                        // Constants should not interfere with placeholder memory
                        // Ensure they start after placeholders are established
                        if (lifetime.start == 0) {
                            lifetime.start = 1;
                            if (@import("build_options").enable_debug_logs) {
                                std.debug.print("Adjusting constant {} start to 1 to avoid placeholder conflicts\n", .{node_id});
                            }
                        }
                    },
                    else => {},
                }
            }
            
            try buffers.append(.{
                .node_id = node_id,
                .size = size,
                .alignment = getAlignment(dtype),
                .lifetime = lifetime,
                .dtype = dtype,
                .shape = try allocator.dupe(i64, concrete_shape),
            });
            
            if (@import("build_options").enable_debug_logs) {
                std.debug.print("Added buffer: node_id={}, size={}, dtype={}, lifetime=[{},{}]\n", 
                    .{ node_id, size, dtype, lifetime.start, lifetime.end });
            }
            std.log.debug("Successfully added buffer for node {}", .{node_id});
        } else {
            std.log.debug("Skipping buffer allocation for node {} with spec {}", .{ node_id, node.spec });
        }
    }
    
    return buffers.toOwnedSlice();
}

/// Compute buffer lifetime with correct semantics
/// 
/// CORRECTED ALGORITHM: Buffer lifetime represents when memory must be available:
/// - start: when the node produces its output (execution step)
/// - end: AFTER the last consumer finishes (last_consumer_step, or maxInt for special cases)
/// 
/// Key insight: A buffer must remain alive DURING consumer execution, not just until it starts
pub fn computeBufferLifetime(graph: *const Graph, node_id: NodeId, step_map: *const std.AutoHashMapUnmanaged(NodeId, u32)) LivenessInterval {
    const production_step = step_map.get(node_id) orelse 0;
    
    if (@import("build_options").enable_debug_logs) {
        std.debug.print("computeBufferLifetime: node {} produces output at step {}\n", .{ node_id, production_step });
    }
    
    // Handle special cases first (these override normal consumer analysis)
    const node = graph.getNode(node_id);
    if (node) |n| {
        // Input placeholders must live until the end to prevent corruption
        if (n.spec == .data and n.spec.data == .placeholder) {
            if (@import("build_options").enable_debug_logs) {
                std.debug.print("  Node {} is a placeholder, lives until end\n", .{node_id});
            }
            return .{ .start = 0, .end = std.math.maxInt(u32) };  // Always available
        }
    }
    
    // Check if this node is an output node
    for (graph.output_nodes.items) |output_id| {
        if (output_id == node_id) {
            if (@import("build_options").enable_debug_logs) {
                std.debug.print("  Node {} is an output node, lives until end\n", .{node_id});
            }
            return .{ .start = production_step, .end = std.math.maxInt(u32) };
        }
    }
    
    // Find all consumers and determine the latest step when the buffer is needed
    var latest_consumer_step: ?u32 = null;
    var consumers_found = false;
    
    // Robust consumer detection: check all nodes for dependencies
    var consumer_node_id: NodeId = 0;
    while (consumer_node_id < graph.next_node_id) : (consumer_node_id += 1) {
        if (graph.hasNode(consumer_node_id)) {
            const consumer_node = graph.getNode(consumer_node_id).?;
            
            // Check if this node uses our node as input
            for (consumer_node.inputs) |input_id| {
                if (input_id == node_id) {
                    consumers_found = true;
                    if (step_map.get(consumer_node_id)) |consumer_step| {
                        if (@import("build_options").enable_debug_logs) {
                            std.debug.print("    Consumer {} uses node {} at step {}\n", 
                                           .{ consumer_node_id, node_id, consumer_step });
                        }
                        latest_consumer_step = if (latest_consumer_step) |current| 
                            @max(current, consumer_step)
                        else 
                            consumer_step;
                    }
                }
            }
        }
    }
    
    // Determine the end of buffer lifetime
    const buffer_end = if (latest_consumer_step) |step| 
        step  // Buffer needed until the last consumer executes
    else
        production_step;  // No consumers, buffer only needed during production
    
    if (@import("build_options").enable_debug_logs) {
        if (!consumers_found) {
            std.debug.print("  Node {} has no consumers, lifetime=[{},{}]\n", .{ node_id, production_step, buffer_end });
        } else {
            std.debug.print("  Node {} lifetime computed as [{},{}]\n", .{ node_id, production_step, buffer_end });
        }
    }
    
    return .{ .start = production_step, .end = buffer_end };
}

/// Optimize memory layout using lifetime analysis
fn optimizeMemoryLayout(buffers: []const BufferInfo, allocator: Allocator) ![]AllocationDecision {
    var allocations = std.ArrayList(AllocationDecision).init(allocator);
    defer allocations.deinit();
    
    // Sort buffers by start time for greedy allocation
    const sorted_buffers = try allocator.dupe(BufferInfo, buffers);
    defer allocator.free(sorted_buffers);
    
    std.sort.block(BufferInfo, sorted_buffers, {}, struct {
        fn lessThan(_: void, a: BufferInfo, b: BufferInfo) bool {
            return a.lifetime.start < b.lifetime.start;
        }
    }.lessThan);
    
    // Track memory regions and their availability
    var regions = std.ArrayList(MemoryRegion).init(allocator);
    defer regions.deinit();
    
    // Greedy allocation with reuse
    for (sorted_buffers) |buffer| {
        if (@import("build_options").enable_debug_logs) {
            std.debug.print("Allocating buffer {}: size={}, lifetime=[{},{}]\n", 
                .{ buffer.node_id, buffer.size, buffer.lifetime.start, buffer.lifetime.end });
        }
        
        // Find a suitable region that's available
        var allocated = false;
        for (regions.items) |*region| {
            // ROBUST MEMORY REUSE: Correct lifetime-based reuse logic
            // 
            // With the corrected lifetime semantics:
            // - buffer.lifetime.start = when buffer is produced
            // - buffer.lifetime.end = when last consumer finishes
            // - region.available_after = when previous buffer's lifetime ends
            //
            // Safe reuse condition: previous buffer's lifetime must completely end
            // BEFORE this buffer's lifetime starts (strict non-overlap)
            //
            // DEBUGGING: The condition appears correct but memory aliasing still occurs.
            // Need to investigate if there's an issue with lifetime computation itself
            // or if there's another factor causing the aliasing.
            const safe_to_reuse = false; // DISABLED AGAIN for debugging
            
            if (@import("build_options").enable_debug_logs) {
                std.debug.print("  Checking region at offset {}: available_after={}, buffer_lifetime=[{},{}], safe_to_reuse={}\n",
                    .{region.offset, region.available_after, buffer.lifetime.start, buffer.lifetime.end, safe_to_reuse});
            }
            if (safe_to_reuse and region.size >= buffer.size) {
                // Reuse this region
                try allocations.append(.{
                    .buffer_id = buffer.node_id,
                    .offset = region.offset,
                    .size = buffer.size,
                });
                if (@import("build_options").enable_debug_logs) {
                    std.debug.print("Allocation decision (reuse): buffer_id={}, offset={}, size={}, lifetime=[{},{}], prev_region_end={}\n", 
                        .{ buffer.node_id, region.offset, buffer.size, buffer.lifetime.start, buffer.lifetime.end, region.available_after });
                }
                
                // Update region availability
                region.available_after = buffer.lifetime.end;
                allocated = true;
                break;
            }
        }
        
        if (!allocated) {
            // Need a new region
            const offset = computeNextOffset(&regions, buffer.alignment);
            try regions.append(.{
                .offset = offset,
                .size = buffer.size,
                .available_after = buffer.lifetime.end,
            });
            
            try allocations.append(.{
                .buffer_id = buffer.node_id,
                .offset = offset,
                .size = buffer.size,
            });
            if (@import("build_options").enable_debug_logs) {
                std.debug.print("Allocation decision (new): buffer_id={}, offset={}, size={}, lifetime=[{},{}]\n", 
                    .{ buffer.node_id, offset, buffer.size, buffer.lifetime.start, buffer.lifetime.end });
            }
        }
    }
    
    return allocations.toOwnedSlice();
}

/// Memory region for tracking allocations
const MemoryRegion = struct {
    offset: usize,
    size: usize,
    available_after: u32,
};

/// Compute next available offset with alignment
fn computeNextOffset(regions: *const std.ArrayList(MemoryRegion), alignment: usize) usize {
    var max_end: usize = 0;
    for (regions.items) |region| {
        max_end = @max(max_end, region.offset + region.size);
    }
    return std.mem.alignForward(usize, max_end, alignment);
}

/// Create final memory plan from allocation decisions
fn createMemoryPlan(
    allocations: []const AllocationDecision,
    buffers: []const BufferInfo,
    allocator: Allocator,
) !ResolvedMemoryPlan {
    // Create buffer map for quick lookup
    var buffer_map = std.AutoHashMapUnmanaged(NodeId, BufferInfo){};
    defer buffer_map.deinit(allocator);
    
    for (buffers) |buffer| {
        try buffer_map.put(allocator, buffer.node_id, buffer);
    }
    
    // Create resolved memory allocations
    var resolved_allocations = std.ArrayList(ResolvedAllocation).init(allocator);
    errdefer resolved_allocations.deinit();
    
    var total_size: usize = 0;
    
    for (allocations) |alloc| {
        const buffer_info = buffer_map.get(alloc.buffer_id) orelse {
            std.log.err("Buffer {} not found in buffer_map", .{alloc.buffer_id});
            continue;
        };
        
        // Create shape copy for the allocation
        const shape_copy = try allocator.dupe(i64, buffer_info.shape);
        
        try resolved_allocations.append(.{
            .node_id = alloc.buffer_id,
            .output_idx = 0, // Default output index
            .offset = alloc.offset,
            .size = alloc.size,
            .dtype = buffer_info.dtype,
            .shape = shape_copy,
            .alignment = buffer_info.alignment,
            .lifetime = buffer_info.lifetime,
        });
        
        total_size = @max(total_size, alloc.offset + alloc.size);
    }
    
    return ResolvedMemoryPlan{
        .allocations = try resolved_allocations.toOwnedSlice(),
        .total_memory = total_size,
        .arena_size = total_size, // Use total_size as arena_size for now
    };
}

/// Calculate buffer size from shape and dtype
fn calculateBufferSize(shape: []const i64, dtype: DataType) usize {
    var total_elements: usize = 1;
    for (shape) |dim| {
        total_elements *= @intCast(dim);
    }
    return total_elements * dtype.byteSize();
}

/// Get alignment requirement for data type
fn getAlignment(dtype: DataType) usize {
    return switch (dtype) {
        .f32, .i32, .u32 => 4,
        .f64, .i64, .u64 => 8,
        .f16, .i16, .u16 => 2,
        .i8, .u8, .bool => 1,
    };
}

/// Resolve symbolic shape to concrete dimensions
fn resolveShapeToI64(shape_tracker: *const shape_mod.ShapeTracker, allocator: Allocator) ![]i64 {
    const dims = try allocator.alloc(i64, shape_tracker.dims.len);
    
    for (shape_tracker.dims, dims) |sym_dim, *out_dim| {
        out_dim.* = switch (sym_dim) {
            .concrete => |v| v,
            .dynamic => blk: {
                // For now, use a default size for dynamic dims
                // In production, these would be resolved from runtime values
                std.log.warn("Memory planning: using default size 100 for dynamic dimension", .{});
                break :blk 100;
            },
        };
    }
    
    return dims;
}

/// Compute memory statistics
fn computeMemoryStats(buffers: []const BufferInfo, plan: ResolvedMemoryPlan) MemoryStats {
    var stats = MemoryStats{};
    
    stats.total_buffers = buffers.len;
    stats.peak_memory = plan.total_memory;
    
    // Calculate total memory without sharing
    var total_without_sharing: usize = 0;
    for (buffers) |buf| {
        total_without_sharing += buf.size;
    }
    stats.total_memory = total_without_sharing;
    
    // Count reuses by checking for overlapping offsets
    var offset_usage = std.AutoHashMap(usize, usize).init(std.heap.page_allocator);
    defer offset_usage.deinit();
    
    for (plan.allocations) |alloc| {
        const count = offset_usage.get(alloc.offset) orelse 0;
        if (count > 0) {
            stats.buffer_reuses += 1;
            stats.bytes_reused += alloc.size;
        }
        offset_usage.put(alloc.offset, count + 1) catch {};
    }
    
    return stats;
}

// ===== Unit Tests =====
//
// These tests verify the correct behavior of the robust lifetime computation
// and memory optimization algorithms. The key insight tested here is that
// buffer lifetimes must represent when memory is needed, not just when nodes execute.

test "buffer lifetime computation" {
    const testing = std.testing;
    const allocator = testing.allocator;
    
    // Create a simple graph: a -> b -> c
    var graph = try Graph.init(allocator);
    defer graph.deinit();
    
    const a = try graph.addPlaceholder(.f32);
    const b = try graph.addNode(.sqrt, &.{a}, .f32);
    const c = try graph.addNode(.exp2, &.{b}, .f32);
    
    // Mark c as output
    try graph.output_nodes.append(graph.arena.allocator(), c);
    
    // Add shape metadata
    for ([_]NodeId{ a, b, c }) |node_id| {
        const node = graph.getNodeMut(node_id).?;
        node.metadata = try graph.arena.allocator().create(@import("graph").NodeMetadata);
        node.metadata.?.* = .{};
        node.metadata.?.output_shape = try shape_mod.ShapeTracker.fromDims(
            &[_]shape_mod.SymbolicDim{.{ .concrete = 10 }},
            graph.arena.allocator(),
            null,
        );
    }
    
    // Compute lifetimes
    const lifetimes = try computeBufferLifetimes(&graph, allocator);
    defer {
        for (lifetimes) |lifetime| {
            allocator.free(lifetime.shape);
        }
        allocator.free(lifetimes);
    }
    
    try testing.expectEqual(@as(usize, 3), lifetimes.len);
    
    // Check that output c has maximum lifetime
    for (lifetimes) |lifetime| {
        if (lifetime.node_id == c) {
            try testing.expectEqual(@as(u32, std.math.maxInt(u32)), lifetime.lifetime.end);
        }
    }
}

test "memory layout optimization" {
    const testing = std.testing;
    const allocator = testing.allocator;
    
    // Create buffers with overlapping lifetimes
    const buffers = [_]BufferInfo{
        // Buffer 0: lives from 0-2
        .{
            .node_id = 0,
            .size = 100,
            .alignment = 4,
            .lifetime = .{ .start = 0, .end = 2 },
            .dtype = .f32,
            .shape = &[_]i64{25},
        },
        // Buffer 1: lives from 1-3 (overlaps with 0)
        .{
            .node_id = 1,
            .size = 100,
            .alignment = 4,
            .lifetime = .{ .start = 1, .end = 3 },
            .dtype = .f32,
            .shape = &[_]i64{25},
        },
        // Buffer 2: lives from 3-4 (can reuse buffer 0's space)
        .{
            .node_id = 2,
            .size = 100,
            .alignment = 4,
            .lifetime = .{ .start = 3, .end = 4 },
            .dtype = .f32,
            .shape = &[_]i64{25},
        },
    };
    
    const allocations = try optimizeMemoryLayout(&buffers, allocator);
    defer allocator.free(allocations);
    
    try testing.expectEqual(@as(usize, 3), allocations.len);
    
    // Buffer 2 should reuse buffer 0's offset
    var buffer0_offset: ?usize = null;
    var buffer2_offset: ?usize = null;
    
    for (allocations) |alloc| {
        if (alloc.buffer_id == 0) buffer0_offset = alloc.offset;
        if (alloc.buffer_id == 2) buffer2_offset = alloc.offset;
    }
    
    try testing.expect(buffer0_offset != null);
    try testing.expect(buffer2_offset != null);
    try testing.expectEqual(buffer0_offset.?, buffer2_offset.?);
}

test "memory reuse with fixed lifetimes" {
    const testing = std.testing;
    const allocator = testing.allocator;
    
    // Test the scenario that was causing the reported bug:
    // Buffer lifetimes should not overlap when they shouldn't
    const buffers = [_]BufferInfo{
        // Input placeholder (lives forever)
        .{
            .node_id = 0,
            .size = 48,  // 3 * f32
            .alignment = 4,
            .lifetime = .{ .start = 0, .end = std.math.maxInt(u32) },
            .dtype = .f32,
            .shape = &[_]i64{3},
        },
        // Constant log2(e) (starts at step 1)
        .{
            .node_id = 1,
            .size = 4,   // scalar f32
            .alignment = 4,
            .lifetime = .{ .start = 1, .end = 3 },  // Used by multiply at step 3
            .dtype = .f32,
            .shape = &[_]i64{},
        },
        // x * log2(e) result (produced at step 3, consumed at step 5)
        .{
            .node_id = 2,
            .size = 48,  // 3 * f32
            .alignment = 4,
            .lifetime = .{ .start = 3, .end = 5 },  // CORRECTED: should be [3,5] not [2,3]
            .dtype = .f32,
            .shape = &[_]i64{3},
        },
        // neg(x) result (produced at step 4, no further consumers)
        .{
            .node_id = 5,
            .size = 48,  // 3 * f32
            .alignment = 4,
            .lifetime = .{ .start = 4, .end = std.math.maxInt(u32) },  // Output node
            .dtype = .f32,
            .shape = &[_]i64{3},
        },
    };
    
    const allocations = try optimizeMemoryLayout(&buffers, allocator);
    defer allocator.free(allocations);
    
    // Verify that buffers with overlapping lifetimes get different offsets
    var offset_to_lifetime = std.AutoHashMap(usize, LivenessInterval).init(allocator);
    defer offset_to_lifetime.deinit();
    
    for (allocations) |alloc| {
        const buffer_info = blk: {
            for (buffers) |buf| {
                if (buf.node_id == alloc.buffer_id) break :blk buf;
            }
            unreachable;
        };
        
        // Check if any existing buffer at this offset overlaps in lifetime
        if (offset_to_lifetime.get(alloc.offset)) |existing_lifetime| {
            // Lifetimes should not overlap for buffers at the same offset
            const no_overlap = existing_lifetime.end < buffer_info.lifetime.start or
                              buffer_info.lifetime.end < existing_lifetime.start or
                              existing_lifetime.end == std.math.maxInt(u32) or
                              buffer_info.lifetime.end == std.math.maxInt(u32);
            
            if (!no_overlap) {
                std.debug.print("ERROR: Overlapping lifetimes at offset {}: [{},{}] and [{},{}]\n", 
                    .{alloc.offset, existing_lifetime.start, existing_lifetime.end, 
                      buffer_info.lifetime.start, buffer_info.lifetime.end});
            }
            try testing.expect(no_overlap);
        } else {
            try offset_to_lifetime.put(alloc.offset, buffer_info.lifetime);
        }
    }
}

test "memory statistics computation" {
    const testing = std.testing;
    const allocator = testing.allocator;
    
    // Create test buffers with different lifetimes
    const buffers = [_]BufferInfo{
        .{
            .node_id = 0,
            .size = 1024,
            .alignment = 16,
            .lifetime = .{ .start = 0, .end = 3 },
            .dtype = .f32,
            .shape = &[_]i64{256},
        },
        .{
            .node_id = 1,
            .size = 2048,
            .alignment = 16,
            .lifetime = .{ .start = 1, .end = 2 },
            .dtype = .f32,
            .shape = &[_]i64{512},
        },
        .{
            .node_id = 2,
            .size = 1024,
            .alignment = 16,
            .lifetime = .{ .start = 3, .end = 4 },
            .dtype = .f32,
            .shape = &[_]i64{256},
        },
    };
    
    // Create allocations where buffer 2 reuses buffer 0's memory
    const allocations = try optimizeMemoryLayout(&buffers, allocator);
    defer allocator.free(allocations);
    
    const memory_plan = try createMemoryPlan(allocations, &buffers, allocator);
    defer memory_plan.deinit(allocator);
    
    const stats = computeMemoryStats(&buffers, memory_plan);
    
    // Verify statistics
    try testing.expectEqual(@as(usize, 3), stats.total_buffers);
    try testing.expectEqual(@as(usize, 4096), stats.total_memory); // 1024 + 2048 + 1024
    try testing.expectEqual(@as(usize, 3072), stats.peak_memory); // 1024 + 2048 (buffers 0 and 1 overlap)
    try testing.expect(stats.buffer_reuses > 0); // Buffer 2 reuses buffer 0's memory
}

test "memory plan with no reuse opportunities" {
    const testing = std.testing;
    const allocator = testing.allocator;
    
    // Create buffers that all overlap in lifetime
    const buffers = [_]BufferInfo{
        .{
            .node_id = 0,
            .size = 1024,
            .alignment = 16,
            .lifetime = .{ .start = 0, .end = 10 },
            .dtype = .f32,
            .shape = &[_]i64{256},
        },
        .{
            .node_id = 1,
            .size = 1024,
            .alignment = 16,
            .lifetime = .{ .start = 0, .end = 10 },
            .dtype = .f32,
            .shape = &[_]i64{256},
        },
        .{
            .node_id = 2,
            .size = 1024,
            .alignment = 16,
            .lifetime = .{ .start = 0, .end = 10 },
            .dtype = .f32,
            .shape = &[_]i64{256},
        },
    };
    
    const allocations = try optimizeMemoryLayout(&buffers, allocator);
    defer allocator.free(allocations);
    
    // All buffers should have different offsets
    var unique_offsets = std.AutoHashMap(usize, void).init(allocator);
    defer unique_offsets.deinit();
    
    for (allocations) |alloc| {
        try unique_offsets.put(alloc.offset, {});
    }
    
    try testing.expectEqual(@as(usize, 3), unique_offsets.count());
}

test "alignment requirements" {
    const testing = std.testing;
    const allocator = testing.allocator;
    
    // Create buffers with different alignment requirements
    const buffers = [_]BufferInfo{
        .{
            .node_id = 0,
            .size = 100,
            .alignment = 4,
            .lifetime = .{ .start = 0, .end = 1 },
            .dtype = .f32,
            .shape = &[_]i64{25},
        },
        .{
            .node_id = 1,
            .size = 200,
            .alignment = 8,
            .lifetime = .{ .start = 1, .end = 2 },
            .dtype = .f64,
            .shape = &[_]i64{25},
        },
        .{
            .node_id = 2,
            .size = 50,
            .alignment = 16,
            .lifetime = .{ .start = 2, .end = 3 },
            .dtype = .f32,
            .shape = &[_]i64{12},
        },
    };
    
    const allocations = try optimizeMemoryLayout(&buffers, allocator);
    defer allocator.free(allocations);
    
    // Verify alignment requirements are met
    for (allocations, 0..) |alloc, i| {
        const required_alignment = buffers[i].alignment;
        try testing.expect(alloc.offset % required_alignment == 0);
    }
}

test "correct buffer lifetime computation" {
    const testing = std.testing;
    const allocator = testing.allocator;
    
    // Create a graph that reproduces the reported bug:
    // x (node 0) -> x * log2(e) (node 2) -> exp2 (node 3)
    // x (node 0) -> neg (node 5)
    var graph = try Graph.init(allocator);
    defer graph.deinit();
    
    const x = try graph.addPlaceholder(.f32);  // node 0
    const log2_e = try graph.addConstant(.f32, 1.4426950408889634);  // node 1 
    const x_mul_log2_e = try graph.addNode(.multiply, &.{x, log2_e}, .f32);  // node 2
    const exp2_result = try graph.addNode(.exp2, &.{x_mul_log2_e}, .f32);  // node 3
    const neg_x = try graph.addNode(.negate, &.{x}, .f32);  // node 5 (assuming some nodes in between)
    
    // Mark exp2_result as output
    try graph.output_nodes.append(graph.arena.allocator(), exp2_result);
    try graph.output_nodes.append(graph.arena.allocator(), neg_x);
    
    // Add shape metadata for all nodes
    for ([_]NodeId{ x, log2_e, x_mul_log2_e, exp2_result, neg_x }) |node_id| {
        const node = graph.getNodeMut(node_id).?;
        node.metadata = try graph.arena.allocator().create(@import("graph").NodeMetadata);
        node.metadata.?.* = .{};
        node.metadata.?.output_shape = try shape_mod.ShapeTracker.fromDims(
            &[_]shape_mod.SymbolicDim{.{ .concrete = 3 }},
            graph.arena.allocator(),
            null,
        );
    }
    
    // Get topological order to simulate execution steps
    // Cast away const since topologicalSortOwned needs mutable access for caching
    const graph_mut = @constCast(graph);
    const topo_order = try graph_mut.topologicalSortOwned(allocator);
    defer allocator.free(topo_order);
    
    // Create step mapping
    var step_map = std.AutoHashMapUnmanaged(NodeId, u32){};
    defer step_map.deinit(allocator);
    
    for (topo_order, 0..) |node_id, step| {
        try step_map.put(allocator, node_id, @intCast(step));
    }
    
    // Test the fixed lifetime computation
    const x_mul_log2_e_lifetime = computeBufferLifetime(&graph, x_mul_log2_e, &step_map);
    const exp2_result_step = step_map.get(exp2_result).?;
    const x_mul_log2_e_step = step_map.get(x_mul_log2_e).?;
    
    // The key test: x_mul_log2_e buffer should live from its production step
    // until the step where exp2_result executes (which consumes it)
    try testing.expectEqual(x_mul_log2_e_step, x_mul_log2_e_lifetime.start);
    try testing.expectEqual(exp2_result_step, x_mul_log2_e_lifetime.end);
    
    // Verify that the buffer lifetime correctly covers the consumer's execution
    try testing.expect(x_mul_log2_e_lifetime.end >= x_mul_log2_e_lifetime.start);
    try testing.expect(x_mul_log2_e_lifetime.end >= exp2_result_step);
}

test "placeholder and output node lifetime handling" {
    const testing = std.testing;
    const allocator = testing.allocator;
    
    var graph = try Graph.init(allocator);
    defer graph.deinit();
    
    const input = try graph.addPlaceholder(.f32);
    const output = try graph.addNode(.sqrt, &.{input}, .f32);
    
    try graph.output_nodes.append(graph.arena.allocator(), output);
    
    // Add metadata
    for ([_]NodeId{ input, output }) |node_id| {
        const node = graph.getNodeMut(node_id).?;
        node.metadata = try graph.arena.allocator().create(@import("graph").NodeMetadata);
        node.metadata.?.* = .{};
        node.metadata.?.output_shape = try shape_mod.ShapeTracker.fromDims(
            &[_]shape_mod.SymbolicDim{.{ .concrete = 10 }},
            graph.arena.allocator(),
            null,
        );
    }
    
    var step_map = std.AutoHashMapUnmanaged(NodeId, u32){};
    defer step_map.deinit(allocator);
    try step_map.put(allocator, input, 0);
    try step_map.put(allocator, output, 1);
    
    // Test placeholder lifetime (should be [0, maxInt])
    const input_lifetime = computeBufferLifetime(&graph, input, &step_map);
    try testing.expectEqual(@as(u32, 0), input_lifetime.start);
    try testing.expectEqual(std.math.maxInt(u32), input_lifetime.end);
    
    // Test output node lifetime (should be [production_step, maxInt])
    const output_lifetime = computeBufferLifetime(&graph, output, &step_map);
    try testing.expectEqual(@as(u32, 1), output_lifetime.start);
    try testing.expectEqual(std.math.maxInt(u32), output_lifetime.end);
}

test "no consumer node lifetime" {
    const testing = std.testing;
    const allocator = testing.allocator;
    
    var graph = try Graph.init(allocator);
    defer graph.deinit();
    
    const input = try graph.addPlaceholder(.f32);
    const intermediate = try graph.addNode(.sqrt, &.{input}, .f32);
    // Note: intermediate is not consumed by any other node and not an output
    
    // Add metadata
    for ([_]NodeId{ input, intermediate }) |node_id| {
        const node = graph.getNodeMut(node_id).?;
        node.metadata = try graph.arena.allocator().create(@import("graph").NodeMetadata);
        node.metadata.?.* = .{};
        node.metadata.?.output_shape = try shape_mod.ShapeTracker.fromDims(
            &[_]shape_mod.SymbolicDim{.{ .concrete = 5 }},
            graph.arena.allocator(),
            null,
        );
    }
    
    var step_map = std.AutoHashMapUnmanaged(NodeId, u32){};
    defer step_map.deinit(allocator);
    try step_map.put(allocator, input, 0);
    try step_map.put(allocator, intermediate, 1);
    
    // Test intermediate node with no consumers
    const intermediate_lifetime = computeBufferLifetime(&graph, intermediate, &step_map);
    try testing.expectEqual(@as(u32, 1), intermediate_lifetime.start);
    try testing.expectEqual(@as(u32, 1), intermediate_lifetime.end);  // Ends at production step
}

test "constant nodes get buffers allocated" {
    const testing = std.testing;
    const allocator = testing.allocator;
    
    var graph = try Graph.init(allocator);
    defer graph.deinit();
    
    // Create a simple graph with a constant
    const x = try graph.addPlaceholder(.f32);
    const c = try graph.addConstant(3.14);
    const result = try graph.addNode(.multiply, &.{x, c}, .f32);
    
    try graph.output_nodes.append(graph.arena.allocator(), result);
    
    // The constant node should already have metadata from addConstant
    const const_node = graph.getNode(c).?;
    try testing.expect(const_node.metadata != null);
    try testing.expect(const_node.metadata.?.output_shape != null);
    try testing.expectEqual(@as(f32, 3.14), const_node.metadata.?.constant_value);
    
    // Add metadata to other nodes
    for ([_]NodeId{ x, result }) |node_id| {
        const node = graph.getNodeMut(node_id).?;
        if (node.metadata == null) {
            node.metadata = try graph.arena.allocator().create(@import("graph").NodeMetadata);
            node.metadata.?.* = .{};
        }
        if (node.metadata.?.output_shape == null) {
            node.metadata.?.output_shape = try shape_mod.ShapeTracker.fromDims(
                &[_]shape_mod.SymbolicDim{.{ .concrete = 5 }},
                graph.arena.allocator(),
                null,
            );
        }
    }
    
    // Run buffer lifetime computation
    const lifetimes = try computeBufferLifetimes(&graph, allocator);
    defer {
        for (lifetimes) |lifetime| {
            allocator.free(lifetime.shape);
        }
        allocator.free(lifetimes);
    }
    
    // Debug output
    std.debug.print("\nTest: constant nodes get buffers allocated\n", .{});
    std.debug.print("Graph has {} nodes\n", .{graph.nodes.items.len});
    std.debug.print("Found {} buffers with lifetimes\n", .{lifetimes.len});
    
    // Find if constant got a buffer
    var constant_found = false;
    for (lifetimes) |buffer| {
        std.debug.print("  Buffer for node {}: size={}, lifetime=[{},{}]\n", 
            .{ buffer.node_id, buffer.size, buffer.lifetime.start, buffer.lifetime.end });
        if (buffer.node_id == c) {
            constant_found = true;
            std.debug.print("    ^ This is the constant node\n", .{});
        }
    }
    
    // The test: constant should have gotten a buffer
    try testing.expect(constant_found);
}

test "mean operation constant divisor gets buffer" {
    const testing = std.testing;
    const allocator = testing.allocator;
    
    // Import tensor functions through the tensor module
    const tensor = @import("tensor");
    
    var graph = try Graph.init(allocator);
    defer graph.deinit();
    
    // Create a tensor and compute its mean
    const x = try tensor.placeholder(&graph, &.{3, 4}, .f32);
    const mean_val = try x.mean(1);
    
    try graph.output_nodes.append(graph.arena.allocator(), mean_val.node_id);
    
    // Find all constant nodes
    std.debug.print("\nTest: mean operation constant divisor gets buffer\n", .{});
    std.debug.print("Looking for constant nodes in graph with {} nodes\n", .{graph.nodes.items.len});
    
    var constant_nodes = std.ArrayList(NodeId).init(allocator);
    defer constant_nodes.deinit();
    
    var node_id: NodeId = 0;
    while (node_id < graph.next_node_id) : (node_id += 1) {
        if (graph.getNode(node_id)) |node| {
            std.debug.print("  Node {}: spec={}", .{ node_id, node.spec });
            if (node.metadata) |metadata| {
                if (metadata.output_shape) |shape| {
                    std.debug.print(", shape_dims={}", .{shape.dims.len});
                } else {
                    std.debug.print(", NO OUTPUT SHAPE", .{});
                }
                if (node.spec == .data and node.spec.data == .constant) {
                    std.debug.print(", constant_value={}", .{metadata.constant_value});
                    try constant_nodes.append(node_id);
                }
            } else {
                std.debug.print(", NO METADATA", .{});
            }
            std.debug.print("\n", .{});
        }
    }
    
    std.debug.print("Found {} constant nodes\n", .{constant_nodes.items.len});
    try testing.expect(constant_nodes.items.len > 0);
    
    // Run buffer lifetime computation
    const lifetimes = try computeBufferLifetimes(&graph, allocator);
    defer {
        for (lifetimes) |lifetime| {
            allocator.free(lifetime.shape);
        }
        allocator.free(lifetimes);
    }
    
    std.debug.print("Computed {} buffer lifetimes\n", .{lifetimes.len});
    
    // Check which constants got buffers
    var constants_with_buffers: usize = 0;
    for (constant_nodes.items) |const_id| {
        var found = false;
        for (lifetimes) |buffer| {
            if (buffer.node_id == const_id) {
                found = true;
                constants_with_buffers += 1;
                std.debug.print("  Constant node {} got buffer: size={}\n", .{ const_id, buffer.size });
                break;
            }
        }
        if (!found) {
            std.debug.print("  Constant node {} DID NOT get buffer!\n", .{const_id});
        }
    }
    
    std.debug.print("Constants with buffers: {} out of {}\n", .{ constants_with_buffers, constant_nodes.items.len });
    
    // All constants should have buffers
    try testing.expectEqual(constant_nodes.items.len, constants_with_buffers);
}