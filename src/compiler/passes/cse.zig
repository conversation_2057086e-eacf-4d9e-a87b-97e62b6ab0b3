/// Common Subexpression Elimination (CSE) Pass
/// 
/// Identifies and eliminates duplicate computations by replacing them
/// with references to a single computation. This reduces redundant work
/// and can improve both compilation time and runtime performance.
/// 
/// Now uses the unified pass template system for consistency.

const std = @import("std");
const Allocator = std.mem.Allocator;

// Import types and compiler infrastructure
const types = @import("types");
const NodeId = types.NodeId;

const Graph = @import("graph").Graph;
const Node = @import("graph").Node;

// Import from parent modules
const PassContext = @import("../unified_pipeline.zig").PassContext;
const pass_templates = @import("../pass_templates.zig");
const transforms = @import("../transforms.zig");
const GraphRewriter = transforms.GraphRewriter;

/// Hash a node based on its operation, inputs, and relevant metadata
fn hashNode(node: Node) u64 {
    var hasher = std.hash.Wyhash.init(0);
    
    // Hash the operation type
    const op_tag = @intFromEnum(node.spec);
    hasher.update(std.mem.asBytes(&op_tag));
    
    if (node.spec == .compute) {
        const compute_op = @intFromEnum(node.spec.compute);
        hasher.update(std.mem.asBytes(&compute_op));
        
        // Hash metadata for operations where it affects semantics
        if (node.metadata) |metadata| {
            switch (node.spec.compute) {
                .sum_reduce, .max_reduce => {
                    // For reduction operations, the axis is critical
                    if (metadata.reduction_axis) |axis| {
                        hasher.update(std.mem.asBytes(&axis));
                    }
                    // Also hash keepdims as it affects the operation
                    const keepdims_byte: u8 = if (metadata.keepdims) 1 else 0;
                    hasher.update(&[_]u8{keepdims_byte});
                },
                else => {},
            }
        }
    }
    
    // Hash the inputs in order
    for (node.inputs) |input_id| {
        hasher.update(std.mem.asBytes(&input_id));
    }
    
    return hasher.final();
}

// ============================================================================
// Common Subexpression Elimination Pass using Template System
// ============================================================================

/// CSE pass using the template system
pub const CommonSubexpressionEliminationPass = pass_templates.createAnalysisPass(.{
    .name = "common_subexpression_elimination",
    .priority = 70,
    .analyze = analyzeAndEliminateCSE,
    .modifies_graph = true,
    .post_conditions = pass_templates.ensureNoCycles,
});

/// Analyze and eliminate common subexpressions
fn analyzeAndEliminateCSE(ctx: *PassContext) !void {
    const graph = ctx.graph;
    
    if (@import("build_options").enable_debug_logs) {
        std.debug.print("\nCSE: Starting\n", .{});
        std.debug.print("  output_nodes = {any}\n", .{graph.output_nodes.items});
        for (0..@min(graph.next_node_id, 5)) |i| {
            const nid: NodeId = @intCast(i);
            std.debug.print("  Node {} is_valid={}\n", .{nid, graph.hasNode(nid)});
        }
    }
    
    var seen = std.AutoHashMapUnmanaged(u64, NodeId){};
    defer seen.deinit(ctx.allocator);
    
    var rewriter = GraphRewriter.init(graph, ctx.allocator);
    defer rewriter.deinit();
    
    // Get topological order to process nodes
    const topo_order = try graph.topologicalSortOwned(ctx.allocator);
    defer ctx.allocator.free(topo_order);
    
    for (topo_order) |node_id| {
        // Resolve current node ID in case it was substituted
        const current_id = graph.resolveCurrentNodeId(node_id);
        const node = graph.getNode(current_id) orelse continue;
        
        // Only consider compute nodes (not data sources)
        const is_data_source = switch (node.spec) {
            .data => true,
            .compute => false,
        };
        if (is_data_source) continue;
        
        // Create hash of node operation and inputs
        const node_hash = hashNode(node.*);
        
        if (seen.get(node_hash)) |existing_id| {
            // Found duplicate - substitute this node with the existing one
            if (@import("build_options").enable_debug_logs) {
                std.debug.print("CSE: Found duplicate - node {} same as node {}\n", .{current_id, existing_id});
            }
            try rewriter.markForSubstitution(current_id, existing_id);
        } else {
            // First occurrence - remember it
            try seen.put(ctx.allocator, node_hash, current_id);
        }
    }
    
    // Apply all substitutions atomically
    try rewriter.commitChanges();
    
    if (@import("build_options").enable_debug_logs) {
        std.debug.print("CSE: After commit, output_nodes = {any}\n", .{graph.output_nodes.items});
        for (0..@min(graph.next_node_id, 5)) |i| {
            const nid: NodeId = @intCast(i);
            std.debug.print("  Node {} is_valid={}\n", .{nid, graph.hasNode(nid)});
        }
    }
}



// ===== Unit Tests =====

test "CSE merges duplicate computations" {
    const testing = std.testing;
    
    var graph = try Graph.init(testing.allocator);
    defer graph.deinit();
    
    // Create duplicate additions
    const a = try graph.addPlaceholder(.f32);
    const b = try graph.addPlaceholder(.f32);
    const add1 = try graph.addNode(.add, &.{a, b}, .f32);
    const add2 = try graph.addNode(.add, &.{a, b}, .f32);
    const result = try graph.addNode(.mul, &.{add1, add2}, .f32);
    
    // Mark result as output
    try graph.output_nodes.append(result);
    
    var ctx = PassContext.init(&graph, testing.allocator);
    try CommonSubexpressionEliminationPass.run(&ctx);
    
    // After CSE, the multiply should use the same add node for both inputs
    const result_node = graph.getNode(result).?;
    try testing.expect(result_node.inputs[0] == result_node.inputs[1]);
}

test "Hash function distinguishes different operations" {
    const testing = std.testing;
    
    const node1 = Node{
        .id = 1,
        .spec = .{ .compute = .add },
        .inputs = &.{2, 3},
        .outputs = &.{},
        .is_valid = true,
        .metadata = null,
    };
    
    const node2 = Node{
        .id = 4,
        .spec = .{ .compute = .mul },
        .inputs = &.{2, 3},
        .outputs = &.{},
        .is_valid = true,
        .metadata = null,
    };
    
    const hash1 = hashNode(node1);
    const hash2 = hashNode(node2);
    
    try testing.expect(hash1 != hash2);
}

test "Hash function identifies identical operations" {
    const testing = std.testing;
    
    const node1 = Node{
        .id = 1,
        .spec = .{ .compute = .add },
        .inputs = &.{2, 3},
        .outputs = &.{},
        .is_valid = true,
        .metadata = null,
    };
    
    const node2 = Node{
        .id = 4,
        .spec = .{ .compute = .add },
        .inputs = &.{2, 3},
        .outputs = &.{},
        .is_valid = true,
        .metadata = null,
    };
    
    const hash1 = hashNode(node1);
    const hash2 = hashNode(node2);
    
    try testing.expect(hash1 == hash2);
}

test "Hash function distinguishes reduction operations with different axes" {
    const testing = std.testing;
    
    var metadata1 = @import("graph").NodeMetadata{
        .reduction_axis = 0,
        .keepdims = true,
    };
    
    var metadata2 = @import("graph").NodeMetadata{
        .reduction_axis = 1,
        .keepdims = true,
    };
    
    const node1 = Node{
        .id = 1,
        .spec = .{ .compute = .sum_reduce },
        .inputs = &.{2},
        .outputs = &.{},
        .is_valid = true,
        .metadata = &metadata1,
    };
    
    const node2 = Node{
        .id = 4,
        .spec = .{ .compute = .sum_reduce },
        .inputs = &.{2},
        .outputs = &.{},
        .is_valid = true,
        .metadata = &metadata2,
    };
    
    const hash1 = hashNode(node1);
    const hash2 = hashNode(node2);
    
    // Should have different hashes due to different reduction axes
    try testing.expect(hash1 != hash2);
}

test "Hash function distinguishes reduction operations with different keepdims" {
    const testing = std.testing;
    
    var metadata1 = @import("graph").NodeMetadata{
        .reduction_axis = 0,
        .keepdims = true,
    };
    
    var metadata2 = @import("graph").NodeMetadata{
        .reduction_axis = 0,
        .keepdims = false,
    };
    
    const node1 = Node{
        .id = 1,
        .spec = .{ .compute = .sum_reduce },
        .inputs = &.{2},
        .outputs = &.{},
        .is_valid = true,
        .metadata = &metadata1,
    };
    
    const node2 = Node{
        .id = 4,
        .spec = .{ .compute = .sum_reduce },
        .inputs = &.{2},
        .outputs = &.{},
        .is_valid = true,
        .metadata = &metadata2,
    };
    
    const hash1 = hashNode(node1);
    const hash2 = hashNode(node2);
    
    // Should have different hashes due to different keepdims values
    try testing.expect(hash1 != hash2);
}

test "CSE preserves data sources" {
    const testing = std.testing;
    
    var graph = try Graph.init(testing.allocator);
    defer graph.deinit();
    
    // Create duplicate constants (should not be merged)
    const const1 = try graph.addConstant(5.0);
    const const2 = try graph.addConstant(5.0);
    const result = try graph.addNode(.add, &.{const1, const2}, .f32);
    
    // Mark result as output
    try graph.output_nodes.append(result);
    
    var ctx = PassContext.init(&graph, testing.allocator);
    try CommonSubexpressionEliminationPass.run(&ctx);
    
    // Constants should not be merged (they're data sources)
    try testing.expect(graph.hasNode(const1));
    try testing.expect(graph.hasNode(const2));
    try testing.expect(const1 != const2);
}

test "CSE handles complex expression trees" {
    const testing = std.testing;
    
    var graph = try Graph.init(testing.allocator);
    defer graph.deinit();
    
    // Create: (a + b) * (a + b) + (c * d) * (c * d)
    const a = try graph.addPlaceholder(.f32);
    const b = try graph.addPlaceholder(.f32);
    const c = try graph.addPlaceholder(.f32);
    const d = try graph.addPlaceholder(.f32);
    
    // First (a + b)
    const ab1 = try graph.addNode(.add, &.{a, b}, .f32);
    // Second (a + b) - duplicate
    const ab2 = try graph.addNode(.add, &.{a, b}, .f32);
    // (a + b) * (a + b)
    const ab_squared = try graph.addNode(.mul, &.{ab1, ab2}, .f32);
    
    // First (c * d)
    const cd1 = try graph.addNode(.mul, &.{c, d}, .f32);
    // Second (c * d) - duplicate
    const cd2 = try graph.addNode(.mul, &.{c, d}, .f32);
    // (c * d) * (c * d)
    const cd_squared = try graph.addNode(.mul, &.{cd1, cd2}, .f32);
    
    // Final result
    const result = try graph.addNode(.add, &.{ab_squared, cd_squared}, .f32);
    
    // Mark result as output
    try graph.output_nodes.append(result);
    
    const initial_count = countValidNodes(&graph);
    
    var ctx = PassContext.init(&graph, testing.allocator);
    try CommonSubexpressionEliminationPass.run(&ctx);
    
    const final_count = countValidNodes(&graph);
    
    // Should have eliminated the duplicate (a+b) and (c*d)
    try testing.expect(final_count < initial_count);
}

/// Count valid nodes in the graph
fn countValidNodes(graph: *const Graph) usize {
    var count: usize = 0;
    for (graph.nodes.items) |node| {
        if (node.is_valid) count += 1;
    }
    return count;
}

test "CSE pass template creation" {
    const testing = std.testing;
    
    // Verify pass properties
    try testing.expectEqualStrings("common_subexpression_elimination", CommonSubexpressionEliminationPass.name);
    try testing.expectEqual(@as(u32, 70), CommonSubexpressionEliminationPass.priority);
    try testing.expectEqual(pass_templates.PassType.analysis, CommonSubexpressionEliminationPass.pass_type);
}

test "CSE template pass execution" {
    const testing = std.testing;
    
    var graph = try Graph.init(testing.allocator);
    defer graph.deinit();
    
    // Create duplicate computations
    const a = try graph.addPlaceholder(.f32);
    const b = try graph.addPlaceholder(.f32);
    const add1 = try graph.addNode(.add, &.{a, b}, .f32);
    const add2 = try graph.addNode(.add, &.{a, b}, .f32); // Duplicate
    const result = try graph.addNode(.mul, &.{add1, add2}, .f32);
    
    // Mark result as output
    try graph.output_nodes.append(result);
    
    var ctx = PassContext.init(&graph, testing.allocator);
    
    // Run using the template pass directly
    try CommonSubexpressionEliminationPass.run(&ctx);
    
    // After CSE, the multiply should use the same node for both inputs
    // (This is a simplified test - in reality we'd need better tracking)
    try testing.expect(graph.nodes.items.len >= 4); // a, b, add, mul
}