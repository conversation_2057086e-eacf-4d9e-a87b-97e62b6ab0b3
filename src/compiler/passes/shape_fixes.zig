/// Shape Fixes Pass
/// 
/// This module implements shape validation and fixes for shape-specific issues
/// using the template system.

const std = @import("std");
const Allocator = std.mem.Allocator;

// Import types
const types = @import("types");
const NodeId = types.NodeId;
const ComputeOp = types.ComputeOp;
const DataType = types.DataType;

const shape_mod = @import("shape");
const SymbolicDim = shape_mod.SymbolicDim;
const ShapeTracker = shape_mod.ShapeTracker;

const Graph = @import("graph").Graph;
const Node = @import("graph").Node;
const NodeSpec = types.NodeSpec;

// Import new template infrastructure
const pass_templates = @import("../pass_templates.zig");
const transforms = @import("../transforms.zig");
const PassContext = @import("../unified_pipeline.zig").PassContext;

const SymbolicPool = @import("symbolic").SymbolicPool;

// ============================================================================
// Shape Validation Logic
// ============================================================================

/// Validate broadcast compatibility for elementwise operations
fn validateBroadcast(graph: *Graph, inputs: []const NodeId) !void {
    _ = graph;
    _ = inputs;
    // TODO: Implement proper broadcast validation
    // For now, this is a placeholder that doesn't fail
}

/// Check if operation can handle non-contiguous inputs
fn canHandleNonContiguous(op: ComputeOp) bool {
    return switch (op) {
        // Most elementwise operations can handle non-contiguous
        .add, .mul, .sub, .div, .mod, .less_than => true,
        .sin, .cos, .exp2, .log2, .sqrt, .recip => true,
        
        // Reductions and matrix operations typically need contiguous
        .sum_reduce, .max_reduce, .min_reduce, .mean_reduce => false,
        .gather, .transpose => false,
        
        // Other operations
        else => false,
    };
}

/// Check if a shape has value-dependent dimensions
pub fn hasValueDependentDims(shape: ShapeTracker) bool {
    for (shape.dims) |dim| {
        switch (dim) {
            .symbolic => return true,
            .concrete => continue,
        }
    }
    return false;
}

/// Visitor function for shape validation
fn shapeValidationVisitor(node_id: NodeId, node: *Node, ctx: *PassContext) !bool {
    _ = node_id;
    const graph = ctx.graph;
    
    switch (node.spec) {
        .compute => |op| {
            if (op.isElementwise()) {
                try validateBroadcast(graph, node.inputs);
            }
        },
        .data => {}, // No inputs to validate
    }
    
    return false; // No modifications made
}

/// Visitor function for contiguous insertion analysis
fn contiguousInsertionVisitor(node_id: NodeId, node: *Node, ctx: *PassContext) !bool {
    _ = node_id;
    _ = ctx;
    
    switch (node.spec) {
        .compute => |op| {
            if (!canHandleNonContiguous(op.op)) {
                // TODO: Analyze if inputs are non-contiguous and insert contiguous nodes
                // For now, this is a placeholder
            }
        },
        .data => {}, // No processing needed
    }
    
    return false; // No modifications made for now
}

/// Visitor function for input-derived shape validation
fn inputDerivedShapeVisitor(node_id: NodeId, node: *Node, ctx: *PassContext) !bool {
    _ = node_id;
    _ = node;
    _ = ctx;
    
    // TODO: Implement input-derived shape validation
    // This ensures that shapes derived from inputs are consistent
    
    return false; // No modifications made
}

// ============================================================================
// Template-Based Pass Definitions
// ============================================================================

/// Shape validation pass using template system
pub const ShapeValidationPass = pass_templates.createTraversalPass(.{
    .name = "shape_validation",
    .priority = 95,
    .visitor = shapeValidationVisitor,
});

/// Contiguous insertion pass using template system
pub const ContiguousInsertionPass = pass_templates.createTraversalPass(.{
    .name = "contiguous_insertion",
    .priority = 60,
    .visitor = contiguousInsertionVisitor,
});

/// Input-derived shape validation pass using template system
pub const InputDerivedShapeValidationPass = pass_templates.createTraversalPass(.{
    .name = "input_derived_shape_validation",
    .priority = 90,
    .visitor = inputDerivedShapeVisitor,
});

// ============================================================================
// Legacy Functions (Remove when migration complete)
// ============================================================================

pub fn validateShapesPass(ctx: *PassContext) !void {
    return ShapeValidationPass.run(ctx);
}

pub fn insertContiguous(ctx: *PassContext) !void {
    return ContiguousInsertionPass.run(ctx);
}

pub fn validateInputDerivedShapes(ctx: *PassContext) !void {
    return InputDerivedShapeValidationPass.run(ctx);
}

// ============================================================================
// Unit Tests
// ============================================================================

test "shape validation pass execution" {
    const testing = std.testing;
    
    var graph = try Graph.init(testing.allocator);
    defer graph.deinit();
    
    // Create elementwise operation
    const a = try graph.addPlaceholder(.f32);
    const b = try graph.addPlaceholder(.f32);
    _ = try graph.addNode(.add, &.{a, b}, .f32);
    
    var ctx = PassContext.init(&graph, testing.allocator);
    try ShapeValidationPass.run(&ctx);
    
    // Should not crash (basic smoke test)
    try testing.expect(graph.nodes.items.len >= 3);
}

test "contiguous insertion analysis" {
    const testing = std.testing;
    
    // Test that operations are correctly classified
    try testing.expect(canHandleNonContiguous(.add));
    try testing.expect(canHandleNonContiguous(.mul));
    try testing.expect(!canHandleNonContiguous(.sum_reduce));
    try testing.expect(!canHandleNonContiguous(.gather));
}

test "value dependent dimensions detection" {
    const testing = std.testing;
    
    var pool = try SymbolicPool.init(testing.allocator);
    defer pool.deinit();
    
    // Test concrete dimensions (no value dependency)
    const concrete_shape = try ShapeTracker.fromDims(&[_]SymbolicDim{
        .{ .concrete = 10 },
        .{ .concrete = 20 },
    }, testing.allocator, &pool);
    defer concrete_shape.deinit(testing.allocator);
    
    try testing.expect(!hasValueDependentDims(concrete_shape));
    
    // Test symbolic dimensions (has value dependency)
    const x_var = try pool.variable("x");
    const symbolic_shape = try ShapeTracker.fromDims(&[_]SymbolicDim{
        .{ .concrete = 10 },
        .{ .symbolic = x_var },
    }, testing.allocator, &pool);
    defer symbolic_shape.deinit(testing.allocator);
    
    try testing.expect(hasValueDependentDims(symbolic_shape));
}

test "template integration" {
    const testing = std.testing;
    
    // Test that passes are correctly configured
    try testing.expectEqualStrings("shape_validation", ShapeValidationPass.name);
    try testing.expectEqualStrings("contiguous_insertion", ContiguousInsertionPass.name);
    try testing.expectEqualStrings("input_derived_shape_validation", InputDerivedShapeValidationPass.name);
    
    try testing.expectEqual(@as(u32, 95), ShapeValidationPass.priority);
    try testing.expectEqual(@as(u32, 60), ContiguousInsertionPass.priority);
    try testing.expectEqual(@as(u32, 90), InputDerivedShapeValidationPass.priority);
}

test "shape passes execution" {
    const testing = std.testing;
    
    var graph = try Graph.init(testing.allocator);
    defer graph.deinit();
    
    const input = try graph.addPlaceholder(.f32);
    _ = try graph.addNode(.sum_reduce, &.{input}, .f32);
    
    var ctx = PassContext.init(&graph, testing.allocator);
    
    // Run all shape-related passes
    try ShapeValidationPass.run(&ctx);
    try ContiguousInsertionPass.run(&ctx);
    try InputDerivedShapeValidationPass.run(&ctx);
    
    // Should not crash
    try testing.expect(graph.nodes.items.len >= 2);
}