/// Pattern Matching and Optimization Pass
///
/// Recognizes and optimizes complex patterns in the computation graph,
/// including matmul patterns, gather operations, and arange patterns.

const std = @import("std");
const Allocator = std.mem.Allocator;

// Import types
const types = @import("types");
const NodeId = types.NodeId;
const ComputeOp = types.ComputeOp;
const DataType = types.DataType;
const CustomOp = types.CustomOp;

// Import graph components
const Graph = @import("graph").Graph;
const Node = @import("graph").Node;
const NodeMetadata = @import("graph").NodeMetadata;

// Import infrastructure
const PassContext = @import("../unified_pipeline.zig").PassContext;
const pass_templates = @import("../pass_templates.zig");
const patterns_mod = @import("../patterns.zig");
const transforms = @import("../transforms.zig");

// Import shape components for metadata
const shape_mod = @import("shape");
const ShapeTracker = shape_mod.ShapeTracker;

// ============================================================================
// Pattern Recognition Pass using Template System
// ============================================================================

/// Create pattern recognition pass with backend-specific patterns
pub fn PatternRecognition(comptime Backend: type) type {
    // Combine common patterns with backend-specific patterns
    const all_patterns = if (@hasDecl(Backend, "patterns"))
        patterns_mod.CommonPatterns.all() ++ Backend.patterns
    else
        patterns_mod.CommonPatterns.all();
    
    return pass_templates.createPatternPass(.{
        .name = "patterns",
        .priority = 85,
        .patterns = all_patterns,
        .max_iterations = 3, // Limit iterations for pattern matching
        .pattern_filter = filterPatterns,
        .pre_conditions = pass_templates.requireShapeMetadata,
    });
}

/// Pattern filter to prioritize certain patterns
fn filterPatterns(pattern: *const patterns_mod.Pattern) bool {
    // For now, accept all patterns
    // In the future, could filter based on backend capabilities
    _ = pattern;
    return true;
}


// ============================================================================
// Custom Operation Metadata Helpers
// ============================================================================

/// Create metadata for matmul operation
fn createMatMulMetadata(
    a_shape: ShapeTracker,
    b_shape: ShapeTracker,
    allocator: Allocator,
) !*NodeMetadata {
    const metadata = try allocator.create(NodeMetadata);
    metadata.* = .{};
    
    // Calculate output shape for matmul
    if (a_shape.dims.len < 2 or b_shape.dims.len < 2) {
        return error.InvalidMatMulShapes;
    }
    
    // For now, simple 2D matmul
    const m = a_shape.dims[a_shape.dims.len - 2];
    const n = b_shape.dims[b_shape.dims.len - 1];
    
    var output_dims = try allocator.alloc(shape_mod.SymbolicDim, 2);
    output_dims[0] = m;
    output_dims[1] = n;
    
    metadata.output_shape = try ShapeTracker.fromDims(
        output_dims,
        allocator,
        &a_shape.pool.*
    );
    
    // Store input shapes
    const input_shapes = try allocator.alloc(ShapeTracker, 2);
    input_shapes[0] = try a_shape.clone(allocator);
    input_shapes[1] = try b_shape.clone(allocator);
    metadata.input_shapes = input_shapes;
    
    return metadata;
}

/// Create metadata for gather operation
fn createGatherMetadata(
    data_shape: ShapeTracker,
    indices_shape: ShapeTracker,
    allocator: Allocator,
) !*NodeMetadata {
    const metadata = try allocator.create(NodeMetadata);
    metadata.* = .{};
    
    // Output shape is typically indices shape
    metadata.output_shape = try indices_shape.clone(allocator);
    
    // Store input shapes
    const input_shapes = try allocator.alloc(ShapeTracker, 2);
    input_shapes[0] = try data_shape.clone(allocator);
    input_shapes[1] = try indices_shape.clone(allocator);
    metadata.input_shapes = input_shapes;
    
    return metadata;
}

/// Create metadata for arange operation
fn createARangeMetadata(
    length: i64,
    allocator: Allocator,
    pool: *shape_mod.SymbolicPool,
) !*NodeMetadata {
    const metadata = try allocator.create(NodeMetadata);
    metadata.* = .{};
    
    // ARange produces 1D tensor with given length
    var output_dims = try allocator.alloc(shape_mod.SymbolicDim, 1);
    output_dims[0] = .{ .concrete = length };
    
    metadata.output_shape = try ShapeTracker.fromDims(
        output_dims,
        allocator,
        pool
    );
    
    metadata.is_arange = true;
    
    return metadata;
}

// ============================================================================
// Pattern-Specific Handlers (if needed for complex transformations)
// ============================================================================

/// Custom handler for matmul pattern (if default replacement isn't sufficient)
pub fn handleMatMulPattern(
    graph: *Graph,
    match: patterns_mod.Match,
    allocator: Allocator,
) !NodeId {
    _ = graph;
    _ = match;
    _ = allocator;
    
    // The pattern system should handle most of this automatically
    // This is here if we need custom logic beyond simple replacement
    return error.NotImplemented;
}

// ============================================================================
// Unit Tests
// ============================================================================

test "pattern recognition pass creation" {
    const testing = std.testing;
    
    // Test with empty backend (no additional patterns)
    const EmptyBackend = struct {};
    const PassType = PatternRecognition(EmptyBackend);
    
    // Verify it's created correctly
    try testing.expectEqualStrings("patterns", PassType.name);
    try testing.expectEqual(@as(u32, 85), PassType.priority);
}

test "pattern recognition with backend patterns" {
    const testing = std.testing;
    
    // Test backend with custom patterns
    const TestBackend = struct {
        pub const patterns = [_]patterns_mod.Pattern{
            patterns_mod.Pattern{
                .name = "test_pattern",
                .match = .{ .op = .{ .op = .add, .inputs = &.{
                    .{ .@"var" = "a" },
                    .{ .@"var" = "b" },
                }}},
                .replace = .{ .custom = .{ .name = "test_add", .inputs = &.{ "a", "b" } } },
            },
        };
    };
    
    const PassType = PatternRecognition(TestBackend);
    try testing.expectEqualStrings("patterns", PassType.name);
}

test "matmul metadata creation" {
    const testing = std.testing;
    
    var pool = try shape_mod.SymbolicPool.init(testing.allocator);
    defer pool.deinit();
    
    // Create 2D shapes for matmul
    const a_shape = try ShapeTracker.fromDims(&[_]shape_mod.SymbolicDim{
        .{ .concrete = 3 },
        .{ .concrete = 4 },
    }, testing.allocator, &pool);
    defer a_shape.deinit(testing.allocator);
    
    const b_shape = try ShapeTracker.fromDims(&[_]shape_mod.SymbolicDim{
        .{ .concrete = 4 },
        .{ .concrete = 5 },
    }, testing.allocator, &pool);
    defer b_shape.deinit(testing.allocator);
    
    const metadata = try createMatMulMetadata(a_shape, b_shape, testing.allocator);
    defer {
        if (metadata.output_shape) |shape| {
            shape.deinit(testing.allocator);
        }
        if (metadata.input_shapes) |shapes| {
            for (shapes) |shape| {
                shape.deinit(testing.allocator);
            }
            testing.allocator.free(shapes);
        }
        testing.allocator.destroy(metadata);
    }
    
    // Output should be [3, 5]
    try testing.expect(metadata.output_shape != null);
    try testing.expectEqual(@as(usize, 2), metadata.output_shape.?.dims.len);
    try testing.expectEqual(@as(i64, 3), metadata.output_shape.?.dims[0].concrete);
    try testing.expectEqual(@as(i64, 5), metadata.output_shape.?.dims[1].concrete);
}

test "arange metadata creation" {
    const testing = std.testing;
    
    var pool = try shape_mod.SymbolicPool.init(testing.allocator);
    defer pool.deinit();
    
    const metadata = try createARangeMetadata(10, testing.allocator, &pool);
    defer {
        if (metadata.output_shape) |shape| {
            shape.deinit(testing.allocator);
        }
        testing.allocator.destroy(metadata);
    }
    
    // Output should be [10]
    try testing.expect(metadata.output_shape != null);
    try testing.expectEqual(@as(usize, 1), metadata.output_shape.?.dims.len);
    try testing.expectEqual(@as(i64, 10), metadata.output_shape.?.dims[0].concrete);
    try testing.expect(metadata.is_arange);
}

test "unified pattern recognition execution" {
    const testing = std.testing;
    
    var graph = try Graph.init(testing.allocator);
    defer graph.deinit();
    
    // Create a graph with patterns to recognize
    const a = try graph.addPlaceholder(.f32);
    const zero = try graph.addConstant(0.0);
    const add_zero = try graph.addNode(.add, &.{a, zero}, .f32); // Should match add_zero pattern
    
    var ctx = PassContext.init(&graph, testing.allocator);
    
    // Run unified pattern recognition
    const GenericPatternRecognition = PatternRecognition(struct {});
    try GenericPatternRecognition.run(&ctx);
    
    // Graph structure should be analyzable (basic smoke test)
    try testing.expect(graph.nodes.items.len >= 3);
    _ = add_zero; // Suppress unused warning
}

test "backend-specific pattern recognition" {
    const testing = std.testing;
    
    // Test with empty backend (no additional patterns)
    const EmptyBackend = struct {};
    const EmptyPatternPass = PatternRecognition(EmptyBackend);
    
    try testing.expectEqualStrings("patterns", EmptyPatternPass.name);
    try testing.expectEqual(@as(u32, 85), EmptyPatternPass.priority);
    
    // Test with backend that has patterns
    const TestBackend = struct {
        pub const patterns = [_]patterns_mod.Pattern{
            patterns_mod.Pattern{
                .name = "test_backend_pattern",
                .match = .{ .op = .{ .op = .mul, .inputs = &.{
                    .{ .@"var" = "x" },
                    .{ .@"var" = "y" },
                }}},
                .replace = .{ .custom = .{ .name = "backend_mul", .inputs = &.{ "x", "y" } } },
                .priority = 70,
            },
        };
    };
    const BackendPatternPass = PatternRecognition(TestBackend);
    
    try testing.expectEqualStrings("patterns", BackendPatternPass.name);
    try testing.expectEqual(@as(u32, 85), BackendPatternPass.priority);
}

test "pattern filter functionality" {
    const testing = std.testing;
    
    // Test pattern filter accepts all patterns
    const test_pattern = patterns_mod.Pattern{
        .name = "test_filter",
        .match = .{ .@"var" = "x" },
        .replace = .{ .@"var" = "x" },
    };
    
    try testing.expect(filterPatterns(&test_pattern));
}

test "template-based pattern recognition" {
    const testing = std.testing;
    
    var graph = try Graph.init(testing.allocator);
    defer graph.deinit();
    
    const a = try graph.addPlaceholder(.f32);
    const b = try graph.addPlaceholder(.f32);
    _ = try graph.addNode(.add, &.{a, b}, .f32);
    
    var ctx = PassContext.init(&graph, testing.allocator);
    
    // All new template-based entry points should work
    const TestBackend = struct {};
    const TestPatternRecognition = PatternRecognition(TestBackend);
    try TestPatternRecognition.run(&ctx);
    
    // Should not crash and maintain graph validity
    try testing.expect(graph.nodes.items.len >= 3);
}

test "metadata creation functions" {
    const testing = std.testing;
    
    var pool = try shape_mod.SymbolicPool.init(testing.allocator);
    defer pool.deinit();
    
    // Test gather metadata creation
    const data_shape = try ShapeTracker.fromDims(&[_]shape_mod.SymbolicDim{
        .{ .concrete = 100 },
        .{ .concrete = 64 },
    }, testing.allocator, &pool);
    defer data_shape.deinit(testing.allocator);
    
    const indices_shape = try ShapeTracker.fromDims(&[_]shape_mod.SymbolicDim{
        .{ .concrete = 10 },
    }, testing.allocator, &pool);
    defer indices_shape.deinit(testing.allocator);
    
    const gather_metadata = try createGatherMetadata(data_shape, indices_shape, testing.allocator);
    defer {
        if (gather_metadata.output_shape) |shape| {
            shape.deinit(testing.allocator);
        }
        if (gather_metadata.input_shapes) |shapes| {
            for (shapes) |shape| {
                shape.deinit(testing.allocator);
            }
            testing.allocator.free(shapes);
        }
        testing.allocator.destroy(gather_metadata);
    }
    
    // Output shape should match indices shape
    try testing.expect(gather_metadata.output_shape != null);
    try testing.expectEqual(@as(usize, 1), gather_metadata.output_shape.?.dims.len);
    try testing.expectEqual(@as(i64, 10), gather_metadata.output_shape.?.dims[0].concrete);
    
    // Should have input shapes stored
    try testing.expect(gather_metadata.input_shapes != null);
    try testing.expectEqual(@as(usize, 2), gather_metadata.input_shapes.?.len);
}

test "pattern recognition error handling" {
    const testing = std.testing;
    
    // Test handleMatMulPattern error case
    var graph = try Graph.init(testing.allocator);
    defer graph.deinit();
    
    var match = patterns_mod.Match{ .root = 0 };
    defer match.deinit(testing.allocator);
    
    // Should return NotImplemented error
    try testing.expectError(
        error.NotImplemented,
        handleMatMulPattern(&graph, match, testing.allocator)
    );
}

test "pattern recognition integration" {
    const testing = std.testing;
    
    var graph = try Graph.init(testing.allocator);
    defer graph.deinit();
    
    // Create a more complex graph that could have multiple patterns
    const a = try graph.addPlaceholder(.f32);
    const b = try graph.addPlaceholder(.f32);
    const one = try graph.addConstant(1.0);
    const zero = try graph.addConstant(0.0);
    
    // x * 1 (should match identity pattern)
    const mul_identity = try graph.addNode(.mul, &.{a, one}, .f32);
    
    // y + 0 (should match add_zero pattern)  
    const add_zero = try graph.addNode(.add, &.{b, zero}, .f32);
    
    // Create sum_reduce(mul(x, y)) pattern for potential matmul recognition
    const mul_node = try graph.addNode(.mul, &.{a, b}, .f32);
    const sum_node = try graph.addNode(.sum_reduce, &.{mul_node}, .f32);
    
    var ctx = PassContext.init(&graph, testing.allocator);
    
    // Run pattern recognition
    const GenericPatternRecognition = PatternRecognition(struct {});
    try GenericPatternRecognition.run(&ctx);
    
    // Graph should still be valid and have all nodes
    try testing.expect(graph.nodes.items.len >= 7);
    
    // Suppress unused variable warnings
    _ = mul_identity;
    _ = add_zero;
    _ = sum_node;
}