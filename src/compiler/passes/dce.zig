/// Dead Code Elimination (DCE) Pass
/// 
/// Removes nodes that have no consumers and aren't graph outputs.
/// This pass helps clean up the graph after other optimizations.
/// 
/// Now uses the unified pass template system for consistency.

const std = @import("std");
const Allocator = std.mem.Allocator;

// Import types and compiler infrastructure
const types = @import("types");
const NodeId = types.NodeId;

const Graph = @import("graph").Graph;
const Node = @import("graph").Node;

// Import from parent modules
const PassContext = @import("../unified_pipeline.zig").PassContext;
const pass_templates = @import("../pass_templates.zig");
const transforms = @import("../transforms.zig");
const GraphRewriter = transforms.GraphRewriter;

// ============================================================================
// Dead Code Elimination Pass using Template System
// ============================================================================

/// Dead Code Elimination pass using the template system
pub const DeadCodeEliminationPass = pass_templates.createAnalysisPass(.{
    .name = "dead_code_elimination",
    .priority = 75,
    .analyze = analyzeLiveness,
    .modifies_graph = true,
    .post_conditions = pass_templates.ensureNoCycles,
});

/// Analyze liveness and remove dead nodes
fn analyzeLiveness(ctx: *PassContext) !void {
    const graph = ctx.graph;
    var to_remove = std.ArrayList(NodeId).init(ctx.allocator);
    defer to_remove.deinit();
    
    // Get owned copy of topological order since we might modify the graph
    const topo_order = try graph.topologicalSortOwned(ctx.allocator);
    defer ctx.allocator.free(topo_order);
    
    // Mark all nodes for removal initially
    var is_live = std.AutoHashMap(types.NodeId, bool).init(ctx.allocator);
    defer is_live.deinit();
    
    // Work backward from output nodes to find live nodes
    var work_list = std.ArrayList(types.NodeId).init(ctx.allocator);
    defer work_list.deinit();
    
    // Start with output nodes
    if (@import("build_options").enable_debug_logs) {
        std.debug.print("DCE: Starting with output nodes:\n", .{});
        for (graph.output_nodes.items, 0..) |node_id, i| {
            std.debug.print("  output_nodes[{}] = {}\n", .{i, node_id});
        }
    }
    for (graph.output_nodes.items) |node_id| {
        try is_live.put(node_id, true);
        try work_list.append(node_id);
    }
    
    // Also include nodes with side effects or that cannot be removed
    for (topo_order) |node_id| {
        const node = graph.getNode(node_id) orelse continue;
        
        // Keep data sources (parameters, placeholders, constants)
        if (node.spec == .data) {
            try is_live.put(node_id, true);
            try work_list.append(node_id);
        }
    }
    
    // Process worklist to find all transitively live nodes
    while (work_list.items.len > 0) {
        const node_id = work_list.orderedRemove(work_list.items.len - 1);
        const node = graph.getNode(node_id) orelse continue;
        
        // Mark all inputs as live
        for (node.inputs) |input_id| {
            const result = try is_live.getOrPut(input_id);
            if (!result.found_existing) {
                result.value_ptr.* = true;
                try work_list.append(input_id);
            }
        }
    }
    
    // Collect nodes to remove
    for (topo_order) |node_id| {
        if (!is_live.contains(node_id)) {
            if (@import("build_options").enable_debug_logs) {
                std.debug.print("DCE: Marking node {} for removal\n", .{node_id});
            }
            try to_remove.append(node_id);
        }
    }
    
    // Use GraphRewriter for safe atomic removal
    var rewriter = GraphRewriter.init(graph, ctx.allocator);
    defer rewriter.deinit();
    
    for (to_remove.items) |node_id| {
        try rewriter.markForDeletion(node_id);
    }
    
    // Apply all deletions atomically
    try rewriter.commitChanges();
}



// ===== Unit Tests =====

test "Dead code elimination removes unused nodes" {
    const testing = std.testing;
    
    var graph = try Graph.init(testing.allocator);
    defer graph.deinit();
    
    // Create a chain: placeholder -> add -> unused_mul
    const input = try graph.addPlaceholder(.f32);
    const add_node = try graph.addNode(.add, &.{input, input}, .f32);
    const unused_mul = try graph.addNode(.mul, &.{add_node, add_node}, .f32);
    
    // Mark add_node as an output (to prevent its removal)
    try graph.output_nodes.append(add_node);
    
    var ctx = PassContext.init(&graph, testing.allocator);
    try DeadCodeEliminationPass.run(&ctx);
    
    // Check that unused node is removed
    try testing.expect(!graph.hasNode(unused_mul));
    
    // Check that used nodes remain
    try testing.expect(graph.hasNode(input));
    try testing.expect(graph.hasNode(add_node));
}

test "DCE preserves data sources" {
    const testing = std.testing;
    
    var graph = try Graph.init(testing.allocator);
    defer graph.deinit();
    
    // Create data sources with no consumers
    const param = try graph.addParameter("weight", .f32);
    const placeholder = try graph.addPlaceholder(.f32);
    const constant = try graph.addConstant(1.0);
    
    // Create an output node that doesn't use these sources
    const other_input = try graph.addPlaceholder(.f32);
    const output = try graph.addNode(.add, &.{other_input, other_input}, .f32);
    try graph.output_nodes.append(output);
    
    var ctx = PassContext.init(&graph, testing.allocator);
    try DeadCodeEliminationPass.run(&ctx);
    
    // All data sources should be preserved
    try testing.expect(graph.hasNode(param));
    try testing.expect(graph.hasNode(placeholder));
    try testing.expect(graph.hasNode(constant));
    try testing.expect(graph.hasNode(other_input));
    try testing.expect(graph.hasNode(output));
}

test "DCE handles complex dependency chains" {
    const testing = std.testing;
    
    var graph = try Graph.init(testing.allocator);
    defer graph.deinit();
    
    // Create complex chain with multiple branches
    const a = try graph.addPlaceholder(.f32);
    const b = try graph.addPlaceholder(.f32);
    
    // Used branch: a -> add1 -> mul1 -> output
    const add1 = try graph.addNode(.add, &.{a, b}, .f32);
    const mul1 = try graph.addNode(.mul, &.{add1, add1}, .f32);
    
    // Unused branch: b -> add2 -> mul2
    const add2 = try graph.addNode(.add, &.{b, b}, .f32);
    const mul2 = try graph.addNode(.mul, &.{add2, add2}, .f32);
    
    // Partially used: mul1, mul2 -> add3 (but add3 isn't output)
    const add3 = try graph.addNode(.add, &.{mul1, mul2}, .f32);
    
    // Mark mul1 as output
    try graph.output_nodes.append(mul1);
    
    var ctx = PassContext.init(&graph, testing.allocator);
    try DeadCodeEliminationPass.run(&ctx);
    
    // Check what remains
    try testing.expect(graph.hasNode(a)); // Used input
    try testing.expect(graph.hasNode(b)); // Data source
    try testing.expect(graph.hasNode(add1)); // Used in output chain
    try testing.expect(graph.hasNode(mul1)); // Output
    
    // These should be removed as they're not needed for outputs
    try testing.expect(!graph.hasNode(add2));
    try testing.expect(!graph.hasNode(mul2));
    try testing.expect(!graph.hasNode(add3));
}

test "DCE pass template creation" {
    const testing = std.testing;
    
    // Verify pass properties
    try testing.expectEqualStrings("dead_code_elimination", DeadCodeEliminationPass.name);
    try testing.expectEqual(@as(u32, 75), DeadCodeEliminationPass.priority);
    try testing.expectEqual(pass_templates.PassType.analysis, DeadCodeEliminationPass.pass_type);
}

test "DCE template pass execution" {
    const testing = std.testing;
    
    var graph = try Graph.init(testing.allocator);
    defer graph.deinit();
    
    // Create nodes to test DCE functionality
    const input = try graph.addPlaceholder(.f32);
    const used_node = try graph.addNode(.add, &.{input, input}, .f32);
    const unused_node = try graph.addNode(.mul, &.{used_node, used_node}, .f32);
    
    // Mark used_node as output
    try graph.output_nodes.append(used_node);
    
    var ctx = PassContext.init(&graph, testing.allocator);
    
    // Run using the template pass directly
    try DeadCodeEliminationPass.run(&ctx);
    
    // Verify results
    try testing.expect(graph.hasNode(input));      // Input preserved
    try testing.expect(graph.hasNode(used_node));  // Output preserved
    try testing.expect(!graph.hasNode(unused_node)); // Unused removed
}