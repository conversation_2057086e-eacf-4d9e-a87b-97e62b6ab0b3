/// Constant Folding Pass
/// 
/// Evaluates compile-time constant expressions and replaces them with
/// their computed values. This includes both pure constant operations
/// and shape-aware optimizations like multiplication by 1 or addition of 0.
/// 
/// Now uses the unified pass template system for consistency.

const std = @import("std");
const Allocator = std.mem.Allocator;

// Import types and compiler infrastructure
const types = @import("types");
const NodeId = types.NodeId;
const ComputeOp = types.ComputeOp;

const Graph = @import("graph").Graph;
const Node = @import("graph").Node;
const NodeSpec = @import("graph").NodeSpec;

// Import from parent modules
const PassContext = @import("../unified_pipeline.zig").PassContext;
const pass_templates = @import("../pass_templates.zig");
const transforms = @import("../transforms.zig");
const GraphRewriter = transforms.GraphRewriter;

// Local utility functions for constant folding

/// Extract constant value from a constant node
fn getConstantValue(node: *const Node) ?f32 {
    if (node.spec != .data or node.spec.data != .constant) return null;
    if (node.metadata) |metadata| {
        return metadata.constant_value;
    }
    return null;
}

/// Evaluate a constant operation at compile time
fn evaluateConstantOperation(node: Node, graph: *const Graph) !?f32 {
    if (node.spec != .compute) return null;
    
    const op = node.spec.compute;
    
    // Get input values
    var input_values: [4]f32 = undefined; // Max 4 inputs for any operation
    for (node.inputs, 0..) |input_id, i| {
        if (i >= 4) return null; // Too many inputs
        const input_node = graph.getNode(input_id) orelse return null;
        input_values[i] = getConstantValue(input_node) orelse return null;
    }
    
    // Evaluate based on operation
    return switch (op) {
        .add => if (node.inputs.len == 2) input_values[0] + input_values[1] else null,
        .mul => if (node.inputs.len == 2) input_values[0] * input_values[1] else null,
        .mod => if (node.inputs.len == 2) @mod(input_values[0], input_values[1]) else null,
        .less_than => if (node.inputs.len == 2) (if (input_values[0] < input_values[1]) 1.0 else 0.0) else null,
        .recip => if (node.inputs.len == 1) (1.0 / input_values[0]) else null,
        .sqrt => if (node.inputs.len == 1) @sqrt(input_values[0]) else null,
        .sin => if (node.inputs.len == 1) @sin(input_values[0]) else null,
        .exp2 => if (node.inputs.len == 1) std.math.pow(f32, 2.0, input_values[0]) else null,
        .log2 => if (node.inputs.len == 1) std.math.log2(input_values[0]) else null,
        // Reductions and other operations cannot be folded without shape info
        .sum_reduce, .max_reduce, .contiguous, .custom => null,
    };
}

// ============================================================================
// Constant Folding Pass using Template System
// ============================================================================

/// Constant Folding pass that processes all nodes in a single batch
pub const ConstantFoldingPass = struct {
    pub const name = "constant_folding";
    pub const pass_type = pass_templates.PassType.custom;
    pub const priority = 80;
    pub const supported_backends = [_][]const u8{};
    
    pub fn run(ctx: *PassContext) !void {
        const graph = ctx.graph;
        
        if (@import("build_options").enable_debug_logs) {
            std.debug.print("\nConstant Folding: Starting\n", .{});
            std.debug.print("  output_nodes = {any}\n", .{graph.output_nodes.items});
            std.debug.print("  graph state: {} nodes, next_node_id={}\n", .{graph.nodes.items.len, graph.next_node_id});
            for (0..@min(graph.next_node_id, 5)) |i| {
                const nid: NodeId = @intCast(i);
                std.debug.print("  Node {} is_valid={}\n", .{nid, graph.hasNode(nid)});
            }
        }
        
        // Process nodes multiple times until no more changes
        var changes_made = true;
        var iteration: u32 = 0;
        const max_iterations = 10;
        
        const enable_debug = @import("build_options").enable_debug_logs;
        
        while (changes_made and iteration < max_iterations) : (iteration += 1) {
            changes_made = false;
            
            // Get fresh topological order for each iteration
            const topo_order = try graph.topologicalSortOwned(ctx.allocator);
            defer ctx.allocator.free(topo_order);
            
            if (enable_debug) {
                std.log.debug("Constant folding iteration {}: processing {} nodes", .{iteration + 1, topo_order.len});
            }
            
            for (topo_order) |node_id| {
                const node = graph.getNode(node_id) orelse continue;
                if (!node.is_valid) continue;
                if (node.spec != .compute) continue;
                
                // Create a new rewriter for each node to avoid complex substitution chains
                var rewriter = GraphRewriter.init(graph, ctx.allocator);
                defer rewriter.deinit();
                
                // Try constant folding for this node
                const made_substitution = try tryFoldNode(ctx, &rewriter, node_id, node);
                
                if (made_substitution) {
                    if (enable_debug) {
                        std.log.debug("Constant folding: substituting node {}", .{node_id});
                    }
                    // Commit this single substitution immediately
                    try rewriter.commitChanges();
                    changes_made = true;
                    // Continue to next node rather than breaking, to process all possible folds
                }
            }
            
            if (enable_debug and !changes_made) {
                std.log.debug("Constant folding: no changes in iteration {}", .{iteration + 1});
            }
        }
        
        if (enable_debug and iteration >= max_iterations) {
            std.log.debug("Constant folding: reached maximum iterations", .{});
        }
    }
};

/// Try to fold a single node, adding substitutions to the rewriter
fn tryFoldNode(ctx: *PassContext, rewriter: *GraphRewriter, node_id: NodeId, node: *const Node) !bool {
    const graph = ctx.graph;
    
    // Check if all inputs are constants
    var all_inputs_constant = true;
    for (node.inputs) |input_id| {
        const input_node = graph.getNode(input_id) orelse continue;
        if (input_node.spec != .data or input_node.spec.data != .constant) {
            all_inputs_constant = false;
            break;
        }
    }
    
    var substitution_made = false;
    
    if (all_inputs_constant) {
        // Evaluate the constant expression
        if (try evaluateConstantOperation(node.*, graph)) |result_value| {
            // Create constant node with the result
            const const_node = try graph.addConstant(result_value);
            
            // Replace the computation with the constant
            try rewriter.replaceNode(node_id, const_node);
            substitution_made = true;
        }
    }
    
    // Skip shape-aware constant folding for now to avoid cycles
    // TODO: Re-enable once we have better cycle detection
    // if (!substitution_made) {
    //     substitution_made = try shapeAwareConstantFolding(ctx, node_id, node, rewriter);
    // }
    
    return substitution_made;
}

/// Shape-aware constant folding with actual substitutions
fn shapeAwareConstantFolding(ctx: *PassContext, node_id: NodeId, node: *const Node, rewriter: *GraphRewriter) !bool {
    const graph = ctx.graph;
    
    // Only process compute nodes
    if (node.spec != .compute) return false;
    
    // Fold operations with scalar constants
    switch (node.spec.compute) {
        .mul => {
            // Check for multiplication by scalar 1
            for (node.inputs, 0..) |input_id, i| {
                if (isOne(graph, input_id) and isScalar(graph, input_id)) {
                    // Return the other operand (identity)
                    const other = node.inputs[1 - i];
                    // Check if this would create a cycle
                    if (try graph.wouldCreateCycle(node_id, other)) {
                        const enable_debug = @import("build_options").enable_debug_logs;
                        if (enable_debug) {
                            std.log.debug("Constant folding: skipping x*1 optimization for node {} -> {} (would create cycle)", .{node_id, other});
                        }
                        continue; // Skip this optimization
                    }
                    const enable_debug = @import("build_options").enable_debug_logs;
                    if (enable_debug) {
                        std.log.debug("Constant folding: replacing node {} (x*1) with node {}", .{node_id, other});
                    }
                    try rewriter.replaceNode(node_id, other);
                    return true;
                }
                // Check for multiplication by scalar 0
                if (isZero(graph, input_id) and isScalar(graph, input_id)) {
                    // Result is zero - try to reuse the existing zero if it's a constant
                    const zero_node = if (graph.getNode(input_id)) |n| blk: {
                        if (n.spec == .data and n.spec.data == .constant) {
                            break :blk input_id; // Reuse the existing zero constant
                        }
                        break :blk try graph.addConstant(0.0);
                    } else try graph.addConstant(0.0);
                    
                    try rewriter.replaceNode(node_id, zero_node);
                    return true;
                }
            }
        },
        .add => {
            // Check for addition of scalar 0
            for (node.inputs, 0..) |input_id, i| {
                if (isZero(graph, input_id) and isScalar(graph, input_id)) {
                    // Return the other operand (identity)
                    const other = node.inputs[1 - i];
                    // Check if this would create a cycle
                    if (try graph.wouldCreateCycle(node_id, other)) {
                        continue; // Skip this optimization
                    }
                    try rewriter.replaceNode(node_id, other);
                    return true;
                }
            }
        },
        else => {},
    }
    
    return false;
}

// ===== Local Utility Functions =====

fn isZero(graph: *const Graph, node_id: NodeId) bool {
    const value = graph.getConstantValue(node_id) orelse return false;
    return value == 0.0;
}

fn isOne(graph: *const Graph, node_id: NodeId) bool {
    const value = graph.getConstantValue(node_id) orelse return false;
    return value == 1.0;
}

fn isScalar(graph: *const Graph, node_id: NodeId) bool {
    const node = graph.getNode(node_id) orelse return false;
    if (node.metadata) |metadata| {
        if (metadata.output_shape) |shape| {
            // A scalar has 0 dimensions or 1 dimension with size 1
            // A scalar has 0 dimensions
            return shape.dims.len == 0;
        }
    }
    return false;
}

// ===== Unit Tests =====

test "constant folding evaluates simple arithmetic" {
    const testing = std.testing;
    
    var graph = try Graph.init(testing.allocator);
    defer graph.deinit();
    
    // Create constant nodes
    const a = try graph.addConstant(2.0);
    const b = try graph.addConstant(3.0);
    
    // Create operations
    const add = try graph.addNode(.add, &.{a, b}, .f32);
    const mul = try graph.addNode(.mul, &.{a, b}, .f32);
    
    // Get nodes
    const add_node = graph.getNode(add).?;
    const mul_node = graph.getNode(mul).?;
    
    // Evaluate operations
    const add_result = try evaluateConstantOperation(add_node.*, &graph);
    const mul_result = try evaluateConstantOperation(mul_node.*, &graph);
    
    try testing.expect(add_result != null);
    try testing.expect(mul_result != null);
    try testing.expectApproxEqAbs(@as(f32, 5.0), add_result.?, 0.001);
    try testing.expectApproxEqAbs(@as(f32, 6.0), mul_result.?, 0.001);
}

test "constant folding evaluates unary operations" {
    const testing = std.testing;
    
    var graph = try Graph.init(testing.allocator);
    defer graph.deinit();
    
    // Create constant
    const a = try graph.addConstant(4.0);
    
    // Create operations
    const sqrt_op = try graph.addNode(.sqrt, &.{a}, .f32);
    const recip = try graph.addNode(.recip, &.{a}, .f32);
    
    // Get nodes
    const sqrt_node = graph.getNode(sqrt_op).?;
    const recip_node = graph.getNode(recip).?;
    
    // Evaluate operations
    const sqrt_result = try evaluateConstantOperation(sqrt_node.*, &graph);
    const recip_result = try evaluateConstantOperation(recip_node.*, &graph);
    
    try testing.expect(sqrt_result != null);
    try testing.expect(recip_result != null);
    try testing.expectApproxEqAbs(@as(f32, 2.0), sqrt_result.?, 0.001);
    try testing.expectApproxEqAbs(@as(f32, 0.25), recip_result.?, 0.001);
}

test "constant folding handles comparison operations" {
    const testing = std.testing;
    
    var graph = try Graph.init(testing.allocator);
    defer graph.deinit();
    
    // Create constants
    const a = try graph.addConstant(2.0);
    const b = try graph.addConstant(3.0);
    
    // Create comparison
    const lt = try graph.addNode(.less_than, &.{a, b}, .f32);
    
    // Get node
    const lt_node = graph.getNode(lt).?;
    
    // Evaluate operation
    const lt_result = try evaluateConstantOperation(lt_node.*, &graph);
    
    try testing.expect(lt_result != null);
    try testing.expectApproxEqAbs(@as(f32, 1.0), lt_result.?, 0.001); // true = 1.0
}

test "constant folding skips non-constant inputs" {
    const testing = std.testing;
    
    var graph = try Graph.init(testing.allocator);
    defer graph.deinit();
    
    // Create one constant and one placeholder
    const a = try graph.addConstant(2.0);
    const b = try graph.addPlaceholder(.f32);
    
    // Create operation
    const add = try graph.addNode(.add, &.{a, b}, .f32);
    
    // Get node
    const add_node = graph.getNode(add).?;
    
    // Should not be able to evaluate
    const result = try evaluateConstantOperation(add_node.*, &graph);
    try testing.expect(result == null);
}

test "constant folding pass properties" {
    const testing = std.testing;
    
    // Verify pass properties
    try testing.expectEqualStrings("constant_folding", ConstantFoldingPass.name);
    try testing.expectEqual(@as(u32, 80), ConstantFoldingPass.priority);
    try testing.expectEqual(pass_templates.PassType.custom, ConstantFoldingPass.pass_type);
}

test "constant folding template pass execution" {
    const testing = std.testing;
    
    var graph = try Graph.init(testing.allocator);
    defer graph.deinit();
    
    // Create constants and operations
    const a = try graph.addConstant(2.0);
    const b = try graph.addConstant(3.0);
    const add_node = try graph.addNode(.add, &.{a, b}, .f32);
    
    // Mark as output to ensure it's not removed
    try graph.output_nodes.append(add_node);
    
    var ctx = PassContext.init(&graph, testing.allocator);
    
    // Run using the template pass directly
    try ConstantFoldingPass.run(&ctx);
    
    // The add_node should be replaced with a constant
    // (Note: this is a simplified test - in reality we'd need to track substitutions)
    try testing.expect(graph.nodes.items.len >= 3); // Original constants + result
}

test "shape-aware constant folding identities" {
    const testing = std.testing;
    
    var graph = try Graph.init(testing.allocator);
    defer graph.deinit();
    
    // Create x + 0 pattern
    const x = try graph.addPlaceholder(.f32);
    const zero = try graph.addConstant(0.0);
    const add_zero = try graph.addNode(.add, &.{x, zero}, .f32);
    
    // Create x * 1 pattern
    const one = try graph.addConstant(1.0);
    const mul_one = try graph.addNode(.mul, &.{x, one}, .f32);
    
    // Mark as outputs
    try graph.output_nodes.append(add_zero);
    try graph.output_nodes.append(mul_one);
    
    var ctx = PassContext.init(&graph, testing.allocator);
    
    // Run constant folding
    try ConstantFoldingPass.run(&ctx);
    
    // Basic smoke test - ensure graph is still valid
    try testing.expect(graph.nodes.items.len >= 4);
}