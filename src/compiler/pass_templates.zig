/// Pass Creation Templates - Compile-time templates for creating consistent optimization passes
/// 
/// This module provides the template system that ensures all compiler passes follow
/// the same patterns and use the unified infrastructure from pass_utils.zig

const std = @import("std");
const Allocator = std.mem.Allocator;

// Import core types
const types = @import("types");
const NodeId = types.NodeId;
const ComputeOp = types.ComputeOp;

const Graph = @import("graph").Graph;
const Node = @import("graph").Node;

const pipeline_mod = @import("unified_pipeline.zig");
const PassContext = pipeline_mod.PassContext;
const patterns = @import("patterns.zig");

// ============================================================================
// Graph Traversal Utilities
// ============================================================================

/// Options for graph traversal
pub const TraversalOptions = struct {
    handle_substitutions: bool = true,
    skip_invalid: bool = true,
    filter_op: ?ComputeOp = null,
    reverse: bool = false,
    owned: bool = true,
};

/// Unified graph traversal function
fn traverse(
    graph: *Graph,
    allocator: Allocator,
    options: TraversalOptions,
    context: anytype,
    comptime visitor: fn(@TypeOf(context), NodeId, *const Node) anyerror!void
) !void {
    // Get topological order
    const topo_order = if (options.owned) 
        try graph.topologicalSortOwned(allocator)
    else 
        try graph.topologicalSort();
    defer if (options.owned) allocator.free(topo_order);
    
    // Traverse in requested order
    const iter_order = if (options.reverse) blk: {
        var reversed = try allocator.alloc(NodeId, topo_order.len);
        for (topo_order, 0..) |node_id, i| {
            reversed[topo_order.len - 1 - i] = node_id;
        }
        break :blk reversed;
    } else topo_order;
    defer if (options.reverse and options.owned) allocator.free(iter_order);
    
    // Visit each node
    for (iter_order) |node_id| {
        // Handle substitutions if requested
        const current_id = if (options.handle_substitutions)
            graph.resolveCurrentNodeId(node_id)
        else 
            node_id;
            
        // Get the node
        const node = graph.getNode(current_id) orelse {
            if (!options.skip_invalid) return error.InvalidNode;
            continue;
        };
        
        // Apply operation filter if specified
        if (options.filter_op) |op| {
            if (node.spec != .compute or node.spec.compute != op) continue;
        }
        
        // Visit the node
        try visitor(context, current_id, node);
    }
}

// ============================================================================
// Base Pass Template
// ============================================================================

/// Configuration for the base pass template
pub const PassConfig = struct {
    name: []const u8,
    pass_type: PassType,
    priority: u32 = 50,
    supported_backends: []const []const u8 = &.{}, // Empty = all backends
    pre_conditions: ?*const fn(*PassContext) anyerror!void = null,
    post_conditions: ?*const fn(*PassContext) anyerror!void = null,
};

/// Pass classification
pub const PassType = enum {
    traversal,    // Graph traversal passes (shape inference, DCE)
    pattern,      // Pattern-based transformation passes
    analysis,     // Analysis passes (memory planning)
    hybrid,       // Combination of traversal and patterns
};

/// Common pass logic as a function instead of a type
fn runPassWithChecks(
    comptime config: PassConfig,
    ctx: *PassContext,
    comptime runImplFn: fn(*PassContext) anyerror!void
) !void {
    const enable_debug = @import("build_options").enable_debug_logs;
    
    if (enable_debug) {
        std.debug.print("\n=== Pass: {s} ===\n", .{config.name});
        std.debug.print("  Type: {s}\n", .{@tagName(config.pass_type)});
        std.debug.print("  Graph nodes: {}\n", .{ctx.graph.nodes.items.len});
    }
    
    // Run pre-conditions if specified
    if (config.pre_conditions) |check| {
        try check(ctx);
    }
    
    // Validate graph integrity before pass
    if (enable_debug) {
        _ = ctx.graph.validateIntegrity() catch |err| {
            std.debug.print("  WARNING: Graph integrity check failed before pass: {}\n", .{err});
        };
    }
    
    // Run the actual pass logic
    try runImplFn(ctx);
    
    // Validate graph integrity after pass
    if (enable_debug) {
        _ = ctx.graph.validateIntegrity() catch |err| {
            std.debug.print("  WARNING: Graph integrity check failed after pass: {}\n", .{err});
        };
    }
    
    // Run post-conditions if specified
    if (config.post_conditions) |check| {
        try check(ctx);
    }
    
    if (enable_debug) {
        std.debug.print("=== End Pass: {s} ===\n\n", .{config.name});
    }
}

// ============================================================================
// Traversal Pass Template
// ============================================================================

/// Configuration for traversal-based passes
pub const TraversalConfig = struct {
    name: []const u8,
    priority: u32 = 50,
    supported_backends: []const []const u8 = &.{},
    
    // Traversal options
    reverse_order: bool = false,
    skip_invalid: bool = true,
    handle_substitutions: bool = true,
    filter_op: ?ComputeOp = null,
    
    // The visitor function that processes each node
    visitor: fn(*PassContext, NodeId, *const Node) anyerror!void,
    
    // Optional conditions
    pre_conditions: ?*const fn(*PassContext) anyerror!void = null,
    post_conditions: ?*const fn(*PassContext) anyerror!void = null,
};

/// Create a traversal-based pass using the unified traversal infrastructure
pub fn createTraversalPass(comptime config: TraversalConfig) type {
    return struct {
        pub const name = config.name;
        pub const pass_type = PassType.traversal;
        pub const priority = config.priority;
        pub const supported_backends = config.supported_backends;
        
        pub fn run(ctx: *PassContext) !void {
            const pass_config = PassConfig{
                .name = config.name,
                .pass_type = .traversal,
                .priority = config.priority,
                .supported_backends = config.supported_backends,
                .pre_conditions = config.pre_conditions,
                .post_conditions = config.post_conditions,
            };
            
            try runPassWithChecks(pass_config, ctx, runImpl);
        }
        
        fn runImpl(ctx: *PassContext) !void {
            // Use the local traversal function
            const options = TraversalOptions{
                .reverse = config.reverse_order,
                .skip_invalid = config.skip_invalid,
                .handle_substitutions = config.handle_substitutions,
                .filter_op = config.filter_op,
                .owned = true,
            };
            
            try traverse(
                ctx.graph,
                ctx.allocator,
                options,
                ctx,
                config.visitor
            );
        }
    };
}

// ============================================================================
// Pattern Pass Template
// ============================================================================

/// Configuration for pattern-based passes
pub const PatternConfig = struct {
    name: []const u8,
    priority: u32 = 50,
    supported_backends: []const []const u8 = &.{},
    
    // Pattern specifications
    patterns: []const patterns.Pattern,
    
    // Pattern application strategy
    apply_once: bool = false, // If true, stop after first match
    max_iterations: u32 = 10, // Maximum iterations for fixpoint
    
    // Optional pattern filter
    pattern_filter: ?*const fn(*const patterns.Pattern) bool = null,
    
    // Optional conditions
    pre_conditions: ?*const fn(*PassContext) anyerror!void = null,
    post_conditions: ?*const fn(*PassContext) anyerror!void = null,
};

/// Create a pattern-based transformation pass
pub fn createPatternPass(comptime config: PatternConfig) type {
    return struct {
        pub const name = config.name;
        pub const pass_type = PassType.pattern;
        pub const priority = config.priority;
        pub const supported_backends = config.supported_backends;
        
        pub fn run(ctx: *PassContext) !void {
            const pass_config = PassConfig{
                .name = config.name,
                .pass_type = .pattern,
                .priority = config.priority,
                .supported_backends = config.supported_backends,
                .pre_conditions = config.pre_conditions,
                .post_conditions = config.post_conditions,
            };
            
            try runPassWithChecks(pass_config, ctx, runImpl);
        }
        
        fn runImpl(ctx: *PassContext) !void {
            _ = ctx; // Pattern passes not implemented yet
            const enable_debug = @import("build_options").enable_debug_logs;
            
            // Apply patterns until fixpoint or max iterations
            var iteration: u32 = 0;
            var changes_made = true;
            
            while (changes_made and iteration < config.max_iterations) : (iteration += 1) {
                changes_made = false;
                
                if (enable_debug) {
                    std.debug.print("  Pattern iteration {}\n", .{iteration + 1});
                }
                
                // For now, pattern passes need to be implemented manually
                // since patterns.zig expects comptime patterns
                // This is a placeholder that does nothing
                if (enable_debug) {
                    std.debug.print("    Pattern pass not implemented with new system yet\n", .{});
                }
                
                // Break to avoid infinite loop
                break;
            }
            
            if (enable_debug and iteration >= config.max_iterations) {
                std.debug.print("  Reached maximum iterations ({})\n", .{config.max_iterations});
            }
        }
    };
}

// ============================================================================
// Analysis Pass Template
// ============================================================================

/// Configuration for analysis passes
pub const AnalysisConfig = struct {
    name: []const u8,
    priority: u32 = 50,
    supported_backends: []const []const u8 = &.{},
    
    // The analysis function
    analyze: *const fn(*PassContext) anyerror!void,
    
    // Whether this analysis modifies the graph
    modifies_graph: bool = false,
    
    // Optional conditions
    pre_conditions: ?*const fn(*PassContext) anyerror!void = null,
    post_conditions: ?*const fn(*PassContext) anyerror!void = null,
};

/// Create an analysis pass (e.g., memory planning)
pub fn createAnalysisPass(comptime config: AnalysisConfig) type {
    return struct {
        pub const name = config.name;
        pub const pass_type = PassType.analysis;
        pub const priority = config.priority;
        pub const supported_backends = config.supported_backends;
        
        pub fn run(ctx: *PassContext) !void {
            const pass_config = PassConfig{
                .name = config.name,
                .pass_type = .analysis,
                .priority = config.priority,
                .supported_backends = config.supported_backends,
                .pre_conditions = config.pre_conditions,
                .post_conditions = config.post_conditions,
            };
            
            try runPassWithChecks(pass_config, ctx, runImpl);
        }
        
        fn runImpl(ctx: *PassContext) !void {
            // Run the analysis function
            try config.analyze(ctx);
            
            // If the analysis doesn't modify the graph, verify that
            if (!config.modifies_graph) {
                // In debug mode, we could track node count to ensure no modifications
                const enable_debug = @import("build_options").enable_debug_logs;
                if (enable_debug) {
                    std.debug.print("  Analysis pass completed (read-only)\n", .{});
                }
            }
        }
    };
}

// ============================================================================
// Hybrid Pass Template
// ============================================================================

/// Configuration for hybrid passes that combine patterns and traversal
pub const HybridConfig = struct {
    name: []const u8,
    priority: u32 = 50,
    supported_backends: []const []const u8 = &.{},
    
    // Phase 1: Pattern matching
    patterns: []const patterns.Pattern = &.{},
    pattern_iterations: u32 = 1,
    
    // Phase 2: Traversal
    visitor: ?*const fn(*PassContext, NodeId, *const Node) anyerror!void = null,
    traversal_options: TraversalOptions = .{},
    
    // Phase 3: Custom logic
    custom_phase: ?*const fn(*PassContext) anyerror!void = null,
    
    // Optional conditions
    pre_conditions: ?*const fn(*PassContext) anyerror!void = null,
    post_conditions: ?*const fn(*PassContext) anyerror!void = null,
};

/// Create a hybrid pass that combines multiple strategies
pub fn createHybridPass(comptime config: HybridConfig) type {
    return struct {
        pub const name = config.name;
        pub const pass_type = PassType.hybrid;
        pub const priority = config.priority;
        pub const supported_backends = config.supported_backends;
        
        pub fn run(ctx: *PassContext) !void {
            const pass_config = PassConfig{
                .name = config.name,
                .pass_type = .hybrid,
                .priority = config.priority,
                .supported_backends = config.supported_backends,
                .pre_conditions = config.pre_conditions,
                .post_conditions = config.post_conditions,
            };
            
            try runPassWithChecks(pass_config, ctx, runImpl);
        }
        
        fn runImpl(ctx: *PassContext) !void {
            const enable_debug = @import("build_options").enable_debug_logs;
            
            // Phase 1: Apply patterns if any
            if (config.patterns.len > 0) {
                if (enable_debug) {
                    std.debug.print("  Phase 1: Pattern matching\n", .{});
                }
                
                const pattern_pass = createPatternPass(.{
                    .name = config.name ++ "_patterns",
                    .patterns = config.patterns,
                    .max_iterations = config.pattern_iterations,
                });
                
                try pattern_pass.runImpl(ctx);
            }
            
            // Phase 2: Traversal if visitor provided
            if (config.visitor) |visitor| {
                if (enable_debug) {
                    std.debug.print("  Phase 2: Graph traversal\n", .{});
                }
                
                try traverse(
                    ctx.graph,
                    ctx.allocator,
                    config.traversal_options,
                    ctx,
                    visitor
                );
            }
            
            // Phase 3: Custom logic if provided
            if (config.custom_phase) |custom| {
                if (enable_debug) {
                    std.debug.print("  Phase 3: Custom logic\n", .{});
                }
                
                try custom(ctx);
            }
        }
    };
}

// ============================================================================
// Common Pre/Post Conditions
// ============================================================================

/// Common pre-condition: Ensure all nodes have shape metadata
pub fn requireShapeMetadata(ctx: *PassContext) !void {
    const topo_order = try ctx.graph.topologicalSort();
    
    for (topo_order) |node_id| {
        const node = ctx.graph.getNode(node_id) orelse continue;
        if (!node.is_valid) continue;
        
        if (node.metadata == null or node.metadata.?.output_shape == null) {
            std.log.err("Pre-condition failed: Node {} has no shape metadata", .{node_id});
            return error.MissingShapeMetadata;
        }
    }
}

/// Common post-condition: Verify no cycles introduced
pub fn ensureNoCycles(ctx: *PassContext) !void {
    // Topological sort will fail if there are cycles
    _ = try ctx.graph.topologicalSort();
}

/// Common post-condition: Verify all output nodes still exist
pub fn ensureOutputNodesExist(ctx: *PassContext) !void {
    for (ctx.graph.output_nodes.items) |output_id| {
        const resolved_id = ctx.graph.resolveCurrentNodeId(output_id);
        if (!ctx.graph.hasNode(resolved_id)) {
            std.log.err("Post-condition failed: Output node {} no longer exists", .{output_id});
            return error.OutputNodeRemoved;
        }
    }
}

// ============================================================================
// Helper Functions
// ============================================================================

/// Create a simple function pass wrapper
pub fn createFunctionPass(
    name: []const u8,
    pass_fn: *const fn(*PassContext) anyerror!void,
    priority: u32,
) type {
    return createAnalysisPass(.{
        .name = name,
        .priority = priority,
        .analyze = pass_fn,
        .modifies_graph = true,
    });
}

/// Create a node visitor function type
pub fn NodeVisitor(comptime Context: type) type {
    return fn(Context, NodeId, *const Node) anyerror!void;
}

// ============================================================================
// Unit Tests
// ============================================================================

test "Pass template creation" {
    const testing = std.testing;
    
    // Test traversal pass creation
    const TestTraversalPass = createTraversalPass(.{
        .name = "test_traversal",
        .priority = 100,
        .visitor = struct {
            fn visit(ctx: *PassContext, node_id: NodeId, node: *const Node) !void {
                _ = ctx;
                _ = node_id;
                _ = node;
            }
        }.visit,
    });
    
    try testing.expectEqualStrings("test_traversal", TestTraversalPass.name);
    try testing.expectEqual(PassType.traversal, TestTraversalPass.pass_type);
    try testing.expectEqual(@as(u32, 100), TestTraversalPass.priority);
}

test "Pattern pass configuration" {
    const testing = std.testing;
    
    // Test pattern pass with configuration
    const TestPatternPass = createPatternPass(.{
        .name = "test_patterns",
        .patterns = &.{},
        .apply_once = true,
        .max_iterations = 5,
    });
    
    try testing.expectEqualStrings("test_patterns", TestPatternPass.name);
    try testing.expectEqual(PassType.pattern, TestPatternPass.pass_type);
}

test "Analysis pass template" {
    const testing = std.testing;
    
    const TestAnalysisPass = createAnalysisPass(.{
        .name = "test_analysis",
        .analyze = struct {
            fn analyze(ctx: *PassContext) !void {
                _ = ctx;
            }
        }.analyze,
        .modifies_graph = false,
    });
    
    try testing.expectEqualStrings("test_analysis", TestAnalysisPass.name);
    try testing.expectEqual(PassType.analysis, TestAnalysisPass.pass_type);
}

test "Hybrid pass template" {
    const testing = std.testing;
    
    const TestHybridPass = createHybridPass(.{
        .name = "test_hybrid",
        .patterns = &.{},
        .pattern_iterations = 2,
        .visitor = struct {
            fn visit(ctx: *PassContext, node_id: NodeId, node: *const Node) !void {
                _ = ctx;
                _ = node_id;
                _ = node;
            }
        }.visit,
    });
    
    try testing.expectEqualStrings("test_hybrid", TestHybridPass.name);
    try testing.expectEqual(PassType.hybrid, TestHybridPass.pass_type);
}

test "Traversal pass execution" {
    const testing = std.testing;
    
    var graph = try @import("graph").Graph.init(testing.allocator);
    defer graph.deinit();
    
    // Create a simple graph
    const a = try graph.addPlaceholder(.f32);
    const b = try graph.addPlaceholder(.f32);
    _ = try graph.addNode(.add, &.{a, b}, .f32);
    
    var ctx = PassContext.init(&graph, testing.allocator);
    
    // Track visited nodes
    var visited_nodes = std.ArrayList(NodeId).init(testing.allocator);
    defer visited_nodes.deinit();
    
    const TestTraversalPass = createTraversalPass(.{
        .name = "test_visitor",
        .priority = 50,
        .visitor = struct {
            fn visit(pass_ctx: *PassContext, node_id: NodeId, node: *const Node) !void {
                _ = node;
                const list = @as(*std.ArrayList(NodeId), @ptrCast(@alignCast(pass_ctx.pass_data)));
                try list.append(node_id);
            }
        }.visit,
    });
    
    // Set up pass data
    ctx.pass_data = &visited_nodes;
    
    // Run the pass
    try TestTraversalPass.run(&ctx);
    
    // Should have visited all 3 nodes
    try testing.expectEqual(@as(usize, 3), visited_nodes.items.len);
}

test "Analysis pass execution" {
    const testing = std.testing;
    
    var graph = try @import("graph").Graph.init(testing.allocator);
    defer graph.deinit();
    
    // Create a graph with a constant
    _ = try graph.addConstant(42.0);
    
    var ctx = PassContext.init(&graph, testing.allocator);
    
    var analysis_result: f32 = 0.0;
    
    const TestAnalysisPass = createAnalysisPass(.{
        .name = "test_analysis",
        .analyze = struct {
            fn analyze(pass_ctx: *PassContext) !void {
                const result_ptr = @as(*f32, @ptrCast(@alignCast(pass_ctx.pass_data)));
                // Sum all constant values in the graph
                for (pass_ctx.graph.nodes.items) |node| {
                    if (node.is_valid and node.spec == .data and node.spec.data == .constant) {
                        if (pass_ctx.graph.getConstantValue(node.id)) |value| {
                            result_ptr.* += value;
                        }
                    }
                }
            }
        }.analyze,
        .modifies_graph = false,
    });
    
    ctx.pass_data = &analysis_result;
    
    // Run the analysis pass
    try TestAnalysisPass.run(&ctx);
    
    // Should have found the constant value
    try testing.expectApproxEqAbs(@as(f32, 42.0), analysis_result, 0.001);
}

test "Pass pre/post conditions" {
    const testing = std.testing;
    
    var graph = try @import("graph").Graph.init(testing.allocator);
    defer graph.deinit();
    
    var ctx = PassContext.init(&graph, testing.allocator);
    
    const TestPass = createAnalysisPass(.{
        .name = "test_conditions",
        .analyze = struct {
            fn analyze(pass_ctx: *PassContext) !void {
                _ = pass_ctx;
                // Do nothing
            }
        }.analyze,
        .pre_conditions = struct {
            fn pre(pass_ctx: *PassContext) !void {
                const state = @as(*bool, @ptrCast(@alignCast(pass_ctx.pass_data)));
                state.* = true;
            }
        }.pre,
        .post_conditions = struct {
            fn post(pass_ctx: *PassContext) !void {
                const states = @as(*[2]bool, @ptrCast(@alignCast(pass_ctx.pass_data)));
                states[1] = true;
            }
        }.post,
    });
    
    var condition_states = [2]bool{ false, false };
    ctx.pass_data = &condition_states;
    
    // Run the pass
    try TestPass.run(&ctx);
    
    // Both conditions should have been called
    try testing.expect(condition_states[0]); // pre-condition
    try testing.expect(condition_states[1]); // post-condition
}

test "Function pass wrapper" {
    const testing = std.testing;
    
    var graph = try @import("graph").Graph.init(testing.allocator);
    defer graph.deinit();
    
    var ctx = PassContext.init(&graph, testing.allocator);
    
    var function_called = false;
    
    const TestFunctionPass = createFunctionPass(
        "test_function",
        struct {
            fn testFn(pass_ctx: *PassContext) !void {
                const called_ptr = @as(*bool, @ptrCast(@alignCast(pass_ctx.pass_data)));
                called_ptr.* = true;
            }
        }.testFn,
        75
    );
    
    ctx.pass_data = &function_called;
    
    // Run the function pass
    try TestFunctionPass.run(&ctx);
    
    // Function should have been called
    try testing.expect(function_called);
    try testing.expectEqualStrings("test_function", TestFunctionPass.name);
    try testing.expectEqual(@as(u32, 75), TestFunctionPass.priority);
}

test "Common pre/post conditions" {
    const testing = std.testing;
    
    var graph = try @import("graph").Graph.init(testing.allocator);
    defer graph.deinit();
    
    // Create a graph with shape metadata
    const a = try graph.addPlaceholder(.f32);
    const node = graph.getNodeMut(a).?;
    
    // Add minimal metadata
    node.metadata = try testing.allocator.create(@import("graph").NodeMetadata);
    node.metadata.?.* = .{};
    
    var ctx = PassContext.init(&graph, testing.allocator);
    
    // ensureNoCycles should pass for acyclic graph
    try ensureNoCycles(&ctx);
    
    // ensureOutputNodesExist should pass if output nodes exist
    try graph.output_nodes.append(a);
    try ensureOutputNodesExist(&ctx);
    
    // Clean up
    if (node.metadata) |metadata| {
        testing.allocator.destroy(metadata);
    }
}