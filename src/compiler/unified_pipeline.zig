/// Unified Compilation Pipeline - Template-based with Comptime Backend Selection
///
/// This replaces the old pipeline.zig with a modern approach using:
/// - Pass templates instead of function pointers
/// - Comptime backend selection instead of VTable dispatch
/// - Zero runtime overhead with compile-time optimization pass selection

const std = @import("std");
const Allocator = std.mem.Allocator;

// Import core types
const types = @import("types");
const Graph = @import("graph").Graph;

// Import pass template system
const pass_templates = @import("pass_templates.zig");
const PassType = pass_templates.PassType;

// Import backend interface
const backend_interface = @import("backend_interface.zig");
const BackendCapabilities = backend_interface.BackendCapabilities;

// Import backend types for memory planning
const backend_types = @import("backend_types");

// Import all migrated passes
const ConstantFoldingPass = @import("passes/constant_folding.zig").ConstantFoldingPass;
const DeadCodeEliminationPass = @import("passes/dce.zig").DeadCodeEliminationPass;
const CommonSubexpressionEliminationPass = @import("passes/cse.zig").CommonSubexpressionEliminationPass;
const MemoryPlanningPass = @import("passes/memory_planning.zig").MemoryPlanningPass;
const AlgebraicSimplificationPass = @import("passes/algebraic.zig").AlgebraicSimplificationPass;
const TrivialReductionPass = @import("passes/trivial_reduce.zig").TrivialReductionPass;
const fusion = @import("passes/fusion.zig");
const pattern_recognition = @import("passes/patterns.zig");

// ============================================================================
// Pass Context (Simplified)
// ============================================================================

/// Simplified PassContext for the new template-based system
pub const PassContext = struct {
    graph: *Graph,
    allocator: Allocator,
    
    // Optional backend-specific data
    backend_capabilities: ?BackendCapabilities = null,
    
    // Pass execution context
    pass_data: ?*anyopaque = null, // For passes to store temporary data
    
    // Memory planning results (store directly to avoid pointer issues)
    memory_plan: ?backend_types.ResolvedMemoryPlan = null,
    
    pub fn init(graph: *Graph, allocator: Allocator) PassContext {
        return .{
            .graph = graph,
            .allocator = allocator,
        };
    }
    
    pub fn deinit(self: *PassContext) void {
        // Clean up memory plan if present
        if (self.memory_plan) |*plan| {
            plan.deinit(self.allocator);
            self.memory_plan = null;
        }
    }
    
    pub fn withCapabilities(self: PassContext, capabilities: BackendCapabilities) PassContext {
        var ctx = self;
        ctx.backend_capabilities = capabilities;
        return ctx;
    }
    
    /// Store memory plan (called by memory planning pass)
    pub fn setMemoryPlan(self: *PassContext, plan: backend_types.ResolvedMemoryPlan) void {
        // Store the plan directly by value (no pointer issues)
        self.memory_plan = plan;
    }
    
    /// Take ownership of memory plan (transfers ownership to caller)
    pub fn takeMemoryPlan(self: *PassContext) ?backend_types.ResolvedMemoryPlan {
        if (self.memory_plan) |plan| {
            self.memory_plan = null; // Clear to prevent double-free
            return plan;
        }
        return null;
    }
};

// ============================================================================
// Unified Pipeline Configuration
// ============================================================================

/// Pipeline configuration for different scenarios
pub const PipelineConfig = struct {
    optimization_level: OptimizationLevel = .balanced,
    enable_debug: bool = false,
    skip_validation: bool = false,
    custom_passes: []const PassDescriptor = &.{},
    
    pub const OptimizationLevel = enum {
        none,       // No optimizations
        basic,      // Essential optimizations only
        balanced,   // Good balance (default)
        aggressive, // Maximum optimizations
    };
};

/// Pass descriptor for custom pass configuration
pub const PassDescriptor = struct {
    name: []const u8,
    pass_type: PassType,
    priority: u32,
    enabled: bool = true,
};

// ============================================================================
// Template-Based Pipeline Execution
// ============================================================================

/// Execute the unified compilation pipeline with comptime backend selection
pub fn executeUnifiedPipeline(
    comptime Backend: type,
    graph: *Graph,
    config: PipelineConfig,
    allocator: Allocator,
) !void {
    // Create context with backend capabilities
    const capabilities = backend_interface.getBackendCapabilities(Backend);
    var ctx = PassContext.init(graph, allocator).withCapabilities(capabilities);
    
    // Execute passes based on optimization level
    switch (config.optimization_level) {
        .none => try executeMinimalPipeline(Backend, &ctx),
        .basic => try executeBasicPipeline(Backend, &ctx),
        .balanced => try executeBalancedPipeline(Backend, &ctx),
        .aggressive => try executeAggressivePipeline(Backend, &ctx),
    }
    
    // Execute custom passes if any
    if (config.custom_passes.len > 0) {
        try executeCustomPasses(Backend, &ctx, config.custom_passes);
    }
}

/// Minimal pipeline (optimization_level = none)
fn executeMinimalPipeline(comptime Backend: type, ctx: *PassContext) !void {
    _ = Backend; // Currently unused, but available for backend-specific logic
    // Only essential passes
    try MemoryPlanningPass.run(ctx);
}

/// Basic pipeline
fn executeBasicPipeline(comptime Backend: type, ctx: *PassContext) !void {
    _ = Backend; // Currently unused, but available for backend-specific logic
    // Essential optimizations
    try ConstantFoldingPass.run(ctx);
    try DeadCodeEliminationPass.run(ctx);
    try MemoryPlanningPass.run(ctx);
}

/// Balanced pipeline (default)
fn executeBalancedPipeline(comptime Backend: type, ctx: *PassContext) !void {
    // Phase 1: Pattern Recognition (backend-specific)
    const PatternRecognitionPass = pattern_recognition.PatternRecognition(Backend);
    try PatternRecognitionPass.run(ctx);
    
    // Phase 2: Basic Optimizations
    try ConstantFoldingPass.run(ctx);
    try AlgebraicSimplificationPass.run(ctx);
    try CommonSubexpressionEliminationPass.run(ctx);
    
    // Phase 3: Fusion (backend-specific)
    const FusionPass = fusion.ElementwiseFusion(Backend);
    try FusionPass.run(ctx);
    
    // Phase 4: Cleanup
    try DeadCodeEliminationPass.run(ctx);
    
    // Phase 5: Memory Planning
    try MemoryPlanningPass.run(ctx);
}

/// Aggressive pipeline (maximum optimizations)
fn executeAggressivePipeline(comptime Backend: type, ctx: *PassContext) !void {
    // Phase 1: Pattern Recognition (backend-specific)
    const PatternRecognitionPass = pattern_recognition.PatternRecognition(Backend);
    try PatternRecognitionPass.run(ctx);
    
    // Phase 2: Multiple rounds of optimization with fusion
    const FusionPass = fusion.ElementwiseFusion(Backend);
    for (0..3) |_| { // Multiple rounds for aggressive optimization
        try ConstantFoldingPass.run(ctx);
        try AlgebraicSimplificationPass.run(ctx);
        try CommonSubexpressionEliminationPass.run(ctx);
        try FusionPass.run(ctx);
        try DeadCodeEliminationPass.run(ctx);
    }
    
    // Phase 3: Final memory planning
    try MemoryPlanningPass.run(ctx);
}

/// Execute custom passes
fn executeCustomPasses(comptime Backend: type, ctx: *PassContext, passes: []const PassDescriptor) !void {
    _ = Backend; // Currently unused, but available for backend-specific logic
    // Sort passes by priority
    const sorted_passes = try ctx.allocator.alloc(PassDescriptor, passes.len);
    defer ctx.allocator.free(sorted_passes);
    
    @memcpy(sorted_passes, passes);
    
    std.mem.sort(PassDescriptor, sorted_passes, {}, comparePassPriority);
    
    // Execute each custom pass
    for (sorted_passes) |pass_desc| {
        if (!pass_desc.enabled) continue;
        
        // TODO: Add runtime dispatch for custom passes
        // For now, we focus on the core template-based passes
        std.log.info("Custom pass '{}' execution not yet implemented", .{pass_desc.name});
    }
}

fn comparePassPriority(context: void, a: PassDescriptor, b: PassDescriptor) bool {
    _ = context;
    return a.priority > b.priority; // Higher priority first
}

// ============================================================================
// Convenience Functions
// ============================================================================

/// Execute default pipeline with CPU backend
pub fn executeDefaultPipeline(graph: *Graph, allocator: Allocator) !void {
    const CpuBackend = @import("backends").cpu;
    try executeUnifiedPipeline(CpuBackend, graph, .{}, allocator);
}

/// Execute pipeline with specific backend and config
pub fn executePipelineWithConfig(
    comptime Backend: type,
    graph: *Graph,
    config: PipelineConfig,
    allocator: Allocator,
) !void {
    try executeUnifiedPipeline(Backend, graph, config, allocator);
}

/// Quick optimization with balanced settings
pub fn quickOptimize(comptime Backend: type, graph: *Graph, allocator: Allocator) !void {
    try executeUnifiedPipeline(Backend, graph, .{ .optimization_level = .balanced }, allocator);
}

/// Maximum optimization
pub fn aggressiveOptimize(comptime Backend: type, graph: *Graph, allocator: Allocator) !void {
    try executeUnifiedPipeline(Backend, graph, .{ .optimization_level = .aggressive }, allocator);
}

// ============================================================================
// Pipeline Introspection
// ============================================================================

/// Get available passes for a backend
pub fn getAvailablePasses(comptime Backend: type) []const []const u8 {
    _ = Backend; // Currently unused, but available for backend-specific filtering
    // TODO: Use comptime reflection to discover available passes
    return &.{
        "constant_folding",
        "algebraic_simplification",
        "dead_code_elimination", 
        "common_subexpression_elimination",
        "patterns",
        "elementwise_fusion",
        "memory_planning",
    };
}

/// Get pipeline statistics
pub const PipelineStats = struct {
    passes_executed: u32,
    nodes_before: u32,
    nodes_after: u32,
    optimization_time_ms: u32,
};

/// Execute pipeline with statistics collection
pub fn executePipelineWithStats(
    comptime Backend: type,
    graph: *Graph,
    config: PipelineConfig,
    allocator: Allocator,
) !PipelineStats {
    const start_time = std.time.milliTimestamp();
    const nodes_before = @as(u32, @intCast(graph.nodes.items.len));
    
    try executeUnifiedPipeline(Backend, graph, config, allocator);
    
    const end_time = std.time.milliTimestamp();
    const nodes_after = @as(u32, @intCast(graph.nodes.items.len));
    
    return PipelineStats{
        .passes_executed = getPassCountForLevel(config.optimization_level),
        .nodes_before = nodes_before,
        .nodes_after = nodes_after,
        .optimization_time_ms = @intCast(end_time - start_time),
    };
}

fn getPassCountForLevel(level: PipelineConfig.OptimizationLevel) u32 {
    return switch (level) {
        .none => 1,
        .basic => 3,
        .balanced => 4,
        .aggressive => 10, // Multiple rounds
    };
}

// ============================================================================
// Unit Tests
// ============================================================================

test "unified pipeline execution" {
    const testing = std.testing;
    const CpuBackend = @import("backends").cpu;
    
    var graph = try Graph.init(testing.allocator);
    defer graph.deinit();
    
    // Create a simple graph
    const a = try graph.addPlaceholder(.f32);
    const b = try graph.addPlaceholder(.f32);
    _ = try graph.addNode(.add, &.{a, b}, .f32);
    
    // Test default pipeline
    try executeDefaultPipeline(&graph, testing.allocator);
    
    // Test with different optimization levels
    try executeUnifiedPipeline(CpuBackend, &graph, .{ .optimization_level = .none }, testing.allocator);
    try executeUnifiedPipeline(CpuBackend, &graph, .{ .optimization_level = .basic }, testing.allocator);
    try executeUnifiedPipeline(CpuBackend, &graph, .{ .optimization_level = .balanced }, testing.allocator);
    try executeUnifiedPipeline(CpuBackend, &graph, .{ .optimization_level = .aggressive }, testing.allocator);
}

test "pipeline with statistics" {
    const testing = std.testing;
    const CpuBackend = @import("backends").cpu;
    
    var graph = try Graph.init(testing.allocator);
    defer graph.deinit();
    
    // Create a graph with optimization opportunities
    const a = try graph.addPlaceholder(.f32);
    const zero = try graph.addConstant(0.0);
    const add_zero = try graph.addNode(.add, &.{a, zero}, .f32); // Should be optimized
    _ = try graph.addNode(.mul, &.{add_zero, add_zero}, .f32);
    
    const stats = try executePipelineWithStats(
        CpuBackend,
        &graph,
        .{ .optimization_level = .balanced },
        testing.allocator
    );
    
    try testing.expect(stats.passes_executed > 0);
    try testing.expect(stats.optimization_time_ms >= 0);
}

test "convenience functions" {
    const testing = std.testing;
    const CpuBackend = @import("backends").cpu;
    
    var graph = try Graph.init(testing.allocator);
    defer graph.deinit();
    
    const a = try graph.addPlaceholder(.f32);
    const b = try graph.addPlaceholder(.f32);
    _ = try graph.addNode(.mul, &.{a, b}, .f32);
    
    // Test convenience functions
    try quickOptimize(CpuBackend, &graph, testing.allocator);
    try aggressiveOptimize(CpuBackend, &graph, testing.allocator);
}

test "pass introspection" {
    const testing = std.testing;
    const CpuBackend = @import("backends").cpu;
    
    const available_passes = getAvailablePasses(CpuBackend);
    try testing.expect(available_passes.len > 0);
    
    // Should include our core passes
    var found_constant_folding = false;
    for (available_passes) |pass_name| {
        if (std.mem.eql(u8, pass_name, "constant_folding")) {
            found_constant_folding = true;
            break;
        }
    }
    try testing.expect(found_constant_folding);
}