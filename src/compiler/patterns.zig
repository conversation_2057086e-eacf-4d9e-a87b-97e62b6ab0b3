/// Unified Compile-Time Pattern System
///
/// This module provides a unified pattern matching and transformation system
/// that works at compile-time, eliminating runtime overhead from pattern matching.

const std = @import("std");
const Allocator = std.mem.Allocator;

// Import core types
const types = @import("types");
const NodeId = types.NodeId;
const ComputeOp = types.ComputeOp;
const DataType = types.DataType;

const Graph = @import("graph").Graph;
const Node = @import("graph").Node;
const NodeSpec = @import("graph").NodeSpec;

const transforms = @import("transforms.zig");
const GraphRewriter = transforms.GraphRewriter;

// ============================================================================
// Pattern Expression Language
// ============================================================================

/// Pattern expression for matching and replacement
pub const PatternExpr = union(enum) {
    /// Match/create a specific operation
    op: struct {
        op: ComputeOp,
        inputs: ?[]const PatternExpr = null,
    },
    
    /// Match/reference a variable binding
    @"var": []const u8,
    
    /// Match a constant value
    constant: struct {
        value: ?f32 = null, // null means any constant
    },
    
    /// Match any node (wildcard)
    any: void,
    
    /// Create a custom operation
    custom: struct {
        name: []const u8,
        inputs: []const []const u8, // Variable references
    },
    
    /// Match with constraints
    constrained: struct {
        expr: *const PatternExpr,
        constraint: Constraint,
    },
};

/// Constraints for pattern matching
pub const Constraint = union(enum) {
    /// Shape must be scalar
    scalar: void,
    
    /// Shape must have specific rank
    rank: u32,
    
    /// Width must be multiple of N (for SIMD)
    width_multiple: u32,
    
    /// Custom constraint function
    custom: *const fn(*const Node) bool,
};

/// Single pattern definition
pub const Pattern = struct {
    name: []const u8,
    match: PatternExpr,
    replace: PatternExpr,
    priority: u32 = 50,
    
    /// Compile-time validation
    pub fn validate(comptime self: Pattern) void {
        // Ensure all variables in replace are bound in match
        const match_vars = comptime collectVariables(self.match);
        const replace_vars = comptime collectVariables(self.replace);
        
        for (replace_vars) |rvar| {
            var found = false;
            for (match_vars) |mvar| {
                if (std.mem.eql(u8, rvar, mvar)) {
                    found = true;
                    break;
                }
            }
            if (!found) {
                @compileError("Pattern '" ++ self.name ++ "': variable '" ++ rvar ++ "' used in replacement but not bound in match");
            }
        }
    }
};

/// Collect all variable names from a pattern expression (compile-time)
fn collectVariables(comptime expr: PatternExpr) []const []const u8 {
    var vars: []const []const u8 = &.{};
    
    switch (expr) {
        .@"var" => |name| {
            vars = vars ++ &[_][]const u8{name};
        },
        .op => |op_expr| {
            if (op_expr.inputs) |inputs| {
                for (inputs) |input| {
                    vars = vars ++ collectVariables(input);
                }
            }
        },
        .constrained => |c| {
            vars = vars ++ collectVariables(c.expr.*);
        },
        .custom => |custom| {
            // Custom inputs are already variable references
            vars = vars ++ custom.inputs;
        },
        else => {},
    }
    
    return vars;
}

// ============================================================================
// Pattern Matching
// ============================================================================

/// A match result containing variable bindings
pub const Match = struct {
    root: NodeId,
    bindings: std.StringHashMapUnmanaged(NodeId) = .{},
    
    pub fn deinit(self: *Match, allocator: Allocator) void {
        self.bindings.deinit(allocator);
    }
};

/// Find all matches of a pattern in the graph
pub fn findMatches(
    comptime pattern: Pattern,
    graph: *Graph,
    allocator: Allocator,
) ![]Match {
    // Validate pattern at compile time
    comptime pattern.validate();
    
    var match_list = std.ArrayList(Match).init(allocator);
    defer match_list.deinit();
    
    // Try to match pattern at each node
    const topo_order = try graph.topologicalSort();
    for (topo_order) |node_id| {
        const node = graph.getNode(node_id) orelse continue;
        if (!node.is_valid) continue;
        
        var match = Match{ .root = node_id };
        if (try matchNode(pattern.match, node_id, graph, &match, allocator)) {
            try match_list.append(match);
        } else {
            match.deinit(allocator);
        }
    }
    
    return match_list.toOwnedSlice();
}

/// Check if a pattern matches at a specific node
pub fn matches(
    comptime pattern: Pattern,
    graph: *Graph,
    node_id: NodeId,
) ?Match {
    var match = Match{ .root = node_id };
    
    if (matchNode(pattern.match, node_id, graph, &match, std.heap.page_allocator) catch false) {
        return match;
    } else {
        match.deinit(std.heap.page_allocator);
        return null;
    }
}

/// Internal: Match a pattern expression against a node
fn matchNode(
    expr: PatternExpr,
    node_id: NodeId,
    graph: *Graph,
    match: *Match,
    allocator: Allocator,
) !bool {
    const node = graph.getNode(node_id) orelse return false;
    
    switch (expr) {
        .op => |op_expr| {
            // Must be compute node with matching operation
            if (node.spec != .compute or node.spec.compute != op_expr.op) {
                return false;
            }
            
            // Match inputs if specified
            if (op_expr.inputs) |input_patterns| {
                if (input_patterns.len != node.inputs.len) return false;
                
                for (input_patterns, node.inputs) |input_pattern, input_id| {
                    if (!try matchNode(input_pattern, input_id, graph, match, allocator)) {
                        return false;
                    }
                }
            }
            
            return true;
        },
        
        .@"var" => |name| {
            // Check if already bound
            if (match.bindings.get(name)) |bound_id| {
                return bound_id == node_id;
            } else {
                // Bind variable
                try match.bindings.put(allocator, name, node_id);
                return true;
            }
        },
        
        .constant => |const_spec| {
            if (node.spec != .data or node.spec.data != .constant) return false;
            
            if (const_spec.value) |expected| {
                const actual = graph.getConstantValue(node_id) orelse return false;
                return actual == expected;
            }
            
            return true; // Any constant matches
        },
        
        .any => return true,
        
        .custom => return false, // Custom ops don't appear in match expressions
        
        .constrained => |c| {
            // First match the inner expression
            if (!try matchNode(c.expr.*, node_id, graph, match, allocator)) {
                return false;
            }
            
            // Then check constraint
            return checkConstraint(c.constraint, node);
        },
    }
}

/// Check if a constraint is satisfied
fn checkConstraint(constraint: Constraint, node: *const Node) bool {
    switch (constraint) {
        .scalar => {
            // Check if output shape is scalar
            const metadata = node.metadata orelse return false;
            const shape = metadata.output_shape orelse return false;
            return shape.dims.len == 0 or (shape.dims.len == 1 and shape.dims[0].concrete == 1);
        },
        
        .rank => |expected_rank| {
            const metadata = node.metadata orelse return false;
            const shape = metadata.output_shape orelse return false;
            return shape.dims.len == expected_rank;
        },
        
        .width_multiple => |multiple| {
            const metadata = node.metadata orelse return false;
            const shape = metadata.output_shape orelse return false;
            if (shape.dims.len == 0) return false;
            
            // Check last dimension
            const last_dim = shape.dims[shape.dims.len - 1];
            if (last_dim != .concrete) return false;
            return @rem(last_dim.concrete, multiple) == 0;
        },
        
        .custom => |check_fn| {
            return check_fn(node);
        },
    }
}

// ============================================================================
// Pattern Application
// ============================================================================

/// Apply a pattern replacement using a match
pub fn applyReplacement(
    comptime pattern: Pattern,
    rewriter: *GraphRewriter,
    match: Match,
) !void {
    // Create the replacement subgraph
    const new_root = try createReplacement(pattern.replace, match, rewriter);
    
    // Substitute the matched root with the new root
    try rewriter.replaceNode(match.root, new_root);
}

/// Create nodes for a replacement expression
fn createReplacement(
    expr: PatternExpr,
    match: Match,
    rewriter: *GraphRewriter,
) !NodeId {
    switch (expr) {
        .op => |op_expr| {
            // Create inputs first
            var inputs = std.ArrayList(NodeId).init(rewriter.allocator);
            defer inputs.deinit();
            
            if (op_expr.inputs) |input_exprs| {
                for (input_exprs) |input_expr| {
                    const input_id = try createReplacement(input_expr, match, rewriter);
                    try inputs.append(input_id);
                }
            }
            
            // Create the operation node
            // TODO: Infer dtype from inputs
            return rewriter.addNode(.compute, op_expr.op, inputs.items, .f32);
        },
        
        .@"var" => |name| {
            // Look up variable binding
            return match.bindings.get(name) orelse error.UnboundVariable;
        },
        
        .constant => |const_spec| {
            const value = const_spec.value orelse 0.0;
            return rewriter.addConstant(value);
        },
        
        .any => return error.WildcardInReplacement,
        
        .custom => |custom| {
            // Resolve input variables
            var inputs = std.ArrayList(NodeId).init(rewriter.allocator);
            defer inputs.deinit();
            
            for (custom.inputs) |var_name| {
                const input_id = match.bindings.get(var_name) orelse return error.UnboundVariable;
                try inputs.append(input_id);
            }
            
            // Create custom operation
            return rewriter.addCustomOp(custom.name, inputs.items);
        },
        
        .constrained => |c| {
            // Constraints don't apply to replacements
            return createReplacement(c.expr.*, match, rewriter);
        },
    }
}

// ============================================================================
// Common Patterns Library
// ============================================================================

pub const CommonPatterns = struct {
    /// Algebraic simplification patterns
    pub const algebraic = [_]Pattern{
        // x + 0 -> x
        Pattern{
            .name = "add_zero_right",
            .match = .{ .op = .{ .op = .add, .inputs = &.{
                .{ .@"var" = "x" },
                .{ .constant = .{ .value = 0.0 } },
            }}},
            .replace = .{ .@"var" = "x" },
            .priority = 100,
        },
        
        // 0 + x -> x
        Pattern{
            .name = "add_zero_left",
            .match = .{ .op = .{ .op = .add, .inputs = &.{
                .{ .constant = .{ .value = 0.0 } },
                .{ .@"var" = "x" },
            }}},
            .replace = .{ .@"var" = "x" },
            .priority = 100,
        },
        
        // x * 1 -> x
        Pattern{
            .name = "mul_one_right",
            .match = .{ .op = .{ .op = .mul, .inputs = &.{
                .{ .@"var" = "x" },
                .{ .constant = .{ .value = 1.0 } },
            }}},
            .replace = .{ .@"var" = "x" },
            .priority = 100,
        },
        
        // 1 * x -> x
        Pattern{
            .name = "mul_one_left",
            .match = .{ .op = .{ .op = .mul, .inputs = &.{
                .{ .constant = .{ .value = 1.0 } },
                .{ .@"var" = "x" },
            }}},
            .replace = .{ .@"var" = "x" },
            .priority = 100,
        },
        
        // x * 0 -> 0
        Pattern{
            .name = "mul_zero",
            .match = .{ .op = .{ .op = .mul, .inputs = &.{
                .{ .@"var" = "x" },
                .{ .constant = .{ .value = 0.0 } },
            }}},
            .replace = .{ .constant = .{ .value = 0.0 } },
            .priority = 100,
        },
        
        // recip(recip(x)) -> x
        Pattern{
            .name = "double_reciprocal",
            .match = .{ .op = .{ .op = .recip, .inputs = &.{
                .{ .op = .{ .op = .recip, .inputs = &.{ .{ .@"var" = "x" } } } },
            }}},
            .replace = .{ .@"var" = "x" },
            .priority = 90,
        },
    };
    
    /// Matrix multiplication pattern
    pub const matmul = Pattern{
        .name = "matmul_pattern",
        .match = .{ .op = .{ .op = .sum_reduce, .inputs = &.{
            .{ .op = .{ .op = .mul, .inputs = &.{
                .{ .@"var" = "a" },
                .{ .@"var" = "b" },
            }}},
        }}},
        .replace = .{ .custom = .{ .name = "matmul", .inputs = &.{ "a", "b" } } },
        .priority = 85,
    };
    
    /// Gather pattern (index -> mul -> sum_reduce)
    pub const gather = Pattern{
        .name = "gather_pattern",
        .match = .{ .op = .{ .op = .sum_reduce, .inputs = &.{
            .{ .op = .{ .op = .mul, .inputs = &.{
                .{ .@"var" = "data" },
                .{ .@"var" = "indices" },
            }}},
        }}},
        .replace = .{ .custom = .{ .name = "gather", .inputs = &.{ "data", "indices" } } },
        .priority = 84,
    };
    
    /// ARange pattern
    pub const arange = Pattern{
        .name = "arange_pattern",
        .match = .{ .op = .{ .op = .add, .inputs = &.{
            .{ .op = .{ .op = .mul, .inputs = &.{
                .{ .@"var" = "indices" },
                .{ .constant = .{ .value = null } }, // Any constant step
            }}},
            .{ .constant = .{ .value = null } }, // Any constant start
        }}},
        .replace = .{ .custom = .{ .name = "arange", .inputs = &.{ "indices" } } },
        .priority = 83,
    };
    
    /// Get all common patterns
    pub fn all() []const Pattern {
        return &algebraic ++ &[_]Pattern{ matmul, gather, arange };
    }
};

// ============================================================================
// Backend Pattern Integration
// ============================================================================

/// CPU-specific patterns
pub const CpuPatterns = struct {
    pub const simd_add4 = Pattern{
        .name = "simd_add4",
        .match = .{ .constrained = .{
            .expr = &PatternExpr{ .op = .{ .op = .add, .inputs = &.{
                .{ .@"var" = "a" },
                .{ .@"var" = "b" },
            }}},
            .constraint = .{ .width_multiple = 4 },
        }},
        .replace = .{ .custom = .{ .name = "simd_add4", .inputs = &.{ "a", "b" } } },
        .priority = 75,
    };
    
    pub const fma = Pattern{
        .name = "fused_multiply_add",
        .match = .{ .op = .{ .op = .add, .inputs = &.{
            .{ .op = .{ .op = .mul, .inputs = &.{
                .{ .@"var" = "a" },
                .{ .@"var" = "b" },
            }}},
            .{ .@"var" = "c" },
        }}},
        .replace = .{ .custom = .{ .name = "fma", .inputs = &.{ "a", "b", "c" } } },
        .priority = 80,
    };
};

/// CUDA-specific patterns
pub const CudaPatterns = struct {
    pub const tensor_core_matmul = Pattern{
        .name = "tensor_core_matmul",
        .match = .{ .constrained = .{
            .expr = &PatternExpr{ .custom = .{ .name = "matmul", .inputs = &.{ "a", "b" } } },
            .constraint = .{ .width_multiple = 16 }, // Tensor cores require 16-aligned dimensions
        }},
        .replace = .{ .custom = .{ .name = "tensor_core_matmul", .inputs = &.{ "a", "b" } } },
        .priority = 90,
    };
};

// ============================================================================
// Pattern Compilation and Optimization
// ============================================================================

/// Compile patterns for efficient matching
pub fn compilePatterns(comptime patterns: []const Pattern) CompiledPatternSet {
    // Group patterns by root operation for faster matching
    var by_op = std.EnumArray(ComputeOp, []const Pattern).initFill(&.{});
    var any_patterns: []const Pattern = &.{};
    
    for (patterns) |pattern| {
        const root_op = getRootOp(pattern.match);
        if (root_op) |op| {
            by_op.set(op, by_op.get(op) ++ &[_]Pattern{pattern});
        } else {
            any_patterns = any_patterns ++ &[_]Pattern{pattern};
        }
    }
    
    return CompiledPatternSet{
        .patterns_by_op = by_op,
        .any_patterns = any_patterns,
    };
}

/// Get the root operation of a pattern (if any)
fn getRootOp(expr: PatternExpr) ?ComputeOp {
    return switch (expr) {
        .op => |op_expr| op_expr.op,
        .constrained => |c| getRootOp(c.expr.*),
        else => null,
    };
}

/// Compiled pattern set for efficient matching
pub const CompiledPatternSet = struct {
    patterns_by_op: std.EnumArray(ComputeOp, []const Pattern),
    any_patterns: []const Pattern,
    
    /// Find patterns that could match a node
    pub fn getPatternsForNode(self: *const CompiledPatternSet, node: *const Node) []const Pattern {
        if (node.spec == .compute) {
            const op_patterns = self.patterns_by_op.get(node.spec.compute);
            return op_patterns ++ self.any_patterns;
        }
        return self.any_patterns;
    }
};

// ============================================================================
// Unit Tests
// ============================================================================

test "Pattern validation" {
    _ = std.testing;
    
    // Valid pattern
    const valid = Pattern{
        .name = "test_valid",
        .match = .{ .op = .{ .op = .add, .inputs = &.{
            .{ .@"var" = "x" },
            .{ .@"var" = "y" },
        }}},
        .replace = .{ .op = .{ .op = .mul, .inputs = &.{
            .{ .@"var" = "x" },
            .{ .@"var" = "y" },
        }}},
    };
    
    // This should compile without error
    comptime valid.validate();
    
    // Invalid pattern (using unbound variable) would fail at compile time:
    // const invalid = Pattern{
    //     .name = "test_invalid",
    //     .match = .{ .@"var" = "x" },
    //     .replace = .{ .@"var" = "y" }, // Error: 'y' not bound in match
    // };
}

test "Pattern matching basic" {
    const testing = std.testing;
    var graph = try Graph.init(testing.allocator);
    defer graph.deinit();
    
    // Create a simple graph: a + 0
    const a = try graph.addPlaceholder(.f32);
    const zero = try graph.addConstant(0.0);
    const add = try graph.addNode(.add, &.{a, zero}, .f32);
    
    // Test matching add_zero_right pattern
    const pattern = CommonPatterns.algebraic[0]; // add_zero_right
    if (matches(pattern, &graph, add)) |match| {
        defer @constCast(&match).deinit(testing.allocator);
        
        try testing.expectEqual(add, match.root);
        try testing.expectEqual(a, match.bindings.get("x").?);
    } else {
        try testing.expect(false); // Should have matched
    }
}

test "Constraint checking" {
    const testing = std.testing;
    
    // Test scalar constraint
    const scalar_constraint = Constraint{ .scalar = {} };
    
    // Create a mock node with scalar shape
    var node = Node{
        .id = 0,
        .spec = .{ .compute = .add },
        .inputs = &.{},
        .outputs = &.{},
        .is_valid = true,
    };
    
    // Without metadata, constraint check should fail
    try testing.expect(!checkConstraint(scalar_constraint, &node));
    
    // Test width_multiple constraint
    const simd_constraint = Constraint{ .width_multiple = 4 };
    try testing.expect(!checkConstraint(simd_constraint, &node));
}

test "Pattern expression matching" {
    const testing = std.testing;
    
    var graph = try Graph.init(testing.allocator);
    defer graph.deinit();
    
    // Create graph: a + b
    const a = try graph.addPlaceholder(.f32);
    const b = try graph.addPlaceholder(.f32);
    _ = try graph.addNode(.add, &.{a, b}, .f32);
    
    // Test variable pattern matching
    const var_pattern = PatternExpr{ .@"var" = "x" };
    var match = Match{ .root = a };
    defer match.deinit(testing.allocator);
    
    // Should match and bind variable
    try testing.expect(try matchNode(var_pattern, a, &graph, &match, testing.allocator));
    try testing.expectEqual(a, match.bindings.get("x").?);
    
    // Should match same variable again
    try testing.expect(try matchNode(var_pattern, a, &graph, &match, testing.allocator));
    
    // Should fail to match different node to same variable
    try testing.expect(!try matchNode(var_pattern, b, &graph, &match, testing.allocator));
}

test "Operation pattern matching" {
    const testing = std.testing;
    
    var graph = try Graph.init(testing.allocator);
    defer graph.deinit();
    
    // Create graph: a + b
    const a = try graph.addPlaceholder(.f32);
    const b = try graph.addPlaceholder(.f32);
    const add_node = try graph.addNode(.add, &.{a, b}, .f32);
    
    // Pattern for add operation
    const add_pattern = PatternExpr{ .op = .{ .op = .add, .inputs = &.{
        .{ .@"var" = "left" },
        .{ .@"var" = "right" },
    }}};
    
    var match = Match{ .root = add_node };
    defer match.deinit(testing.allocator);
    
    // Should match add operation
    try testing.expect(try matchNode(add_pattern, add_node, &graph, &match, testing.allocator));
    try testing.expect(match.bindings.contains("left"));
    try testing.expect(match.bindings.contains("right"));
    
    // Should not match placeholders
    try testing.expect(!try matchNode(add_pattern, a, &graph, &match, testing.allocator));
}

test "Constant pattern matching" {
    const testing = std.testing;
    
    var graph = try Graph.init(testing.allocator);
    defer graph.deinit();
    
    // Create constants
    const five = try graph.addConstant(5.0);
    const zero = try graph.addConstant(0.0);
    
    // Pattern for specific constant value
    const five_pattern = PatternExpr{ .constant = .{ .value = 5.0 } };
    var match = Match{ .root = five };
    defer match.deinit(testing.allocator);
    
    // Should match exact constant value
    try testing.expect(try matchNode(five_pattern, five, &graph, &match, testing.allocator));
    
    // Should not match different constant value
    try testing.expect(!try matchNode(five_pattern, zero, &graph, &match, testing.allocator));
    
    // Pattern for any constant
    const any_const_pattern = PatternExpr{ .constant = .{} };
    try testing.expect(try matchNode(any_const_pattern, five, &graph, &match, testing.allocator));
    try testing.expect(try matchNode(any_const_pattern, zero, &graph, &match, testing.allocator));
}

test "Pattern replacement creation" {
    const testing = std.testing;
    
    var graph = try Graph.init(testing.allocator);
    defer graph.deinit();
    
    // Create graph for pattern matching
    const a = try graph.addPlaceholder(.f32);
    const b = try graph.addPlaceholder(.f32);
    const add_node = try graph.addNode(.add, &.{a, b}, .f32);
    
    // Create match result
    var match = Match{ .root = add_node };
    defer match.deinit(testing.allocator);
    try match.bindings.put(testing.allocator, "x", a);
    try match.bindings.put(testing.allocator, "y", b);
    
    // Create rewriter
    var rewriter = GraphRewriter.init(&graph, testing.allocator);
    defer rewriter.deinit();
    
    // Test variable replacement
    const var_replacement = PatternExpr{ .@"var" = "x" };
    const result_id = try createReplacement(var_replacement, match, &rewriter);
    try testing.expectEqual(a, result_id);
    
    // Test constant replacement
    const const_replacement = PatternExpr{ .constant = .{ .value = 42.0 } };
    _ = try createReplacement(const_replacement, match, &rewriter);
    
    // Test custom operation replacement
    const custom_replacement = PatternExpr{ .custom = .{ 
        .name = "test_op", 
        .inputs = &.{ "x", "y" } 
    }};
    _ = try createReplacement(custom_replacement, match, &rewriter);
}

test "Common patterns compilation" {
    const testing = std.testing;
    
    // Test that all common patterns compile and validate
    const algebraic_patterns = CommonPatterns.algebraic;
    
    // Each pattern should validate at compile time
    for (algebraic_patterns) |pattern| {
        comptime pattern.validate();
        
        // Pattern names should be meaningful
        try testing.expect(pattern.name.len > 0);
        try testing.expect(pattern.priority > 0);
    }
    
    // Test specific patterns
    const all_patterns = CommonPatterns.all();
    try testing.expect(all_patterns.len >= 5); // Should have algebraic + special patterns
    
    // Find the add_zero pattern
    var found_add_zero = false;
    for (all_patterns) |pattern| {
        if (std.mem.eql(u8, pattern.name, "add_zero_right")) {
            found_add_zero = true;
            try testing.expectEqual(@as(u32, 100), pattern.priority);
        }
    }
    try testing.expect(found_add_zero);
}

test "Backend-specific patterns" {
    const testing = std.testing;
    
    // Test CPU patterns
    const cpu_simd = CpuPatterns.simd_add4;
    comptime cpu_simd.validate();
    try testing.expectEqualStrings("simd_add4", cpu_simd.name);
    try testing.expectEqual(@as(u32, 75), cpu_simd.priority);
    
    const cpu_fma = CpuPatterns.fma;
    comptime cpu_fma.validate();
    try testing.expectEqualStrings("fused_multiply_add", cpu_fma.name);
    try testing.expectEqual(@as(u32, 80), cpu_fma.priority);
    
    // Test CUDA patterns
    const cuda_tensorcore = CudaPatterns.tensor_core_matmul;
    comptime cuda_tensorcore.validate();
    try testing.expectEqualStrings("tensor_core_matmul", cuda_tensorcore.name);
    try testing.expectEqual(@as(u32, 90), cuda_tensorcore.priority);
}

test "Pattern compilation and optimization" {
    const testing = std.testing;
    
    // Test pattern compilation
    const test_patterns = [_]Pattern{
        CommonPatterns.algebraic[0], // add_zero_right
        CommonPatterns.matmul,
    };
    
    const compiled = compilePatterns(&test_patterns);
    
    // Should organize patterns by operation
    const add_patterns = compiled.patterns_by_op.get(.add);
    try testing.expect(add_patterns.len > 0);
    
    // Should handle special patterns
    const matmul_patterns = compiled.patterns_by_op.get(.sum_reduce);
    try testing.expect(matmul_patterns.len > 0);
}

test "Match result memory management" {
    const testing = std.testing;
    
    var graph = try Graph.init(testing.allocator);
    defer graph.deinit();
    
    // Create a simple graph
    const a = try graph.addPlaceholder(.f32);
    const zero = try graph.addConstant(0.0);
    const add_node = try graph.addNode(.add, &.{a, zero}, .f32);
    
    // Test pattern matching with proper memory cleanup
    const add_zero_pattern = CommonPatterns.algebraic[0];
    const match_results = try findMatches(add_zero_pattern, &graph, testing.allocator);
    defer {
        for (match_results) |*match_result| {
            var mut_match = match_result.*;
            mut_match.deinit(testing.allocator);
        }
        testing.allocator.free(match_results);
    }
    
    // Should find the add_zero pattern
    try testing.expect(match_results.len > 0);
    try testing.expectEqual(add_node, match_results[0].root);
}

test "Pattern application" {
    const testing = std.testing;
    
    var graph = try Graph.init(testing.allocator);
    defer graph.deinit();
    
    // Create graph: x + 0
    const x = try graph.addPlaceholder(.f32);
    const zero = try graph.addConstant(0.0);
    const add_node = try graph.addNode(.add, &.{x, zero}, .f32);
    
    // Create rewriter
    var rewriter = GraphRewriter.init(&graph, testing.allocator);
    defer rewriter.deinit();
    
    // Create match for x + 0 -> x transformation
    var match = Match{ .root = add_node };
    defer match.deinit(testing.allocator);
    try match.bindings.put(testing.allocator, "x", x);
    
    // Apply the add_zero_right pattern
    const pattern = CommonPatterns.algebraic[0];
    try applyReplacement(pattern, &rewriter, match);
    
    // Should have queued the replacement
    try testing.expect(rewriter.hasOperations());
    const counts = rewriter.getOperationCounts();
    try testing.expectEqual(@as(u32, 1), counts.substitutions);
}