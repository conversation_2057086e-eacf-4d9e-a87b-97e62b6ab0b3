/// Session-based Tensor API - Mid-level interface that eliminates boilerplate
///
/// This module provides a Session-based API that manages the graph/compile/execute lifecycle
/// automatically while maintaining explicit resource management. It follows idiomatic Zig
/// patterns with method-based operations on SessionTensor objects.
///
/// Usage:
///   var session = try Session.init(allocator);
///   defer session.deinit();
///
///   const a = try session.tensor(&.{2, 3}, .f32, data);
///   const b = try session.ones(&.{2, 3}, .f32);
///   const result = try a.add(b);
///
///   const output = result.data(f32);
const std = @import("std");
const Allocator = std.mem.Allocator;
const ArrayListUnmanaged = std.ArrayListUnmanaged;

// Import core components
const types = @import("types");
const DataType = types.DataType;
const NodeId = types.NodeId;
const Device = types.Device;

const Graph = @import("graph").Graph;
const TensorHandle = @import("tensor").TensorHandle;
const shape_mod = @import("shape");
const ShapeTracker = shape_mod.ShapeTracker;
const compiler = @import("compiler");
const execution = @import("execution");

// Type aliases for convenience
const CompiledGraph = execution.CompiledGraph;
const Executor = execution.Executor;

/// Execution mode for Session
pub const ExecutionMode = enum {
    eager, // Execute operations immediately (default)
    lazy, // Build graph and execute on demand
};

/// Backend configuration for Session
pub const BackendConfig = struct {
    device: Device = .cpu,
    // Future: add backend-specific options here
    // cuda_device_id: ?u32 = null,
    // metal_device_id: ?u32 = null,
};

/// Errors that can occur in Session operations
pub const SessionError = error{
    GraphCompilationFailed,
    ExecutionFailed,
    InvalidShape,
    InvalidDataType,
    IncompatibleTensors,
    NotCompiled,
    NotExecuted,
} || Allocator.Error;

/// Session manages the computation graph lifecycle and provides high-level tensor operations
pub const Session = struct {
    allocator: Allocator,
    graph: Graph,
    compiled: ?*CompiledGraph = null,
    executor: ?*Executor = null,
    mode: ExecutionMode = .eager,
    backend_config: BackendConfig = .{},
    next_output_id: u32 = 0,
    // Track allocated shapes for cleanup
    allocated_shapes: ArrayListUnmanaged([]i64) = .{},
    // Track compilation state for automatic recompilation
    last_compiled_version: u32 = 0,
    // Track which output nodes were included in the last compilation
    compiled_outputs: std.AutoHashMapUnmanaged(NodeId, void) = .{},
    // Track pending outputs that need to be included in next compilation
    pending_outputs: std.AutoHashMapUnmanaged(NodeId, void) = .{},

    /// Initialize a new Session with the given allocator
    pub fn init(allocator: Allocator) !Session {
        return initWithBackend(allocator, .{});
    }

    /// Initialize a new Session with specific backend configuration
    pub fn initWithBackend(allocator: Allocator, backend_config: BackendConfig) !Session {
        const graph = try Graph.init(allocator);
        return Session{
            .allocator = allocator,
            .graph = graph,
            .backend_config = backend_config,
        };
    }

    /// Clean up all resources managed by this Session
    pub fn deinit(self: *Session) void {
        // Clean up allocated shapes
        for (self.allocated_shapes.items) |shape| {
            self.allocator.free(shape);
        }
        self.allocated_shapes.deinit(self.allocator);

        // Clean up output tracking
        self.compiled_outputs.deinit(self.allocator);
        self.pending_outputs.deinit(self.allocator);

        if (self.executor) |exec| {
            exec.deinit();
            self.allocator.destroy(exec);
        }
        if (self.compiled) |comp| {
            comp.deinit(self.allocator);
            self.allocator.destroy(comp);
        }
        self.graph.deinit();
    }

    /// Set the execution mode (eager or lazy)
    pub fn setMode(self: *Session, mode: ExecutionMode) void {
        // If switching modes, we might need to recompile
        // but we keep the output tracking intact
        self.mode = mode;
    }

    /// Get the current execution mode
    pub fn getMode(self: *const Session) ExecutionMode {
        return self.mode;
    }

    /// Create a tensor with the given shape, data type, and initial data
    pub fn tensor(self: *Session, shape: []const i64, dtype: DataType, data: anytype) !SessionTensor {
        // Create TensorHandle using existing tensor creation functions from tensor module
        const tensor_mod = @import("tensor");

        // Create a constant tensor with the provided data
        const scalar_value = extractScalar(data, dtype);
        const handle = try tensor_mod.constant(&self.graph, @floatCast(scalar_value), dtype);

        // If shape has more than 0 dimensions, we need to create a proper tensor
        if (shape.len > 0) {
            // For now, create a placeholder and we'll set the data during execution
            const placeholder_handle = try tensor_mod.placeholder(&self.graph, shape, dtype);
            return self.createSessionTensor(placeholder_handle, shape, dtype, data);
        } else {
            // Scalar case
            return self.createSessionTensor(handle, &.{}, dtype, null);
        }
    }

    /// Create a tensor filled with zeros
    pub fn zeros(self: *Session, shape: []const i64, dtype: DataType) !SessionTensor {
        const tensor_mod = @import("tensor");
        const handle = try tensor_mod.zeros(&self.graph, shape, dtype);
        return self.createSessionTensor(handle, shape, dtype, null);
    }

    /// Create a tensor filled with ones
    pub fn ones(self: *Session, shape: []const i64, dtype: DataType) !SessionTensor {
        const tensor_mod = @import("tensor");
        const handle = try tensor_mod.ones(&self.graph, shape, dtype);
        return self.createSessionTensor(handle, shape, dtype, null);
    }

    /// Create a placeholder tensor (for inputs that will be set later)
    pub fn placeholder(self: *Session, shape: []const i64, dtype: DataType) !SessionTensor {
        const tensor_mod = @import("tensor");
        const handle = try tensor_mod.placeholder(&self.graph, shape, dtype);
        return self.createSessionTensor(handle, shape, dtype, null);
    }

    /// Run the computation (for lazy mode)
    pub fn run(self: *Session) !void {
        try self.ensureCompiled();
        if (self.executor) |exec| {
            try exec.run();
        }
    }

    /// Internal helper to create SessionTensor with proper lifecycle management
    fn createSessionTensor(self: *Session, handle: TensorHandle, shape: []const i64, dtype: DataType, _: anytype) !SessionTensor {
        // Store shape in session-managed memory
        const owned_shape = try self.allocator.dupe(i64, shape);
        try self.allocated_shapes.append(self.allocator, owned_shape);

        const session_tensor = SessionTensor{
            .session = self,
            .handle = handle,
            .shape = owned_shape,
            .dtype = dtype,
        };

        // Note: We no longer mark tensors as outputs here
        // This happens lazily when operations are performed or data is requested
        // This elegant design prevents unnecessary compilations

        return session_tensor;
    }

    /// Ensure the graph is compiled with all required outputs
    fn ensureCompiled(self: *Session) !void {
        const current_version = self.graph.modifications_count;
        const graph_changed = self.compiled == null or current_version != self.last_compiled_version;
        const has_new_outputs = self.pending_outputs.count() > 0;

        // Only recompile if graph structure changed OR we have new output nodes
        if (graph_changed or has_new_outputs) {
            // First, update the graph's output nodes with ALL required outputs
            self.graph.output_nodes.clearRetainingCapacity();

            // Add all previously compiled outputs
            var iter = self.compiled_outputs.iterator();
            while (iter.next()) |entry| {
                try self.graph.output_nodes.append(self.graph.arena.allocator(), entry.key_ptr.*);
            }

            // Add all pending outputs
            var pending_iter = self.pending_outputs.iterator();
            while (pending_iter.next()) |entry| {
                const node_id = entry.key_ptr.*;
                try self.graph.output_nodes.append(self.graph.arena.allocator(), node_id);
                // Move from pending to compiled
                try self.compiled_outputs.put(self.allocator, node_id, {});
            }
            self.pending_outputs.clearRetainingCapacity();

            // Clean up existing compiled graph and executor
            if (self.executor) |exec| {
                exec.deinit();
                self.allocator.destroy(exec);
                self.executor = null;
            }
            if (self.compiled) |comp| {
                comp.deinit(self.allocator);
                self.allocator.destroy(comp);
                self.compiled = null;
            }

            // Compile with current graph state
            const compiled = try self.allocator.create(CompiledGraph);

            // Compile for the configured backend
            switch (self.backend_config.device) {
                .cpu => {
                    compiled.* = try compiler.compile.compileCpu(&self.graph, self.allocator);
                },
                else => {
                    std.log.err("Backend {} not yet implemented, falling back to CPU", .{self.backend_config.device});
                    compiled.* = try compiler.compile.compileCpu(&self.graph, self.allocator);
                },
            }

            self.compiled = compiled;
            self.executor = try self.allocator.create(Executor);
            self.executor.?.* = try Executor.init(self.allocator, compiled, null);

            // Update version tracking
            self.last_compiled_version = current_version;
        }
    }

    /// Ensure the graph is compiled and executed (for eager mode)
    fn ensureCompiledAndExecuted(self: *Session) !void {
        try self.ensureCompiled();
        // Note: Individual tensor operations will trigger execution as needed
    }

    /// Set data for a tensor node (internal helper)
    fn setTensorData(self: *Session, node_id: NodeId, data: anytype, shape: []const i64, dtype: DataType) !void {
        if (self.executor) |*exec| {
            const data_bytes = switch (@TypeOf(data)) {
                []const f32 => std.mem.sliceAsBytes(data),
                []const f64 => std.mem.sliceAsBytes(data),
                []const i32 => std.mem.sliceAsBytes(data),
                []const i64 => std.mem.sliceAsBytes(data),
                else => @compileError("Unsupported data type"),
            };
            try exec.setInput(node_id, data_bytes, shape, dtype);
        }
    }

    /// Extract scalar value from various data types
    fn extractScalar(data: anytype, _: DataType) f64 {
        return switch (@TypeOf(data)) {
            f32, f64 => @floatCast(data),
            i32, i64 => @floatFromInt(data),
            comptime_int => @floatFromInt(data),
            comptime_float => @floatCast(data),
            else => @compileError("Unsupported scalar type"),
        };
    }
};

/// SessionTensor represents a tensor within a Session context with method-based operations
pub const SessionTensor = struct {
    session: *Session,
    handle: TensorHandle,
    shape: []const i64,
    dtype: DataType,

    /// Element-wise addition
    pub fn add(self: SessionTensor, other: SessionTensor) !SessionTensor {
        if (self.session != other.session) {
            return SessionError.IncompatibleTensors;
        }

        const result_handle = try self.handle.add(other.handle);
        const result_shape = try inferResultShapeTracked(self.shape, other.shape, self.session);

        const result = SessionTensor{
            .session = self.session,
            .handle = result_handle,
            .shape = result_shape,
            .dtype = self.dtype,
        };

        try self.executeIfEager(result);
        return result;
    }

    /// Element-wise multiplication
    pub fn mul(self: SessionTensor, other: SessionTensor) !SessionTensor {
        if (self.session != other.session) {
            return SessionError.IncompatibleTensors;
        }

        const result_handle = try self.handle.mul(other.handle);
        const result_shape = try inferResultShapeTracked(self.shape, other.shape, self.session);

        const result = SessionTensor{
            .session = self.session,
            .handle = result_handle,
            .shape = result_shape,
            .dtype = self.dtype,
        };

        try self.executeIfEager(result);
        return result;
    }

    /// Element-wise subtraction
    pub fn subtract(self: SessionTensor, other: SessionTensor) !SessionTensor {
        if (self.session != other.session) {
            return SessionError.IncompatibleTensors;
        }

        const result_handle = try self.handle.subtract(other.handle);
        const result_shape = try inferResultShapeTracked(self.shape, other.shape, self.session);

        const result = SessionTensor{
            .session = self.session,
            .handle = result_handle,
            .shape = result_shape,
            .dtype = self.dtype,
        };

        try self.executeIfEager(result);
        return result;
    }

    /// Element-wise division
    pub fn divide(self: SessionTensor, other: SessionTensor) !SessionTensor {
        if (self.session != other.session) {
            return SessionError.IncompatibleTensors;
        }

        const result_handle = try self.handle.divide(other.handle);
        const result_shape = try inferResultShapeTracked(self.shape, other.shape, self.session);

        const result = SessionTensor{
            .session = self.session,
            .handle = result_handle,
            .shape = result_shape,
            .dtype = self.dtype,
        };

        try self.executeIfEager(result);
        return result;
    }

    /// Matrix multiplication
    pub fn matmul(self: SessionTensor, other: SessionTensor) !SessionTensor {
        if (self.session != other.session) {
            return SessionError.IncompatibleTensors;
        }

        const result_handle = try self.handle.matmul(other.handle);
        const result_shape = try inferMatmulShapeTracked(self.shape, other.shape, self.session);

        const result = SessionTensor{
            .session = self.session,
            .handle = result_handle,
            .shape = result_shape,
            .dtype = self.dtype,
        };

        try self.executeIfEager(result);
        return result;
    }

    /// ReLU activation
    pub fn relu(self: SessionTensor) !SessionTensor {
        const result_handle = try self.handle.relu();
        const result_shape = try self.session.allocator.dupe(i64, self.shape);
        try self.session.allocated_shapes.append(self.session.allocator, result_shape);

        const result = SessionTensor{
            .session = self.session,
            .handle = result_handle,
            .shape = result_shape,
            .dtype = self.dtype,
        };

        try self.executeIfEager(result);
        return result;
    }

    /// Reshape tensor to new shape
    pub fn reshape(self: SessionTensor, new_shape: []const i64) !SessionTensor {
        const result_handle = try self.handle.reshape(new_shape);
        const result_shape = try self.session.allocator.dupe(i64, new_shape);
        try self.session.allocated_shapes.append(self.session.allocator, result_shape);

        const result = SessionTensor{
            .session = self.session,
            .handle = result_handle,
            .shape = result_shape,
            .dtype = self.dtype,
        };

        try self.executeIfEager(result);
        return result;
    }

    /// Get the computed data as a slice of the specified type
    pub fn data(self: SessionTensor, comptime T: type) ![]const T {
        // Resolve current node ID in case of substitutions during recompilation
        const current_node_id = self.handle.getCurrentNodeId();

        // Check if this node is already a compiled output
        const already_compiled = self.session.compiled_outputs.contains(current_node_id);

        // Only mark as pending if not already compiled
        if (!already_compiled) {
            try self.session.pending_outputs.put(self.session.allocator, current_node_id, {});
        }

        // Ensure compilation and execution are up to date
        try self.session.run();

        if (self.session.executor == null) {
            return SessionError.NotExecuted;
        }

        const output = try self.session.executor.?.getOutput(current_node_id);
        const typed_slice = @as([*]const T, @ptrCast(@alignCast(output.data.ptr)));
        const element_count = @divExact(output.data.len, @sizeOf(T));

        return typed_slice[0..element_count];
    }

    /// Copy the tensor data into a new slice owned by the caller
    pub fn copy(self: SessionTensor, comptime T: type, allocator: Allocator) ![]T {
        const source_data = try self.data(T);
        return try allocator.dupe(T, source_data);
    }

    /// Get tensor rank (number of dimensions)
    pub fn rank(self: SessionTensor) usize {
        return self.shape.len;
    }

    /// Get total number of elements
    pub fn size(self: SessionTensor) i64 {
        var total: i64 = 1;
        for (self.shape) |dim| {
            total *= dim;
        }
        return total;
    }

    /// Execute in eager mode if needed
    fn executeIfEager(self: SessionTensor, result: SessionTensor) !void {
        if (self.session.mode == .eager) {
            // In eager mode, mark the result as a pending output
            // This elegant approach avoids immediate compilation while ensuring
            // the node will be included when data is eventually requested
            const node_id = result.handle.getCurrentNodeId();
            if (!self.session.compiled_outputs.contains(node_id)) {
                try self.session.pending_outputs.put(self.session.allocator, node_id, {});
            }
        }
    }
};

/// Infer the result shape for element-wise operations (broadcasting) with memory tracking
fn inferResultShapeTracked(shape1: []const i64, shape2: []const i64, session: *Session) ![]const i64 {
    // Simple broadcasting: if shapes are the same, return the same shape
    // For more complex broadcasting, we'd need to implement full broadcasting rules
    if (shape1.len == shape2.len) {
        for (shape1, shape2) |dim1, dim2| {
            if (dim1 != dim2 and dim1 != 1 and dim2 != 1) {
                return SessionError.IncompatibleTensors;
            }
        }
        // Return the larger dimensions
        const result = try session.allocator.alloc(i64, shape1.len);
        try session.allocated_shapes.append(session.allocator, result);
        for (shape1, shape2, 0..) |dim1, dim2, i| {
            result[i] = @max(dim1, dim2);
        }
        return result;
    } else {
        // For now, just return the first shape (simplified)
        const result = try session.allocator.dupe(i64, shape1);
        try session.allocated_shapes.append(session.allocator, result);
        return result;
    }
}

/// Infer the result shape for matrix multiplication with memory tracking
fn inferMatmulShapeTracked(shape1: []const i64, shape2: []const i64, session: *Session) ![]const i64 {
    if (shape1.len < 2 or shape2.len < 2) {
        return SessionError.InvalidShape;
    }

    // For 2D case: [M, K] @ [K, N] = [M, N]
    if (shape1.len == 2 and shape2.len == 2) {
        if (shape1[1] != shape2[0]) {
            return SessionError.IncompatibleTensors;
        }
        const result = try session.allocator.alloc(i64, 2);
        try session.allocated_shapes.append(session.allocator, result);
        result[0] = shape1[0];
        result[1] = shape2[1];
        return result;
    }

    // For higher dimensions, implement batch matmul logic
    // FIX: For now, return simplified result
    const result = try session.allocator.dupe(i64, shape1);
    try session.allocated_shapes.append(session.allocator, result);
    return result;
}

// ===== Tests =====

test "Session - basic creation and destruction" {
    const testing = std.testing;

    var session = try Session.init(testing.allocator);
    defer session.deinit();

    try testing.expect(session.mode == .eager);
    try testing.expect(session.compiled == null);
    try testing.expect(session.executor == null);
}

test "Session - tensor creation" {
    const testing = std.testing;

    var session = try Session.init(testing.allocator);
    defer session.deinit();

    // Test zeros
    const zeros_tensor = try session.zeros(&.{ 2, 3 }, .f32);
    try testing.expectEqual(@as(usize, 2), zeros_tensor.rank());
    try testing.expectEqual(@as(i64, 6), zeros_tensor.size());

    // Test ones
    const ones_tensor = try session.ones(&.{ 3, 2 }, .f32);
    try testing.expectEqual(@as(usize, 2), ones_tensor.rank());
    try testing.expectEqual(@as(i64, 6), ones_tensor.size());
}

test "Session - execution modes" {
    const testing = std.testing;

    var session = try Session.init(testing.allocator);
    defer session.deinit();

    // Default mode
    try testing.expect(session.getMode() == .eager);

    // Change mode
    session.setMode(.lazy);
    try testing.expect(session.getMode() == .lazy);

    session.setMode(.eager);
    try testing.expect(session.getMode() == .eager);
}

test "SessionTensor - shape and size operations" {
    const testing = std.testing;

    var session = try Session.init(testing.allocator);
    defer session.deinit();

    const tensor = try session.zeros(&.{ 2, 3, 4 }, .f32);

    try testing.expectEqual(@as(usize, 3), tensor.rank());
    try testing.expectEqual(@as(i64, 24), tensor.size());
    try testing.expectEqual(@as(i64, 2), tensor.shape[0]);
    try testing.expectEqual(@as(i64, 3), tensor.shape[1]);
    try testing.expectEqual(@as(i64, 4), tensor.shape[2]);
}
