/// Test Runner - Comprehensive Test Suite
///
/// This module imports and runs tests from all major components of the Zing tensor library.
/// It serves as the main entry point for the comprehensive test suite.

const std = @import("std");

// Core modules
const types = @import("types");
const graph = @import("graph");
const shape = @import("shape");
const symbolic = @import("symbolic");
const storage = @import("storage");
const backends = @import("backends");
const execution = @import("execution");
const compiler = @import("compiler");
const tensor = @import("tensor");
const session = @import("session");
const training = @import("training");

// Note: All test files are run independently via build.zig
// This file only contains basic smoke tests to verify core modules work together

test "Core modules compile and basic functionality works" {
    const testing = std.testing;
    
    // Test that all modules can be imported and basic types work
    _ = types.DataType.f32;
    _ = types.NodeId;
    
    // Test that session module works
    var sess = try session.Session.init(testing.allocator);
    defer sess.deinit();
    
    try testing.expect(sess.getMode() == .eager);
}


// Basic smoke tests for all core modules
test "Types module" {
    const testing = std.testing;
    
    // Test basic type creation
    const dtype = types.DataType.f32;
    try testing.expect(dtype == .f32);
    
    const node_id: types.NodeId = 1;
    try testing.expectEqual(@as(u32, 1), node_id);
}

test "Graph module basic functionality" {
    const testing = std.testing;
    
    var test_graph = try graph.Graph.init(testing.allocator);
    defer test_graph.deinit();
    
    try testing.expectEqual(@as(usize, 0), test_graph.nodes.items.len);
}

test "Session module integration" {
    const testing = std.testing;
    
    var sess = try session.Session.init(testing.allocator);
    defer sess.deinit();
    
    // Test basic tensor creation
    const zeros_tensor = try sess.zeros(&.{2, 3}, .f32);
    try testing.expectEqual(@as(usize, 2), zeros_tensor.rank());
    try testing.expectEqual(@as(i64, 6), zeros_tensor.size());
    
    const ones_tensor = try sess.ones(&.{3, 2}, .f32);
    try testing.expectEqual(@as(usize, 2), ones_tensor.rank());
    try testing.expectEqual(@as(i64, 6), ones_tensor.size());
}

test "Training module basic functionality" {
    const testing = std.testing;
    
    // Test that training module exports are accessible
    _ = training.loss;
    _ = training.optimizer;
    _ = training.scheduler;
    _ = training.trainer;
    // autograd is now a compiler pass, not a separate module
    
    // Test basic loss function
    var g = try graph.Graph.init(testing.allocator);
    defer g.deinit();
    
    const pred = try tensor.constant(&g, 1.0, .f32);
    const target = try tensor.constant(&g, 1.5, .f32);
    
    const mse = try training.loss.mseLoss(pred, target);
    try testing.expect(mse.node_id != 0);
}