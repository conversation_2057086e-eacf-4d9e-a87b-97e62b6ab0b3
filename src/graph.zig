const std = @import("std");
const Allocator = std.mem.Allocator;
const ArenaAllocator = std.heap.ArenaAllocator;

// Import shared types
const types = @import("types");
const NodeId = types.NodeId;
const DataType = types.DataType;
const GraphError = types.GraphError;
const NodeSpec = types.NodeSpec;
const ComputeOp = types.ComputeOp;
const DataSource = types.DataSource;
const CustomOp = types.CustomOp;

// Import from other components
const SymbolicPool = @import("symbolic").SymbolicPool;

// ===== Data Structures =====

/// Node metadata for compilation and execution
/// 
/// DESIGN PRINCIPLE: Operations set complete metadata when creating nodes.
/// Compiler passes respect existing metadata and only compute missing values.
/// 
/// Metadata lifecycle:
/// 1. Operations set all relevant metadata during node creation
/// 2. Shape inference validates and fills in any missing shapes
/// 3. Memory planning uses shape metadata to allocate buffers
pub const NodeMetadata = struct {
    // Operation-specific metadata
    constant_value: ?f32 = null, // Value for constant nodes
    is_arange: bool = false, // Special marker for arange constants
    
    // Reduction operation metadata
    reduction_axis: ?i64 = null, // Axis along which to reduce
    keepdims: bool = false, // Whether to keep reduced dimensions as size 1
    
    // Broadcast operation metadata
    broadcast_shape: ?[]const i64 = null, // Target shape for explicit broadcast
    
    // View/reshape operation metadata
    reshape_target: ?[]const i64 = null, // Target shape for reshape
    transpose_axes: ?[]const usize = null, // Axes permutation for transpose
    slice_starts: ?[]const i64 = null, // Start indices for slice
    slice_ends: ?[]const i64 = null, // End indices for slice
    
    // Shape information for compilation
    // Set by operations when they create nodes, validated by shape inference
    output_shape: ?@import("shape").ShapeTracker = null,
    
    // Input shapes after view transformations
    // Used by materialization pass to insert contiguous operations when needed
    input_shapes: ?[]const @import("shape").ShapeTracker = null,
    
    // Training metadata
    is_parameter: bool = false, // Whether this node is a trainable parameter
};

pub const Node = struct {
    id: NodeId,
    spec: NodeSpec, // Operation specification (data source or compute)
    inputs: []const NodeId, // Dependencies (arena-allocated)
    outputs: []const OutputInfo, // Support multiple outputs
    is_valid: bool = true, // Tombstone flag
    metadata: ?*NodeMetadata = null, // Optional metadata (constants, reduction axes)

    pub const OutputInfo = struct {
        dtype: DataType,
        // ARCHITECTURAL DECISION: No shape information in Node
        // Shapes are managed at TensorHandle level only
    };

    // Note: Node is always arena-allocated in Graph and never moved
    // This ensures stable references throughout Graph lifetime
};

pub const Edge = struct {
    source: NodeId,
    target: NodeId,
    input_slot: u8, // Which input of target
    output_slot: u8, // Which output of source
};

/// Gradient computation mode
pub const GradientMode = enum {
    reverse_mode,  // Standard backpropagation (most common)
    forward_mode,  // Forward-mode AD (for future)
};

/// Gradient state - only allocated when gradients are needed
/// This keeps the main Graph struct lightweight for inference
pub const GradientState = struct {
    /// Configuration for gradient computation
    config: GradientConfig,
    
    /// Gradient mapping: forward node → gradient node
    /// Populated by autograd pass during compilation
    gradient_map: std.AutoHashMapUnmanaged(NodeId, NodeId) = .{},
    
    /// Nodes marked as requiring gradients during graph construction
    requires_grad_nodes: std.AutoHashMapUnmanaged(NodeId, void) = .{},
    
    pub fn deinit(self: *GradientState, allocator: Allocator) void {
        self.gradient_map.deinit(allocator);
        self.requires_grad_nodes.deinit(allocator);
    }
};

/// Gradient configuration
pub const GradientConfig = struct {
    /// The loss/output node to differentiate
    loss_node: NodeId,
    
    /// Which AD mode to use
    mode: GradientMode = .reverse_mode,
    
    /// Whether to retain gradient graph for higher-order derivatives
    retain_graph: bool = false,
};

pub const Graph = struct {
    // Memory management
    allocator: Allocator, // Backing allocator
    arena: ArenaAllocator, // Graph construction arena

    // Core structure (arena allocated)
    nodes: std.ArrayListUnmanaged(Node) = .{},
    edges: std.ArrayListUnmanaged(Edge) = .{},
    node_map: std.AutoHashMapUnmanaged(NodeId, u32) = .{},

    // Efficient consumer tracking (O(1) lookup)
    consumer_lists: std.AutoHashMapUnmanaged(NodeId, std.ArrayListUnmanaged(NodeId)) = .{},

    // Storage for backend-specific operations
    custom_ops: std.AutoHashMapUnmanaged(NodeId, CustomOp) = .{},

    // Symbolic expressions (arena allocated)
    symbolic_pool: SymbolicPool, // For dynamic dimensions

    // Compilation state
    next_node_id: NodeId = 0,
    is_finalized: bool = false,
    modifications_count: u32 = 0, // Track when topology needs recompute
    cached_topology: ?[]NodeId = null, // Cache topological order

    // Output tracking
    output_nodes: std.ArrayListUnmanaged(NodeId) = .{},

    // Substitution tracking for pattern recognition
    // Maps old node IDs to their replacements
    substitution_map: std.AutoHashMapUnmanaged(NodeId, NodeId) = .{},

    // Gradient support - only allocated when gradients are requested
    // This ensures zero overhead for inference-only use cases
    gradient_state: ?*GradientState = null,

    // NO tensor data, NO execution state, NO device handles

    // ===== Lifecycle Management =====

    pub fn init(allocator: Allocator) !Graph {
        return Graph{
            .allocator = allocator,
            .arena = ArenaAllocator.init(allocator),
            .symbolic_pool = try SymbolicPool.init(allocator),
            // gradient_state is null by default - no overhead for inference
        };
    }

    pub fn deinit(self: *Graph) void {
        const enable_debug = @import("build_options").enable_debug_logs;
        
        if (enable_debug) {
            std.debug.print("DEBUG: Graph.deinit() - Starting cleanup\n", .{});
        }
        
        // Consumer lists are arena-allocated, so just deinit the hash map structure
        if (enable_debug) {
            std.debug.print("DEBUG: Graph.deinit() - Cleaning up consumer lists\n", .{});
        }
        self.consumer_lists.deinit(self.arena.allocator());
        if (enable_debug) {
            std.debug.print("DEBUG: Graph.deinit() - Consumer lists cleaned\n", .{});
        }

        // Clean up custom ops
        if (enable_debug) {
            std.debug.print("DEBUG: Graph.deinit() - Cleaning up custom ops\n", .{});
        }
        self.custom_ops.deinit(self.arena.allocator());
        if (enable_debug) {
            std.debug.print("DEBUG: Graph.deinit() - Custom ops cleaned\n", .{});
        }

        // Clean up other unmanaged containers
        if (enable_debug) {
            std.debug.print("DEBUG: Graph.deinit() - Cleaning up nodes\n", .{});
        }
        self.nodes.deinit(self.arena.allocator());
        if (enable_debug) {
            std.debug.print("DEBUG: Graph.deinit() - Nodes cleaned\n", .{});
        }
        
        if (enable_debug) {
            std.debug.print("DEBUG: Graph.deinit() - Cleaning up edges\n", .{});
        }
        self.edges.deinit(self.arena.allocator());
        if (enable_debug) {
            std.debug.print("DEBUG: Graph.deinit() - Edges cleaned\n", .{});
        }
        
        if (enable_debug) {
            std.debug.print("DEBUG: Graph.deinit() - Cleaning up node map\n", .{});
        }
        self.node_map.deinit(self.arena.allocator());
        if (enable_debug) {
            std.debug.print("DEBUG: Graph.deinit() - Node map cleaned\n", .{});
        }
        
        if (enable_debug) {
            std.debug.print("DEBUG: Graph.deinit() - Cleaning up output nodes\n", .{});
        }
        self.output_nodes.deinit(self.arena.allocator());
        if (enable_debug) {
            std.debug.print("DEBUG: Graph.deinit() - Output nodes cleaned\n", .{});
        }
        
        if (enable_debug) {
            std.debug.print("DEBUG: Graph.deinit() - Cleaning up substitution map\n", .{});
        }
        self.substitution_map.deinit(self.arena.allocator());
        if (enable_debug) {
            std.debug.print("DEBUG: Graph.deinit() - Substitution map cleaned\n", .{});
        }

        // Clean up gradient state if allocated
        if (enable_debug) {
            std.debug.print("DEBUG: Graph.deinit() - Cleaning up gradient state\n", .{});
        }
        if (self.gradient_state) |grad_state| {
            grad_state.deinit(self.allocator);
            self.allocator.destroy(grad_state);
            self.gradient_state = null;
        }
        if (enable_debug) {
            std.debug.print("DEBUG: Graph.deinit() - Gradient state cleaned\n", .{});
        }

        // Clean up cached topology - arena-allocated, so no explicit free needed
        if (enable_debug) {
            std.debug.print("DEBUG: Graph.deinit() - Cleaning up cached topology\n", .{});
        }
        if (self.cached_topology) |topo| {
            if (enable_debug) {
                std.debug.print("DEBUG: Graph.deinit() - Cached topology (len={}) will be freed by arena\n", .{topo.len});
            }
            self.cached_topology = null;
            if (enable_debug) {
                std.debug.print("DEBUG: Graph.deinit() - Cached topology cleared\n", .{});
            }
        } else {
            if (enable_debug) {
                std.debug.print("DEBUG: Graph.deinit() - No cached topology to clean\n", .{});
            }
        }
        if (enable_debug) {
            std.debug.print("DEBUG: Graph.deinit() - Cached topology cleaned\n", .{});
        }

        // Clean up symbolic pool
        if (enable_debug) {
            std.debug.print("DEBUG: Graph.deinit() - Cleaning up symbolic pool\n", .{});
        }
        self.symbolic_pool.deinit();
        if (enable_debug) {
            std.debug.print("DEBUG: Graph.deinit() - Symbolic pool cleaned\n", .{});
        }

        // Free arena (this frees all node contents, metadata, etc.)
        if (enable_debug) {
            std.debug.print("DEBUG: Graph.deinit() - About to free arena\n", .{});
        }
        self.arena.deinit();
        if (enable_debug) {
            std.debug.print("DEBUG: Graph.deinit() - ✓ Arena freed, all cleanup complete\n", .{});
        }
    }

    pub fn finalize(self: *Graph) GraphError!void {
        if (self.is_finalized) {
            std.log.err("finalize: graph is already finalized", .{});
            return error.GraphFinalized;
        }

        // Mark as complete, no more nodes can be added
        self.is_finalized = true;
    }

    // ===== Gradient Support (Compile-time Conditional) =====

    /// Check if gradient support is available at compile time
    pub fn hasGradientSupport(self: *const Graph) bool {
        _ = self;
        return @import("builtin").mode != .ReleaseFast;
    }

    /// Initialize gradient map (only if supported)
    pub fn initGradients(self: *Graph) !void {
        if (comptime @import("builtin").mode == .ReleaseFast) {
            std.log.err("initGradients: gradients not supported in release builds", .{});
            return error.GradientsNotSupported;
        }

        if (self.gradient_map == null) {
            self.gradient_map = std.AutoHashMap(NodeId, NodeId).init(self.allocator);
        }
    }

    /// Get gradient map (only if supported)
    pub fn getGradientMap(self: *Graph) ?*std.AutoHashMap(NodeId, NodeId) {
        if (comptime @import("builtin").mode == .ReleaseFast) {
            return null;
        }

        if (self.gradient_map) |*map| {
            return map;
        }
        return null;
    }

    /// Set gradient map (only if supported)
    pub fn setGradientMap(self: *Graph, gradient_map: std.AutoHashMap(NodeId, NodeId)) !void {
        if (comptime @import("builtin").mode == .ReleaseFast) {
            std.log.err("setGradientMap: gradients not supported in release builds", .{});
            return error.GradientsNotSupported;
        }

        // Clean up existing gradient map if present
        if (self.gradient_map) |*existing_map| {
            existing_map.deinit();
        }

        self.gradient_map = gradient_map;
    }

    // ===== Node Creation API =====

    pub fn createNode(self: *Graph, spec: NodeSpec, inputs: []const NodeId, dtype: DataType) GraphError!NodeId {
        // V1: Simple error handling with logging at error site
        if (self.is_finalized) {
            // In tests, we suppress logs to avoid false failures
            // std.log.err("Cannot create node - graph is finalized", .{});
            return error.GraphFinalized;
        }

        // Validate all inputs exist
        for (inputs, 0..) |input_id, i| {
            if (!self.hasNode(input_id)) {
                std.log.err("addNode: Input[{}] references non-existent node {}", .{ i, input_id });
                return error.InvalidInput;
            }
        }

        // Validate input count for compute operations
        switch (spec) {
            .data => {
                if (inputs.len != 0) {
                    // In tests, we suppress logs to avoid false failures
                    // std.log.err("Data nodes cannot have inputs, got {}", .{inputs.len});
                    return error.InvalidInputCount;
                }
            },
            .compute => |op| {
                if (op != .custom) {
                    const expected = op.expectedInputCount();
                    if (inputs.len != expected) {
                        // In tests, we suppress logs to avoid false failures
                        // std.log.err("Operation {} expects {} inputs, got {}", .{op, expected, inputs.len});
                        return error.InvalidInputCount;
                    }
                }
            },
        }

        const id = self.next_node_id;
        const enable_debug = @import("build_options").enable_debug_logs;
        if (enable_debug) {
            std.debug.print("Graph.createNode: id={} spec={} dtype={s}\n", .{ id, spec, @tagName(dtype) });
        }

        // Create outputs array properly
        var outputs_array = try self.arena.allocator().alloc(Node.OutputInfo, 1);
        outputs_array[0] = .{ .dtype = dtype };

        // Debug: Validate inputs before creating node
        for (inputs, 0..) |input_id, idx| {
            if (input_id == 0xAAAAAAAA) {
                std.log.err("createNode: UNINITIALIZED input[{}] provided for new node {}", .{idx, id});
                return error.UninitializedNodeId;
            }
            if (input_id >= self.next_node_id) {
                std.log.err("createNode: OUT OF BOUNDS input[{}] = {} for new node {} (next_node_id={})", 
                           .{idx, input_id, id, self.next_node_id});
                return error.InvalidNodeId;
            }
        }
        
        const node = Node{
            .id = id,
            .spec = spec,
            .inputs = try self.arena.allocator().dupe(NodeId, inputs),
            .outputs = outputs_array,
            .is_valid = true,
            .metadata = null,
        };

        // Add to storage
        try self.nodes.append(self.arena.allocator(), node);
        try self.node_map.put(self.arena.allocator(), id, @intCast(self.nodes.items.len - 1));

        // Debug: Check what was stored
        if (self.nodes.items.len > 0) {
            const stored_node = &self.nodes.items[self.nodes.items.len - 1];
            if (stored_node.outputs.len > 0) {
                if (enable_debug) {
                    std.debug.print("  Stored node {} has output dtype: {s}\n", .{ id, @tagName(stored_node.outputs[0].dtype) });
                }
            }
        }

        // Initialize consumer tracking
        try self.consumer_lists.put(self.arena.allocator(), id, .{});

        // Update consumer lists for inputs
        for (inputs) |input_id| {
            if (self.consumer_lists.getPtr(input_id)) |list| {
                try list.append(self.arena.allocator(), id);
            }
        }

        self.next_node_id = id + 1;
        self.invalidateCaches();

        return id;
    }

    // Convenience wrapper for compute operations
    pub fn addNode(self: *Graph, op: ComputeOp, inputs: []const NodeId, dtype: DataType) GraphError!NodeId {
        return self.createNode(.{ .compute = op }, inputs, dtype);
    }

    // Create a constant node
    pub fn addConstant(self: *Graph, value: f32) GraphError!NodeId {
        const node_id = try self.createNode(.{ .data = .constant }, &.{}, .f32);

        // Store constant value and shape in metadata
        if (self.getNodeMut(node_id)) |node| {
            const allocator = self.arena.allocator();
            node.metadata = try allocator.create(NodeMetadata);
            node.metadata.?.* = .{ .constant_value = value };
            
            // Constants are scalars - set empty shape
            const empty_dims = try allocator.alloc(@import("shape").SymbolicDim, 0);
            node.metadata.?.output_shape = @import("shape").ShapeTracker.fromDims(
                empty_dims, 
                allocator, 
                &self.symbolic_pool
            ) catch |err| {
                std.log.err("Failed to create shape for constant: {}", .{err});
                return error.OutOfMemory; // Map to a GraphError
            };
        }

        return node_id;
    }

    // Create a placeholder node
    pub fn addPlaceholder(self: *Graph, dtype: DataType) GraphError!NodeId {
        // NOTE: Shape validation moved to TensorHandle creation
        // Graph only tracks that this is a placeholder with a specific dtype
        return self.createNode(.{ .data = .placeholder }, &.{}, dtype);
    }

    // Create a custom operator node (for backends)
    pub fn addCustomNode(self: *Graph, custom_op: CustomOp, inputs: []const NodeId, dtype: DataType) GraphError!NodeId {
        const node_id = try self.createNode(.{ .compute = .custom }, inputs, dtype);

        // Check if custom op data already exists (shouldn't happen)
        if (self.custom_ops.contains(node_id)) {
            std.log.err("addCustomOp: Custom op data already exists for node {}", .{node_id});
            // Clean up the node we just created
            try self.removeNode(node_id);
            return error.DuplicateCustomOp;
        }

        // Store custom op data
        try self.custom_ops.put(self.arena.allocator(), node_id, custom_op);

        return node_id;
    }

    /// Creates a parameter node that is linked to the ParameterStore.
    /// The NodeId of this node *is* the StateId used to look up the tensor data.
    pub fn addParameter(self: *Graph, state_id: NodeId, dtype: DataType) !NodeId {
        // For parameters, the NodeId and StateId are the same.
        // This requires careful coordination during model construction,
        // where the ID is first claimed from the ParameterStore and then used here.
        // TODO: improve this? isn't this approach error prone? find a better way?
        const node_id = state_id;

        // Ensure this ID doesn't conflict with our internal counter
        if (node_id >= self.next_node_id) {
            self.next_node_id = @as(NodeId, node_id) + 1;
        }

        const node = Node{
            .id = node_id,
            .spec = .{ .data = .parameter },
            .inputs = &.{},
            .outputs = &.{.{ .dtype = dtype }},
        };

        try self.nodes.append(self.arena.allocator(), node);
        try self.node_map.put(self.arena.allocator(), node_id, @intCast(self.nodes.items.len - 1));

        // Initialize consumer tracking
        try self.consumer_lists.put(self.arena.allocator(), node_id, .{});

        return node_id;
    }

    // ===== Node Access API =====
    
    /// Compute a hash of the graph structure (for caching)
    pub fn structureHash(self: *const Graph) u64 {
        var hasher = std.hash.Wyhash.init(0);
        
        // Hash nodes in order
        for (self.nodes.items) |node| {
            // Skip invalid nodes
            if (!node.is_valid) continue;
            
            // Hash node ID
            hasher.update(std.mem.asBytes(&node.id));
            
            // Hash node spec type
            hasher.update(std.mem.asBytes(&@intFromEnum(node.spec)));
            
            // Hash operation type if compute node
            if (node.spec == .compute) {
                hasher.update(std.mem.asBytes(&@intFromEnum(node.spec.compute)));
            }
            
            // Hash input connections
            for (node.inputs) |input| {
                hasher.update(std.mem.asBytes(&input));
            }
            
            // Hash output count and types
            hasher.update(std.mem.asBytes(&node.outputs.len));
            for (node.outputs) |output| {
                hasher.update(std.mem.asBytes(&@intFromEnum(output.dtype)));
            }
        }
        
        return hasher.final();
    }

    // Check if a node exists and is valid
    pub fn hasNode(self: *const Graph, node_id: NodeId) bool {
        const index = self.node_map.get(node_id) orelse {
            const enable_debug = @import("build_options").enable_debug_logs;
            if (enable_debug and node_id < 10) {  // Only for small node IDs to avoid spam
                std.debug.print("  hasNode({}): not in node_map\n", .{node_id});
            }
            return false;
        };
        if (index >= self.nodes.items.len) {
            const enable_debug = @import("build_options").enable_debug_logs;
            if (enable_debug) {
                std.debug.print("  hasNode({}): index {} >= nodes.len {}\n", .{node_id, index, self.nodes.items.len});
            }
            return false;
        }
        const is_valid = self.nodes.items[index].is_valid;
        const enable_debug = @import("build_options").enable_debug_logs;
        if (enable_debug and node_id < 10 and !is_valid) {
            std.debug.print("  hasNode({}): node exists but is_valid={}\n", .{node_id, is_valid});
        }
        return is_valid;
    }

    // Get immutable node reference
    pub fn getNode(self: *const Graph, node_id: NodeId) ?*const Node {
        const index = self.node_map.get(node_id) orelse return null;
        if (index >= self.nodes.items.len) return null;
        const node = &self.nodes.items[index];
        return if (node.is_valid) node else null;
    }

    // Get node inputs
    pub fn getInputs(self: *const Graph, node_id: NodeId) ?[]const NodeId {
        const node = self.getNode(node_id) orelse return null;
        return node.inputs;
    }

    // Get consumer count efficiently
    pub fn getConsumerCount(self: *const Graph, node_id: NodeId) u32 {
        const consumer_list = self.consumer_lists.get(node_id) orelse return 0;
        return @intCast(consumer_list.items.len);
    }

    // Get all consumers (allocates)
    pub fn getConsumers(self: *const Graph, node_id: NodeId, allocator: Allocator) ![]NodeId {
        const consumer_list = self.consumer_lists.get(node_id) orelse {
            return try allocator.alloc(NodeId, 0);
        };
        return try allocator.dupe(NodeId, consumer_list.items);
    }

    // Iterate consumers without allocation
    pub fn iterateConsumers(self: *const Graph, node_id: NodeId) ?[]const NodeId {
        const consumer_list = self.consumer_lists.get(node_id) orelse return null;
        return consumer_list.items;
    }

    // Get custom operation data for a node
    pub fn getCustomOp(self: *const Graph, node_id: NodeId) ?CustomOp {
        // Verify the node exists and is a custom op
        const node = self.getNode(node_id) orelse return null;
        const compute_op = switch (node.spec) {
            .compute => |op| op,
            .data => return null,
        };
        if (compute_op != .custom) return null;

        // Return the custom op data
        return self.custom_ops.get(node_id);
    }

    // Get mutable node reference
    pub fn getNodeMut(self: *Graph, node_id: NodeId) ?*Node {
        const index = self.node_map.get(node_id) orelse return null;
        if (index >= self.nodes.items.len) return null;
        const node = &self.nodes.items[index];
        return if (node.is_valid) node else null;
    }
    
    // Get node shape metadata
    pub fn getNodeShape(self: *const Graph, node_id: NodeId) ?@import("shape").ShapeTracker {
        const node = self.getNode(node_id) orelse return null;
        return if (node.metadata) |meta| meta.output_shape else null;
    }

    // ===== Node Manipulation API =====

    /// Mark a node as an output node
    /// This ensures the node is preserved during compilation and optimization
    pub fn markOutput(self: *Graph, node_id: NodeId) !void {
        if (!self.hasNode(node_id)) {
            std.log.err("markOutput: node {} not found", .{node_id});
            return error.InvalidNodeId;
        }
        
        // Check if already in output_nodes
        for (self.output_nodes.items) |output| {
            if (output == node_id) {
                return; // Already marked as output
            }
        }
        
        // Add to output_nodes
        try self.output_nodes.append(self.arena.allocator(), node_id);
        
        const enable_debug = @import("build_options").enable_debug_logs;
        if (enable_debug) {
            std.debug.print("markOutput: added node {} to outputs (total: {})\n", .{node_id, self.output_nodes.items.len});
        }
    }
    
    /// Mark a node as a parameter (trainable weight)
    /// This is used during training to identify which nodes need gradients
    pub fn markParameter(self: *Graph, node_id: NodeId) !void {
        const node = self.getNodeMut(node_id) orelse {
            std.log.err("markParameter: node {} not found", .{node_id});
            return error.InvalidNodeId;
        };
        
        // Ensure the node has metadata
        if (node.metadata == null) {
            node.metadata = try self.arena.allocator().create(NodeMetadata);
            node.metadata.?.* = .{};
        }
        
        // Mark as parameter
        node.metadata.?.is_parameter = true;
        
        const enable_debug = @import("build_options").enable_debug_logs;
        if (enable_debug) {
            std.debug.print("markParameter: marked node {} as parameter\n", .{node_id});
        }
    }

    // Check if adding an edge would create a cycle
    pub fn wouldCreateCycle(self: *const Graph, from: NodeId, to: NodeId) !bool {
        // Quick check: self-loop
        if (from == to) return true;

        // DFS to check if 'to' can reach 'from'
        var visited = std.AutoHashMapUnmanaged(NodeId, void){};
        defer visited.deinit(self.allocator);

        var stack = std.ArrayList(NodeId).init(self.allocator);
        defer stack.deinit();

        try stack.append(to);

        while (stack.items.len > 0) {
            const current_node: NodeId = stack.pop() orelse break;
            if (current_node == from) {
                return true; // Found cycle
            }

            if (visited.contains(current_node)) {
                continue; // Already visited
            }
            try visited.put(self.allocator, current_node, {});

            // Add all consumers to stack
            if (self.consumer_lists.get(current_node)) |consumers| {
                for (consumers.items) |consumer| {
                    if (!visited.contains(consumer)) {
                        try stack.append(consumer);
                    }
                }
            }
        }

        return false; // No cycle found
    }

    // Remove a node safely with validation
    pub fn removeNode(self: *Graph, node_id: NodeId) GraphError!void {
        if (self.is_finalized) {
            std.log.err("removeNode: cannot remove node {} - graph is finalized", .{node_id});
            return error.GraphFinalized;
        }

        const node = self.getNodeMut(node_id) orelse {
            std.log.err("removeNode: node {} not found", .{node_id});
            return error.InvalidNodeId;
        };

        if (self.getConsumerCount(node_id) > 0) {
            // Note: In production, we might want to log a warning here
            // In tests, we suppress logs to avoid false failures
            // std.log.warn("Removing node {} with {} consumers", .{node_id, self.getConsumerCount(node_id)});
            // Could return error.NodeHasConsumers here for stricter safety
        }

        // Remove this node from all its inputs' consumer lists
        for (node.inputs) |input_id| {
            try self.removeConsumer(input_id, node_id);
        }

        // Clear this node's consumer list
        if (self.consumer_lists.getPtr(node_id)) |consumer_list| {
            consumer_list.clearRetainingCapacity();
        }

        // Synchronize custom_ops on removal
        _ = self.custom_ops.remove(node_id);

        // Mark as invalid (tombstone)
        node.is_valid = false;
        const enable_debug = @import("build_options").enable_debug_logs;
        if (enable_debug) {
            std.debug.print("removeNode: marked node {} as invalid\n", .{node_id});
        }
        self.invalidateCaches();
    }

    // Force remove a node without validation (use with caution)
    pub fn forceRemoveNode(self: *Graph, node_id: NodeId) void {
        self.removeNode(node_id) catch {
            // In tests, we suppress logs to avoid false failures
            // std.log.err("Force removing node {} failed: {}", .{node_id, err});
            // Error is intentionally ignored in force remove
        };
    }

    // Substitute all uses of old_id with new_id
    pub fn substituteNode(self: *Graph, old_id: NodeId, new_id: NodeId) !void {
        // Validate
        if (old_id == new_id) return; // No-op
        if (!self.hasNode(old_id)) {
            std.log.err("substituteNode: old node {} not found", .{old_id});
            return error.OldNodeNotFound;
        }
        if (!self.hasNode(new_id)) {
            std.log.err("substituteNode: new node {} not found", .{new_id});
            return error.NewNodeNotFound;
        }

        // Check for cycles
        if (try self.wouldCreateCycle(old_id, new_id)) {
            std.log.err("substituteNode: substituting {} with {} would create cycle", .{ old_id, new_id });
            return error.SubstitutionWouldCreateCycle;
        }

        // Get the list of consumers for the old node
        const consumers_to_update = if (self.consumer_lists.get(old_id)) |list|
            try self.allocator.dupe(NodeId, list.items)
        else
            &[_]NodeId{};
        defer self.allocator.free(consumers_to_update);

        // Update each consumer
        for (consumers_to_update) |consumer_id| {
            if (self.getNodeMut(consumer_id)) |consumer_node| {
                var updated = false;
                for (consumer_node.inputs, 0..) |input, idx| {
                    if (input == old_id) {
                        // Need to create new array since inputs is const
                        const new_inputs = try self.arena.allocator().dupe(NodeId, consumer_node.inputs);
                        
                        // Debug: Check what we're about to write
                        if (new_id == 0xAAAAAAAA) {
                            std.log.err("substituteNode: About to set UNINITIALIZED new_id as input[{}] for consumer {}", 
                                       .{idx, consumer_id});
                            return error.UninitializedNodeId;
                        }
                        
                        new_inputs[idx] = new_id;
                        consumer_node.inputs = new_inputs;
                        updated = true;
                    }
                }

                if (updated) {
                    // Remove from old node's consumer list
                    if (self.consumer_lists.getPtr(old_id)) |old_list| {
                        var i: usize = 0;
                        while (i < old_list.items.len) {
                            if (old_list.items[i] == consumer_id) {
                                _ = old_list.swapRemove(i);
                                break;
                            } else {
                                i += 1;
                            }
                        }
                    }

                    // Add to new node's consumer list
                    if (self.consumer_lists.getPtr(new_id)) |new_list| {
                        try new_list.append(self.arena.allocator(), consumer_id);
                    }
                }
            }
        }

        // Remove custom_op data from old node (don't transfer it)
        _ = self.custom_ops.remove(old_id);

        // Update output nodes if needed
        for (self.output_nodes.items) |*output| {
            if (output.* == old_id) {
                output.* = new_id;
            }
        }

        // Track the substitution in our map
        try self.substitution_map.put(self.arena.allocator(), old_id, new_id);

        // Mark old node as invalid (tombstone) since it's been replaced
        if (self.getNodeMut(old_id)) |old_node| {
            old_node.is_valid = false;
        }

        self.invalidateCaches();
    }

    // Methods for consumer tracking
    pub fn addConsumer(self: *Graph, node_id: NodeId, consumer_id: NodeId) !void {
        if (self.consumer_lists.getPtr(node_id)) |list| {
            try list.append(self.arena.allocator(), consumer_id);
        } else {
            var new_list = std.ArrayListUnmanaged(NodeId){};
            try new_list.append(self.arena.allocator(), consumer_id);
            try self.consumer_lists.put(self.arena.allocator(), node_id, new_list);
        }
    }

    pub fn removeConsumer(self: *Graph, node_id: NodeId, consumer_id: NodeId) !void {
        if (self.consumer_lists.getPtr(node_id)) |list| {
            var i: usize = 0;
            while (i < list.items.len) {
                if (list.items[i] == consumer_id) {
                    _ = list.swapRemove(i);
                    return;
                } else {
                    i += 1;
                }
            }
        }
    }

    // ===== Helper Methods =====

    // Get constant value from a constant node
    pub fn getConstantValue(self: *const Graph, node_id: NodeId) ?f32 {
        const node = self.getNode(node_id) orelse return null;
        const data_source = switch (node.spec) {
            .data => |source| source,
            .compute => return null,
        };
        if (data_source != .constant) return null;
        return if (node.metadata) |meta| meta.constant_value else null;
    }
    
    /// Update constant value for a node
    pub fn updateConstantValue(self: *Graph, node_id: NodeId, value: f32) !void {
        // Get mutable node reference
        const node = self.getNodeMut(node_id) orelse {
            std.log.err("updateConstantValue: node {} not found", .{node_id});
            return error.NodeNotFound;
        };
        
        // Verify it's a constant node
        const data_source = switch (node.spec) {
            .data => |source| source,
            .compute => {
                std.log.err("updateConstantValue: node {} is not a data node", .{node_id});
                return error.NotAConstant;
            },
        };
        
        if (data_source != .constant) {
            std.log.err("updateConstantValue: node {} is not a constant", .{node_id});
            return error.NotAConstant;
        }
        
        // Ensure metadata exists
        if (node.metadata == null) {
            node.metadata = try self.arena.allocator().create(NodeMetadata);
            node.metadata.?.* = .{};
        }
        
        // Update the constant value
        node.metadata.?.constant_value = value;
        
        // Increment modification count to trigger recompilation
        self.modifications_count += 1;
        
        // Invalidate cached topology since the graph has changed
        self.cached_topology = null;
    }

    /// Set reduction axis for reduction operations
    pub fn setReductionAxis(self: *Graph, node_id: NodeId, axis: i64) !void {
        const node = self.getNodeMut(node_id) orelse {
            std.log.err("setReductionAxis: node {} not found", .{node_id});
            return error.InvalidNodeId;
        };
        const compute_op = switch (node.spec) {
            .compute => |op| op,
            .data => {
                std.log.err("setReductionAxis: node {} is not a compute node", .{node_id});
                return error.NotAReductionNode;
            },
        };
        if (compute_op != .sum_reduce and compute_op != .max_reduce) {
            std.log.err("setReductionAxis: node {} operation {} is not a reduction", .{ node_id, compute_op });
            return error.NotAReductionNode;
        }
        if (node.metadata == null) {
            node.metadata = try self.arena.allocator().create(NodeMetadata);
            node.metadata.?.* = .{};
        }
        node.metadata.?.reduction_axis = axis;
    }
    
    /// Set keepdims flag for reduction operations
    pub fn setReductionKeepdims(self: *Graph, node_id: NodeId, keepdims: bool) !void {
        const node = self.getNodeMut(node_id) orelse {
            std.log.err("setReductionKeepdims: node {} not found", .{node_id});
            return error.InvalidNodeId;
        };
        const compute_op = switch (node.spec) {
            .compute => |op| op,
            .data => {
                std.log.err("setReductionKeepdims: node {} is not a compute node", .{node_id});
                return error.NotAReductionNode;
            },
        };
        if (compute_op != .sum_reduce and compute_op != .max_reduce) {
            std.log.err("setReductionKeepdims: node {} operation {} is not a reduction", .{ node_id, compute_op });
            return error.NotAReductionNode;
        }
        if (node.metadata == null) {
            node.metadata = try self.arena.allocator().create(NodeMetadata);
            node.metadata.?.* = .{};
        }
        node.metadata.?.keepdims = keepdims;
    }

    // Set reshape target for reshape operations
    pub fn setReshapeTarget(self: *Graph, node_id: NodeId, target_shape: []const i64) !void {
        const node = self.getNodeMut(node_id) orelse {
            std.log.err("setReshapeTarget: node {} not found", .{node_id});
            return error.InvalidNodeId;
        };
        const compute_op = switch (node.spec) {
            .compute => |op| op,
            .data => {
                std.log.err("setReshapeTarget: node {} is not a compute node", .{node_id});
                return error.NotAViewOperation;
            },
        };
        if (compute_op != .reshape) {
            std.log.err("setReshapeTarget: node {} operation {} is not a reshape", .{ node_id, compute_op });
            return error.NotAViewOperation;
        }
        if (node.metadata == null) {
            node.metadata = try self.arena.allocator().create(NodeMetadata);
            node.metadata.?.* = .{};
        }
        
        // Clone target shape into arena memory
        const arena_shape = try self.arena.allocator().dupe(i64, target_shape);
        node.metadata.?.reshape_target = arena_shape;
    }

    // Set transpose axes for transpose operations
    pub fn setTransposeAxes(self: *Graph, node_id: NodeId, axes: []const usize) !void {
        const node = self.getNodeMut(node_id) orelse {
            std.log.err("setTransposeAxes: node {} not found", .{node_id});
            return error.InvalidNodeId;
        };
        const compute_op = switch (node.spec) {
            .compute => |op| op,
            .data => {
                std.log.err("setTransposeAxes: node {} is not a compute node", .{node_id});
                return error.NotAViewOperation;
            },
        };
        if (compute_op != .transpose) {
            std.log.err("setTransposeAxes: node {} operation {} is not a transpose", .{ node_id, compute_op });
            return error.NotAViewOperation;
        }
        if (node.metadata == null) {
            node.metadata = try self.arena.allocator().create(NodeMetadata);
            node.metadata.?.* = .{};
        }
        
        // Clone axes into arena memory
        const arena_axes = try self.arena.allocator().dupe(usize, axes);
        node.metadata.?.transpose_axes = arena_axes;
    }

    // Set slice parameters for slice operations
    pub fn setSliceParams(self: *Graph, node_id: NodeId, starts: []const i64, ends: []const i64) !void {
        const node = self.getNodeMut(node_id) orelse {
            std.log.err("setSliceParams: node {} not found", .{node_id});
            return error.InvalidNodeId;
        };
        const compute_op = switch (node.spec) {
            .compute => |op| op,
            .data => {
                std.log.err("setSliceParams: node {} is not a compute node", .{node_id});
                return error.NotAViewOperation;
            },
        };
        if (compute_op != .slice) {
            std.log.err("setSliceParams: node {} operation {} is not a slice", .{ node_id, compute_op });
            return error.NotAViewOperation;
        }
        if (node.metadata == null) {
            node.metadata = try self.arena.allocator().create(NodeMetadata);
            node.metadata.?.* = .{};
        }
        
        // Clone slice parameters into arena memory
        const arena_starts = try self.arena.allocator().dupe(i64, starts);
        const arena_ends = try self.arena.allocator().dupe(i64, ends);
        node.metadata.?.slice_starts = arena_starts;
        node.metadata.?.slice_ends = arena_ends;
    }

    // Set shape metadata for a node (for use by tensor operations)
    // This helper function ensures consistent shape metadata setup across all operations
    pub fn setNodeShapeMetadata(
        self: *Graph,
        node_id: NodeId,
        input_shapes: []const @import("shape").ShapeTracker,
        output_shape: @import("shape").ShapeTracker,
    ) !void {
        const node = self.getNodeMut(node_id) orelse {
            std.log.err("setNodeShapeMetadata: node {} not found", .{node_id});
            return error.InvalidNodeId;
        };
        
        const alloc = self.arena.allocator();
        
        // Create metadata if it doesn't exist
        if (node.metadata == null) {
            node.metadata = try alloc.create(NodeMetadata);
            node.metadata.?.* = .{};
        }
        
        // IMPORTANT: Store the actual input shapes from the TensorHandles
        // This preserves view transformations that don't create new nodes
        if (input_shapes.len > 0) {
            const cloned_shapes = try alloc.alloc(@import("shape").ShapeTracker, input_shapes.len);
            for (input_shapes, cloned_shapes) |shape, *cloned| {
                cloned.* = try shape.clone(alloc);
            }
            node.metadata.?.input_shapes = cloned_shapes;
        }
        
        // Clone output shape
        node.metadata.?.output_shape = try output_shape.clone(alloc);
    }

    // ===== Graph Inspection =====

    // Consumer tracking provides efficient graph analysis
    pub fn hasConsumers(self: *const Graph, node_id: NodeId) bool {
        return self.getConsumerCount(node_id) > 0;
    }

    pub fn isOutputNode(self: *const Graph, node_id: NodeId) bool {
        return !self.hasConsumers(node_id);
    }

    /// Find the current node ID for a given node, following any substitutions
    /// This is essential for pattern recognition where nodes may be replaced
    pub fn resolveCurrentNodeId(self: *const Graph, original_id: NodeId) NodeId {
        // First check if node still exists and is valid
        if (self.getNode(original_id)) |node| {
            if (node.is_valid) {
                return original_id;
            }
        }
        
        // Check substitution map - follow chain of substitutions
        var current_id = original_id;
        var iterations: u32 = 0;
        const max_iterations = 100; // Prevent infinite loops
        
        while (self.substitution_map.get(current_id)) |new_id| {
            current_id = new_id;
            iterations += 1;
            if (iterations > max_iterations) {
                std.log.err("resolveCurrentNodeId: substitution chain too long for node {}", .{original_id});
                break;
            }
            
            // If we found a node that exists and is valid, return it
            if (self.getNode(current_id)) |node| {
                if (node.is_valid) {
                    return current_id;
                }
            }
        }
        
        // If we found a substitution but the final node doesn't exist or isn't valid,
        // this indicates a problem with the substitution chain
        if (current_id != original_id) {
            if (self.getNode(current_id)) |node| {
                if (!node.is_valid) {
                    std.log.warn("resolveCurrentNodeId: substitution chain for {} leads to invalid node {}", .{ original_id, current_id });
                }
            } else {
                std.log.warn("resolveCurrentNodeId: substitution chain for {} leads to non-existent node {}", .{ original_id, current_id });
            }
        }
        
        return current_id;
    }

    // Get nodes in topological order (borrowed reference - do not hold across modifications)
    pub fn topologicalSort(self: *Graph) ![]const NodeId {
        if (self.cached_topology) |topo| {
            // Debug: Verify cached topology is still valid
            for (topo, 0..) |node_id, idx| {
                if (node_id == 0xAAAAAAAA) {
                    std.log.err("topologicalSort: CACHED topo has UNINITIALIZED node_id at index {}", .{idx});
                    // Invalidate cache and recompute (arena-allocated, no explicit free needed)
                    self.cached_topology = null;
                    break;
                }
            }
            if (self.cached_topology) |valid_topo| {
                return valid_topo;
            }
        }

        const topo = try self.computeTopology();
        self.cached_topology = topo;
        return topo;
    }
    
    // Get nodes in topological order (owned copy - caller must free)
    pub fn topologicalSortOwned(self: *Graph, allocator: Allocator) ![]NodeId {
        const topo = try self.topologicalSort();
        return try allocator.dupe(NodeId, topo);
    }
    
    // Get nodes in topological order from const graph (owned copy - caller must free)
    pub fn topologicalSortOwnedConst(self: *const Graph, allocator: Allocator) ![]NodeId {
        // Debug check for valid state
        const enable_debug = @import("build_options").enable_debug_logs;
        if (enable_debug) {
            std.debug.print("topologicalSortOwnedConst: graph nodes.len={}, next_node_id={}\n", .{ self.nodes.items.len, self.next_node_id });
            
            // Check for corrupted nodes
            for (self.nodes.items, 0..) |node, i| {
                if (node.is_valid) {
                    for (node.inputs) |input_id| {
                        if (input_id >= self.next_node_id or input_id == 0xAAAAAAAA) {
                            std.debug.print("  WARNING: Node {} has corrupted input: {} (0x{x})\n", .{ i, input_id, input_id });
                        }
                    }
                }
            }
        }
        
        // For const graph, we need to compute topology without caching
        const visited = try allocator.alloc(bool, self.next_node_id);
        defer allocator.free(visited);
        @memset(visited, false);
        
        var result = std.ArrayList(NodeId).init(allocator);
        errdefer result.deinit();
        
        // Visit all nodes
        for (0..self.next_node_id) |node_id| {
            if (!visited[node_id] and self.hasNode(@intCast(node_id))) {
                if (enable_debug) {
                    std.debug.print("topologicalSortOwnedConst: visiting node {}\n", .{node_id});
                }
                try self.topologicalSortDFSConst(@intCast(node_id), visited, &result);
            } else if (enable_debug and !self.hasNode(@intCast(node_id))) {
                std.debug.print("topologicalSortOwnedConst: node {} doesn't exist\n", .{node_id});
            }
        }
        
        return result.toOwnedSlice();
    }
    
    // DFS helper for const graph
    fn topologicalSortDFSConst(self: *const Graph, node_id: NodeId, visited: []bool, result: *std.ArrayList(NodeId)) !void {
        const enable_debug = @import("build_options").enable_debug_logs;
        if (enable_debug) {
            std.debug.print("  topologicalSortDFSConst: entering for node {}\n", .{node_id});
        }
        
        // Bounds check
        if (node_id >= visited.len) {
            std.log.err("topologicalSortDFSConst: node_id {} (0x{x}) out of bounds (visited.len = {})", .{ node_id, node_id, visited.len });
            // Check if this is uninitialized memory pattern
            if (node_id == 0xAAAAAAAA) {
                std.log.err("  This appears to be uninitialized memory!", .{});
            }
            return;
        }
        
        if (visited[node_id]) return;
        visited[node_id] = true;
        
        const node = self.getNode(node_id) orelse return;
        for (node.inputs) |input_id| {
            // Debug log for suspicious IDs
            if (input_id >= self.next_node_id or input_id == 0xAAAAAAAA) {
                std.log.err("topologicalSortDFSConst: suspicious input_id {} (0x{x}) for node {}", .{ input_id, input_id, node_id });
            }
            
            const resolved_id = self.resolveCurrentNodeId(input_id);
            // Bounds check for resolved ID
            if (resolved_id >= visited.len) {
                std.log.err("topologicalSortDFSConst: resolved_id {} (0x{x}) out of bounds (visited.len = {})", .{ resolved_id, resolved_id, visited.len });
                // Check if this is uninitialized memory pattern
                if (resolved_id == 0xAAAAAAAA) {
                    std.log.err("  This appears to be uninitialized memory! Original input_id was {}", .{input_id});
                }
                continue;
            }
            if (!visited[resolved_id]) {
                try self.topologicalSortDFSConst(resolved_id, visited, result);
            }
        }
        
        try result.append(node_id);
    }

    // Invalidate cached data after modifications
    pub fn invalidateCaches(self: *Graph) void {
        // Clear cached topology pointer (arena-allocated, no explicit free needed)
        if (self.cached_topology) |_| {
            self.cached_topology = null;
        }
        self.modifications_count += 1;
    }

    // ===== Graph Snapshot and Restoration for Transactions =====

    /// Complete graph state snapshot for atomic rollback capability
    pub const GraphSnapshot = struct {
        // Core state
        nodes: []Node,
        edges: []Edge,
        node_map: std.AutoHashMapUnmanaged(NodeId, u32),
        consumer_lists: std.AutoHashMapUnmanaged(NodeId, std.ArrayListUnmanaged(NodeId)),
        custom_ops: std.AutoHashMapUnmanaged(NodeId, CustomOp),
        output_nodes: []NodeId,

        // Metadata
        next_node_id: NodeId,
        is_finalized: bool,
        modifications_count: u32,

        // Memory management
        allocator: Allocator,

        pub fn deinit(self: *GraphSnapshot) void {
            // Free all copied data
            self.allocator.free(self.nodes);
            self.allocator.free(self.edges);
            self.allocator.free(self.output_nodes);

            // Free hash map contents
            self.node_map.deinit(self.allocator);
            self.custom_ops.deinit(self.allocator);

            // Free consumer lists
            var iter = self.consumer_lists.iterator();
            while (iter.next()) |entry| {
                entry.value_ptr.deinit(self.allocator);
            }
            self.consumer_lists.deinit(self.allocator);
        }
    };

    /// Create a complete snapshot of graph state for rollback
    pub fn createSnapshot(self: *const Graph) !GraphSnapshot {
        var snapshot = GraphSnapshot{
            .nodes = try self.allocator.dupe(Node, self.nodes.items),
            .edges = try self.allocator.dupe(Edge, self.edges.items),
            .node_map = .{},
            .consumer_lists = .{},
            .custom_ops = .{},
            .output_nodes = try self.allocator.dupe(NodeId, self.output_nodes.items),
            .next_node_id = self.next_node_id,
            .is_finalized = self.is_finalized,
            .modifications_count = self.modifications_count,
            .allocator = self.allocator,
        };

        // Deep copy node_map
        var node_iter = self.node_map.iterator();
        while (node_iter.next()) |entry| {
            try snapshot.node_map.put(self.allocator, entry.key_ptr.*, entry.value_ptr.*);
        }

        // Deep copy custom_ops
        var custom_iter = self.custom_ops.iterator();
        while (custom_iter.next()) |entry| {
            try snapshot.custom_ops.put(self.allocator, entry.key_ptr.*, entry.value_ptr.*);
        }

        // Deep copy consumer_lists
        var consumer_iter = self.consumer_lists.iterator();
        while (consumer_iter.next()) |entry| {
            var list_copy = std.ArrayListUnmanaged(NodeId){};
            try list_copy.appendSlice(self.allocator, entry.value_ptr.items);
            try snapshot.consumer_lists.put(self.allocator, entry.key_ptr.*, list_copy);
        }

        return snapshot;
    }

    /// Restore graph from snapshot (atomic rollback)
    pub fn restoreFromSnapshot(self: *Graph, snapshot: GraphSnapshot) !void {
        // Clear current state
        self.nodes.clearRetainingCapacity();
        self.edges.clearRetainingCapacity();
        self.output_nodes.clearRetainingCapacity();

        // Clear hash maps
        self.node_map.clearRetainingCapacity();
        self.custom_ops.clearRetainingCapacity();

        // Clear consumer lists
        var iter = self.consumer_lists.iterator();
        while (iter.next()) |entry| {
            entry.value_ptr.deinit(self.arena.allocator());
        }
        self.consumer_lists.clearRetainingCapacity();

        // Restore all data
        try self.nodes.appendSlice(self.arena.allocator(), snapshot.nodes);
        try self.edges.appendSlice(self.arena.allocator(), snapshot.edges);
        try self.output_nodes.appendSlice(self.arena.allocator(), snapshot.output_nodes);

        // Restore hash maps
        var node_iter = snapshot.node_map.iterator();
        while (node_iter.next()) |entry| {
            try self.node_map.put(self.arena.allocator(), entry.key_ptr.*, entry.value_ptr.*);
        }

        var custom_iter = snapshot.custom_ops.iterator();
        while (custom_iter.next()) |entry| {
            try self.custom_ops.put(self.arena.allocator(), entry.key_ptr.*, entry.value_ptr.*);
        }

        // Restore consumer lists
        var consumer_iter = snapshot.consumer_lists.iterator();
        while (consumer_iter.next()) |entry| {
            var list_copy = std.ArrayListUnmanaged(NodeId){};
            try list_copy.appendSlice(self.arena.allocator(), entry.value_ptr.items);
            try self.consumer_lists.put(self.arena.allocator(), entry.key_ptr.*, list_copy);
        }

        // Restore metadata
        self.next_node_id = snapshot.next_node_id;
        self.is_finalized = snapshot.is_finalized;
        self.modifications_count = snapshot.modifications_count;
        // Invalidate cache when restoring from snapshot (arena-allocated, no explicit free needed)
        if (self.cached_topology) |_| {
            self.cached_topology = null;
        }
    }

    /// Verify graph state integrity with concurrent modification detection
    pub fn verifyStateIntegrity(self: *Graph, expected_mod_count: u32) !void {
        // Check for concurrent modifications
        if (self.modifications_count != expected_mod_count) {
            std.log.err("verifyStateIntegrity: concurrent modification detected - expected count {}, got {}", .{ expected_mod_count, self.modifications_count });
            return error.ConcurrentModification;
        }

        // Perform comprehensive integrity check
        try self.validateIntegrity();
    }

    // Compute topological order (internal helper)
    fn computeTopology(self: *Graph) ![]NodeId {
        // Use arena allocator for cached topology to avoid ownership issues
        const arena_allocator = self.arena.allocator();
        
        var in_degree = std.AutoHashMapUnmanaged(NodeId, u32){};
        defer in_degree.deinit(arena_allocator);

        var queue = std.ArrayList(NodeId).init(arena_allocator);
        defer queue.deinit();

        var result = std.ArrayList(NodeId).init(arena_allocator);
        errdefer result.deinit();

        // Calculate in-degrees
        for (self.nodes.items) |node| {
            if (!node.is_valid) continue;
            try in_degree.put(arena_allocator, node.id, 0);
        }

        for (self.nodes.items) |node| {
            if (!node.is_valid) continue;
            
            // Debug: Check for corrupted inputs
            for (node.inputs, 0..) |input_id, idx| {
                if (input_id == 0xAAAAAAAA) {
                    std.log.err("computeTopology: Node {} has UNINITIALIZED input[{}]", .{node.id, idx});
                    return error.UninitializedNodeId;
                }
                if (input_id >= self.next_node_id) {
                    std.log.err("computeTopology: Node {} has OUT OF BOUNDS input[{}] = {} (next_node_id={})", 
                               .{node.id, idx, input_id, self.next_node_id});
                    return error.InvalidNodeId;
                }
            }
            
            // Set in-degree to the number of inputs
            if (in_degree.getPtr(node.id)) |ptr| {
                ptr.* = @intCast(node.inputs.len);
            }
        }

        // Find nodes with no dependencies
        var iter = in_degree.iterator();
        while (iter.next()) |entry| {
            if (entry.value_ptr.* == 0) {
                try queue.append(entry.key_ptr.*);
            }
        }

        // Process nodes in topological order
        while (queue.items.len > 0) {
            const node_id = queue.orderedRemove(0);
            try result.append(node_id);

            // Reduce in-degree of consumers
            if (self.consumer_lists.get(node_id)) |consumers| {
                for (consumers.items) |consumer| {
                    if (in_degree.getPtr(consumer)) |ptr| {
                        ptr.* -= 1;
                        if (ptr.* == 0) {
                            try queue.append(consumer);
                        }
                    }
                }
            }
        }

        // Check for cycles
        if (result.items.len != in_degree.count()) {
            std.log.err("computeTopology: cycle detected - only {} of {} nodes could be ordered", .{ result.items.len, in_degree.count() });
            return error.CyclicDependency;
        }

        return result.toOwnedSlice();
    }

    /// Validate graph integrity (used in tests)
    pub fn validateIntegrity(self: *Graph) !void {
        // Check that all nodes have valid inputs
        for (self.nodes.items) |node| {
            if (!node.is_valid) continue;

            // Check that all input nodes exist and are valid
            for (node.inputs, 0..) |input_id, idx| {
                const input_node = self.getNode(input_id) orelse {
                    std.log.err("validateIntegrity: node {} has invalid input {} at index {}", .{ node.id, input_id, idx });
                    return error.InvalidInput;
                };
                if (!input_node.is_valid) {
                    std.log.err("validateIntegrity: node {} references tombstoned input {} at index {}", .{ node.id, input_id, idx });
                    return error.InvalidInput;
                }
            }
        }

        // Check that node_map is consistent with nodes array
        for (self.nodes.items, 0..) |node, i| {
            if (node.is_valid) {
                const mapped_index = self.node_map.get(node.id) orelse {
                    std.log.err("validateIntegrity: node {} at index {} not found in node_map", .{ node.id, i });
                    return error.InconsistentNodeMap;
                };
                if (mapped_index != i) {
                    std.log.err("validateIntegrity: node {} mapped to index {} but found at index {}", .{ node.id, mapped_index, i });
                    return error.InconsistentNodeMap;
                }
            }
        }
    }
};

// ===== Helper Functions =====

// Helper functions to clarify the distinction between data sources and operations
pub fn isSource(spec: NodeSpec) bool {
    return spec == .data;
}

pub fn isOperation(spec: NodeSpec) bool {
    return spec == .compute;
}

pub fn requiresComputation(spec: NodeSpec) bool {
    return spec == .compute;
}

// ===== Graph Validation =====

// Consistent with codebase-wide error handling pattern
pub const GraphValidationError = error{
    DanglingReference,
    ReferenceToInvalidNode,
    CyclicDependency,
    InvalidOutputNode,
    OutputNodeRemoved,
    OrphanedCustomOp,
    OutOfMemory,
    ConcurrentModification,
    UninitializedNodeId,
    InvalidNodeId,
};

pub fn validateIntegrity(graph: *Graph) GraphValidationError!void {
    // Check all edges point to valid nodes
    for (graph.nodes.items) |node| {
        if (!node.is_valid) continue;

        for (node.inputs) |input_id| {
            const input_index = graph.node_map.get(input_id) orelse {
                std.log.err("validateIntegrity: node {} has dangling reference to input {}", .{ node.id, input_id });
                return error.DanglingReference;
            };

            if (!graph.nodes.items[input_index].is_valid) {
                std.log.err("validateIntegrity: node {} references invalid/tombstoned node {}", .{ node.id, input_id });
                return error.ReferenceToInvalidNode;
            }
        }
    }

    // Check for cycles
    _ = graph.topologicalSort() catch |err| {
        return switch (err) {
            error.CyclicDependency => error.CyclicDependency,
            error.UninitializedNodeId => error.UninitializedNodeId,
            error.InvalidNodeId => error.InvalidNodeId,
            else => err,
        };
    };

    // Check output nodes are valid
    for (graph.output_nodes.items) |output_id| {
        const index = graph.node_map.get(output_id) orelse {
            std.log.err("validateIntegrity: output node {} not found in node_map", .{output_id});
            return error.InvalidOutputNode;
        };

        if (!graph.nodes.items[index].is_valid) {
            std.log.err("validateIntegrity: output node {} has been removed/tombstoned", .{output_id});
            return error.OutputNodeRemoved;
        }
    }

    // Check for orphaned custom ops
    var custom_op_iter = graph.custom_ops.keyIterator();
    while (custom_op_iter.next()) |key| {
        if (!graph.hasNode(key.*)) {
            std.log.err("validateIntegrity: orphaned custom op for non-existent node {}", .{key.*});
            return error.OrphanedCustomOp;
        }
        switch (graph.getNode(key.*).?.spec) {
            .compute => |op| if (op != .custom) {
                std.log.err("validateIntegrity: custom op data exists for non-custom node {} with op {s}", .{ key.*, @tagName(op) });
                return error.OrphanedCustomOp;
            },
            .data => {
                std.log.err("validateIntegrity: custom op data exists for data node {}", .{key.*});
                return error.OrphanedCustomOp;
            },
        }
    }
}

// ===== Unit Tests =====

test "Graph lifecycle" {
    const testing = std.testing;

    var graph = try Graph.init(testing.allocator);
    defer graph.deinit();

    // Should start empty
    try testing.expect(graph.nodes.items.len == 0);
    try testing.expect(graph.next_node_id == 0);
    try testing.expect(!graph.is_finalized);

    // Should be able to finalize
    try graph.finalize();
    try testing.expect(graph.is_finalized);

    // Should not be able to finalize twice
    try testing.expectError(error.GraphFinalized, graph.finalize());
}

test "Node creation" {
    const testing = std.testing;

    var graph = try Graph.init(testing.allocator);
    defer graph.deinit();

    // Create placeholder
    const placeholder = try graph.addPlaceholder(.f32);
    try testing.expectEqual(@as(NodeId, 0), placeholder);
    try testing.expect(graph.hasNode(placeholder));

    // Create constant
    const constant = try graph.addConstant(3.14);
    try testing.expectEqual(@as(NodeId, 1), constant);
    try testing.expect(graph.hasNode(constant));

    // Get constant value
    const value = graph.getConstantValue(constant);
    try testing.expect(value != null);
    try testing.expectEqual(@as(f32, 3.14), value.?);

    // Create compute node
    const add_node = try graph.addNode(.add, &.{ placeholder, constant }, .f32);
    try testing.expectEqual(@as(NodeId, 2), add_node);
    try testing.expect(graph.hasNode(add_node));

    // Check inputs
    const inputs = graph.getInputs(add_node);
    try testing.expect(inputs != null);
    try testing.expectEqual(@as(usize, 2), inputs.?.len);
    try testing.expectEqual(placeholder, inputs.?[0]);
    try testing.expectEqual(constant, inputs.?[1]);
}

test "Consumer tracking" {
    const testing = std.testing;

    var graph = try Graph.init(testing.allocator);
    defer graph.deinit();

    // Create nodes
    const a = try graph.addPlaceholder(.f32);
    const b = try graph.addPlaceholder(.f32);
    const c = try graph.addNode(.add, &.{ a, b }, .f32);
    const d = try graph.addNode(.mul, &.{ a, c }, .f32);

    // Check consumer counts
    try testing.expectEqual(@as(u32, 2), graph.getConsumerCount(a)); // used by c and d
    try testing.expectEqual(@as(u32, 1), graph.getConsumerCount(b)); // used by c
    try testing.expectEqual(@as(u32, 1), graph.getConsumerCount(c)); // used by d
    try testing.expectEqual(@as(u32, 0), graph.getConsumerCount(d)); // not used

    // Check output node detection
    try testing.expect(!graph.isOutputNode(a));
    try testing.expect(!graph.isOutputNode(b));
    try testing.expect(!graph.isOutputNode(c));
    try testing.expect(graph.isOutputNode(d));
}

test "Node removal" {
    const testing = std.testing;

    var graph = try Graph.init(testing.allocator);
    defer graph.deinit();

    // Create nodes
    const a = try graph.addPlaceholder(.f32);
    const b = try graph.addPlaceholder(.f32);
    const c = try graph.addNode(.add, &.{ a, b }, .f32);

    // Remove node c
    try graph.removeNode(c);

    // c should no longer exist
    try testing.expect(!graph.hasNode(c));

    // a and b should have no consumers
    try testing.expectEqual(@as(u32, 0), graph.getConsumerCount(a));
    try testing.expectEqual(@as(u32, 0), graph.getConsumerCount(b));
}

test "Cycle detection" {
    const testing = std.testing;

    var graph = try Graph.init(testing.allocator);
    defer graph.deinit();

    // Create nodes
    const a = try graph.addPlaceholder(.f32);
    const b = try graph.addPlaceholder(.f32);

    // Should not create cycle
    try testing.expect(!try graph.wouldCreateCycle(a, b));

    // Self-loop should be detected as cycle
    try testing.expect(try graph.wouldCreateCycle(a, a));
}

test "Topological sort" {
    const testing = std.testing;

    var graph = try Graph.init(testing.allocator);
    defer graph.deinit();

    // Create a simple DAG: a -> c <- b
    const a = try graph.addPlaceholder(.f32);
    const b = try graph.addPlaceholder(.f32);
    const c = try graph.addNode(.add, &.{ a, b }, .f32);

    const topo = try graph.topologicalSort();
    // Note: topo is cached by the graph, don't free it!

    // Should have all nodes
    try testing.expectEqual(@as(usize, 3), topo.len);

    // c should come after both a and b
    var a_idx: ?usize = null;
    var b_idx: ?usize = null;
    var c_idx: ?usize = null;

    for (topo, 0..) |node_id, i| {
        if (node_id == a) a_idx = i;
        if (node_id == b) b_idx = i;
        if (node_id == c) c_idx = i;
    }

    try testing.expect(a_idx != null);
    try testing.expect(b_idx != null);
    try testing.expect(c_idx != null);
    try testing.expect(c_idx.? > a_idx.?);
    try testing.expect(c_idx.? > b_idx.?);
}

test "Graph validation" {
    const testing = std.testing;

    var graph = try Graph.init(testing.allocator);
    defer graph.deinit();

    // Create valid graph
    const a = try graph.addPlaceholder(.f32);
    const b = try graph.addPlaceholder(.f32);
    const c = try graph.addNode(.add, &.{ a, b }, .f32);
    _ = c;

    // Should validate successfully
    try validateIntegrity(&graph);

    // Mark a node as invalid to test validation
    if (graph.getNodeMut(a)) |node| {
        node.is_valid = false;
    }

    // Should fail validation due to invalid reference
    try testing.expectError(error.ReferenceToInvalidNode, validateIntegrity(&graph));
}

test "Custom operations" {
    const testing = std.testing;

    var graph = try Graph.init(testing.allocator);
    defer graph.deinit();

    // Create a custom operation
    const custom_op = CustomOp{
        .id = 42,
        .backend_name = "test_backend",
        .fusion_pattern = .{
            .ops = &.{ .add, .mul },
            .dtype = .f32,
            .input_count = 2,
            .has_reduction = false,
        },
        .debug_name = "test_fusion",
    };

    const a = try graph.addPlaceholder(.f32);
    const b = try graph.addPlaceholder(.f32);
    const custom_node = try graph.addCustomNode(custom_op, &.{ a, b }, .f32);

    // Should be able to retrieve custom op data
    const retrieved_op = graph.getCustomOp(custom_node);
    try testing.expect(retrieved_op != null);
    try testing.expectEqual(@as(u64, 42), retrieved_op.?.id);
    try testing.expectEqualStrings("test_backend", retrieved_op.?.backend_name);

    // Non-custom nodes should return null
    try testing.expect(graph.getCustomOp(a) == null);
    try testing.expect(graph.getCustomOp(b) == null);

    // Non-existent node should return null
    try testing.expect(graph.getCustomOp(999) == null);
}

test "Parameter nodes" {
    const testing = std.testing;

    var graph = try Graph.init(testing.allocator);
    defer graph.deinit();

    // Create parameter with specific ID
    const param_id: NodeId = 100;
    const param = try graph.addParameter(param_id, .f32);

    // Should use the provided ID
    try testing.expectEqual(param_id, param);
    try testing.expect(graph.hasNode(param));

    // Should update next_node_id
    try testing.expect(graph.next_node_id > param_id);

    // Verify it's a parameter node
    const node = graph.getNode(param).?;
    try testing.expect(node.spec == .data);
    try testing.expect(node.spec.data == .parameter);
    try testing.expectEqual(@as(usize, 0), node.inputs.len);

    // Create another parameter with lower ID
    const param2_id: NodeId = 50;
    const param2 = try graph.addParameter(param2_id, .i32);
    try testing.expectEqual(param2_id, param2);
}

test "Node substitution" {
    const testing = std.testing;

    var graph = try Graph.init(testing.allocator);
    defer graph.deinit();

    // Create graph: a -> b -> c
    //                    └─> d
    const a = try graph.addPlaceholder(.f32);
    const b = try graph.addNode(.sqrt, &.{a}, .f32);
    const c = try graph.addNode(.exp2, &.{b}, .f32);
    const d = try graph.addNode(.sin, &.{b}, .f32);

    // Create replacement node
    const b_new = try graph.addNode(.log2, &.{a}, .f32);

    // Substitute b with b_new
    try graph.substituteNode(b, b_new);

    // Check that c and d now use b_new
    const c_inputs = graph.getInputs(c).?;
    try testing.expectEqual(b_new, c_inputs[0]);

    const d_inputs = graph.getInputs(d).?;
    try testing.expectEqual(b_new, d_inputs[0]);

    // Check consumer counts
    try testing.expectEqual(@as(u32, 0), graph.getConsumerCount(b));
    try testing.expectEqual(@as(u32, 2), graph.getConsumerCount(b_new));

    // Test no-op substitution
    try graph.substituteNode(a, a); // Should be no-op

    // Test cycle detection
    try testing.expectError(error.SubstitutionWouldCreateCycle, graph.substituteNode(c, a));

    // Test invalid nodes
    try testing.expectError(error.OldNodeNotFound, graph.substituteNode(999, a));
    try testing.expectError(error.NewNodeNotFound, graph.substituteNode(a, 999));
}

test "Node substitution with custom ops" {
    const testing = std.testing;

    var graph = try Graph.init(testing.allocator);
    defer graph.deinit();

    // Create custom op
    const custom_op = CustomOp{
        .id = 77,
        .backend_name = "test",
        .fusion_pattern = .{
            .ops = &.{.add},
            .dtype = .f32,
            .input_count = 1,
            .has_reduction = false,
        },
        .debug_name = "test_op",
    };

    const a = try graph.addPlaceholder(.f32);
    const b = try graph.addCustomNode(custom_op, &.{a}, .f32);
    const c = try graph.addNode(.sqrt, &.{b}, .f32);

    // Create replacement as another custom op
    const custom_op2 = CustomOp{
        .id = 88,
        .backend_name = "test2",
        .fusion_pattern = .{
            .ops = &.{.mul},
            .dtype = .f32,
            .input_count = 1,
            .has_reduction = false,
        },
        .debug_name = "test_op2",
    };
    const b_new = try graph.addCustomNode(custom_op2, &.{a}, .f32);

    // Substitute should replace old custom op with new one
    try graph.substituteNode(b, b_new);

    // Original node should not have custom op
    try testing.expect(graph.getCustomOp(b) == null);

    // New node should still have its original custom op (not transferred)
    const new_op = graph.getCustomOp(b_new);
    try testing.expect(new_op != null);
    try testing.expectEqual(@as(u64, 88), new_op.?.id);

    // Check that c now uses b_new
    const c_inputs = graph.getInputs(c).?;
    try testing.expectEqual(b_new, c_inputs[0]);
}

test "createNode direct API" {
    const testing = std.testing;

    var graph = try Graph.init(testing.allocator);
    defer graph.deinit();

    // Test data source creation
    const constant = try graph.createNode(.{ .data = .constant }, &.{}, .f32);
    try testing.expect(graph.hasNode(constant));

    // Test compute node creation
    const a = try graph.addPlaceholder(.f32);
    const b = try graph.addPlaceholder(.f32);
    const add = try graph.createNode(.{ .compute = .add }, &.{ a, b }, .f32);
    try testing.expect(graph.hasNode(add));

    // Test error cases
    try testing.expectError(error.InvalidInputCount, graph.createNode(.{ .data = .placeholder }, &.{a}, .f32));

    try testing.expectError(error.InvalidInput, graph.createNode(.{ .compute = .add }, &.{ 999, 888 }, .f32));
}

test "getNode and related accessors" {
    const testing = std.testing;

    var graph = try Graph.init(testing.allocator);
    defer graph.deinit();

    const a = try graph.addPlaceholder(.f32);

    // Test getNode
    const node = graph.getNode(a);
    try testing.expect(node != null);
    try testing.expectEqual(a, node.?.id);

    // Test non-existent node
    try testing.expect(graph.getNode(999) == null);

    // Test removed node
    const b = try graph.addConstant(2.0);
    try graph.removeNode(b);
    try testing.expect(graph.getNode(b) == null);
}

test "Consumer list operations" {
    const testing = std.testing;

    var graph = try Graph.init(testing.allocator);
    defer graph.deinit();

    // Create simple graph
    const a = try graph.addPlaceholder(.f32);
    const b = try graph.addNode(.sqrt, &.{a}, .f32);
    const c = try graph.addNode(.exp2, &.{a}, .f32);

    // Test hasConsumers
    try testing.expect(graph.hasConsumers(a));
    try testing.expect(!graph.hasConsumers(b));
    try testing.expect(!graph.hasConsumers(c));

    // Test getConsumers (allocating)
    const consumers = try graph.getConsumers(a, testing.allocator);
    defer testing.allocator.free(consumers);
    try testing.expectEqual(@as(usize, 2), consumers.len);

    // Test empty consumers
    const empty_consumers = try graph.getConsumers(b, testing.allocator);
    defer testing.allocator.free(empty_consumers);
    try testing.expectEqual(@as(usize, 0), empty_consumers.len);

    // Test iterateConsumers (non-allocating)
    const iter_consumers = graph.iterateConsumers(a);
    try testing.expect(iter_consumers != null);
    try testing.expectEqual(@as(usize, 2), iter_consumers.?.len);

    // Test null for non-existent node
    try testing.expect(graph.iterateConsumers(999) == null);
}

test "Manual consumer management" {
    const testing = std.testing;

    var graph = try Graph.init(testing.allocator);
    defer graph.deinit();

    const a = try graph.addPlaceholder(.f32);
    const b = try graph.addPlaceholder(.f32);

    // Manually add consumer
    try graph.addConsumer(a, b);
    try testing.expectEqual(@as(u32, 1), graph.getConsumerCount(a));

    // Add same consumer again (should work)
    try graph.addConsumer(a, b);
    try testing.expectEqual(@as(u32, 2), graph.getConsumerCount(a));

    // Remove consumer
    try graph.removeConsumer(a, b);
    try testing.expectEqual(@as(u32, 1), graph.getConsumerCount(a));

    // Remove again
    try graph.removeConsumer(a, b);
    try testing.expectEqual(@as(u32, 0), graph.getConsumerCount(a));

    // Remove non-existent (should be no-op)
    try graph.removeConsumer(a, b);
    try testing.expectEqual(@as(u32, 0), graph.getConsumerCount(a));
}

test "Reduction operations metadata" {
    const testing = std.testing;

    var graph = try Graph.init(testing.allocator);
    defer graph.deinit();

    const a = try graph.addPlaceholder(.f32);
    const sum = try graph.addNode(.sum_reduce, &.{a}, .f32);
    const max = try graph.addNode(.max_reduce, &.{a}, .f32);

    // Set reduction axes
    try graph.setReductionAxis(sum, 0);
    try graph.setReductionAxis(max, 1);

    // Verify axes were set
    const sum_node = graph.getNode(sum).?;
    try testing.expect(sum_node.metadata != null);
    try testing.expectEqual(@as(i64, 0), sum_node.metadata.?.reduction_axis.?);

    const max_node = graph.getNode(max).?;
    try testing.expect(max_node.metadata != null);
    try testing.expectEqual(@as(i64, 1), max_node.metadata.?.reduction_axis.?);

    // Test error cases
    try testing.expectError(error.InvalidNodeId, graph.setReductionAxis(999, 0));
    try testing.expectError(error.NotAReductionNode, graph.setReductionAxis(a, 0));

    // Test non-reduction compute node
    const sqrt = try graph.addNode(.sqrt, &.{a}, .f32);
    try testing.expectError(error.NotAReductionNode, graph.setReductionAxis(sqrt, 0));
}

test "forceRemoveNode" {
    const testing = std.testing;

    var graph = try Graph.init(testing.allocator);
    defer graph.deinit();

    const a = try graph.addPlaceholder(.f32);
    const b = try graph.addNode(.sqrt, &.{a}, .f32);
    const c = try graph.addNode(.exp2, &.{b}, .f32);
    _ = c;

    // Force remove node with consumers (normally would warn)
    graph.forceRemoveNode(b);
    try testing.expect(!graph.hasNode(b));

    // Force remove non-existent node (should handle error internally)
    graph.forceRemoveNode(999);
}

test "Helper functions" {
    const testing = std.testing;

    // Test NodeSpec helpers
    const data_spec = NodeSpec{ .data = .constant };
    const compute_spec = NodeSpec{ .compute = .add };

    try testing.expect(isSource(data_spec));
    try testing.expect(!isSource(compute_spec));

    try testing.expect(!isOperation(data_spec));
    try testing.expect(isOperation(compute_spec));

    try testing.expect(!requiresComputation(data_spec));
    try testing.expect(requiresComputation(compute_spec));
}

test "All compute operations" {
    const testing = std.testing;

    var graph = try Graph.init(testing.allocator);
    defer graph.deinit();

    const a = try graph.addPlaceholder(.f32);
    const b = try graph.addPlaceholder(.f32);

    // Test all binary operations
    const add = try graph.addNode(.add, &.{ a, b }, .f32);
    const mul = try graph.addNode(.mul, &.{ a, b }, .f32);
    const mod = try graph.addNode(.mod, &.{ a, b }, .f32);
    const less = try graph.addNode(.less_than, &.{ a, b }, .f32);

    // Test all unary operations
    const recip = try graph.addNode(.recip, &.{a}, .f32);
    const sqrt = try graph.addNode(.sqrt, &.{a}, .f32);
    const sin = try graph.addNode(.sin, &.{a}, .f32);
    const exp2 = try graph.addNode(.exp2, &.{a}, .f32);
    const log2 = try graph.addNode(.log2, &.{a}, .f32);

    // Test reduction operations
    const sum_reduce = try graph.addNode(.sum_reduce, &.{a}, .f32);
    const max_reduce = try graph.addNode(.max_reduce, &.{a}, .f32);

    // Test memory operations
    const contiguous = try graph.addNode(.contiguous, &.{a}, .f32);

    // Verify all were created
    try testing.expect(graph.hasNode(add));
    try testing.expect(graph.hasNode(mul));
    try testing.expect(graph.hasNode(mod));
    try testing.expect(graph.hasNode(less));
    try testing.expect(graph.hasNode(recip));
    try testing.expect(graph.hasNode(sqrt));
    try testing.expect(graph.hasNode(sin));
    try testing.expect(graph.hasNode(exp2));
    try testing.expect(graph.hasNode(log2));
    try testing.expect(graph.hasNode(sum_reduce));
    try testing.expect(graph.hasNode(max_reduce));
    try testing.expect(graph.hasNode(contiguous));

    // Test wrong input counts
    try testing.expectError(error.InvalidInputCount, graph.addNode(.add, &.{a}, .f32));
    try testing.expectError(error.InvalidInputCount, graph.addNode(.sqrt, &.{ a, b }, .f32));
}

test "Graph modification after finalization" {
    const testing = std.testing;

    var graph = try Graph.init(testing.allocator);
    defer graph.deinit();

    const a = try graph.addPlaceholder(.f32);

    // Finalize graph
    try graph.finalize();

    // All modifications should fail
    try testing.expectError(error.GraphFinalized, graph.addPlaceholder(.f32));
    try testing.expectError(error.GraphFinalized, graph.addConstant(1.0));
    try testing.expectError(error.GraphFinalized, graph.addNode(.sqrt, &.{a}, .f32));
    try testing.expectError(error.GraphFinalized, graph.removeNode(a));
}

test "Complex graph topology" {
    const testing = std.testing;

    var graph = try Graph.init(testing.allocator);
    defer graph.deinit();

    // Create diamond pattern:
    //     a
    //    / \
    //   b   c
    //    \ /
    //     d
    const a = try graph.addPlaceholder(.f32);
    const b = try graph.addNode(.sqrt, &.{a}, .f32);
    const c = try graph.addNode(.sin, &.{a}, .f32);
    const d = try graph.addNode(.add, &.{ b, c }, .f32);

    // Verify topology
    const topo = try graph.topologicalSort();
    // Note: topo is cached by the graph, don't free it!

    try testing.expectEqual(@as(usize, 4), topo.len);

    // Find positions
    var positions = std.AutoHashMap(NodeId, usize).init(testing.allocator);
    defer positions.deinit();

    for (topo, 0..) |node_id, i| {
        try positions.put(node_id, i);
    }

    // a must come before b and c
    try testing.expect(positions.get(a).? < positions.get(b).?);
    try testing.expect(positions.get(a).? < positions.get(c).?);

    // b and c must come before d
    try testing.expect(positions.get(b).? < positions.get(d).?);
    try testing.expect(positions.get(c).? < positions.get(d).?);
}

test "Output node tracking" {
    const testing = std.testing;

    var graph = try Graph.init(testing.allocator);
    defer graph.deinit();

    // Add nodes to output_nodes list
    const a = try graph.addPlaceholder(.f32);
    const b = try graph.addNode(.sqrt, &.{a}, .f32);

    try graph.output_nodes.append(graph.arena.allocator(), b);

    // Validate with output nodes
    try validateIntegrity(&graph);

    // Remove output node
    try graph.removeNode(b);

    // Should fail validation
    try testing.expectError(error.OutputNodeRemoved, validateIntegrity(&graph));
}

test "Memory stress test" {
    const testing = std.testing;

    var graph = try Graph.init(testing.allocator);
    defer graph.deinit();

    // Create many nodes
    const node_count = 1000;
    var nodes = try testing.allocator.alloc(NodeId, node_count);
    defer testing.allocator.free(nodes);

    // Create chain of nodes
    nodes[0] = try graph.addPlaceholder(.f32);
    for (1..node_count) |i| {
        nodes[i] = try graph.addNode(.sqrt, &.{nodes[i - 1]}, .f32);
    }

    // Verify all exist
    for (nodes) |node| {
        try testing.expect(graph.hasNode(node));
    }

    // Get topology
    const topo = try graph.topologicalSort();
    // Note: topo is cached by the graph, don't free it!
    try testing.expectEqual(node_count, topo.len);

    // Remove some nodes from the end (so we don't break the chain)
    for (900..1000) |i| {
        try graph.removeNode(nodes[i]);
    }

    // Verify topology still works
    const topo2 = try graph.topologicalSort();
    // Note: topo2 is cached by the graph, don't free it!
    try testing.expectEqual(@as(usize, 900), topo2.len);
}

test "markOutput functionality" {
    const testing = std.testing;
    
    var graph = try Graph.init(testing.allocator);
    defer graph.deinit();
    
    // Create some nodes
    const a = try graph.addPlaceholder(.f32);
    const b = try graph.addNode(.sqrt, &.{a}, .f32);
    const c = try graph.addNode(.exp2, &.{b}, .f32);
    
    // Initially no output nodes
    try testing.expectEqual(@as(usize, 0), graph.output_nodes.items.len);
    
    // Mark b as output
    try graph.markOutput(b);
    try testing.expectEqual(@as(usize, 1), graph.output_nodes.items.len);
    try testing.expectEqual(b, graph.output_nodes.items[0]);
    
    // Mark c as output
    try graph.markOutput(c);
    try testing.expectEqual(@as(usize, 2), graph.output_nodes.items.len);
    
    // Marking same node again should be idempotent
    try graph.markOutput(b);
    try testing.expectEqual(@as(usize, 2), graph.output_nodes.items.len);
    
    // Test error on invalid node
    try testing.expectError(error.InvalidNodeId, graph.markOutput(999));
}

test "markParameter functionality" {
    const testing = std.testing;
    
    var graph = try Graph.init(testing.allocator);
    defer graph.deinit();
    
    // Create parameter node
    const w = try graph.addConstant(1.0);
    const b = try graph.addConstant(0.0);
    
    // Mark as parameters
    try graph.markParameter(w);
    try graph.markParameter(b);
    
    // Verify they are marked
    const w_node = graph.getNode(w).?;
    const b_node = graph.getNode(b).?;
    
    try testing.expect(w_node.metadata != null);
    try testing.expect(w_node.metadata.?.is_parameter);
    try testing.expect(b_node.metadata != null);
    try testing.expect(b_node.metadata.?.is_parameter);
    
    // Test error on invalid node
    try testing.expectError(error.InvalidNodeId, graph.markParameter(999));
}
