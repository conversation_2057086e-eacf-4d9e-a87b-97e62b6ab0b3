/// Zing Backend Infrastructure - Re-exports and Legacy Compatibility
///
/// This module provides backward compatibility by re-exporting types from
/// backend_types.zig. It also exports available backend implementations.
///
/// Design principles:
/// - All backend types are defined in backend_types.zig
/// - This module only re-exports for compatibility
/// - Backends import from backend_types.zig, not from here
/// - Compiler imports backends through this module

const std = @import("std");
const Allocator = std.mem.Allocator;

// Import backend types module
const backend_types = @import("backend_types");

// Import shared types
const types = @import("types");
const BufferId = types.BufferId;
const ExprId = types.ExprId;

// ===== Re-export Backend Types for Backward Compatibility =====
// All types are now defined in backend_types.zig

pub const BackendCapabilities = backend_types.BackendCapabilities;
pub const TargetArch = backend_types.TargetArch;
pub const VectorizationHints = backend_types.VectorizationHints;
pub const FusionHints = backend_types.FusionHints;
pub const MemoryHints = backend_types.MemoryHints;

// Re-export pattern types
pub const Pattern = backend_types.Pattern;
pub const PatternMatchFn = backend_types.PatternMatchFn;
pub const PatternCostFn = backend_types.PatternCostFn;
pub const PatternContext = backend_types.PatternContext;
pub const PatternMatch = backend_types.PatternMatch;

// Re-export execution types
pub const KernelFn = backend_types.KernelFn;
pub const KernelArgs = backend_types.KernelArgs;
pub const BackendArtifact = backend_types.BackendArtifact;
pub const KernelRegistry = backend_types.KernelRegistry;

// Re-export execution plan types
pub const ExecutionStep = backend_types.ExecutionStep;
pub const LivenessInterval = backend_types.LivenessInterval;
pub const ResolvedAllocation = backend_types.ResolvedAllocation;
pub const ResolvedMemoryPlan = backend_types.ResolvedMemoryPlan;
pub const SymbolicMemoryPlan = backend_types.SymbolicMemoryPlan;

// Re-export compiled graph
pub const CompiledGraph = backend_types.CompiledGraph;

// Re-export backend metadata types
pub const GatherMetadata = backend_types.GatherMetadata;
pub const BatchedMatMulMetadata = backend_types.BatchedMatMulMetadata;
pub const FusedUnaryMetadata = backend_types.FusedUnaryMetadata;

// Re-export error types
pub const BackendError = backend_types.BackendError;

// ===== Legacy Support =====

/// Legacy backend context type (deprecated)
/// New system uses compile-time backend selection without contexts
pub const BackendContext = opaque {};

// ===== Export Backend Implementations =====
// Export available backends as namespaces
pub const cpu = @import("backends/cpu.zig");
// Future backends:
// pub const cuda = @import("backends/cuda.zig");
// pub const metal = @import("backends/metal.zig");

// ===== Backend Interface Documentation =====
//
// Each backend implementation (cpu.zig, cuda.zig, etc.) must provide:
//
// 1. pub const name: []const u8 - Backend identifier
// 2. pub const capabilities: BackendCapabilities - Hardware capabilities
// 3. pub fn compile(graph: *Graph, allocator: Allocator) !CompiledGraph
//
// Backends should:
// - Import types from this module only
// - Be self-contained with no compiler imports
// - Be imported directly by compiler/compile.zig
//
// This design ensures:
// - No circular dependencies
// - Clear separation of concerns
// - Compile-time backend selection
// - Zero runtime overhead

// ===== Unit Tests =====

const testing = std.testing;

test "Backend capabilities" {
    // Test backend capabilities structure
    const caps = BackendCapabilities{
        .supports_simd = true,
        .simd_width = 256,
        .max_threads = 8,
    };
    
    try testing.expect(caps.supports_simd);
    try testing.expectEqual(@as(u32, 256), caps.simd_width);
}

test "Backend capabilities hints" {
    const caps = BackendCapabilities{
        .supports_simd = true,
        .simd_width = 256,
        .supports_tensor_cores = false,
        .memory_coalescing_alignment = 64,
        .max_threads = 8,
    };

    const vec_hints = caps.getVectorizationHints();
    try testing.expectEqual(@as(u32, 256), vec_hints.preferred_width);
    try testing.expectEqual(false, vec_hints.supports_mixed_precision);

    const mem_hints = caps.getMemoryHints();
    try testing.expectEqual(@as(u32, 64), mem_hints.preferred_alignment);
}