const std = @import("std");
const Allocator = std.mem.Allocator;

// ===== Core Identifiers =====

/// Numeric identifiers for graph entities
pub const NodeId = u32;
pub const TrackerId = u32;
pub const BufferId = u32;
pub const TensorId = u32;
pub const ExprId = u32;

/// Invalid sentinel values
pub const INVALID_NODE_ID: NodeId = std.math.maxInt(NodeId);
pub const INVALID_TRACKER_ID: TrackerId = std.math.maxInt(TrackerId);
pub const INVALID_BUFFER_ID: BufferId = std.math.maxInt(BufferId);
pub const INVALID_EXPR_ID: ExprId = std.math.maxInt(ExprId);

// ===== Shape Types =====

/// Index expression for efficient index computation
/// This enables broadcasting and view transformations without data copying
pub const IndexExpr = struct {
    /// Coefficients for each dimension (stride values)
    coeffs: []const i64,
    /// Modulo values for each dimension (shape values)
    /// Used for broadcasting where modulo=1 makes dimension "fake"
    mods: []const i64,
    /// Base offset
    offset: i64,

    const Self = @This();

    /// Compute physical index from logical flat index
    /// Uses comptime optimization when beneficial for static shapes
    pub fn compute(self: Self, logical_idx: usize) usize {
        // For small static shapes, use comptime-optimized path
        // Enable comptime optimization for small shapes (≤4D)
        if (self.mods.len <= 4 and self.mods.len > 0) {
            return self.computeComptimeOptimized(logical_idx);
        }

        // Default runtime path
        return self.computeRuntime(logical_idx);
    }

    /// Comptime-optimized computation for small static shapes
    fn computeComptimeOptimized(self: Self, logical_idx: usize) usize {
        var physical_idx = @as(usize, @intCast(self.offset));

        // Unrolled coordinate calculation for up to 4D
        var coords: [4]usize = undefined;
        var remaining = logical_idx;

        // Unroll the coordinate extraction loop
        comptime var i = 0;
        inline while (i < 4) : (i += 1) {
            if (i < self.mods.len) {
                const dim_idx = self.mods.len - 1 - i;
                const dim_size = @as(usize, @intCast(self.mods[dim_idx]));
                coords[dim_idx] = remaining % dim_size;
                remaining /= dim_size;
            }
        }

        // Unroll the physical index calculation
        comptime var j = 0;
        inline while (j < 4) : (j += 1) {
            if (j < self.coeffs.len) {
                physical_idx += coords[j] * @as(usize, @intCast(self.coeffs[j]));
            }
        }

        return physical_idx;
    }

    /// Runtime computation path for dynamic or large shapes
    fn computeRuntime(self: Self, logical_idx: usize) usize {
        var physical_idx = @as(usize, @intCast(self.offset));

        // Handle empty shape case (scalar or uninitialized)
        if (self.mods.len == 0) {
            return physical_idx;
        }

        // Inline coordinate calculation to avoid circular import
        var coords: [8]usize = undefined;

        // Convert logical flat index to coordinates using output shape (mods)
        var remaining = logical_idx;
        var i: usize = self.mods.len;
        while (i > 0) {
            i -= 1;
            const dim_size = @as(usize, @intCast(self.mods[i]));
            coords[i] = remaining % dim_size;
            remaining /= dim_size;
        }

        // Convert coordinates to physical index using coeffs (input strides)
        for (0..self.coeffs.len) |j| {
            physical_idx += coords[j] * @as(usize, @intCast(self.coeffs[j]));
        }

        return physical_idx;
    }
};

/// Concrete shape representation for execution.
///
/// This is a lightweight struct containing only resolved dimensions for kernel execution.
/// For symbolic shapes and view operations during graph building, see ShapeTracker.
///
/// For design rationale of why we have two shape types, see docs/shape_system_design.md
///
/// Design rationale:
/// - Graph building needs complex shape tracking (views, symbolic dims, broadcasting)
/// - Execution needs simple, fast access to concrete dimensions
/// - The two have fundamentally different requirements, justifying separate types
///
/// Lifecycle:
/// - ShapeTracker: Created during graph building, may contain symbolic dimensions
/// - Shape: Created from ShapeTracker during compilation when dims become concrete
///
/// This separation is NOT a "patch" but a deliberate architectural decision that:
/// 1. Reduces memory footprint during execution (no symbolic overhead)
/// 2. Simplifies kernel interfaces (no dynamic dispatch on dimension types)
/// 3. Enables compile-time shape validation where possible
/// 4. Keeps symbolic reasoning isolated to the compilation phase
pub const Shape = struct {
    dims: []const i64, // Concrete dimensions only
    strides: []const i64, // Memory strides for each dimension
    offset: i64, // Starting offset in elements
    index_expr: IndexExpr, // Expression for computing physical indices

    const Self = @This();

    pub fn numElements(self: Self) i64 {
        var total: i64 = 1;
        for (self.dims) |d| total *= d;
        return total;
    }

    pub fn isContiguous(self: Self) bool {
        var expected_stride: i64 = 1;
        for (0..self.dims.len) |i| {
            const idx = self.dims.len - 1 - i;
            if (self.strides[idx] != expected_stride) return false;
            expected_stride *= self.dims[idx];
        }
        return true;
    }

    /// Check if two shapes are compatible for element-wise operations
    pub fn isCompatible(self: Self, other: Self) bool {
        if (self.dims.len != other.dims.len) return false;
        for (self.dims, other.dims) |d1, d2| {
            if (d1 != d2) return false;
        }
        return true;
    }

    /// Get the size in bytes for a given data type
    pub fn byteSize(self: Self, dtype: DataType) usize {
        const num_elements = @as(usize, @intCast(self.numElements()));
        return num_elements * dtype.byteSize();
    }
};

// ===== Core Operation Types =====

/// Compute operations - the primitive operations that create graph nodes
pub const ComputeOp = enum(u8) {
    // Binary operations (2 inputs)
    add, // Element-wise addition
    mul, // Element-wise multiplication
    mod, // Element-wise modulo
    less_than, // Element-wise less-than comparison

    // Unary operations (1 input)
    recip, // Reciprocal 1/x
    sqrt, // Square root
    sin, // Sine function
    exp2, // Base-2 exponential
    log2, // Base-2 logarithm

    // Reduction operations (1 input + axis metadata)
    sum_reduce, // Sum reduction along axis
    max_reduce, // Max reduction along axis

    // Memory operations
    contiguous, // Make tensor contiguous in memory

    // Backend extension
    custom, // Custom fused operations for backends

    // ===== Classification Methods =====

    pub fn isElementwise(self: ComputeOp) bool {
        return switch (self) {
            .add, .mul, .mod, .less_than, .recip, .sqrt, .sin, .exp2, .log2 => true,
            .sum_reduce, .max_reduce, .contiguous, .custom => false,
        };
    }

    pub fn isReduction(self: ComputeOp) bool {
        return self == .sum_reduce or self == .max_reduce;
    }

    pub fn isUnary(self: ComputeOp) bool {
        return switch (self) {
            .recip, .sqrt, .sin, .exp2, .log2, .sum_reduce, .max_reduce, .contiguous => true,
            .add, .mul, .mod, .less_than => false,
            .custom => false, // Depends on specific custom operation
        };
    }

    pub fn isBinary(self: ComputeOp) bool {
        return switch (self) {
            .add, .mul, .mod, .less_than => true,
            .recip, .sqrt, .sin, .exp2, .log2, .sum_reduce, .max_reduce, .contiguous => false,
            .custom => false, // Depends on specific custom operation
        };
    }

    pub fn expectedInputCount(self: ComputeOp) usize {
        return switch (self) {
            // Binary operations
            .add, .mul, .mod, .less_than => 2,

            // Unary operations
            .recip, .sqrt, .sin, .exp2, .log2 => 1,

            // Reductions (single input + axis in metadata)
            .sum_reduce, .max_reduce => 1,

            // Memory operations
            .contiguous => 1,

            // Custom operations validate themselves
            .custom => 0, // Skip validation
        };
    }

    pub fn isCommutative(self: ComputeOp) bool {
        return self == .add or self == .mul;
    }

    pub fn isAssociative(self: ComputeOp) bool {
        return self == .add or self == .mul;
    }

    pub fn isShapePreserving(self: ComputeOp) bool {
        return switch (self) {
            .add, .mul, .mod, .less_than, .recip, .sqrt, .sin, .exp2, .log2 => true,
            .sum_reduce, .max_reduce => false,
            .contiguous => true,
            .custom => false, // Depends on specific operation
        };
    }

    pub fn isPrimitive(self: ComputeOp) bool {
        return self != .custom;
    }
};

/// Data source types
pub const DataSource = enum(u8) {
    constant,
    placeholder,
    parameter, // Reference to persistent tensor in ParameterStore
};

// ===== Data Types =====

pub const DataType = enum(u8) {
    f16,
    f32,
    f64,
    i8,
    i16,
    i32,
    i64,
    u8,
    u16,
    u32,
    u64,
    bool,

    pub fn byteSize(self: DataType) usize {
        return switch (self) {
            .bool, .i8, .u8 => 1,
            .f16, .i16, .u16 => 2,
            .f32, .i32, .u32 => 4,
            .f64, .i64, .u64 => 8,
        };
    }

    pub fn isFloating(self: DataType) bool {
        return switch (self) {
            .f16, .f32, .f64 => true,
            else => false,
        };
    }

    pub fn isSigned(self: DataType) bool {
        return switch (self) {
            .f16, .f32, .f64, .i8, .i16, .i32, .i64 => true,
            else => false,
        };
    }
};

// ===== Node Specification =====

/// Unified node specification - the primary way to represent node types
pub const NodeSpec = union(enum) {
    data: DataSource,
    compute: ComputeOp,

    // ===== Classification Methods =====

    pub fn isDataSource(self: NodeSpec) bool {
        return self == .data;
    }

    pub fn isCompute(self: NodeSpec) bool {
        return self == .compute;
    }

    pub fn isElementwise(self: NodeSpec) bool {
        return switch (self) {
            .data => false,
            .compute => |op| op.isElementwise(),
        };
    }

    pub fn isReduction(self: NodeSpec) bool {
        return switch (self) {
            .data => false,
            .compute => |op| op.isReduction(),
        };
    }

    pub fn isShapePreserving(self: NodeSpec) bool {
        return switch (self) {
            .data => true, // Data sources preserve shape
            .compute => |op| op.isShapePreserving(),
        };
    }

    pub fn isPrimitive(self: NodeSpec) bool {
        return switch (self) {
            .data => true,
            .compute => |op| op.isPrimitive(),
        };
    }
};

// ===== Device Types =====

pub const Device = enum {
    cpu,
    cuda,
    metal,
    wasm,

    pub fn toString(self: Device) []const u8 {
        return @tagName(self);
    }

    pub fn fromString(str: []const u8) !Device {
        inline for (@typeInfo(Device).@"enum".fields) |field| {
            if (std.mem.eql(u8, str, field.name)) {
                return @enumFromInt(field.value);
            }
        }
        std.log.err("fromString: unknown device '{s}'", .{str});
        return error.UnknownDevice;
    }
};

// ===== Core Error Types =====

/// Shared error set for shape operations
pub const ShapeError = error{
    IncompatibleShapes,
    InvalidAxis,
    UnboundSymbol,
    ShapeMismatch,
    InvalidBroadcast,
    InvalidReshape,
    InvalidPermutation,
};

/// Shared error set for graph operations
pub const GraphError = error{
    NodeNotFound,
    CyclicDependency,
    InvalidInput,
    DuplicateNode,
    InvalidOperation,
    DuplicateCustomOp,
    OrphanedCustomOp,
    GraphFinalized,
    InvalidNodeId,
    InvalidInputCount,
    NodeHasConsumers,
    OldNodeNotFound,
    NewNodeNotFound,
    SubstitutionWouldCreateCycle,
    NotAReductionNode,
    OutOfMemory,
    InconsistentNodeMap,
    UninitializedNodeId,
};

/// Shared error set for compilation
pub const CompilationError = error{
    UnsupportedOperation,
    InvalidGraph,
    BackendNotFound,
    CompilationFailed,
    OptimizationFailed,
};

/// TensorHandle stub - needed by many modules
/// The actual implementation is in tensor.zig to avoid circular deps
pub const TensorHandle = struct {
    node_id: NodeId,
    shape: @import("shape").ShapeTracker,
    dtype: DataType,
};

// ===== Compiler-related Types =====

/// Describes a fusion pattern for AOT kernel selection
pub const FusionPattern = struct {
    /// Operations in the fused sequence (only compute ops can be fused)
    ops: []const ComputeOp,
    /// Data type for the fusion
    dtype: DataType,
    /// Number of inputs
    input_count: u8,
    /// Whether pattern includes reductions
    has_reduction: bool,
};

/// Custom operation representation for AOT fusion
pub const CustomOp = struct {
    /// Unique identifier for the fused operation pattern
    id: u64,
    /// Backend that will execute this operation
    backend_name: []const u8,
    /// Fusion pattern information for AOT kernel selection
    fusion_pattern: FusionPattern,
    /// Human-readable name for debugging
    debug_name: []const u8,

    pub fn hash(self: CustomOp) u64 {
        return self.id;
    }

    pub fn eql(a: CustomOp, b: CustomOp) bool {
        return a.id == b.id;
    }
};

// ===== Shared Map Types =====

/// Map from BufferId to size for memory planning
pub const BufferSizeMap = std.AutoHashMapUnmanaged(BufferId, usize);

// ===== Broadcasting Types =====

/// Strategy for handling broadcasting during execution
pub const BroadcastStrategy = enum {
    /// No broadcasting needed - shapes already match
    no_broadcast,
    /// Materialize broadcast by copying/expanding data
    materialize,
    /// Use optimized scalar kernel (avoid materialization)
    scalar_kernel,
    /// Use SIMD-optimized vector kernel
    vector_kernel,
    /// Use backend-specific optimization
    backend_optimized,
};

/// Pattern classification for common broadcast scenarios
pub const BroadcastPattern = enum {
    /// No broadcasting needed
    none,
    /// Broadcasting scalar to tensor: [1] + [n, m]
    scalar_to_tensor,
    /// Broadcasting vector to matrix: [m] + [n, m]
    vector_to_matrix,
    /// Broadcasting matrix: [1, m] + [n, m]
    matrix_row_broadcast,
    /// Broadcasting matrix: [n, 1] + [n, m]
    matrix_col_broadcast,
    /// Complex multi-dimensional broadcasting
    general,

    pub fn isSimple(self: BroadcastPattern) bool {
        return switch (self) {
            .none, .scalar_to_tensor, .vector_to_matrix => true,
            .matrix_row_broadcast, .matrix_col_broadcast, .general => false,
        };
    }
};

/// Describes how a single input needs to be transformed for broadcasting
pub const InputTransform = struct {
    /// Whether this input needs broadcasting transformation
    needs_broadcast: bool,
    /// Original shape of the input
    original_shape: []const i64,
    /// Target shape after broadcasting
    target_shape: []const i64,
    /// Pattern classification for optimization
    pattern: BroadcastPattern,
    /// Recommended strategy for this transform
    strategy: BroadcastStrategy,
};

/// Complete plan for handling broadcasting in an operation
pub const BroadcastPlan = struct {
    /// Shape that all inputs will be broadcast to
    result_shape: []const i64,
    /// How each input should be transformed
    input_transforms: []const InputTransform,
    /// Overall pattern classification
    overall_pattern: BroadcastPattern,
    /// Memory allocator used for this plan
    allocator: std.mem.Allocator,

    pub fn deinit(self: *BroadcastPlan) void {
        self.allocator.free(self.result_shape);
        for (self.input_transforms) |*transform| {
            self.allocator.free(transform.original_shape);
            self.allocator.free(transform.target_shape);
        }
        self.allocator.free(self.input_transforms);
    }

    pub fn needsBroadcasting(self: BroadcastPlan) bool {
        return self.overall_pattern != .none;
    }

    pub fn isSimpleBroadcast(self: BroadcastPlan) bool {
        return self.overall_pattern.isSimple();
    }
};

// ===== Backend Types - Moved to backend_types.zig =====
//
// All backend-specific types have been moved to backend_types.zig to enforce
// proper module boundaries. This prevents circular dependencies by ensuring
// backends import only from backend_types.zig and core modules.
//
// The following types are now in backend_types.zig:
// - BackendCapabilities
// - VectorizationHints, FusionHints, MemoryHints
// - KernelFn, KernelArgs, KernelRegistry
// - BackendArtifact
// - ExecutionStep, LivenessInterval, ResolvedAllocation, ResolvedMemoryPlan
// - CompiledGraph
// - GatherMetadata, BatchedMatMulMetadata, FusedUnaryMetadata
// - BackendError
//
// Import them from backend_types.zig when needed.

// ===== Unit Tests =====

test "ComputeOp classification" {
    const testing = std.testing;

    // Test elementwise operations
    try testing.expect(ComputeOp.add.isElementwise());
    try testing.expect(ComputeOp.recip.isElementwise());
    try testing.expect(!ComputeOp.sum_reduce.isElementwise());

    // Test reductions
    try testing.expect(ComputeOp.sum_reduce.isReduction());
    try testing.expect(ComputeOp.max_reduce.isReduction());
    try testing.expect(!ComputeOp.add.isReduction());

    // Test arity
    try testing.expect(ComputeOp.add.isBinary());
    try testing.expect(ComputeOp.sqrt.isUnary());
    try testing.expect(!ComputeOp.add.isUnary());
    try testing.expect(!ComputeOp.sqrt.isBinary());

    // Test input counts
    try testing.expectEqual(@as(usize, 2), ComputeOp.add.expectedInputCount());
    try testing.expectEqual(@as(usize, 1), ComputeOp.sqrt.expectedInputCount());
    try testing.expectEqual(@as(usize, 1), ComputeOp.sum_reduce.expectedInputCount());
}

test "ComputeOp properties" {
    const testing = std.testing;

    // Test commutativity
    try testing.expect(ComputeOp.add.isCommutative());
    try testing.expect(ComputeOp.mul.isCommutative());
    try testing.expect(!ComputeOp.mod.isCommutative());

    // Test associativity
    try testing.expect(ComputeOp.add.isAssociative());
    try testing.expect(ComputeOp.mul.isAssociative());
    try testing.expect(!ComputeOp.mod.isAssociative());

    // Test shape preservation
    try testing.expect(ComputeOp.add.isShapePreserving());
    try testing.expect(!ComputeOp.sum_reduce.isShapePreserving());
}

test "NodeSpec classification" {
    const testing = std.testing;

    // Test data sources
    const data_spec = NodeSpec{ .data = .constant };
    try testing.expect(data_spec.isDataSource());
    try testing.expect(!data_spec.isCompute());
    try testing.expect(!data_spec.isElementwise());

    // Test compute operations
    const compute_spec = NodeSpec{ .compute = .add };
    try testing.expect(!compute_spec.isDataSource());
    try testing.expect(compute_spec.isCompute());
    try testing.expect(compute_spec.isElementwise());

    // Test shape preservation
    try testing.expect(data_spec.isShapePreserving());
    try testing.expect(compute_spec.isShapePreserving());

    const reduce_spec = NodeSpec{ .compute = .sum_reduce };
    try testing.expect(!reduce_spec.isShapePreserving());
}

test "DataType properties" {
    const testing = std.testing;

    // Test byte sizes
    try testing.expectEqual(@as(usize, 4), DataType.f32.byteSize());
    try testing.expectEqual(@as(usize, 8), DataType.f64.byteSize());
    try testing.expectEqual(@as(usize, 1), DataType.i8.byteSize());

    // Test floating point types
    try testing.expect(DataType.f32.isFloating());
    try testing.expect(!DataType.i32.isFloating());

    // Test signed types
    try testing.expect(DataType.f32.isSigned());
    try testing.expect(DataType.i32.isSigned());
    try testing.expect(!DataType.u32.isSigned());
}

test "Device string conversion" {
    const testing = std.testing;

    try testing.expectEqualStrings("cpu", Device.cpu.toString());
    try testing.expectEqual(Device.cuda, try Device.fromString("cuda"));
    try testing.expectError(error.UnknownDevice, Device.fromString("invalid"));
}

test "ExprId conversion" {
    const testing = std.testing;

    const id: ExprId = 42;
    try testing.expectEqual(@as(u32, 42), id);

    const invalid = INVALID_EXPR_ID;
    try testing.expectEqual(std.math.maxInt(u32), invalid);
}
