//TODO: to check if the logic here is all in right place? Shall we split across files?

const std = @import("std");
const Allocator = std.mem.Allocator;

// Import shared types
const types = @import("types");
const DataType = types.DataType;
const Device = types.Device;
const NodeId = types.NodeId;
const BufferId = types.BufferId;

// Import compile-time layout optimization
const comptime_layout = @import("storage/comptime_layout.zig");
pub const ComptimeLayout = comptime_layout.ComptimeLayout;
pub const ComptimeMemoryPool = comptime_layout.ComptimeMemoryPool;
pub const ComptimeTiling = comptime_layout.ComptimeTiling;
pub const LayoutConfig = comptime_layout.LayoutConfig;

// Helper function to convert DataType to Zig type
fn dtypeToZigType(dtype: DataType) type {
    return switch (dtype) {
        .f32 => f32,
        .f64 => f64,
        .i32 => i32,
        .i64 => i64,
        .u32 => u32,
        .u64 => u64,
        .i16 => i16,
        .u16 => u16,
        .i8 => i8,
        .u8 => u8,
        .f16 => f16,
        .bool => bool,
    };
}

// ===== Backend Storage Configuration =====

/// Backend-specific storage configuration that can be customized per device
pub const BackendStorageConfig = struct {
    /// Cache line size for the target architecture
    cache_line_size: usize = 64,
    
    /// L1 cache size for tiling optimizations
    l1_cache_size: usize = 32 * 1024,
    
    /// SIMD alignment requirement for the backend
    simd_alignment: usize = 32,
    
    /// Preferred tile size for matrix operations
    preferred_tile_size: usize = 64,
    
    /// Maximum vector width in bytes
    max_vector_width: usize = 32,
    
    /// Whether the backend supports unaligned access efficiently
    supports_unaligned: bool = false,
    
    /// Memory page size for large allocations
    page_size: usize = 4096,
    
    /// Get default configuration for a device type
    pub fn default(device: Device) BackendStorageConfig {
        return switch (device) {
            .cpu => .{
                // x86-64 with AVX defaults
                .cache_line_size = 64,
                .l1_cache_size = 32 * 1024,
                .simd_alignment = 32,  // AVX
                .preferred_tile_size = 64,
                .max_vector_width = 32,
                .supports_unaligned = true,
                .page_size = 4096,
            },
            .cuda => .{
                // GPU-optimized settings
                .cache_line_size = 128,
                .l1_cache_size = 48 * 1024,
                .simd_alignment = 256,  // Warp-aligned
                .preferred_tile_size = 32,  // Warp size
                .max_vector_width = 128,
                .supports_unaligned = false,
                .page_size = 65536,
            },
            .metal => .{
                // Metal/Apple GPU settings
                .cache_line_size = 64,
                .l1_cache_size = 32 * 1024,
                .simd_alignment = 256,
                .preferred_tile_size = 32,
                .max_vector_width = 128,
                .supports_unaligned = false,
                .page_size = 16384,
            },
            .wasm => .{
                // WebAssembly SIMD
                .cache_line_size = 64,
                .l1_cache_size = 16 * 1024,
                .simd_alignment = 16,  // WASM SIMD is 128-bit
                .preferred_tile_size = 32,
                .max_vector_width = 16,
                .supports_unaligned = false,
                .page_size = 65536,
            },
        };
    }
};

// ===== Core Storage Types =====

/// A unique, stable identifier for a piece of state in the parameter store
pub const StateId = u64;

/// Buffer management key for caching and deduplication
pub const BufferKey = struct {
    device: Device,
    size: usize,
    alignment: usize,

    pub fn hash(self: BufferKey) u64 {
        var h = std.hash.Wyhash.init(0);
        h.update(std.mem.asBytes(&self.device));
        h.update(std.mem.asBytes(&self.size));
        h.update(std.mem.asBytes(&self.alignment));
        return h.final();
    }

    pub fn eql(a: BufferKey, b: BufferKey) bool {
        return a.device == b.device and
            a.size == b.size and
            a.alignment == b.alignment;
    }
};

/// Allocation statistics for monitoring memory usage
pub const AllocationStats = struct {
    total_allocated: usize = 0,
    peak_allocated: usize = 0,
    allocation_count: usize = 0,
    deallocation_count: usize = 0,

    pub fn recordAllocation(self: *AllocationStats, size: usize) void {
        self.total_allocated += size;
        self.allocation_count += 1;
        self.peak_allocated = @max(self.peak_allocated, self.total_allocated);
    }

    pub fn recordDeallocation(self: *AllocationStats, size: usize) void {
        self.total_allocated -= size;
        self.deallocation_count += 1;
    }
};

// ===== Tensor Data Types =====

/// Non-owning, read-only view of tensor data
pub const TensorView = struct {
    data: []const u8,
    shape: []const i64,
    dtype: DataType,
    strides: ?[]const i64 = null,

    pub fn toOwned(self: TensorView, allocator: Allocator) !OwnedTensor {
        const data_copy = try allocator.alloc(u8, self.data.len);
        @memcpy(data_copy, self.data);

        const shape_copy = try allocator.dupe(i64, self.shape);

        const strides_copy = if (self.strides) |s|
            try allocator.dupe(i64, s)
        else
            null;

        return OwnedTensor{
            .data = data_copy,
            .shape = shape_copy,
            .dtype = self.dtype,
            .strides = strides_copy,
            .allocator = allocator,
        };
    }
};

/// An owning tensor that manages its own memory
pub const OwnedTensor = struct {
    data: []u8,
    shape: []i64,
    dtype: DataType,
    strides: ?[]i64,
    allocator: Allocator,

    pub fn init(allocator: Allocator, shape: []const i64, dtype: DataType) !OwnedTensor {
        var total_elements: usize = 1;
        for (shape) |dim| {
            total_elements *= @intCast(dim);
        }

        const data_size = total_elements * dtype.byteSize();
        const data = try allocator.alloc(u8, data_size);
        const shape_copy = try allocator.dupe(i64, shape);

        return OwnedTensor{
            .data = data,
            .shape = shape_copy,
            .dtype = dtype,
            .strides = null,
            .allocator = allocator,
        };
    }

    pub fn deinit(self: *OwnedTensor) void {
        self.allocator.free(self.data);
        self.allocator.free(self.shape);
        if (self.strides) |s| {
            self.allocator.free(s);
        }
    }

    pub fn view(self: *const OwnedTensor) TensorView {
        return .{
            .data = self.data,
            .shape = self.shape,
            .dtype = self.dtype,
            .strides = self.strides,
        };
    }
};

/// A mutable view into a raw memory buffer, used by backend kernels
pub const BufferView = struct {
    data: []u8,
    shape: []const i64,
    dtype: DataType,
};

/// Tensor metadata for enhanced buffer operations
pub const TensorMetadata = struct {
    shape: []const i64,
    dtype: DataType,
    strides: ?[]const i64 = null,
    layout: DataLayout = .row_major,

    pub fn elementCount(self: TensorMetadata) usize {
        var count: usize = 1;
        for (self.shape) |dim| {
            count *= @intCast(dim);
        }
        return count;
    }

    pub fn byteSize(self: TensorMetadata) usize {
        return self.elementCount() * self.dtype.byteSize();
    }
};

// ===== Memory Layout Types =====

/// Memory layout for tensor data
pub const DataLayout = enum {
    row_major, // C-style, last dimension contiguous
    column_major, // Fortran-style, first dimension contiguous

    pub fn isContiguous(self: DataLayout, strides: []const i64, shape: []const i64) bool {
        return switch (self) {
            .row_major => checkRowMajorContiguous(strides, shape),
            .column_major => checkColumnMajorContiguous(strides, shape),
        };
    }
};

/// Memory format for different tensor storage strategies
pub const MemoryFormat = enum {
    dense, // Standard dense tensor
    sparse_coo, // Coordinate format sparse
    sparse_csr, // Compressed sparse row
    sparse_csc, // Compressed sparse column
};

// Helper functions for layout checking
fn checkRowMajorContiguous(strides: []const i64, shape: []const i64) bool {
    if (strides.len != shape.len) return false;
    if (strides.len == 0) return true;

    var expected_stride: i64 = 1;
    var i = strides.len;
    while (i > 0) {
        i -= 1;
        if (strides[i] != expected_stride) return false;
        expected_stride *= shape[i];
    }
    return true;
}

fn checkColumnMajorContiguous(strides: []const i64, shape: []const i64) bool {
    if (strides.len != shape.len) return false;
    if (strides.len == 0) return true;

    var expected_stride: i64 = 1;
    for (strides, shape) |stride, dim| {
        if (stride != expected_stride) return false;
        expected_stride *= dim;
    }
    return true;
}

// ===== DataStorage: Ephemeral Execution Memory =====

// Import buffer pool if available
// TODO: Import conditionally when buffer pool is implemented
// const buffer_pool = @import("execution/buffer_pool.zig");
const buffer_pool = null; // Disabled for now

/// Device-specific memory allocation interface with optional pooling
const DeviceMemory = struct {
    fn allocate(device: Device, allocator: Allocator, size: usize) ![]u8 {
        // Try to use buffer pool if available
        if (buffer_pool) |pool_mod| {
            const pool = pool_mod.getGlobalPool(allocator) catch null;
            if (pool) |p| {
                return p.acquire(size) catch {
                    // Fall back to regular allocation
                    return try allocator.alloc(u8, size);
                };
            }
        }
        
        // V1: All devices use regular allocator, backends handle device-specific memory
        _ = device;
        return try allocator.alloc(u8, size);
    }

    fn free(device: Device, allocator: Allocator, memory: []u8) void {
        // Try to return to pool if available
        if (buffer_pool) |pool_mod| {
            const pool = pool_mod.getGlobalPool(allocator) catch null;
            if (pool) |p| {
                p.release(memory) catch {
                    // Fall back to regular free
                    allocator.free(memory);
                };
                return;
            }
        }
        
        _ = device;
        allocator.free(memory);
    }
};

/// Manages ephemeral tensor memory for a single graph execution using an arena allocator
pub const DataStorage = struct {
    const Self = @This();

    allocator: Allocator,
    device: Device,

    /// The single memory block for all intermediate tensors
    arena: []u8,

    /// The offset into the arena for the next allocation
    offset: usize,

    /// Maps a NodeId to its allocated memory slice within the arena
    allocations: std.AutoHashMapUnmanaged(NodeId, []u8),

    /// Maps NodeId to tensor metadata for enhanced buffer access
    metadata: std.AutoHashMapUnmanaged(NodeId, TensorMetadata),

    /// Statistics for monitoring
    stats: AllocationStats,
    
    /// Track if this storage was allocated from pool
    from_pool: bool,
    
    /// Backend-specific storage configuration
    backend_config: BackendStorageConfig,

    /// Initializes a new DataStorage with a fixed-size memory arena
    pub fn init(allocator: Allocator, device: Device, total_size: usize) !Self {
        return initWithConfig(allocator, device, total_size, BackendStorageConfig.default(device));
    }
    
    /// Initializes a new DataStorage with a custom backend configuration
    pub fn initWithConfig(allocator: Allocator, device: Device, total_size: usize, config: BackendStorageConfig) !Self {
        const arena_memory = try DeviceMemory.allocate(device, allocator, total_size);

        return Self{
            .allocator = allocator,
            .device = device,
            .arena = arena_memory,
            .offset = 0,
            .allocations = .{},
            .metadata = .{},
            .stats = .{},
            .from_pool = buffer_pool != null,
            .backend_config = config,
        };
    }

    /// Releases the entire memory arena
    pub fn deinit(self: *Self) void {
        // Free metadata shape arrays before releasing the hash map
        var iter = self.metadata.iterator();
        while (iter.next()) |entry| {
            self.allocator.free(entry.value_ptr.shape);
            if (entry.value_ptr.strides) |strides| {
                self.allocator.free(strides);
            }
        }
        self.metadata.deinit(self.allocator);
        
        self.allocations.deinit(self.allocator);
        DeviceMemory.free(self.device, self.allocator, self.arena);
    }

    /// Get recommended alignment for a data type based on backend configuration
    pub fn getRecommendedAlignment(self: *const Self, dtype: DataType) usize {
        const element_size = dtype.byteSize();
        const vector_elements = self.backend_config.max_vector_width / element_size;
        _ = @alignOf(dtypeToZigType(dtype)); // Could be used for future optimizations
        
        // Use the minimum of SIMD alignment and what's needed for the vector width
        return @min(self.backend_config.simd_alignment, vector_elements * element_size);
    }
    
    /// Allocates a buffer for a tensor from the arena
    /// This is a simple and fast "bump" allocation
    pub fn allocate(self: *Self, node_id: NodeId, size: usize, alignment: usize) ![]u8 {
        const enable_debug = @import("build_options").enable_debug_logs;
        if (enable_debug) {
            std.debug.print("DataStorage.allocate: node {} requesting {} bytes\n", .{ node_id, size });
        }

        const aligned_offset = std.mem.alignForward(usize, self.offset, alignment);

        if (aligned_offset + size > self.arena.len) {
            std.log.err("allocate: out of memory - requested {} bytes at alignment {}, only {} bytes available", 
                       .{ size, alignment, self.arena.len - aligned_offset });
            return error.OutOfMemory;
        }

        const buffer = self.arena[aligned_offset .. aligned_offset + size];
        self.offset = aligned_offset + size;

        try self.allocations.put(self.allocator, node_id, buffer);
        self.stats.recordAllocation(size);

        if (enable_debug) {
            std.debug.print("  Allocated at offset {}, buffer len = {}\n", .{ aligned_offset, buffer.len });
        }

        return buffer;
    }

    /// Allocates a buffer with tensor metadata for enhanced backend operations
    pub fn allocateWithMetadata(self: *Self, node_id: NodeId, shape: []const i64, dtype: DataType, alignment: usize, strides: ?[]const i64) ![]u8 {
        const metadata = TensorMetadata{
            .shape = try self.allocator.dupe(i64, shape),
            .dtype = dtype,
            .strides = if (strides) |s| try self.allocator.dupe(i64, s) else null,
            .layout = .row_major,
        };

        const size = metadata.byteSize();

        const enable_debug = @import("build_options").enable_debug_logs;
        if (enable_debug) {
            std.debug.print("DataStorage.allocateWithMetadata: node {} shape [", .{node_id});
            for (shape) |d| std.debug.print("{any} ", .{d});
            std.debug.print("] dtype {s} = {} bytes\n", .{ @tagName(dtype), size });
        }

        const buffer = try self.allocate(node_id, size, alignment);

        try self.metadata.put(self.allocator, node_id, metadata);

        return buffer;
    }

    /// Retrieves the buffer for a given node
    pub fn getBuffer(self: *const Self, node_id: NodeId) ?[]u8 {
        return self.allocations.get(node_id);
    }

    /// Gets a tensor view for a given node with full metadata
    pub fn getTensor(self: *const Self, node_id: NodeId) !TensorView {
        const buffer = self.getBuffer(node_id) orelse {
            std.log.err("getTensor: buffer not found for node {}", .{node_id});
            return error.BufferNotFound;
        };
        const metadata = self.metadata.get(node_id) orelse {
            std.log.err("getTensor: metadata not found for node {}", .{node_id});
            return error.MetadataNotFound;
        };

        return TensorView{
            .data = buffer,
            .shape = metadata.shape,
            .dtype = metadata.dtype,
            .strides = metadata.strides,
        };
    }

    /// Gets buffer with metadata for backend kernels
    pub fn getBufferWithMetadata(self: *const Self, node_id: NodeId) !struct {
        data: []u8,
        shape: []const i64,
        dtype: DataType,
        strides: ?[]const i64,
        layout: DataLayout,
    } {
        const buffer = self.getBuffer(node_id) orelse {
            std.log.err("getBufferWithMetadata: buffer not found for node {}", .{node_id});
            return error.BufferNotFound;
        };
        const metadata = self.metadata.get(node_id) orelse {
            std.log.err("getBufferWithMetadata: metadata not found for node {}", .{node_id});
            return error.MetadataNotFound;
        };

        return .{
            .data = buffer,
            .shape = metadata.shape,
            .dtype = metadata.dtype,
            .strides = metadata.strides,
            .layout = metadata.layout,
        };
    }

    /// Gets tensor metadata for a node (without buffer data)
    pub fn getMetadata(self: *const Self, node_id: NodeId) ?TensorMetadata {
        return self.metadata.get(node_id);
    }

    /// Resets the allocator for a new run, avoiding deallocation and reallocation
    pub fn reset(self: *Self) void {
        self.offset = 0;
        
        // Clear allocations map
        self.allocations.clearAndFree(self.allocator);
        self.allocations = .{};

        // Free metadata shape arrays and clear map
        var iter = self.metadata.iterator();
        while (iter.next()) |entry| {
            self.allocator.free(entry.value_ptr.shape);
            if (entry.value_ptr.strides) |strides| {
                self.allocator.free(strides);
            }
        }
        self.metadata.clearAndFree(self.allocator);
        self.metadata = .{};

        // Reset stats for new execution
        self.stats = .{};
    }

    /// Ensures the arena has at least the specified capacity
    pub fn ensureCapacity(self: *Self, required_size: usize) !void {
        if (self.arena.len >= required_size) return;

        // Clear metadata before reallocating to free shape arrays
        var iter = self.metadata.iterator();
        while (iter.next()) |entry| {
            self.allocator.free(entry.value_ptr.shape);
            if (entry.value_ptr.strides) |strides| {
                self.allocator.free(strides);
            }
        }
        self.metadata.clearAndFree(self.allocator);
        self.allocations.clearAndFree(self.allocator);

        // Need to reallocate the arena
        DeviceMemory.free(self.device, self.allocator, self.arena);
        self.arena = try DeviceMemory.allocate(self.device, self.allocator, required_size);
        self.offset = 0;
        
        // Re-initialize empty hash maps
        self.allocations = .{};
        self.metadata = .{};
        self.from_pool = buffer_pool != null;
    }

    /// Mark a buffer as valid (used for dependency tracking)
    pub fn markValid(self: *Self, node_id: NodeId, output_idx: u8) !void {
        _ = self;
        _ = node_id;
        _ = output_idx;
        // V1: No-op, validation handled elsewhere
    }

    /// Retrieves buffer by BufferId (for new execution system)
    pub fn getBufferById(self: *const Self, buffer_id: BufferId) ?[]u8 {
        // V1: Simple mapping - buffer_id is NodeId
        const node_id: NodeId = buffer_id;
        return self.getBuffer(node_id);
    }
};

// ===== ParameterStore: Persistent Model State =====

/// Manages the lifecycle of all model parameters, buffers, and their gradients
pub const ParameterStore = struct {
    const Self = @This();

    allocator: Allocator,

    // Core state storage
    tensors: std.AutoHashMapUnmanaged(StateId, OwnedTensor),

    // Metadata to track the role of each tensor
    parameters: std.ArrayListUnmanaged(StateId),
    buffers: std.ArrayListUnmanaged(StateId),

    // Gradients are associated with trainable parameters
    grads: std.AutoHashMapUnmanaged(StateId, OwnedTensor),

    next_id: StateId,

    /// Initializes a new, empty parameter store
    pub fn init(allocator: Allocator) Self {
        return Self{
            .allocator = allocator,
            .tensors = .{},
            .parameters = .{},
            .buffers = .{},
            .grads = .{},
            .next_id = 0,
        };
    }

    /// Releases all memory owned by the store
    pub fn deinit(self: *Self) void {
        // Deinitialize all tensors
        var tensor_iter = self.tensors.iterator();
        while (tensor_iter.next()) |entry| {
            entry.value_ptr.deinit();
        }

        var grad_iter = self.grads.iterator();
        while (grad_iter.next()) |entry| {
            entry.value_ptr.deinit();
        }

        self.tensors.deinit(self.allocator);
        self.parameters.deinit(self.allocator);
        self.buffers.deinit(self.allocator);
        self.grads.deinit(self.allocator);
    }

    fn createTensor(self: *Self, shape: []const i64, dtype: DataType, comptime init_fn: anytype) !StateId {
        const id = self.next_id;
        self.next_id += 1;

        var tensor = try OwnedTensor.init(self.allocator, shape, dtype);
        try init_fn(&tensor);

        try self.tensors.put(self.allocator, id, tensor);
        return id;
    }

    /// Creates a new trainable parameter, initializes it, and stores it
    /// Returns a stable ID for the new parameter
    pub fn createParameter(
        self: *Self,
        shape: []const i64,
        dtype: DataType,
        comptime init_fn: fn (*OwnedTensor) anyerror!void,
    ) !StateId {
        const id = try self.createTensor(shape, dtype, init_fn);
        try self.parameters.append(self.allocator, id);
        return id;
    }

    /// Creates a new non-trainable buffer, initializes it, and stores it
    pub fn createBuffer(
        self: *Self,
        shape: []const i64,
        dtype: DataType,
        comptime init_fn: fn (*OwnedTensor) anyerror!void,
    ) !StateId {
        const id = try self.createTensor(shape, dtype, init_fn);
        try self.buffers.append(self.allocator, id);
        return id;
    }

    /// Retrieves a tensor (parameter or buffer) by its ID
    pub fn getTensor(self: *const Self, id: StateId) ?*const OwnedTensor {
        return self.tensors.getPtr(id);
    }

    /// Retrieves a mutable tensor (parameter or buffer) by its ID
    pub fn getTensorMut(self: *Self, id: StateId) ?*OwnedTensor {
        return self.tensors.getPtr(id);
    }

    /// Gets or creates the gradient tensor for a given parameter ID
    pub fn getOrCreateGrad(self: *Self, param_id: StateId) !*OwnedTensor {
        if (self.grads.getPtr(param_id)) |grad| {
            return grad;
        }

        const param = self.tensors.getPtr(param_id) orelse {
            std.log.err("getOrCreateGrad: parameter {} not found", .{param_id});
            return error.ParameterNotFound;
        };
        var grad_tensor = try OwnedTensor.init(self.allocator, param.shape, param.dtype);
        errdefer grad_tensor.deinit();

        // Initialize gradient to zero
        @memset(grad_tensor.data, 0);

        try self.grads.put(self.allocator, param_id, grad_tensor);
        return self.grads.getPtr(param_id).?;
    }

    /// Gets the gradient tensor for a parameter (if it exists)
    pub fn getGrad(self: *const Self, param_id: StateId) ?*const OwnedTensor {
        return self.grads.getPtr(param_id);
    }

    /// Lists all parameter IDs
    pub fn getParameters(self: *const Self) []const StateId {
        return self.parameters.items;
    }

    /// Lists all buffer IDs
    pub fn getBuffers(self: *const Self) []const StateId {
        return self.buffers.items;
    }

    /// Checks if a StateId refers to a parameter
    pub fn isParameter(self: *const Self, id: StateId) bool {
        for (self.parameters.items) |param_id| {
            if (param_id == id) return true;
        }
        return false;
    }

    /// Checks if a StateId refers to a buffer
    pub fn isBuffer(self: *const Self, id: StateId) bool {
        for (self.buffers.items) |buffer_id| {
            if (buffer_id == id) return true;
        }
        return false;
    }
};

// ===== Common Error Types =====

pub const StorageError = error{
    OutOfMemory,
    BufferNotFound,
    ParameterNotFound,
    InvalidStateId,
    DeviceError,
    MetadataNotFound,
};

// ===== Unit Tests =====

const testing = std.testing;

// ===== BufferKey Tests =====

test "BufferKey hash and equality" {
    const key1 = BufferKey{ .device = .cpu, .size = 1024, .alignment = 16 };
    const key2 = BufferKey{ .device = .cpu, .size = 1024, .alignment = 16 };
    const key3 = BufferKey{ .device = .cuda, .size = 1024, .alignment = 16 };
    const key4 = BufferKey{ .device = .cpu, .size = 2048, .alignment = 16 };

    // Test equality
    try testing.expect(BufferKey.eql(key1, key2));
    try testing.expect(!BufferKey.eql(key1, key3)); // Different device
    try testing.expect(!BufferKey.eql(key1, key4)); // Different size

    // Test hash consistency
    try testing.expectEqual(key1.hash(), key2.hash());
    try testing.expect(key1.hash() != key3.hash());
    try testing.expect(key1.hash() != key4.hash());
}

// ===== AllocationStats Tests =====

test "AllocationStats tracking" {
    var stats = AllocationStats{};

    // Test initial state
    try testing.expectEqual(@as(usize, 0), stats.total_allocated);
    try testing.expectEqual(@as(usize, 0), stats.peak_allocated);
    try testing.expectEqual(@as(usize, 0), stats.allocation_count);
    try testing.expectEqual(@as(usize, 0), stats.deallocation_count);

    // Test allocation tracking
    stats.recordAllocation(100);
    try testing.expectEqual(@as(usize, 100), stats.total_allocated);
    try testing.expectEqual(@as(usize, 100), stats.peak_allocated);
    try testing.expectEqual(@as(usize, 1), stats.allocation_count);

    stats.recordAllocation(200);
    try testing.expectEqual(@as(usize, 300), stats.total_allocated);
    try testing.expectEqual(@as(usize, 300), stats.peak_allocated);
    try testing.expectEqual(@as(usize, 2), stats.allocation_count);

    // Test deallocation tracking
    stats.recordDeallocation(50);
    try testing.expectEqual(@as(usize, 250), stats.total_allocated);
    try testing.expectEqual(@as(usize, 300), stats.peak_allocated); // Peak unchanged
    try testing.expectEqual(@as(usize, 1), stats.deallocation_count);

    // Test that peak is maintained even after deallocations
    stats.recordAllocation(10);
    try testing.expectEqual(@as(usize, 260), stats.total_allocated);
    try testing.expectEqual(@as(usize, 300), stats.peak_allocated); // Peak still 300
}

// ===== TensorView Tests =====

test "TensorView creation and properties" {
    const data = [_]u8{ 1, 2, 3, 4, 5, 6 };
    const shape = [_]i64{ 2, 3 };
    const strides = [_]i64{ 3, 1 };

    const view = TensorView{
        .data = &data,
        .shape = &shape,
        .dtype = .u8,
        .strides = &strides,
    };

    try testing.expectEqual(@as(usize, 6), view.data.len);
    try testing.expectEqual(@as(usize, 2), view.shape.len);
    try testing.expectEqual(@as(i64, 2), view.shape[0]);
    try testing.expectEqual(@as(i64, 3), view.shape[1]);
    try testing.expectEqual(DataType.u8, view.dtype);
    try testing.expect(view.strides != null);
    try testing.expectEqual(@as(i64, 3), view.strides.?[0]);
    try testing.expectEqual(@as(i64, 1), view.strides.?[1]);
}

test "TensorView to OwnedTensor conversion" {
    const allocator = testing.allocator;

    const data = [_]u8{ 1, 2, 3, 4 };
    const shape = [_]i64{ 2, 2 };

    const view = TensorView{
        .data = &data,
        .shape = &shape,
        .dtype = .u8,
    };

    var owned = try view.toOwned(allocator);
    defer owned.deinit();

    try testing.expectEqual(@as(usize, 4), owned.data.len);
    try testing.expectEqual(@as(usize, 2), owned.shape.len);
    try testing.expectEqual(@as(i64, 2), owned.shape[0]);
    try testing.expectEqual(@as(i64, 2), owned.shape[1]);
    try testing.expectEqual(DataType.u8, owned.dtype);

    // Verify data was copied
    try testing.expectEqualSlices(u8, view.data, owned.data);
}

test "TensorView to OwnedTensor with strides" {
    const allocator = testing.allocator;

    const data = [_]u8{ 1, 2, 3, 4, 5, 6 };
    const shape = [_]i64{ 2, 3 };
    const strides = [_]i64{ 3, 1 };

    const view = TensorView{
        .data = &data,
        .shape = &shape,
        .dtype = .u8,
        .strides = &strides,
    };

    var owned = try view.toOwned(allocator);
    defer owned.deinit();

    try testing.expect(owned.strides != null);
    try testing.expectEqualSlices(i64, view.strides.?, owned.strides.?);
}

// ===== OwnedTensor Tests =====

test "OwnedTensor creation and destruction" {
    const allocator = testing.allocator;

    var tensor = try OwnedTensor.init(allocator, &.{ 3, 4 }, .f32);
    defer tensor.deinit();

    try testing.expectEqual(@as(usize, 48), tensor.data.len); // 3*4*4 bytes
    try testing.expectEqual(@as(usize, 2), tensor.shape.len);
    try testing.expectEqual(@as(i64, 3), tensor.shape[0]);
    try testing.expectEqual(@as(i64, 4), tensor.shape[1]);
    try testing.expectEqual(DataType.f32, tensor.dtype);
    try testing.expect(tensor.strides == null); // Default no strides
}

test "OwnedTensor different data types" {
    const allocator = testing.allocator;

    // Test f64
    {
        var tensor = try OwnedTensor.init(allocator, &.{ 2, 2 }, .f64);
        defer tensor.deinit();
        try testing.expectEqual(@as(usize, 32), tensor.data.len); // 2*2*8 bytes
    }

    // Test i32
    {
        var tensor = try OwnedTensor.init(allocator, &.{5}, .i32);
        defer tensor.deinit();
        try testing.expectEqual(@as(usize, 20), tensor.data.len); // 5*4 bytes
    }

    // Test bool
    {
        var tensor = try OwnedTensor.init(allocator, &.{10}, .bool);
        defer tensor.deinit();
        try testing.expectEqual(@as(usize, 10), tensor.data.len); // 10*1 bytes
    }
}

test "OwnedTensor view method" {
    const allocator = testing.allocator;

    var tensor = try OwnedTensor.init(allocator, &.{ 2, 3 }, .i16);
    defer tensor.deinit();

    // Fill with test data
    for (tensor.data, 0..) |*byte, i| {
        byte.* = @intCast(i % 256);
    }

    const view = tensor.view();

    try testing.expectEqual(tensor.data.ptr, view.data.ptr);
    try testing.expectEqual(tensor.data.len, view.data.len);
    try testing.expectEqual(tensor.shape.ptr, view.shape.ptr);
    try testing.expectEqual(tensor.dtype, view.dtype);
    try testing.expectEqual(tensor.strides, view.strides);
}

// ===== BufferView Tests =====

test "BufferView creation" {
    var data = [_]u8{0} ** 24;
    const shape = [_]i64{ 2, 3, 4 };

    const buffer_view = BufferView{
        .data = &data,
        .shape = &shape,
        .dtype = .u8,
    };

    try testing.expectEqual(@as(usize, 24), buffer_view.data.len);
    try testing.expectEqual(@as(usize, 3), buffer_view.shape.len);
    try testing.expectEqual(DataType.u8, buffer_view.dtype);
}

// ===== DataLayout Tests =====

test "DataLayout contiguity checking" {
    // Row major: [6, 3, 1] for shape [2, 2, 3]
    const row_strides = [_]i64{ 6, 3, 1 };
    const shape = [_]i64{ 2, 2, 3 };

    try testing.expect(DataLayout.row_major.isContiguous(&row_strides, &shape));
    try testing.expect(!DataLayout.column_major.isContiguous(&row_strides, &shape));

    // Column major: [1, 2, 4] for shape [2, 2, 3]
    const col_strides = [_]i64{ 1, 2, 4 };
    try testing.expect(!DataLayout.row_major.isContiguous(&col_strides, &shape));
    try testing.expect(DataLayout.column_major.isContiguous(&col_strides, &shape));
}

test "DataLayout edge cases" {
    // Empty tensor
    const empty_strides = [_]i64{};
    const empty_shape = [_]i64{};
    try testing.expect(DataLayout.row_major.isContiguous(&empty_strides, &empty_shape));
    try testing.expect(DataLayout.column_major.isContiguous(&empty_strides, &empty_shape));

    // Scalar tensor
    const scalar_strides = [_]i64{};
    const scalar_shape = [_]i64{};
    try testing.expect(DataLayout.row_major.isContiguous(&scalar_strides, &scalar_shape));
    try testing.expect(DataLayout.column_major.isContiguous(&scalar_strides, &scalar_shape));

    // 1D tensor
    const vec_strides = [_]i64{1};
    const vec_shape = [_]i64{5};
    try testing.expect(DataLayout.row_major.isContiguous(&vec_strides, &vec_shape));
    try testing.expect(DataLayout.column_major.isContiguous(&vec_strides, &vec_shape));

    // Mismatched lengths
    const bad_strides = [_]i64{ 6, 3 };
    const bad_shape = [_]i64{ 2, 2, 3 };
    try testing.expect(!DataLayout.row_major.isContiguous(&bad_strides, &bad_shape));
    try testing.expect(!DataLayout.column_major.isContiguous(&bad_strides, &bad_shape));
}

// ===== DataStorage Tests =====

test "DataStorage arena allocation" {
    const allocator = testing.allocator;

    var storage = try DataStorage.init(allocator, .cpu, 1024);
    defer storage.deinit();

    try testing.expectEqual(Device.cpu, storage.device);
    try testing.expectEqual(@as(usize, 1024), storage.arena.len);
    try testing.expectEqual(@as(usize, 0), storage.offset);

    // Test basic allocation
    const buffer1 = try storage.allocate(0, 100, 1);
    try testing.expectEqual(@as(usize, 100), buffer1.len);
    try testing.expectEqual(@as(usize, 100), storage.offset);
    try testing.expectEqual(@as(usize, 1), storage.stats.allocation_count);

    // Test aligned allocation
    const buffer2 = try storage.allocate(1, 50, 16);
    try testing.expectEqual(@as(usize, 50), buffer2.len);
    // Offset should be aligned to 16 bytes, then advanced by 50
    const expected_offset = std.mem.alignForward(usize, 100, 16) + 50;
    try testing.expectEqual(expected_offset, storage.offset);

    // Test retrieval
    const retrieved1 = storage.getBuffer(0);
    try testing.expect(retrieved1 != null);
    try testing.expectEqual(@as(usize, 100), retrieved1.?.len);

    const retrieved2 = storage.getBuffer(1);
    try testing.expect(retrieved2 != null);
    try testing.expectEqual(@as(usize, 50), retrieved2.?.len);

    // Test non-existent buffer
    const non_existent = storage.getBuffer(999);
    try testing.expect(non_existent == null);
}

test "DataStorage out of memory" {
    const allocator = testing.allocator;

    var storage = try DataStorage.init(allocator, .cpu, 100); // Small arena
    defer storage.deinit();

    // First allocation should succeed
    const buffer1 = try storage.allocate(0, 50, 1);
    try testing.expectEqual(@as(usize, 50), buffer1.len);

    // Second allocation should succeed
    const buffer2 = try storage.allocate(1, 30, 1);
    try testing.expectEqual(@as(usize, 30), buffer2.len);

    // Third allocation should fail
    try testing.expectError(error.OutOfMemory, storage.allocate(2, 50, 1));
}

test "DataStorage alignment" {
    const allocator = testing.allocator;

    var storage = try DataStorage.init(allocator, .cpu, 1024);
    defer storage.deinit();

    // Allocate with various alignments
    const buffer1 = try storage.allocate(0, 7, 1); // No alignment
    const addr1 = @intFromPtr(buffer1.ptr);

    const buffer2 = try storage.allocate(1, 10, 8); // 8-byte alignment
    const addr2 = @intFromPtr(buffer2.ptr);
    try testing.expectEqual(@as(usize, 0), addr2 % 8);

    const buffer3 = try storage.allocate(2, 5, 16); // 16-byte alignment
    const addr3 = @intFromPtr(buffer3.ptr);
    try testing.expectEqual(@as(usize, 0), addr3 % 16);

    // Verify buffers don't overlap
    try testing.expect(addr1 + buffer1.len <= addr2);
    try testing.expect(addr2 + buffer2.len <= addr3);
}

test "DataStorage reset" {
    const allocator = testing.allocator;

    var storage = try DataStorage.init(allocator, .cpu, 1024);
    defer storage.deinit();

    // Make some allocations
    _ = try storage.allocate(0, 100, 1);
    _ = try storage.allocate(1, 200, 1);

    try testing.expectEqual(@as(usize, 300), storage.offset);
    try testing.expectEqual(@as(usize, 2), storage.stats.allocation_count);
    try testing.expect(storage.getBuffer(0) != null);
    try testing.expect(storage.getBuffer(1) != null);

    // Reset and verify state
    storage.reset();

    try testing.expectEqual(@as(usize, 0), storage.offset);
    try testing.expectEqual(@as(usize, 0), storage.stats.allocation_count);
    try testing.expect(storage.getBuffer(0) == null);
    try testing.expect(storage.getBuffer(1) == null);

    // Should be able to allocate again
    const new_buffer = try storage.allocate(5, 100, 1);
    try testing.expectEqual(@as(usize, 100), new_buffer.len);
}

test "DataStorage ensureCapacity" {
    const allocator = testing.allocator;

    var storage = try DataStorage.init(allocator, .cpu, 100);
    defer storage.deinit();

    // Initial capacity
    try testing.expectEqual(@as(usize, 100), storage.arena.len);

    // Ensure smaller capacity (should be no-op)
    try storage.ensureCapacity(50);
    try testing.expectEqual(@as(usize, 100), storage.arena.len);

    // Ensure larger capacity
    try storage.ensureCapacity(500);
    try testing.expectEqual(@as(usize, 500), storage.arena.len);
    try testing.expectEqual(@as(usize, 0), storage.offset); // Should reset offset
}

test "DataStorage different devices" {
    const allocator = testing.allocator;

    const devices = [_]Device{ .cpu, .cuda, .metal, .wasm };

    for (devices) |device| {
        var storage = try DataStorage.init(allocator, device, 256);
        defer storage.deinit();

        try testing.expectEqual(device, storage.device);

        // Should be able to allocate regardless of device
        const buffer = try storage.allocate(0, 64, 1);
        try testing.expectEqual(@as(usize, 64), buffer.len);
    }
}

// ===== ParameterStore Tests =====

test "ParameterStore basic operations" {
    const allocator = testing.allocator;

    var store = ParameterStore.init(allocator);
    defer store.deinit();

    try testing.expectEqual(@as(StateId, 0), store.next_id);
    try testing.expectEqual(@as(usize, 0), store.getParameters().len);
    try testing.expectEqual(@as(usize, 0), store.getBuffers().len);

    // Create a parameter
    const param_id = try store.createParameter(&.{ 2, 3 }, .f32, struct {
        fn init(tensor: *OwnedTensor) !void {
            @memset(tensor.data, 0);
        }
    }.init);

    try testing.expectEqual(@as(StateId, 0), param_id);
    try testing.expectEqual(@as(StateId, 1), store.next_id);

    // Verify parameter exists
    const param = store.getTensor(param_id);
    try testing.expect(param != null);
    try testing.expectEqual(@as(i64, 2), param.?.shape[0]);
    try testing.expectEqual(@as(i64, 3), param.?.shape[1]);
    try testing.expectEqual(DataType.f32, param.?.dtype);

    // Test parameter classification
    try testing.expect(store.isParameter(param_id));
    try testing.expect(!store.isBuffer(param_id));

    // Test parameter listing
    const params = store.getParameters();
    try testing.expectEqual(@as(usize, 1), params.len);
    try testing.expectEqual(param_id, params[0]);
}

test "ParameterStore buffer operations" {
    const allocator = testing.allocator;

    var store = ParameterStore.init(allocator);
    defer store.deinit();

    // Create a buffer
    const buffer_id = try store.createBuffer(&.{5}, .i32, struct {
        fn init(tensor: *OwnedTensor) !void {
            // Initialize with incrementing values
            const values = std.mem.bytesAsSlice(i32, tensor.data);
            for (values, 0..) |*val, i| {
                val.* = @intCast(i);
            }
        }
    }.init);

    try testing.expectEqual(@as(StateId, 0), buffer_id);

    // Verify buffer exists
    const buffer = store.getTensor(buffer_id);
    try testing.expect(buffer != null);
    try testing.expectEqual(@as(usize, 1), buffer.?.shape.len);
    try testing.expectEqual(@as(i64, 5), buffer.?.shape[0]);
    try testing.expectEqual(DataType.i32, buffer.?.dtype);

    // Test buffer classification
    try testing.expect(store.isBuffer(buffer_id));
    try testing.expect(!store.isParameter(buffer_id));

    // Verify buffer data was initialized correctly
    const values = std.mem.bytesAsSlice(i32, buffer.?.data);
    for (values, 0..) |val, i| {
        try testing.expectEqual(@as(i32, @intCast(i)), val);
    }

    // Test buffer listing
    const buffers = store.getBuffers();
    try testing.expectEqual(@as(usize, 1), buffers.len);
    try testing.expectEqual(buffer_id, buffers[0]);
}

test "ParameterStore gradient operations" {
    const allocator = testing.allocator;

    var store = ParameterStore.init(allocator);
    defer store.deinit();

    // Create a parameter
    const param_id = try store.createParameter(&.{ 3, 2 }, .f64, struct {
        fn init(tensor: *OwnedTensor) !void {
            // Initialize with ones
            const values = std.mem.bytesAsSlice(f64, tensor.data);
            for (values) |*val| {
                val.* = 1.0;
            }
        }
    }.init);

    // Initially no gradient
    try testing.expect(store.getGrad(param_id) == null);

    // Create gradient
    const grad = try store.getOrCreateGrad(param_id);
    try testing.expect(grad.shape.len > 0); // grad is never null, it's a pointer
    try testing.expectEqual(@as(usize, 2), grad.shape.len);
    try testing.expectEqual(@as(i64, 3), grad.shape[0]);
    try testing.expectEqual(@as(i64, 2), grad.shape[1]);
    try testing.expectEqual(DataType.f64, grad.dtype);

    // Gradient should be zero-initialized
    const grad_values = std.mem.bytesAsSlice(f64, grad.data);
    for (grad_values) |val| {
        try testing.expectEqual(@as(f64, 0.0), val);
    }

    // Second call should return same gradient
    const grad2 = try store.getOrCreateGrad(param_id);
    try testing.expectEqual(@intFromPtr(grad), @intFromPtr(grad2));

    // Now getGrad should return the gradient
    const retrieved_grad = store.getGrad(param_id);
    try testing.expect(retrieved_grad != null);
    try testing.expectEqual(@intFromPtr(grad), @intFromPtr(retrieved_grad.?));
}

test "ParameterStore mutable access" {
    const allocator = testing.allocator;

    var store = ParameterStore.init(allocator);
    defer store.deinit();

    // Create a parameter
    const param_id = try store.createParameter(&.{2}, .f32, struct {
        fn init(tensor: *OwnedTensor) !void {
            @memset(tensor.data, 0);
        }
    }.init);

    // Get mutable reference and modify
    const param_mut = store.getTensorMut(param_id);
    try testing.expect(param_mut != null);

    const values = std.mem.bytesAsSlice(f32, param_mut.?.data);
    values[0] = 1.5;
    values[1] = 2.5;

    // Verify changes through const reference
    const param_const = store.getTensor(param_id);
    const const_values = std.mem.bytesAsSlice(f32, param_const.?.data);
    try testing.expectEqual(@as(f32, 1.5), const_values[0]);
    try testing.expectEqual(@as(f32, 2.5), const_values[1]);
}

test "ParameterStore error cases" {
    const allocator = testing.allocator;

    var store = ParameterStore.init(allocator);
    defer store.deinit();

    // Test gradient for non-existent parameter
    try testing.expectError(error.ParameterNotFound, store.getOrCreateGrad(999));

    // Test getTensor for non-existent ID
    try testing.expect(store.getTensor(999) == null);
    try testing.expect(store.getTensorMut(999) == null);
    try testing.expect(store.getGrad(999) == null);

    // Test classification for non-existent ID
    try testing.expect(!store.isParameter(999));
    try testing.expect(!store.isBuffer(999));
}

test "ParameterStore mixed parameters and buffers" {
    const allocator = testing.allocator;

    var store = ParameterStore.init(allocator);
    defer store.deinit();

    // Create mixed tensors
    const param1_id = try store.createParameter(&.{ 2, 2 }, .f32, struct {
        fn init(tensor: *OwnedTensor) !void {
            @memset(tensor.data, 0);
        }
    }.init);

    const buffer1_id = try store.createBuffer(&.{3}, .i32, struct {
        fn init(tensor: *OwnedTensor) !void {
            @memset(tensor.data, 0);
        }
    }.init);

    const param2_id = try store.createParameter(&.{ 1, 4 }, .f64, struct {
        fn init(tensor: *OwnedTensor) !void {
            @memset(tensor.data, 0);
        }
    }.init);

    const buffer2_id = try store.createBuffer(&.{ 2, 2, 2 }, .u8, struct {
        fn init(tensor: *OwnedTensor) !void {
            @memset(tensor.data, 0);
        }
    }.init);

    // Verify ID assignment
    try testing.expectEqual(@as(StateId, 0), param1_id);
    try testing.expectEqual(@as(StateId, 1), buffer1_id);
    try testing.expectEqual(@as(StateId, 2), param2_id);
    try testing.expectEqual(@as(StateId, 3), buffer2_id);

    // Verify classifications
    try testing.expect(store.isParameter(param1_id));
    try testing.expect(!store.isBuffer(param1_id));
    try testing.expect(store.isBuffer(buffer1_id));
    try testing.expect(!store.isParameter(buffer1_id));
    try testing.expect(store.isParameter(param2_id));
    try testing.expect(!store.isBuffer(param2_id));
    try testing.expect(store.isBuffer(buffer2_id));
    try testing.expect(!store.isParameter(buffer2_id));

    // Verify listings
    const params = store.getParameters();
    const buffers = store.getBuffers();

    try testing.expectEqual(@as(usize, 2), params.len);
    try testing.expectEqual(@as(usize, 2), buffers.len);

    try testing.expectEqual(param1_id, params[0]);
    try testing.expectEqual(param2_id, params[1]);
    try testing.expectEqual(buffer1_id, buffers[0]);
    try testing.expectEqual(buffer2_id, buffers[1]);
}

// ===== Enhanced Buffer Metadata Tests =====

test "TensorMetadata operations" {
    const shape = [_]i64{ 2, 3, 4 };
    const metadata = TensorMetadata{
        .shape = &shape,
        .dtype = .f32,
        .strides = null,
        .layout = .row_major,
    };

    try testing.expectEqual(@as(usize, 24), metadata.elementCount());
    try testing.expectEqual(@as(usize, 96), metadata.byteSize()); // 24 * 4 bytes
}

test "DataStorage with metadata allocation" {
    const allocator = testing.allocator;

    var storage = try DataStorage.init(allocator, .cpu, 1024);
    defer storage.deinit();

    const shape = [_]i64{ 2, 3 };
    const strides = [_]i64{ 3, 1 };

    // Allocate buffer with metadata
    const buffer = try storage.allocateWithMetadata(0, &shape, .f32, 16, &strides);
    try testing.expectEqual(@as(usize, 24), buffer.len); // 2*3*4 bytes

    // Retrieve with metadata
    const buffer_meta = try storage.getBufferWithMetadata(0);
    try testing.expectEqual(@as(usize, 24), buffer_meta.data.len);
    try testing.expectEqualSlices(i64, &shape, buffer_meta.shape);
    try testing.expectEqual(DataType.f32, buffer_meta.dtype);
    try testing.expect(buffer_meta.strides != null);
    try testing.expectEqualSlices(i64, &strides, buffer_meta.strides.?);
    try testing.expectEqual(DataLayout.row_major, buffer_meta.layout);

    // Retrieve tensor view
    const tensor = try storage.getTensor(0);
    try testing.expectEqualSlices(i64, &shape, tensor.shape);
    try testing.expectEqual(DataType.f32, tensor.dtype);
    try testing.expectEqualSlices(i64, &strides, tensor.strides.?);
}

test "DataStorage metadata not found errors" {
    const allocator = testing.allocator;

    var storage = try DataStorage.init(allocator, .cpu, 1024);
    defer storage.deinit();

    // Allocate buffer without metadata
    _ = try storage.allocate(0, 100, 1);

    // Should fail when requesting metadata
    try testing.expectError(error.MetadataNotFound, storage.getTensor(0));
    try testing.expectError(error.MetadataNotFound, storage.getBufferWithMetadata(0));

    // Basic buffer access should still work
    const buffer = storage.getBuffer(0);
    try testing.expect(buffer != null);
    try testing.expectEqual(@as(usize, 100), buffer.?.len);
}

test "DataStorage metadata reset and cleanup" {
    const allocator = testing.allocator;

    var storage = try DataStorage.init(allocator, .cpu, 1024);
    defer storage.deinit();

    const shape = [_]i64{ 4, 4 };
    _ = try storage.allocateWithMetadata(0, &shape, .f32, 1, null);

    // Verify metadata exists
    const metadata = storage.getMetadata(0);
    try testing.expect(metadata != null);
    try testing.expectEqualSlices(i64, &shape, metadata.?.shape);

    // Reset should clear metadata
    storage.reset();

    // Metadata should be gone
    try testing.expect(storage.getMetadata(0) == null);
    try testing.expect(storage.getBuffer(0) == null);
}

// ===== Integration Tests =====

test "DataStorage and ParameterStore integration" {
    const allocator = testing.allocator;

    // Create both storage systems
    var data_storage = try DataStorage.init(allocator, .cpu, 2048);
    defer data_storage.deinit();

    var param_store = ParameterStore.init(allocator);
    defer param_store.deinit();

    // Create a parameter in the store
    const weight_id = try param_store.createParameter(&.{ 4, 4 }, .f32, struct {
        fn init(tensor: *OwnedTensor) !void {
            // Initialize with Xavier initialization pattern
            const values = std.mem.bytesAsSlice(f32, tensor.data);
            for (values, 0..) |*val, i| {
                val.* = @as(f32, @floatFromInt(i)) * 0.1;
            }
        }
    }.init);

    // Allocate intermediate computation buffers
    const input_buffer = try data_storage.allocate(100, 64, 16); // 4x4 f32 input
    const output_buffer = try data_storage.allocate(101, 64, 16); // 4x4 f32 output

    // Verify everything was allocated correctly
    try testing.expectEqual(@as(usize, 64), input_buffer.len);
    try testing.expectEqual(@as(usize, 64), output_buffer.len);

    const weight = param_store.getTensor(weight_id);
    try testing.expect(weight != null);
    try testing.expectEqual(@as(usize, 64), weight.?.data.len); // 4*4*4 bytes

    // Simulate computation: initialize input, create gradient
    @memset(input_buffer, 1); // Fill input with ones

    const grad = try param_store.getOrCreateGrad(weight_id);
    try testing.expectEqual(weight.?.data.len, grad.data.len);

    // Reset data storage for next batch
    data_storage.reset();
    try testing.expectEqual(@as(usize, 0), data_storage.offset);
    try testing.expect(data_storage.getBuffer(100) == null);
    try testing.expect(data_storage.getBuffer(101) == null);

    // Parameter store should be unaffected by data storage reset
    try testing.expect(param_store.getTensor(weight_id) != null);
    try testing.expect(param_store.getGrad(weight_id) != null);
}

test "Storage error types" {
    // Test that all storage error types are properly defined
    const errors = [_]anyerror{
        StorageError.OutOfMemory,
        StorageError.BufferNotFound,
        StorageError.ParameterNotFound,
        StorageError.InvalidStateId,
        StorageError.DeviceError,
    };

    // Just verify we can reference all error types
    for (errors) |err| {
        switch (err) {
            StorageError.OutOfMemory => {},
            StorageError.BufferNotFound => {},
            StorageError.ParameterNotFound => {},
            StorageError.InvalidStateId => {},
            StorageError.DeviceError => {},
            else => unreachable,
        }
    }
}

test "compile-time memory layout optimization" {
    const allocator = testing.allocator;
    
    // Test compile-time layout for a 32x32 f32 matrix
    const Layout = ComptimeLayout(&.{ 32, 32 }, .f32, null);
    
    // Verify compile-time computed properties
    try testing.expectEqual(@as(i64, 1024), Layout.total_elements);
    try testing.expectEqual(@as(usize, 4096), Layout.total_bytes);
    try testing.expectEqual(@as(usize, 32), Layout.simd_alignment);
    try testing.expect(Layout.can_use_simd);
    
    // Allocate using optimized layout
    const buffer = try Layout.allocate(allocator);
    defer allocator.free(buffer);
    
    // Test SIMD-optimized fill
    const FillKernel = Layout.generateFillKernel();
    FillKernel.fill(buffer, 3.14);
    
    // Verify fill
    const data = std.mem.bytesAsSlice(f32, buffer);
    for (data) |val| {
        try testing.expectApproxEqRel(@as(f32, 3.14), val, 0.001);
    }
}

test "compile-time memory pool optimization" {
    const allocator = testing.allocator;
    
    // Define multiple tensor layouts at compile time
    const configs = [_]LayoutConfig{
        .{ .shape = &.{ 16, 16 }, .dtype = .f32 },
        .{ .shape = &.{ 8, 32 }, .dtype = .f32 },
        .{ .shape = &.{ 64, 4 }, .dtype = .f32 },
    };
    
    // Create compile-time optimized pool
    const Pool = ComptimeMemoryPool(&configs, null);
    
    // Verify pool size is computed at compile time
    comptime {
        if (Pool.pool_size < 3072) { // 3 * 256 * 4 bytes minimum
            @compileError("Pool size too small");
        }
    }
    
    var pool = try Pool.init(allocator);
    defer pool.deinit(allocator);
    
    // Get buffers and verify they don't overlap
    const buf0 = pool.getBuffer(0);
    const buf1 = pool.getBuffer(1);
    const buf2 = pool.getBuffer(2);
    
    try testing.expectEqual(@as(usize, 16 * 16 * 4), buf0.len);
    try testing.expectEqual(@as(usize, 8 * 32 * 4), buf1.len);
    try testing.expectEqual(@as(usize, 64 * 4 * 4), buf2.len);
}

test "compile-time tiling for cache optimization" {
    // Test compile-time tiling computation
    const Tiling = ComptimeTiling(128, 128, 128, .f32, null);
    
    // Verify tiles fit in L1 cache
    const elements_per_tile: usize = @as(usize, Tiling.TILE_M) * @as(usize, Tiling.TILE_N) + 
                                    @as(usize, Tiling.TILE_M) * @as(usize, Tiling.TILE_K) + 
                                    @as(usize, Tiling.TILE_K) * @as(usize, Tiling.TILE_N);
    const bytes_per_tile = elements_per_tile * @sizeOf(f32);
    
    try testing.expect(bytes_per_tile <= 32 * 1024); // Fits in 32KB L1 cache
    
    // Verify complete coverage
    try testing.expect(Tiling.m_tiles * Tiling.TILE_M >= Tiling.M);
    try testing.expect(Tiling.n_tiles * Tiling.TILE_N >= Tiling.N);
    try testing.expect(Tiling.k_tiles * Tiling.TILE_K >= Tiling.K);
}
