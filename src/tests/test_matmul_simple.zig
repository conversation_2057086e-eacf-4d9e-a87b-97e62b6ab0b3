/// Simple matmul gradient test
const std = @import("std");
const testing = std.testing;
const expect = testing.expect;

const Graph = @import("graph").Graph;
const tensor = @import("tensor");
const types = @import("types");
const training = @import("training");
const compiler = @import("compiler");
const PassContext = compiler.PassContext;

test "simple matmul gradient - minimal case" {
    const allocator = testing.allocator;
    var graph = try Graph.init(allocator);
    defer graph.deinit();
    
    // Very simple case: A[2,3] @ B[3,2] = C[2,2]
    const A = try tensor.placeholder(&graph, &.{2, 3}, .f32);
    const B = try tensor.placeholder(&graph, &.{3, 2}, .f32);
    
    try graph.markParameter(A.node_id);
    try graph.markParameter(B.node_id);
    
    const C = try A.matmul(B);
    
    var ctx = PassContext{
        .graph = &graph,
        .allocator = allocator,
    };
    
    // Try just with loss = sum(C) for simplicity
    const loss = try C.sumAll();
    try training.autograd.applyAutograd(&ctx, &.{A.node_id, B.node_id}, loss.node_id);
    
    const grad_map = graph.getGradientMap().?;
    const grad_A = grad_map.get(A.node_id);
    const grad_B = grad_map.get(B.node_id);
    
    std.log.info("Matmul gradient test: grad_A = {?}, grad_B = {?}", .{grad_A, grad_B});
    
    // Check if gradients exist
    try expect(grad_A != null);
    try expect(grad_B != null);
}

test "matmul + bias gradient chain" {
    const allocator = testing.allocator;
    var graph = try Graph.init(allocator);
    defer graph.deinit();
    
    // Linear layer: y = x @ W + b
    const x = try tensor.placeholder(&graph, &.{4, 3}, .f32); // batch_size=4, input_dim=3
    const W = try tensor.placeholder(&graph, &.{3, 5}, .f32); // input_dim=3, output_dim=5
    const b = try tensor.placeholder(&graph, &.{5}, .f32);    // output_dim=5
    
    try graph.markParameter(x.node_id);
    try graph.markParameter(W.node_id);
    try graph.markParameter(b.node_id);
    
    // Forward: linear = x @ W + b
    const linear = try x.matmul(W);  // [4,3] @ [3,5] = [4,5]
    const output = try linear.add(b); // [4,5] + [5] = [4,5] (broadcast)
    const loss = try output.sumAll();
    
    var ctx = PassContext{
        .graph = &graph,
        .allocator = allocator,
    };
    try training.autograd.applyAutograd(&ctx, &.{x.node_id, W.node_id, b.node_id}, loss.node_id);
    
    const grad_map = graph.getGradientMap().?;
    try expect(grad_map.get(x.node_id) != null); // Should be [4,3]
    try expect(grad_map.get(W.node_id) != null); // Should be [3,5]
    try expect(grad_map.get(b.node_id) != null); // Should be [5]
    
    std.log.info("Linear layer gradient test passed", .{});
}

test "matmul + activation gradient chain" {
    const allocator = testing.allocator;
    var graph = try Graph.init(allocator);
    defer graph.deinit();
    
    // Neural network layer: y = ReLU(x @ W)
    const x = try tensor.placeholder(&graph, &.{2, 4}, .f32);
    const W = try tensor.placeholder(&graph, &.{4, 3}, .f32);
    
    try graph.markParameter(x.node_id);
    try graph.markParameter(W.node_id);
    
    // Forward: matmul -> activation
    const linear = try x.matmul(W);     // [2,4] @ [4,3] = [2,3]
    const activated = try linear.relu(); // ReLU([2,3])
    const loss = try activated.sumAll();
    
    var ctx = PassContext{
        .graph = &graph,
        .allocator = allocator,
    };
    try training.autograd.applyAutograd(&ctx, &.{x.node_id, W.node_id}, loss.node_id);
    
    const grad_map = graph.getGradientMap().?;
    try expect(grad_map.get(x.node_id) != null); // Should be [2,4]
    try expect(grad_map.get(W.node_id) != null); // Should be [4,3]
    
    std.log.info("Matmul + ReLU gradient test passed", .{});
}

test "chained matmul gradient - two layer network" {
    const allocator = testing.allocator;
    var graph = try Graph.init(allocator);
    defer graph.deinit();
    
    // Two layer network: y = (x @ W1) @ W2
    const x = try tensor.placeholder(&graph, &.{3, 4}, .f32);   // input
    const W1 = try tensor.placeholder(&graph, &.{4, 6}, .f32);  // first layer weight
    const W2 = try tensor.placeholder(&graph, &.{6, 2}, .f32);  // second layer weight
    
    try graph.markParameter(x.node_id);
    try graph.markParameter(W1.node_id);
    try graph.markParameter(W2.node_id);
    
    // Forward: two matmuls
    const h1 = try x.matmul(W1);   // [3,4] @ [4,6] = [3,6]
    const h2 = try h1.matmul(W2);  // [3,6] @ [6,2] = [3,2]
    const loss = try h2.sumAll();
    
    var ctx = PassContext{
        .graph = &graph,
        .allocator = allocator,
    };
    try training.autograd.applyAutograd(&ctx, &.{x.node_id, W1.node_id, W2.node_id}, loss.node_id);
    
    const grad_map = graph.getGradientMap().?;
    try expect(grad_map.get(x.node_id) != null);  // Should be [3,4]
    try expect(grad_map.get(W1.node_id) != null); // Should be [4,6]
    try expect(grad_map.get(W2.node_id) != null); // Should be [6,2]
    
    std.log.info("Chained matmul gradient test passed", .{});
}

test "matmul + complex operations gradient chain" {
    const allocator = testing.allocator;
    var graph = try Graph.init(allocator);
    defer graph.deinit();
    
    // Complex chain: y = sigmoid(ReLU(x @ W + b) * scale)
    const x = try tensor.placeholder(&graph, &.{2, 3}, .f32);
    const W = try tensor.placeholder(&graph, &.{3, 4}, .f32);
    const b = try tensor.placeholder(&graph, &.{4}, .f32);
    const scale = try tensor.placeholder(&graph, &.{4}, .f32);
    
    try graph.markParameter(x.node_id);
    try graph.markParameter(W.node_id);
    try graph.markParameter(b.node_id);
    try graph.markParameter(scale.node_id);
    
    // Forward: complex computation chain
    const linear = try x.matmul(W);        // [2,3] @ [3,4] = [2,4]
    const with_bias = try linear.add(b);   // [2,4] + [4] = [2,4]
    const activated = try with_bias.relu(); // ReLU([2,4])
    const scaled = try activated.mul(scale); // [2,4] * [4] = [2,4]
    const output = try scaled.sigmoid();   // sigmoid([2,4])
    const loss = try output.sumAll();
    
    var ctx = PassContext{
        .graph = &graph,
        .allocator = allocator,
    };
    try training.autograd.applyAutograd(&ctx, &.{x.node_id, W.node_id, b.node_id, scale.node_id}, loss.node_id);
    
    const grad_map = graph.getGradientMap().?;
    try expect(grad_map.get(x.node_id) != null);     // Should be [2,3]
    try expect(grad_map.get(W.node_id) != null);     // Should be [3,4]
    try expect(grad_map.get(b.node_id) != null);     // Should be [4]
    try expect(grad_map.get(scale.node_id) != null); // Should be [4]
    
    std.log.info("Complex matmul chain gradient test passed", .{});
}

test "batch matmul gradient" {
    const allocator = testing.allocator;
    var graph = try Graph.init(allocator);
    defer graph.deinit();
    
    // Batch matrix multiplication: A[batch, m, k] @ B[batch, k, n] = C[batch, m, n]
    const A = try tensor.placeholder(&graph, &.{2, 3, 4}, .f32); // batch=2, m=3, k=4
    const B = try tensor.placeholder(&graph, &.{2, 4, 5}, .f32); // batch=2, k=4, n=5
    
    try graph.markParameter(A.node_id);
    try graph.markParameter(B.node_id);
    
    // Forward: batch matmul (if supported)
    const C = A.matmul(B) catch |err| switch (err) {
        error.UnsupportedDimensions => {
            std.log.info("Batch matmul not yet supported, skipping test", .{});
            return; // Skip this test if batch matmul isn't implemented
        },
        else => return err,
    };
    
    const loss = try C.sumAll();
    
    var ctx = PassContext{
        .graph = &graph,
        .allocator = allocator,
    };
    try training.autograd.applyAutograd(&ctx, &.{A.node_id, B.node_id}, loss.node_id);
    
    const grad_map = graph.getGradientMap().?;
    try expect(grad_map.get(A.node_id) != null); // Should be [2,3,4]
    try expect(grad_map.get(B.node_id) != null); // Should be [2,4,5]
    
    std.log.info("Batch matmul gradient test passed", .{});
}

test "matmul with broadcasting gradient" {
    const allocator = testing.allocator;
    var graph = try Graph.init(allocator);
    defer graph.deinit();
    
    // Matrix @ vector with broadcasting: A[m,k] @ B[k] = C[m]
    const A = try tensor.placeholder(&graph, &.{3, 4}, .f32);
    const B = try tensor.placeholder(&graph, &.{4, 1}, .f32); // Treat as column vector
    
    try graph.markParameter(A.node_id);
    try graph.markParameter(B.node_id);
    
    // Forward: matrix-vector multiplication
    const C = try A.matmul(B);      // [3,4] @ [4,1] = [3,1]
    const squeezed = try C.squeeze(1); // [3,1] -> [3] (remove dimension of size 1)
    const loss = try squeezed.sumAll();
    
    var ctx = PassContext{
        .graph = &graph,
        .allocator = allocator,
    };
    try training.autograd.applyAutograd(&ctx, &.{A.node_id, B.node_id}, loss.node_id);
    
    const grad_map = graph.getGradientMap().?;
    try expect(grad_map.get(A.node_id) != null); // Should be [3,4]
    try expect(grad_map.get(B.node_id) != null); // Should be [4,1]
    
    std.log.info("Matmul with broadcasting gradient test passed", .{});
}

test "residual connection with matmul gradient" {
    const allocator = testing.allocator;
    var graph = try Graph.init(allocator);
    defer graph.deinit();
    
    // Residual connection: y = x + x @ W (where shapes work out)
    const x = try tensor.placeholder(&graph, &.{3, 3}, .f32);   // Square matrix for residual
    const W = try tensor.placeholder(&graph, &.{3, 3}, .f32);   // Weight matrix
    
    try graph.markParameter(x.node_id);
    try graph.markParameter(W.node_id);
    
    // Forward: residual connection
    const linear = try x.matmul(W);     // [3,3] @ [3,3] = [3,3]
    const residual = try x.add(linear); // [3,3] + [3,3] = [3,3]
    const loss = try residual.sumAll();
    
    var ctx = PassContext{
        .graph = &graph,
        .allocator = allocator,
    };
    try training.autograd.applyAutograd(&ctx, &.{x.node_id, W.node_id}, loss.node_id);
    
    const grad_map = graph.getGradientMap().?;
    try expect(grad_map.get(x.node_id) != null); // Should be [3,3] (accumulated from both paths)
    try expect(grad_map.get(W.node_id) != null); // Should be [3,3]
    
    std.log.info("Residual connection gradient test passed", .{});
}