/// Integration tests for math tensor operations
/// Tests math operations that are built on top of primitives
/// All math operations: exp, log/ln, cos, sin, tan, sinh, cosh

const std = @import("std");
const testing = std.testing;
const print = std.debug.print;

// Import Zing components
const types = @import("types");
const Graph = @import("graph").Graph;
const tensor = @import("tensor");
const compiler = @import("compiler");
const execution = @import("execution");

// Helper to compare results with tolerance
fn expectApproxEqSlice(expected: []const f32, actual: []const f32, tolerance: f32) !void {
    try testing.expectEqual(expected.len, actual.len);
    for (expected, actual, 0..) |e, a, i| {
        if (@abs(e - a) > tolerance) {
            print("Mismatch at index {}: expected {d:.6}, got {d:.6}\n", .{ i, e, a });
            return error.ValueMismatch;
        }
    }
}

test "math: exp operation" {
    const allocator = testing.allocator;
    
    print("\n=== Testing: exp operation (e^x) ===\n", .{});
    
    var graph = try Graph.init(allocator);
    defer graph.deinit();
    
    const x = try tensor.placeholder(&graph, &.{6}, .f32);
    const exp_x = try x.exp();
    
    try graph.output_nodes.append(graph.arena.allocator(), exp_x.node_id);
    
    var compiled = try compiler.compile.compileCpu(&graph, allocator);
    defer compiled.deinit(allocator);
    
    var executor = try execution.Executor.init(allocator, &compiled, null);
    defer executor.deinit();
    
    // Test values including negative, zero, and positive
    const x_data = [_]f32{ -2, -1, 0, 1, 2, 3 };
    const shape = [_]i64{6};
    
    try executor.setInput(x.node_id, std.mem.asBytes(&x_data), &shape, .f32);
    
    try executor.run();
    
    const output = try executor.getOutput(exp_x.node_id);
    const output_f32 = @as([*]const f32, @ptrCast(@alignCast(output.data.ptr)))[0..6];
    
    // Expected values: e^x for each input
    const expected = [_]f32{ 
        0.1353353,  // e^(-2)
        0.3678794,  // e^(-1)
        1.0,        // e^0
        2.7182818,  // e^1
        7.3890561,  // e^2
        20.0855369  // e^3
    };
    try expectApproxEqSlice(&expected, output_f32, 1e-4);
    
    print("✓ exp operation test passed!\n", .{});
}

test "math: log operation" {
    const allocator = testing.allocator;
    
    print("\n=== Testing: log operation (ln) ===\n", .{});
    
    var graph = try Graph.init(allocator);
    defer graph.deinit();
    
    const x = try tensor.placeholder(&graph, &.{5}, .f32);
    const log_x = try x.log();
    
    try graph.output_nodes.append(graph.arena.allocator(), log_x.node_id);
    
    var compiled = try compiler.compile.compileCpu(&graph, allocator);
    defer compiled.deinit(allocator);
    
    var executor = try execution.Executor.init(allocator, &compiled, null);
    defer executor.deinit();
    
    // Test values (all positive for log)
    const x_data = [_]f32{ 0.1, 0.5, 1.0, 2.0, 10.0 };
    const shape = [_]i64{5};
    
    try executor.setInput(x.node_id, std.mem.asBytes(&x_data), &shape, .f32);
    
    try executor.run();
    
    const output = try executor.getOutput(log_x.node_id);
    const output_f32 = @as([*]const f32, @ptrCast(@alignCast(output.data.ptr)))[0..5];
    
    // Expected values: ln(x) for each input
    const expected = [_]f32{ 
        -2.3025851, // ln(0.1)
        -0.6931472, // ln(0.5)
        0.0,        // ln(1)
        0.6931472,  // ln(2)
        2.3025851   // ln(10)
    };
    try expectApproxEqSlice(&expected, output_f32, 1e-5);
    
    print("✓ log operation test passed!\n", .{});
}

test "math: cos operation" {
    const allocator = testing.allocator;
    
    print("\n=== Testing: cos operation ===\n", .{});
    
    var graph = try Graph.init(allocator);
    defer graph.deinit();
    
    const x = try tensor.placeholder(&graph, &.{6}, .f32);
    const cos_x = try x.cos();
    
    try graph.output_nodes.append(graph.arena.allocator(), cos_x.node_id);
    
    var compiled = try compiler.compile.compileCpu(&graph, allocator);
    defer compiled.deinit(allocator);
    
    var executor = try execution.Executor.init(allocator, &compiled, null);
    defer executor.deinit();
    
    // Test values: multiples of π/4
    const x_data = [_]f32{ 
        0,           // 0
        0.7853982,   // π/4
        1.5707963,   // π/2
        3.1415927,   // π
        -0.7853982,  // -π/4
        -1.5707963   // -π/2
    };
    const shape = [_]i64{6};
    
    try executor.setInput(x.node_id, std.mem.asBytes(&x_data), &shape, .f32);
    
    try executor.run();
    
    const output = try executor.getOutput(cos_x.node_id);
    const output_f32 = @as([*]const f32, @ptrCast(@alignCast(output.data.ptr)))[0..6];
    
    // Expected values
    const expected = [_]f32{ 
        1.0,        // cos(0)
        0.7071068,  // cos(π/4)
        0.0,        // cos(π/2)
        -1.0,       // cos(π)
        0.7071068,  // cos(-π/4)
        0.0         // cos(-π/2)
    };
    try expectApproxEqSlice(&expected, output_f32, 1e-5);
    
    print("✓ cos operation test passed!\n", .{});
}

test "math: tan operation" {
    const allocator = testing.allocator;
    
    print("\n=== Testing: tan operation ===\n", .{});
    
    var graph = try Graph.init(allocator);
    defer graph.deinit();
    
    const x = try tensor.placeholder(&graph, &.{5}, .f32);
    const tan_x = try x.tan();
    
    try graph.output_nodes.append(graph.arena.allocator(), tan_x.node_id);
    
    var compiled = try compiler.compile.compileCpu(&graph, allocator);
    defer compiled.deinit(allocator);
    
    var executor = try execution.Executor.init(allocator, &compiled, null);
    defer executor.deinit();
    
    // Test values (avoiding π/2 where tan is undefined)
    const x_data = [_]f32{ 
        0,          // 0
        0.7853982,  // π/4
        0.5235988,  // π/6
        -0.7853982, // -π/4
        0.3926991   // π/8
    };
    const shape = [_]i64{5};
    
    try executor.setInput(x.node_id, std.mem.asBytes(&x_data), &shape, .f32);
    
    try executor.run();
    
    const output = try executor.getOutput(tan_x.node_id);
    const output_f32 = @as([*]const f32, @ptrCast(@alignCast(output.data.ptr)))[0..5];
    
    // Expected values
    const expected = [_]f32{ 
        0.0,        // tan(0)
        1.0,        // tan(π/4)
        0.5773503,  // tan(π/6)
        -1.0,       // tan(-π/4)
        0.4142136   // tan(π/8)
    };
    try expectApproxEqSlice(&expected, output_f32, 1e-5);
    
    print("✓ tan operation test passed!\n", .{});
}

test "math: sinh operation" {
    const allocator = testing.allocator;
    
    print("\n=== Testing: sinh operation ===\n", .{});
    
    var graph = try Graph.init(allocator);
    defer graph.deinit();
    
    const x = try tensor.placeholder(&graph, &.{5}, .f32);
    const sinh_x = try x.sinh();
    
    try graph.output_nodes.append(graph.arena.allocator(), sinh_x.node_id);
    
    var compiled = try compiler.compile.compileCpu(&graph, allocator);
    defer compiled.deinit(allocator);
    
    var executor = try execution.Executor.init(allocator, &compiled, null);
    defer executor.deinit();
    
    const x_data = [_]f32{ -2, -1, 0, 1, 2 };
    const shape = [_]i64{5};
    
    try executor.setInput(x.node_id, std.mem.asBytes(&x_data), &shape, .f32);
    
    try executor.run();
    
    const output = try executor.getOutput(sinh_x.node_id);
    const output_f32 = @as([*]const f32, @ptrCast(@alignCast(output.data.ptr)))[0..5];
    
    // Expected values: sinh(x) = (e^x - e^(-x)) / 2
    const expected = [_]f32{ 
        -3.6268604, // sinh(-2)
        -1.1752012, // sinh(-1)
        0.0,        // sinh(0)
        1.1752012,  // sinh(1)
        3.6268604   // sinh(2)
    };
    try expectApproxEqSlice(&expected, output_f32, 1e-5);
    
    print("✓ sinh operation test passed!\n", .{});
}

test "math: cosh operation" {
    const allocator = testing.allocator;
    
    print("\n=== Testing: cosh operation ===\n", .{});
    
    var graph = try Graph.init(allocator);
    defer graph.deinit();
    
    const x = try tensor.placeholder(&graph, &.{5}, .f32);
    const cosh_x = try x.cosh();
    
    try graph.output_nodes.append(graph.arena.allocator(), cosh_x.node_id);
    
    var compiled = try compiler.compile.compileCpu(&graph, allocator);
    defer compiled.deinit(allocator);
    
    var executor = try execution.Executor.init(allocator, &compiled, null);
    defer executor.deinit();
    
    const x_data = [_]f32{ -2, -1, 0, 1, 2 };
    const shape = [_]i64{5};
    
    try executor.setInput(x.node_id, std.mem.asBytes(&x_data), &shape, .f32);
    
    try executor.run();
    
    const output = try executor.getOutput(cosh_x.node_id);
    const output_f32 = @as([*]const f32, @ptrCast(@alignCast(output.data.ptr)))[0..5];
    
    // Expected values: cosh(x) = (e^x + e^(-x)) / 2
    const expected = [_]f32{ 
        3.7621957,  // cosh(-2)
        1.5430806,  // cosh(-1)
        1.0,        // cosh(0)
        1.5430806,  // cosh(1)
        3.7621957   // cosh(2)
    };
    try expectApproxEqSlice(&expected, output_f32, 1e-5);
    
    print("✓ cosh operation test passed!\n", .{});
}

test "math: combined exp and log operations" {
    const allocator = testing.allocator;
    
    print("\n=== Testing: combined exp and log operations ===\n", .{});
    
    var graph = try Graph.init(allocator);
    defer graph.deinit();
    
    const x = try tensor.placeholder(&graph, &.{4}, .f32);
    
    // Test: log(exp(x)) should equal x
    const exp_x = try x.exp();
    const log_exp_x = try exp_x.log();
    
    try graph.output_nodes.append(graph.arena.allocator(), log_exp_x.node_id);
    
    var compiled = try compiler.compile.compileCpu(&graph, allocator);
    defer compiled.deinit(allocator);
    
    var executor = try execution.Executor.init(allocator, &compiled, null);
    defer executor.deinit();
    
    const x_data = [_]f32{ -1, 0, 1, 2 };
    const shape = [_]i64{4};
    
    try executor.setInput(x.node_id, std.mem.asBytes(&x_data), &shape, .f32);
    
    try executor.run();
    
    const output = try executor.getOutput(log_exp_x.node_id);
    const output_f32 = @as([*]const f32, @ptrCast(@alignCast(output.data.ptr)))[0..4];
    
    // Expected: should get back original values
    try expectApproxEqSlice(&x_data, output_f32, 1e-5);
    
    print("✓ Combined exp and log test passed!\n", .{});
}

test "math: trigonometric identity sin^2 + cos^2 = 1" {
    const allocator = testing.allocator;
    
    print("\n=== Testing: trigonometric identity sin²(x) + cos²(x) = 1 ===\n", .{});
    
    var graph = try Graph.init(allocator);
    defer graph.deinit();
    
    const x = try tensor.placeholder(&graph, &.{5}, .f32);
    
    // Compute sin²(x) + cos²(x)
    const sin_x = try x.sin();
    const cos_x = try x.cos();
    const sin2_x = try sin_x.mul(sin_x);
    const cos2_x = try cos_x.mul(cos_x);
    const result = try sin2_x.add(cos2_x);
    
    try graph.output_nodes.append(graph.arena.allocator(), result.node_id);
    
    var compiled = try compiler.compile.compileCpu(&graph, allocator);
    defer compiled.deinit(allocator);
    
    var executor = try execution.Executor.init(allocator, &compiled, null);
    defer executor.deinit();
    
    const x_data = [_]f32{ 0, 0.5, 1.0, 1.5, 2.0 };
    const shape = [_]i64{5};
    
    try executor.setInput(x.node_id, std.mem.asBytes(&x_data), &shape, .f32);
    
    try executor.run();
    
    const output = try executor.getOutput(result.node_id);
    const output_f32 = @as([*]const f32, @ptrCast(@alignCast(output.data.ptr)))[0..5];
    
    // Expected: all should be 1.0
    const expected = [_]f32{ 1.0, 1.0, 1.0, 1.0, 1.0 };
    try expectApproxEqSlice(&expected, output_f32, 1e-5);
    
    print("✓ Trigonometric identity test passed!\n", .{});
}

test "math: chain of math operations" {
    const allocator = testing.allocator;
    
    print("\n=== Testing: chain of math operations ===\n", .{});
    
    var graph = try Graph.init(allocator);
    defer graph.deinit();
    
    const x = try tensor.placeholder(&graph, &.{3}, .f32);
    
    // Chain: cosh(log(exp(x) + 1))
    const one = try tensor.constant(&graph, 1.0, .f32);
    const exp_x = try x.exp();
    const exp_x_plus_1 = try exp_x.add(one);
    const log_result = try exp_x_plus_1.log();
    const result = try log_result.cosh();
    
    try graph.output_nodes.append(graph.arena.allocator(), result.node_id);
    
    var compiled = try compiler.compile.compileCpu(&graph, allocator);
    defer compiled.deinit(allocator);
    
    var executor = try execution.Executor.init(allocator, &compiled, null);
    defer executor.deinit();
    
    const x_data = [_]f32{ 0, 1, -1 };
    const shape = [_]i64{3};
    
    try executor.setInput(x.node_id, std.mem.asBytes(&x_data), &shape, .f32);
    
    try executor.run();
    
    const output = try executor.getOutput(result.node_id);
    const output_f32 = @as([*]const f32, @ptrCast(@alignCast(output.data.ptr)))[0..3];
    
    // Compute expected values
    // For x=0: cosh(log(exp(0)+1)) = cosh(log(2)) = cosh(0.693) ≈ 1.255
    // For x=1: cosh(log(exp(1)+1)) = cosh(log(3.718)) = cosh(1.313) ≈ 2.018
    // For x=-1: cosh(log(exp(-1)+1)) = cosh(log(1.368)) = cosh(0.313) ≈ 1.049
    const expected = [_]f32{ 1.2551690, 2.0183290, 1.0492567 };
    try expectApproxEqSlice(&expected, output_f32, 0.03); // Increased tolerance for chain operations
    
    print("✓ Chain of math operations test passed!\n", .{});
}

test "math operations integration test suite" {
    print("\n" ++ "=" ** 60 ++ "\n", .{});
    print("MATH OPERATIONS INTEGRATION TEST SUITE\n", .{});
    print("=" ** 60 ++ "\n", .{});
    
    print("\nThis test suite verifies all math tensor operations:\n", .{});
    print("• Exponential and logarithm: exp, log/ln\n", .{});
    print("• Trigonometric: cos, tan (sin is primitive)\n", .{});
    print("• Hyperbolic: sinh, cosh\n", .{});
    print("• Combined operations and identities\n", .{});
    
    print("\n" ++ "=" ** 60 ++ "\n", .{});
    print("✅ ALL MATH OPERATION TESTS COMPLETED SUCCESSFULLY!\n", .{});
    print("=" ** 60 ++ "\n", .{});
}