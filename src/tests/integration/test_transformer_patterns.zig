const std = @import("std");
const testing = std.testing;
const print = std.debug.print;

const Graph = @import("graph").Graph;
const tensor = @import("tensor");
const compiler = @import("compiler");
const execution = @import("execution");

// Test transformer-specific patterns and operations

test "transformer: Multi-Head Attention (simplified)" {
    const allocator = testing.allocator;
    
    var graph = try Graph.init(allocator);
    defer graph.deinit();
    
    // Simplified MHA: batch=1, seq=4, heads=2, dim_per_head=8
    const hidden_dim = 16; // heads * dim_per_head
    const num_heads = 2;
    const dim_per_head = 8;
    const seq_len = 4;
    
    // Input: [batch=1, seq=4, hidden=16]
    const input = try tensor.placeholder(&graph, &.{1, seq_len, hidden_dim}, .f32);
    
    // Weight matrices for Q, K, V projections
    const W_q = try tensor.placeholder(&graph, &.{hidden_dim, hidden_dim}, .f32);
    const W_k = try tensor.placeholder(&graph, &.{hidden_dim, hidden_dim}, .f32);
    const W_v = try tensor.placeholder(&graph, &.{hidden_dim, hidden_dim}, .f32);
    
    // Project to Q, K, V
    const Q_2d = try input.reshape(&.{seq_len, hidden_dim});
    const K_2d = try input.reshape(&.{seq_len, hidden_dim});
    const V_2d = try input.reshape(&.{seq_len, hidden_dim});
    
    const Q_proj = try Q_2d.matmul(W_q);
    const K_proj = try K_2d.matmul(W_k);
    const V_proj = try V_2d.matmul(W_v);
    
    // Reshape for multi-head: [seq, heads, dim_per_head]
    const Q_heads = try Q_proj.reshape(&.{seq_len, num_heads, dim_per_head});
    const K_heads = try K_proj.reshape(&.{seq_len, num_heads, dim_per_head});
    const V_heads = try V_proj.reshape(&.{seq_len, num_heads, dim_per_head});
    
    // Transpose for attention: [heads, seq, dim_per_head]
    const Q_t = try Q_heads.transpose(&.{1, 0, 2});
    const K_t = try K_heads.transpose(&.{1, 0, 2});
    const V_t = try V_heads.transpose(&.{1, 0, 2});
    
    // Attention scores: Q @ K^T / sqrt(d)
    const K_t_final = try K_t.transpose(&.{0, 2, 1}); // [heads, dim_per_head, seq]
    const scores = try Q_t.matmul(K_t_final); // [heads, seq, seq]
    
    const scale = try tensor.constant(&graph, 1.0 / @sqrt(@as(f32, dim_per_head)), .f32);
    const scaled_scores = try scores.mul(scale);
    
    // Softmax over last dimension
    const exp_scores = try scaled_scores.exp();
    const sum_exp = try exp_scores.sumReduce(2, true);
    const attention_weights = try exp_scores.divide(sum_exp);
    
    // Apply attention to values
    const attended = try attention_weights.matmul(V_t); // [heads, seq, dim_per_head]
    
    // Reshape back
    const attended_t = try attended.transpose(&.{1, 0, 2}); // [seq, heads, dim_per_head]
    const output = try attended_t.reshape(&.{1, seq_len, hidden_dim});
    
    try graph.output_nodes.append(graph.arena.allocator(), output.node_id);
    
    var compiled = try compiler.compile.compileCpu(&graph, allocator);
    defer compiled.deinit(allocator);
    
    var executor = try execution.Executor.init(allocator, &compiled, null);
    defer executor.deinit();
    
    // Initialize inputs
    var input_data: [64]f32 = undefined;
    var W_q_data: [256]f32 = undefined;
    var W_k_data: [256]f32 = undefined;
    var W_v_data: [256]f32 = undefined;
    
    // Simple patterns for testing
    for (0..64) |i| {
        input_data[i] = @as(f32, @floatFromInt(i % 8)) * 0.1 - 0.4;
    }
    
    // Initialize projection matrices with small values
    for (0..256) |i| {
        const scale_factor: f32 = 0.1;
        W_q_data[i] = if (i % 17 == 0) scale_factor else scale_factor * 0.1;
        W_k_data[i] = if ((i + 5) % 17 == 0) scale_factor else scale_factor * 0.1;
        W_v_data[i] = if ((i + 10) % 17 == 0) scale_factor else scale_factor * 0.1;
    }
    
    try executor.setInput(input.node_id, std.mem.sliceAsBytes(&input_data), &.{1, seq_len, hidden_dim}, .f32);
    try executor.setInput(W_q.node_id, std.mem.sliceAsBytes(&W_q_data), &.{hidden_dim, hidden_dim}, .f32);
    try executor.setInput(W_k.node_id, std.mem.sliceAsBytes(&W_k_data), &.{hidden_dim, hidden_dim}, .f32);
    try executor.setInput(W_v.node_id, std.mem.sliceAsBytes(&W_v_data), &.{hidden_dim, hidden_dim}, .f32);
    
    try executor.run();
    
    const result = try executor.getOutput(output.node_id);
    const result_f32 = @as([*]const f32, @ptrCast(@alignCast(result.data.ptr)))[0..64];
    
    print("\n=== Multi-Head Attention Pattern ===\n", .{});
    print("Input: [1, 4, 16], Heads: 2, Dim per head: 8\n", .{});
    print("Output shape: [1, 4, 16]\n", .{});
    
    // Verify output properties
    var all_finite = true;
    var min_val: f32 = std.math.inf(f32);
    var max_val: f32 = -std.math.inf(f32);
    
    for (result_f32) |val| {
        if (!std.math.isFinite(val)) {
            all_finite = false;
        }
        min_val = @min(min_val, val);
        max_val = @max(max_val, val);
    }
    
    print("All values finite: {}\n", .{all_finite});
    print("Value range: [{d:.3}, {d:.3}]\n", .{min_val, max_val});
    
    try testing.expect(all_finite);
}

test "transformer: Positional Encoding Addition" {
    const allocator = testing.allocator;
    
    var graph = try Graph.init(allocator);
    defer graph.deinit();
    
    // Input embeddings: [batch=2, seq=8, dim=16]
    const embeddings = try tensor.placeholder(&graph, &.{2, 8, 16}, .f32);
    const pos_encoding = try tensor.placeholder(&graph, &.{1, 8, 16}, .f32);
    
    // Add positional encoding (broadcasts across batch)
    const encoded = try embeddings.add(pos_encoding);
    
    // Layer norm after encoding
    const epsilon = try tensor.constant(&graph, 1e-5, .f32);
    const mean = try encoded.mean(2, true);
    const centered = try encoded.subtract(mean);
    const squared = try centered.square();
    const variance = try squared.mean(2, true);
    const var_plus_eps = try variance.add(epsilon);
    const std_dev = try var_plus_eps.sqrt();
    const normalized = try centered.divide(std_dev);
    
    try graph.output_nodes.append(graph.arena.allocator(), normalized.node_id);
    
    var compiled = try compiler.compile.compileCpu(&graph, allocator);
    defer compiled.deinit(allocator);
    
    var executor = try execution.Executor.init(allocator, &compiled, null);
    defer executor.deinit();
    
    // Initialize embeddings
    var embed_data: [256]f32 = undefined;
    var pos_data: [128]f32 = undefined;
    
    // Simple embedding pattern
    for (0..256) |i| {
        embed_data[i] = @as(f32, @floatFromInt(i % 16)) * 0.05;
    }
    
    // Sinusoidal-like positional encoding
    for (0..8) |pos| {
        for (0..16) |dim| {
            const angle = @as(f32, @floatFromInt(pos)) / std.math.pow(f32, 10000.0, @as(f32, @floatFromInt(dim)) / 16.0);
            pos_data[pos * 16 + dim] = if (dim % 2 == 0) @sin(angle) * 0.1 else @cos(angle) * 0.1;
        }
    }
    
    try executor.setInput(embeddings.node_id, std.mem.sliceAsBytes(&embed_data), &.{2, 8, 16}, .f32);
    try executor.setInput(pos_encoding.node_id, std.mem.sliceAsBytes(&pos_data), &.{1, 8, 16}, .f32);
    
    try executor.run();
    
    const result = try executor.getOutput(normalized.node_id);
    const result_f32 = @as([*]const f32, @ptrCast(@alignCast(result.data.ptr)))[0..256];
    
    print("\n=== Positional Encoding Pattern ===\n", .{});
    print("Embeddings: [2, 8, 16] + Positional: [1, 8, 16]\n", .{});
    print("With layer normalization\n", .{});
    
    // Check normalization worked
    for (0..2) |batch| {
        for (0..8) |seq| {
            var sum: f32 = 0;
            var sum_sq: f32 = 0;
            for (0..16) |dim| {
                const val = result_f32[batch * 128 + seq * 16 + dim];
                sum += val;
                sum_sq += val * val;
            }
            const mean_val = sum / 16.0;
            const var_val = sum_sq / 16.0 - mean_val * mean_val;
            
            // Should be normalized
            try testing.expect(@abs(mean_val) < 1e-5);
            try testing.expect(@abs(var_val - 1.0) < 0.1);
        }
    }
    
    print("Layer norm verification: ✓ (mean≈0, var≈1)\n", .{});
}

test "transformer: Feed-Forward Network (FFN)" {
    const allocator = testing.allocator;
    
    var graph = try Graph.init(allocator);
    defer graph.deinit();
    
    // FFN: Linear -> ReLU -> Linear
    // Input: [batch=2, seq=4, hidden=16]
    const input = try tensor.placeholder(&graph, &.{2, 4, 16}, .f32);
    const W1 = try tensor.placeholder(&graph, &.{16, 64}, .f32); // Expand 4x
    const b1 = try tensor.placeholder(&graph, &.{64}, .f32);
    const W2 = try tensor.placeholder(&graph, &.{64, 16}, .f32); // Project back
    const b2 = try tensor.placeholder(&graph, &.{16}, .f32);
    
    // First linear layer
    const input_2d = try input.reshape(&.{8, 16});
    const hidden = try input_2d.matmul(W1);
    const hidden_biased = try hidden.add(b1);
    
    // Activation (ReLU)
    const activated = try hidden_biased.relu();
    
    // Second linear layer
    const output_2d = try activated.matmul(W2);
    const output_biased = try output_2d.add(b2);
    
    // Reshape back and add residual
    const output_reshaped = try output_biased.reshape(&.{2, 4, 16});
    const with_residual = try output_reshaped.add(input);
    
    try graph.output_nodes.append(graph.arena.allocator(), with_residual.node_id);
    
    var compiled = try compiler.compile.compileCpu(&graph, allocator);
    defer compiled.deinit(allocator);
    
    var executor = try execution.Executor.init(allocator, &compiled, null);
    defer executor.deinit();
    
    // Initialize data
    var input_data: [128]f32 = undefined;
    var W1_data: [1024]f32 = undefined;
    var b1_data: [64]f32 = undefined;
    var W2_data: [1024]f32 = undefined;
    var b2_data: [16]f32 = undefined;
    
    for (0..128) |i| {
        input_data[i] = @as(f32, @floatFromInt(i % 8)) * 0.1 - 0.4;
    }
    
    // Xavier-like initialization
    const scale1 = @sqrt(2.0 / 16.0);
    const scale2 = @sqrt(2.0 / 64.0);
    
    for (0..1024) |i| {
        W1_data[i] = if (i % 7 == 0) scale1 * 0.5 else scale1 * 0.05;
        W2_data[i] = if (i % 7 == 0) scale2 * 0.5 else scale2 * 0.05;
    }
    
    for (0..64) |i| b1_data[i] = 0.0;
    for (0..16) |i| b2_data[i] = 0.0;
    
    try executor.setInput(input.node_id, std.mem.sliceAsBytes(&input_data), &.{2, 4, 16}, .f32);
    try executor.setInput(W1.node_id, std.mem.sliceAsBytes(&W1_data), &.{16, 64}, .f32);
    try executor.setInput(b1.node_id, std.mem.sliceAsBytes(&b1_data), &.{64}, .f32);
    try executor.setInput(W2.node_id, std.mem.sliceAsBytes(&W2_data), &.{64, 16}, .f32);
    try executor.setInput(b2.node_id, std.mem.sliceAsBytes(&b2_data), &.{16}, .f32);
    
    try executor.run();
    
    const result = try executor.getOutput(with_residual.node_id);
    const result_f32 = @as([*]const f32, @ptrCast(@alignCast(result.data.ptr)))[0..128];
    
    print("\n=== Feed-Forward Network Pattern ===\n", .{});
    print("Input: [2, 4, 16] -> FFN(expand 4x) -> [2, 4, 16]\n", .{});
    print("With residual connection\n", .{});
    
    // Check that residual helped prevent vanishing
    var num_changed: usize = 0;
    for (0..128) |i| {
        if (@abs(result_f32[i] - input_data[i]) > 1e-6) {
            num_changed += 1;
        }
        try testing.expect(std.math.isFinite(result_f32[i]));
    }
    
    print("Values changed from input: {}/128\n", .{num_changed});
    print("All values finite: ✓\n", .{});
}

test "transformer: Masked Self-Attention (Causal)" {
    const allocator = testing.allocator;
    
    var graph = try Graph.init(allocator);
    defer graph.deinit();
    
    const seq_len = 5;
    const hidden_dim = 8;
    
    // Input: [batch=1, seq=5, hidden=8]
    const input = try tensor.placeholder(&graph, &.{1, seq_len, hidden_dim}, .f32);
    const W_qkv = try tensor.placeholder(&graph, &.{hidden_dim, hidden_dim * 3}, .f32);
    
    // Causal mask (upper triangular = -inf)
    const mask = try tensor.placeholder(&graph, &.{seq_len, seq_len}, .f32);
    
    // Project to Q, K, V in one go
    const input_2d = try input.reshape(&.{seq_len, hidden_dim});
    const qkv = try input_2d.matmul(W_qkv);
    
    // Split into Q, K, V
    const q = try qkv.slice(&.{0, 0}, &.{seq_len, hidden_dim});
    const k = try qkv.slice(&.{0, hidden_dim}, &.{seq_len, hidden_dim * 2});
    const v = try qkv.slice(&.{0, hidden_dim * 2}, &.{seq_len, hidden_dim * 3});
    
    // Compute attention scores
    const k_t = try k.transpose(&.{1, 0});
    const scores = try q.matmul(k_t); // [seq, seq]
    
    const scale = try tensor.constant(&graph, 1.0 / @sqrt(@as(f32, hidden_dim)), .f32);
    const scaled_scores = try scores.mul(scale);
    
    // Apply mask
    const masked_scores = try scaled_scores.add(mask);
    
    // Softmax
    const exp_scores = try masked_scores.exp();
    const sum_exp = try exp_scores.sumReduce(1, true);
    const attention = try exp_scores.divide(sum_exp);
    
    // Apply attention
    const output = try attention.matmul(v);
    const output_3d = try output.reshape(&.{1, seq_len, hidden_dim});
    
    try graph.output_nodes.append(graph.arena.allocator(), output_3d.node_id);
    try graph.output_nodes.append(graph.arena.allocator(), attention.node_id);
    
    var compiled = try compiler.compile.compileCpu(&graph, allocator);
    defer compiled.deinit(allocator);
    
    var executor = try execution.Executor.init(allocator, &compiled, null);
    defer executor.deinit();
    
    // Initialize inputs
    var input_data: [40]f32 = undefined;
    var W_qkv_data: [192]f32 = undefined;
    var mask_data: [25]f32 = undefined;
    
    for (0..40) |i| {
        input_data[i] = @as(f32, @floatFromInt(i % 4)) * 0.25;
    }
    
    for (0..192) |i| {
        W_qkv_data[i] = if (i % 13 == 0) 0.2 else 0.02;
    }
    
    // Create causal mask
    for (0..seq_len) |i| {
        for (0..seq_len) |j| {
            mask_data[i * seq_len + j] = if (j > i) -1e9 else 0.0;
        }
    }
    
    try executor.setInput(input.node_id, std.mem.sliceAsBytes(&input_data), &.{1, seq_len, hidden_dim}, .f32);
    try executor.setInput(W_qkv.node_id, std.mem.sliceAsBytes(&W_qkv_data), &.{hidden_dim, hidden_dim * 3}, .f32);
    try executor.setInput(mask.node_id, std.mem.sliceAsBytes(&mask_data), &.{seq_len, seq_len}, .f32);
    
    try executor.run();
    
    const output_result = try executor.getOutput(output_3d.node_id);
    _ = output_result; // We don't verify the output in this test
    
    const attn = try executor.getOutput(attention.node_id);
    const attn_f32 = @as([*]const f32, @ptrCast(@alignCast(attn.data.ptr)))[0..25];
    
    print("\n=== Masked Self-Attention Pattern ===\n", .{});
    print("Input: [1, 5, 8], Causal mask applied\n", .{});
    print("Attention matrix:\n", .{});
    
    for (0..seq_len) |i| {
        print("  Position {}: [", .{i});
        for (0..seq_len) |j| {
            print("{d:.3} ", .{attn_f32[i * seq_len + j]});
        }
        print("]\n", .{});
        
        // Verify causal property
        for (i + 1..seq_len) |j| {
            try testing.expect(attn_f32[i * seq_len + j] < 1e-8);
        }
    }
    
    print("Causal property verified: ✓\n", .{});
}

test "transformer: Cross-Attention Pattern" {
    const allocator = testing.allocator;
    
    var graph = try Graph.init(allocator);
    defer graph.deinit();
    
    // Query from decoder, Key/Value from encoder
    const query_seq = 3;
    const kv_seq = 5;
    const hidden_dim = 8;
    
    const queries = try tensor.placeholder(&graph, &.{1, query_seq, hidden_dim}, .f32);
    const keys = try tensor.placeholder(&graph, &.{1, kv_seq, hidden_dim}, .f32);
    const values = try tensor.placeholder(&graph, &.{1, kv_seq, hidden_dim}, .f32);
    
    // Project Q, K, V
    const W_q = try tensor.placeholder(&graph, &.{hidden_dim, hidden_dim}, .f32);
    const W_k = try tensor.placeholder(&graph, &.{hidden_dim, hidden_dim}, .f32);
    const W_v = try tensor.placeholder(&graph, &.{hidden_dim, hidden_dim}, .f32);
    
    const q_2d = try queries.reshape(&.{query_seq, hidden_dim});
    const k_2d = try keys.reshape(&.{kv_seq, hidden_dim});
    const v_2d = try values.reshape(&.{kv_seq, hidden_dim});
    
    const q_proj = try q_2d.matmul(W_q);
    const k_proj = try k_2d.matmul(W_k);
    const v_proj = try v_2d.matmul(W_v);
    
    // Attention computation
    const k_t = try k_proj.transpose(&.{1, 0});
    const scores = try q_proj.matmul(k_t); // [query_seq, kv_seq]
    
    const scale = try tensor.constant(&graph, 1.0 / @sqrt(@as(f32, hidden_dim)), .f32);
    const scaled = try scores.mul(scale);
    
    const exp_scores = try scaled.exp();
    const sum_exp = try exp_scores.sum(1, true);
    const attention = try exp_scores.divide(sum_exp);
    
    const output = try attention.matmul(v_proj);
    const output_3d = try output.reshape(&.{1, query_seq, hidden_dim});
    
    try graph.output_nodes.append(graph.arena.allocator(), output_3d.node_id);
    try graph.output_nodes.append(graph.arena.allocator(), attention.node_id);
    
    var compiled = try compiler.compile.compileCpu(&graph, allocator);
    defer compiled.deinit(allocator);
    
    var executor = try execution.Executor.init(allocator, &compiled, null);
    defer executor.deinit();
    
    // Initialize
    var q_data: [24]f32 = undefined;
    var k_data: [40]f32 = undefined;
    var v_data: [40]f32 = undefined;
    var W_q_data: [64]f32 = undefined;
    var W_k_data: [64]f32 = undefined;
    var W_v_data: [64]f32 = undefined;
    
    // Different patterns for Q and K/V
    for (0..24) |i| q_data[i] = @as(f32, @floatFromInt(i % 3)) * 0.3;
    for (0..40) |i| {
        k_data[i] = @as(f32, @floatFromInt(i % 5)) * 0.2;
        v_data[i] = @as(f32, @floatFromInt((i + 2) % 5)) * 0.2;
    }
    
    for (0..64) |i| {
        W_q_data[i] = if (i % 9 == 0) 0.3 else 0.03;
        W_k_data[i] = if ((i + 1) % 9 == 0) 0.3 else 0.03;
        W_v_data[i] = if ((i + 2) % 9 == 0) 0.3 else 0.03;
    }
    
    try executor.setInput(queries.node_id, std.mem.sliceAsBytes(&q_data), &.{1, query_seq, hidden_dim}, .f32);
    try executor.setInput(keys.node_id, std.mem.sliceAsBytes(&k_data), &.{1, kv_seq, hidden_dim}, .f32);
    try executor.setInput(values.node_id, std.mem.sliceAsBytes(&v_data), &.{1, kv_seq, hidden_dim}, .f32);
    try executor.setInput(W_q.node_id, std.mem.sliceAsBytes(&W_q_data), &.{hidden_dim, hidden_dim}, .f32);
    try executor.setInput(W_k.node_id, std.mem.sliceAsBytes(&W_k_data), &.{hidden_dim, hidden_dim}, .f32);
    try executor.setInput(W_v.node_id, std.mem.sliceAsBytes(&W_v_data), &.{hidden_dim, hidden_dim}, .f32);
    
    try executor.run();
    
    const attn = try executor.getOutput(attention.node_id);
    const attn_f32 = @as([*]const f32, @ptrCast(@alignCast(attn.data.ptr)))[0..15];
    
    print("\n=== Cross-Attention Pattern ===\n", .{});
    print("Query: [1, 3, 8], Key/Value: [1, 5, 8]\n", .{});
    print("Cross-attention matrix [3x5]:\n", .{});
    
    for (0..query_seq) |q| {
        print("  Query {} attends to: [", .{q});
        var sum: f32 = 0;
        for (0..kv_seq) |k| {
            const val = attn_f32[q * kv_seq + k];
            print("{d:.3} ", .{val});
            sum += val;
        }
        print("] sum={d:.6}\n", .{sum});
        
        // Verify attention sums to 1
        try testing.expect(@abs(sum - 1.0) < 1e-5);
    }
}

test "transformer: GELU activation pattern" {
    const allocator = testing.allocator;
    
    var graph = try Graph.init(allocator);
    defer graph.deinit();
    
    // GELU(x) = 0.5 * x * (1 + tanh(sqrt(2/π) * (x + 0.044715 * x^3)))
    // Approximate: x * sigmoid(1.702 * x)
    
    const input = try tensor.placeholder(&graph, &.{2, 8}, .f32);
    
    // Approximate GELU
    const factor = try tensor.constant(&graph, 1.702, .f32);
    const scaled = try input.mul(factor);
    const sigmoid = try scaled.sigmoid();
    const gelu = try input.mul(sigmoid);
    
    // Also compute exact version for comparison
    const c1 = try tensor.constant(&graph, 0.7978845608, .f32); // sqrt(2/pi)
    const c2 = try tensor.constant(&graph, 0.044715, .f32);
    const half = try tensor.constant(&graph, 0.5, .f32);
    const one = try tensor.constant(&graph, 1.0, .f32);
    
    const x_cubed = try input.cube();
    const c2_x_cubed = try x_cubed.mul(c2);
    const inner = try input.add(c2_x_cubed);
    const scaled_inner = try inner.mul(c1);
    const tanh_val = try scaled_inner.tanh();
    const one_plus_tanh = try one.add(tanh_val);
    const x_factor = try input.mul(one_plus_tanh);
    const gelu_exact = try half.mul(x_factor);
    
    try graph.output_nodes.append(graph.arena.allocator(), gelu.node_id);
    try graph.output_nodes.append(graph.arena.allocator(), gelu_exact.node_id);
    
    var compiled = try compiler.compile.compileCpu(&graph, allocator);
    defer compiled.deinit(allocator);
    
    var executor = try execution.Executor.init(allocator, &compiled, null);
    defer executor.deinit();
    
    // Test with range of values
    const input_data = [_]f32{
        -3.0, -2.0, -1.0, -0.5, 0.0, 0.5, 1.0, 2.0,
        -2.5, -1.5, -0.8, -0.2, 0.2, 0.8, 1.5, 2.5,
    };
    
    try executor.setInput(input.node_id, std.mem.sliceAsBytes(&input_data), &.{2, 8}, .f32);
    
    try executor.run();
    
    const approx = try executor.getOutput(gelu.node_id);
    const exact = try executor.getOutput(gelu_exact.node_id);
    const approx_f32 = @as([*]const f32, @ptrCast(@alignCast(approx.data.ptr)))[0..16];
    const exact_f32 = @as([*]const f32, @ptrCast(@alignCast(exact.data.ptr)))[0..16];
    
    print("\n=== GELU Activation Pattern ===\n", .{});
    print("Testing GELU approximation vs exact\n", .{});
    
    var max_diff: f32 = 0;
    for (0..16) |i| {
        const diff = @abs(approx_f32[i] - exact_f32[i]);
        max_diff = @max(max_diff, diff);
        
        if (i < 8) {
            print("  x={d:.1}: approx={d:.3}, exact={d:.3}, diff={d:.6}\n", .{
                input_data[i], approx_f32[i], exact_f32[i], diff
            });
        }
        
        // Both should be finite
        try testing.expect(std.math.isFinite(approx_f32[i]));
        try testing.expect(std.math.isFinite(exact_f32[i]));
    }
    
    print("Max approximation error: {d:.6}\n", .{max_diff});
    try testing.expect(max_diff < 0.02); // Good approximation (allow 2% error)
}