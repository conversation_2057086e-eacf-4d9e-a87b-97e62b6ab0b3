/// Integration tests for reduction tensor operations
/// Tests reduction operations: sum, max, min, mean, variance, stddev, and all-element reductions

const std = @import("std");
const testing = std.testing;
const print = std.debug.print;

// Import Zing components
const types = @import("types");
const Graph = @import("graph").Graph;
const tensor = @import("tensor");
const compiler = @import("compiler");
const execution = @import("execution");

// Helper to compare results with tolerance
fn expectApproxEqSlice(expected: []const f32, actual: []const f32, tolerance: f32) !void {
    try testing.expectEqual(expected.len, actual.len);
    for (expected, actual, 0..) |e, a, i| {
        if (@abs(e - a) > tolerance) {
            print("Mismatch at index {}: expected {d:.6}, got {d:.6}\n", .{ i, e, a });
            return error.ValueMismatch;
        }
    }
}

test "reduction: sum along axis" {
    const allocator = testing.allocator;
    
    print("\n=== Testing: sum reduction along axis ===\n", .{});
    
    var graph = try Graph.init(allocator);
    defer graph.deinit();
    
    // Test 2D tensor: [[1, 2, 3], [4, 5, 6]]
    const x = try tensor.placeholder(&graph, &.{2, 3}, .f32);
    
    // Sum along axis 0 (columns): [1+4, 2+5, 3+6] = [5, 7, 9]
    const sum_axis0 = try x.sum(0, true);
    // Sum along axis 1 (rows): [1+2+3, 4+5+6] = [6, 15]
    const sum_axis1 = try x.sum(1, true);
    
    try graph.output_nodes.append(graph.arena.allocator(), sum_axis0.node_id);
    try graph.output_nodes.append(graph.arena.allocator(), sum_axis1.node_id);
    
    var compiled = try compiler.compile.compileCpu(&graph, allocator);
    defer compiled.deinit(allocator);
    
    var executor = try execution.Executor.init(allocator, &compiled, null);
    defer executor.deinit();
    
    // Input data: [[1, 2, 3], [4, 5, 6]]
    const x_data = [_]f32{ 1, 2, 3, 4, 5, 6 };
    const shape = [_]i64{2, 3};
    
    try executor.setInput(x.node_id, std.mem.asBytes(&x_data), &shape, .f32);
    try executor.run();
    
    const output_axis0 = try executor.getOutput(sum_axis0.node_id);
    // sum(0) on [2,3] gives [1,3], but we want to check 3 values
    try testing.expectEqual(@as(usize, 2), output_axis0.shape.len);
    try testing.expectEqual(@as(i64, 1), output_axis0.shape[0]);
    try testing.expectEqual(@as(i64, 3), output_axis0.shape[1]);
    const output_axis0_f32 = @as([*]const f32, @ptrCast(@alignCast(output_axis0.data.ptr)))[0..3];
    
    const output_axis1 = try executor.getOutput(sum_axis1.node_id);
    // sum(1) on [2,3] gives [2,1], but we want to check 2 values
    try testing.expectEqual(@as(usize, 2), output_axis1.shape.len);
    try testing.expectEqual(@as(i64, 2), output_axis1.shape[0]);
    try testing.expectEqual(@as(i64, 1), output_axis1.shape[1]);
    const output_axis1_f32 = @as([*]const f32, @ptrCast(@alignCast(output_axis1.data.ptr)))[0..2];
    
    // Expected results
    const expected_axis0 = [_]f32{ 5, 7, 9 };
    const expected_axis1 = [_]f32{ 6, 15 };
    
    try expectApproxEqSlice(&expected_axis0, output_axis0_f32, 1e-6);
    try expectApproxEqSlice(&expected_axis1, output_axis1_f32, 1e-6);
    
    print("✓ sum reduction along axis test passed!\n", .{});
}

test "reduction: max along axis" {
    const allocator = testing.allocator;
    
    print("\n=== Testing: max reduction along axis ===\n", .{});
    
    var graph = try Graph.init(allocator);
    defer graph.deinit();
    
    // Test 2D tensor: [[1, 5, 2], [4, 2, 6]]
    const x = try tensor.placeholder(&graph, &.{2, 3}, .f32);
    
    // Max along axis 0 (columns): [max(1,4), max(5,2), max(2,6)] = [4, 5, 6]
    const max_axis0 = try x.max(0, true);
    // Max along axis 1 (rows): [max(1,5,2), max(4,2,6)] = [5, 6]
    const max_axis1 = try x.max(1, true);
    
    try graph.output_nodes.append(graph.arena.allocator(), max_axis0.node_id);
    try graph.output_nodes.append(graph.arena.allocator(), max_axis1.node_id);
    
    var compiled = try compiler.compile.compileCpu(&graph, allocator);
    defer compiled.deinit(allocator);
    
    var executor = try execution.Executor.init(allocator, &compiled, null);
    defer executor.deinit();
    
    // Input data: [[1, 5, 2], [4, 2, 6]]
    const x_data = [_]f32{ 1, 5, 2, 4, 2, 6 };
    const shape = [_]i64{2, 3};
    
    try executor.setInput(x.node_id, std.mem.asBytes(&x_data), &shape, .f32);
    try executor.run();
    
    const output_axis0 = try executor.getOutput(max_axis0.node_id);
    // max(0) on [2,3] gives [1,3], but we want to check 3 values
    try testing.expectEqual(@as(usize, 2), output_axis0.shape.len);
    try testing.expectEqual(@as(i64, 1), output_axis0.shape[0]);
    try testing.expectEqual(@as(i64, 3), output_axis0.shape[1]);
    const output_axis0_f32 = @as([*]const f32, @ptrCast(@alignCast(output_axis0.data.ptr)))[0..3];
    
    const output_axis1 = try executor.getOutput(max_axis1.node_id);
    // max(1) on [2,3] gives [2,1], but we want to check 2 values
    try testing.expectEqual(@as(usize, 2), output_axis1.shape.len);
    try testing.expectEqual(@as(i64, 2), output_axis1.shape[0]);
    try testing.expectEqual(@as(i64, 1), output_axis1.shape[1]);
    const output_axis1_f32 = @as([*]const f32, @ptrCast(@alignCast(output_axis1.data.ptr)))[0..2];
    
    // Expected results
    const expected_axis0 = [_]f32{ 4, 5, 6 };
    const expected_axis1 = [_]f32{ 5, 6 };
    
    try expectApproxEqSlice(&expected_axis0, output_axis0_f32, 1e-6);
    try expectApproxEqSlice(&expected_axis1, output_axis1_f32, 1e-6);
    
    print("✓ max reduction along axis test passed!\n", .{});
}

test "reduction: mean along axis" {
    const allocator = testing.allocator;
    
    print("\n=== Testing: mean reduction along axis ===\n", .{});
    
    var graph = try Graph.init(allocator);
    defer graph.deinit();
    
    // Test 2D tensor: [[2, 4, 6], [8, 10, 12]]
    const x = try tensor.placeholder(&graph, &.{2, 3}, .f32);
    
    // Mean along axis 0 (columns): [(2+8)/2, (4+10)/2, (6+12)/2] = [5, 7, 9]
    const mean_axis0 = try x.mean(0, true);
    // Mean along axis 1 (rows): [(2+4+6)/3, (8+10+12)/3] = [4, 10]
    const mean_axis1 = try x.mean(1, true);
    
    try graph.output_nodes.append(graph.arena.allocator(), mean_axis0.node_id);
    try graph.output_nodes.append(graph.arena.allocator(), mean_axis1.node_id);
    
    var compiled = try compiler.compile.compileCpu(&graph, allocator);
    defer compiled.deinit(allocator);
    
    var executor = try execution.Executor.init(allocator, &compiled, null);
    defer executor.deinit();
    
    // Input data: [[2, 4, 6], [8, 10, 12]]
    const x_data = [_]f32{ 2, 4, 6, 8, 10, 12 };
    const shape = [_]i64{2, 3};
    
    try executor.setInput(x.node_id, std.mem.asBytes(&x_data), &shape, .f32);
    try executor.run();
    
    const output_axis0 = try executor.getOutput(mean_axis0.node_id);
    // mean(0) on [2,3] gives [1,3], but we want to check 3 values
    try testing.expectEqual(@as(usize, 2), output_axis0.shape.len);
    try testing.expectEqual(@as(i64, 1), output_axis0.shape[0]);
    try testing.expectEqual(@as(i64, 3), output_axis0.shape[1]);
    const output_axis0_f32 = @as([*]const f32, @ptrCast(@alignCast(output_axis0.data.ptr)))[0..3];
    
    const output_axis1 = try executor.getOutput(mean_axis1.node_id);
    // mean(1) on [2,3] gives [2,1], but we want to check 2 values
    try testing.expectEqual(@as(usize, 2), output_axis1.shape.len);
    try testing.expectEqual(@as(i64, 2), output_axis1.shape[0]);
    try testing.expectEqual(@as(i64, 1), output_axis1.shape[1]);
    const output_axis1_f32 = @as([*]const f32, @ptrCast(@alignCast(output_axis1.data.ptr)))[0..2];
    
    // Expected results
    const expected_axis0 = [_]f32{ 5, 7, 9 };
    const expected_axis1 = [_]f32{ 4, 10 };
    
    try expectApproxEqSlice(&expected_axis0, output_axis0_f32, 1e-6);
    try expectApproxEqSlice(&expected_axis1, output_axis1_f32, 1e-6);
    
    print("✓ mean reduction along axis test passed!\n", .{});
}

test "reduction: sumAll operation" {
    const allocator = testing.allocator;
    
    print("\n=== Testing: sumAll operation ===\n", .{});
    
    var graph = try Graph.init(allocator);
    defer graph.deinit();
    
    // Test 2D tensor: [[1, 2], [3, 4]]
    const x = try tensor.placeholder(&graph, &.{2, 2}, .f32);
    const sum_all = try x.sumAll();
    
    try graph.output_nodes.append(graph.arena.allocator(), sum_all.node_id);
    
    var compiled = try compiler.compile.compileCpu(&graph, allocator);
    defer compiled.deinit(allocator);
    
    var executor = try execution.Executor.init(allocator, &compiled, null);
    defer executor.deinit();
    
    // Input data: [[1, 2], [3, 4]]
    const x_data = [_]f32{ 1, 2, 3, 4 };
    const shape = [_]i64{2, 2};
    
    try executor.setInput(x.node_id, std.mem.asBytes(&x_data), &shape, .f32);
    try executor.run();
    
    const output = try executor.getOutput(sum_all.node_id);
    const output_f32 = @as([*]const f32, @ptrCast(@alignCast(output.data.ptr)))[0..1];
    
    // Expected: 1 + 2 + 3 + 4 = 10
    const expected = [_]f32{ 10 };
    try expectApproxEqSlice(&expected, output_f32, 1e-6);
    
    print("✓ sumAll operation test passed!\n", .{});
}

test "reduction: meanAll operation" {
    const allocator = testing.allocator;
    
    print("\n=== Testing: meanAll operation ===\n", .{});
    
    var graph = try Graph.init(allocator);
    defer graph.deinit();
    
    // Test 2D tensor: [[2, 4], [6, 8]]
    const x = try tensor.placeholder(&graph, &.{2, 2}, .f32);
    const mean_all = try x.meanAll();
    
    try graph.output_nodes.append(graph.arena.allocator(), mean_all.node_id);
    
    var compiled = try compiler.compile.compileCpu(&graph, allocator);
    defer compiled.deinit(allocator);
    
    var executor = try execution.Executor.init(allocator, &compiled, null);
    defer executor.deinit();
    
    // Input data: [[2, 4], [6, 8]]
    const x_data = [_]f32{ 2, 4, 6, 8 };
    const shape = [_]i64{2, 2};
    
    try executor.setInput(x.node_id, std.mem.asBytes(&x_data), &shape, .f32);
    try executor.run();
    
    const output = try executor.getOutput(mean_all.node_id);
    const output_f32 = @as([*]const f32, @ptrCast(@alignCast(output.data.ptr)))[0..1];
    
    // Expected: (2 + 4 + 6 + 8) / 4 = 20 / 4 = 5
    const expected = [_]f32{ 5 };
    try expectApproxEqSlice(&expected, output_f32, 1e-6);
    
    print("✓ meanAll operation test passed!\n", .{});
}

test "reduction: 3D tensor operations" {
    const allocator = testing.allocator;
    
    print("\n=== Testing: 3D tensor reduction operations ===\n", .{});
    
    var graph = try Graph.init(allocator);
    defer graph.deinit();
    
    // Test 3D tensor: shape [2, 2, 2]
    const x = try tensor.placeholder(&graph, &.{2, 2, 2}, .f32);
    
    // Sum along axis 2 (innermost): should result in shape [2, 2]
    const sum_axis2 = try x.sum(2, true);
    
    try graph.output_nodes.append(graph.arena.allocator(), sum_axis2.node_id);
    
    var compiled = try compiler.compile.compileCpu(&graph, allocator);
    defer compiled.deinit(allocator);
    
    var executor = try execution.Executor.init(allocator, &compiled, null);
    defer executor.deinit();
    
    // Input data: [[[1, 2], [3, 4]], [[5, 6], [7, 8]]]
    const x_data = [_]f32{ 1, 2, 3, 4, 5, 6, 7, 8 };
    const shape = [_]i64{2, 2, 2};
    
    try executor.setInput(x.node_id, std.mem.asBytes(&x_data), &shape, .f32);
    try executor.run();
    
    const output = try executor.getOutput(sum_axis2.node_id);
    // sum(2) on [2,2,2] gives [2,2,1], but we want to check 4 values
    try testing.expectEqual(@as(usize, 3), output.shape.len);
    try testing.expectEqual(@as(i64, 2), output.shape[0]);
    try testing.expectEqual(@as(i64, 2), output.shape[1]);
    try testing.expectEqual(@as(i64, 1), output.shape[2]);
    const output_f32 = @as([*]const f32, @ptrCast(@alignCast(output.data.ptr)))[0..4];
    
    // Expected: [[1+2, 3+4], [5+6, 7+8]] = [[3, 7], [11, 15]]
    const expected = [_]f32{ 3, 7, 11, 15 };
    try expectApproxEqSlice(&expected, output_f32, 1e-6);
    
    print("✓ 3D tensor reduction operations test passed!\n", .{});
}

test "reduction: variance and stddev" {
    const allocator = testing.allocator;
    
    print("\n=== Testing: variance and standard deviation ===\n", .{});
    
    var graph = try Graph.init(allocator);
    defer graph.deinit();
    
    // Test simple case where we know the variance: [1, 3, 5] along axis 0
    const x = try tensor.placeholder(&graph, &.{3, 1}, .f32);
    const variance_result = try x.variance(0, true);
    const stddev_result = try x.stddev(0, true);
    
    try graph.output_nodes.append(graph.arena.allocator(), variance_result.node_id);
    try graph.output_nodes.append(graph.arena.allocator(), stddev_result.node_id);
    
    var compiled = try compiler.compile.compileCpu(&graph, allocator);
    defer compiled.deinit(allocator);
    
    var executor = try execution.Executor.init(allocator, &compiled, null);
    defer executor.deinit();
    
    // Input data: [[1], [3], [5]] - mean = 3, variance = ((1-3)² + (3-3)² + (5-3)²)/3 = (4+0+4)/3 = 8/3
    const x_data = [_]f32{ 1, 3, 5 };
    const shape = [_]i64{3, 1};
    
    try executor.setInput(x.node_id, std.mem.asBytes(&x_data), &shape, .f32);
    try executor.run();
    
    const var_output = try executor.getOutput(variance_result.node_id);
    // variance(0) on [3,1] gives [1,1], but we want to check 1 value
    try testing.expectEqual(@as(usize, 2), var_output.shape.len);
    try testing.expectEqual(@as(i64, 1), var_output.shape[0]);
    try testing.expectEqual(@as(i64, 1), var_output.shape[1]);
    const var_f32 = @as([*]const f32, @ptrCast(@alignCast(var_output.data.ptr)))[0..1];
    
    const std_output = try executor.getOutput(stddev_result.node_id);
    // stddev(0) on [3,1] gives [1,1], but we want to check 1 value
    try testing.expectEqual(@as(usize, 2), std_output.shape.len);
    try testing.expectEqual(@as(i64, 1), std_output.shape[0]);
    try testing.expectEqual(@as(i64, 1), std_output.shape[1]);
    const std_f32 = @as([*]const f32, @ptrCast(@alignCast(std_output.data.ptr)))[0..1];
    
    // Expected variance: 8/3 = 2.666..., stddev: sqrt(8/3) = 1.633...
    const expected_var = [_]f32{ 8.0/3.0 };
    const expected_std = [_]f32{ @sqrt(8.0/3.0) };
    
    try expectApproxEqSlice(&expected_var, var_f32, 1e-5);
    try expectApproxEqSlice(&expected_std, std_f32, 1e-5);
    
    print("✓ variance and standard deviation test passed!\n", .{});
}

test "reduction: product operations" {
    const allocator = testing.allocator;
    
    print("\n=== Testing: product operations ===\n", .{});
    
    var graph = try Graph.init(allocator);
    defer graph.deinit();
    
    // Test simple product: [2, 3, 4] -> prod = 24
    const x = try tensor.placeholder(&graph, &.{1, 3}, .f32);
    const prod_result = try x.prod(1, true);  // Product along axis 1
    
    try graph.output_nodes.append(graph.arena.allocator(), prod_result.node_id);
    
    var compiled = try compiler.compile.compileCpu(&graph, allocator);
    defer compiled.deinit(allocator);
    
    var executor = try execution.Executor.init(allocator, &compiled, null);
    defer executor.deinit();
    
    // Input data: [[2, 3, 4]]
    const x_data = [_]f32{ 2, 3, 4 };
    const shape = [_]i64{1, 3};
    
    try executor.setInput(x.node_id, std.mem.asBytes(&x_data), &shape, .f32);
    try executor.run();
    
    const output = try executor.getOutput(prod_result.node_id);
    // prod(1) on [1,3] gives [1,1], but we want to check 1 value
    try testing.expectEqual(@as(usize, 2), output.shape.len);
    try testing.expectEqual(@as(i64, 1), output.shape[0]);
    try testing.expectEqual(@as(i64, 1), output.shape[1]);
    const output_f32 = @as([*]const f32, @ptrCast(@alignCast(output.data.ptr)))[0..1];
    
    // Expected: 2 * 3 * 4 = 24
    const expected = [_]f32{ 24 };
    try expectApproxEqSlice(&expected, output_f32, 1e-5);
    
    print("✓ product operations test passed!\n", .{});
}

// ========== Tests from test_reduction_debug.zig ==========

// Helper to run a simple test
fn runSimpleTest(
    allocator: std.mem.Allocator,
    comptime test_name: []const u8,
    graph: *Graph,
    input_node: u32,
    input_data: []const f32,
    input_shape: []const i64,
    output_node: u32,
    expected: []const f32,
) !void {
    print("\n=== Testing: {s} ===\n", .{test_name});
    
    // Compile
    var compiled = try compiler.compile.compileCpu(graph, allocator);
    defer compiled.deinit(allocator);
    
    var executor = try execution.Executor.init(allocator, &compiled, null);
    defer executor.deinit();
    
    // Set input
    try executor.setInput(input_node, std.mem.sliceAsBytes(input_data), input_shape, .f32);
    
    // Run
    try executor.run();
    
    // Check output
    const output = try executor.getOutput(output_node);
    const output_f32 = @as([*]const f32, @ptrCast(@alignCast(output.data.ptr)))[0..expected.len];
    
    print("Input shape: [", .{});
    for (input_shape) |dim| print("{} ", .{dim});
    print("]\n", .{});
    
    print("Input data: [", .{});
    for (input_data[0..@min(10, input_data.len)]) |v| print("{d:.1} ", .{v});
    if (input_data.len > 10) print("...({} total)", .{input_data.len});
    print("]\n", .{});
    
    print("Output shape: [", .{});
    for (output.shape) |dim| print("{} ", .{dim});
    print("]\n", .{});
    
    print("Output: [", .{});
    for (output_f32[0..@min(10, output_f32.len)]) |v| print("{d:.1} ", .{v});
    if (output_f32.len > 10) print("...({} total)", .{output_f32.len});
    print("]\n", .{});
    
    print("Expected: [", .{});
    for (expected[0..@min(10, expected.len)]) |v| print("{d:.1} ", .{v});
    if (expected.len > 10) print("...({} total)", .{expected.len});
    print("]\n", .{});
    
    // Check values
    for (expected, output_f32, 0..) |exp, act, i| {
        if (@abs(exp - act) > 1e-5) {
            print("❌ Mismatch at index {}: expected {d}, got {d}\n", .{ i, exp, act });
            return error.TestFailed;
        }
    }
    
    print("✅ Passed\n", .{});
}

test "debug: simple sum reduction axis 0" {
    // From test_reduction_debug.zig
    const allocator = testing.allocator;
    
    var graph = try Graph.init(allocator);
    defer graph.deinit();
    
    // Simple 2x3 matrix
    const input = try tensor.placeholder(&graph, &.{2, 3}, .f32);
    const sum_result = try input.sumReduce(0, true); // [2,3] -> [1,3]
    const squeezed = try sum_result.squeeze(0); // [1,3] -> [3]
    
    try graph.output_nodes.append(graph.arena.allocator(), squeezed.node_id);
    
    // Input: [[1, 2, 3], [4, 5, 6]]
    // Sum axis 0: [5, 7, 9]
    const input_data = [_]f32{ 1, 2, 3, 4, 5, 6 };
    const expected = [_]f32{ 5, 7, 9 };
    
    try runSimpleTest(
        allocator,
        "sum axis 0 on 2x3 matrix",
        &graph,
        input.node_id,
        &input_data,
        &.{2, 3},
        squeezed.node_id,
        &expected,
    );
}

test "debug: simple sum reduction axis 1" {
    // From test_reduction_debug.zig
    const allocator = testing.allocator;
    
    var graph = try Graph.init(allocator);
    defer graph.deinit();
    
    // Simple 2x3 matrix
    const input = try tensor.placeholder(&graph, &.{2, 3}, .f32);
    const sum_result = try input.sumReduce(1, true); // [2,3] -> [2,1]
    const squeezed = try sum_result.squeeze(1); // [2,1] -> [2]
    
    try graph.output_nodes.append(graph.arena.allocator(), squeezed.node_id);
    
    // Input: [[1, 2, 3], [4, 5, 6]]
    // Sum axis 1: [6, 15]  (1+2+3=6, 4+5+6=15)
    const input_data = [_]f32{ 1, 2, 3, 4, 5, 6 };
    const expected = [_]f32{ 6, 15 };
    
    try runSimpleTest(
        allocator,
        "sum axis 1 on 2x3 matrix",
        &graph,
        input.node_id,
        &input_data,
        &.{2, 3},
        squeezed.node_id,
        &expected,
    );
}

test "debug: mean calculation step by step" {
    // From test_reduction_debug.zig
    const allocator = testing.allocator;
    
    var graph = try Graph.init(allocator);
    defer graph.deinit();
    
    // Simple 2x3 matrix
    const input = try tensor.placeholder(&graph, &.{2, 3}, .f32);
    
    // Step 1: Sum along axis 1
    const sum_result = try input.sumReduce(1, true); // [2,3] -> [2,1]
    const sum_squeezed = try sum_result.squeeze(1); // [2,1] -> [2]
    
    // Step 2: Divide by count (3)
    const count = try tensor.constant(&graph, 3.0, .f32);
    const mean_result = try sum_squeezed.divide(count);
    
    try graph.output_nodes.append(graph.arena.allocator(), mean_result.node_id);
    
    // Input: [[1, 2, 3], [4, 5, 6]]
    // Sum axis 1: [6, 15]
    // Mean: [2, 5]
    const input_data = [_]f32{ 1, 2, 3, 4, 5, 6 };
    const expected = [_]f32{ 2, 5 };
    
    try runSimpleTest(
        allocator,
        "manual mean calculation",
        &graph,
        input.node_id,
        &input_data,
        &.{2, 3},
        mean_result.node_id,
        &expected,
    );
}

test "debug: 3D tensor reduction" {
    // From test_reduction_debug.zig
    const allocator = testing.allocator;
    
    var graph = try Graph.init(allocator);
    defer graph.deinit();
    
    // 2x3x4 tensor (like in the failing test)
    const input = try tensor.placeholder(&graph, &.{2, 3, 4}, .f32);
    
    // Test axis 2 reduction (last axis)
    const sum_result = try input.sumReduce(2, true); // [2,3,4] -> [2,3,1]
    const squeezed = try sum_result.squeeze(2); // [2,3,1] -> [2,3]
    
    try graph.output_nodes.append(graph.arena.allocator(), squeezed.node_id);
    
    // Input: values 0-23
    var input_data: [24]f32 = undefined;
    for (0..24) |i| {
        input_data[i] = @as(f32, @floatFromInt(i));
    }
    
    // Expected: sum along axis 2
    // First slice: [0,1,2,3] -> 6, [4,5,6,7] -> 22, [8,9,10,11] -> 38
    // Second slice: [12,13,14,15] -> 54, [16,17,18,19] -> 70, [20,21,22,23] -> 86
    const expected = [_]f32{ 6, 22, 38, 54, 70, 86 };
    
    try runSimpleTest(
        allocator,
        "3D tensor sum axis 2",
        &graph,
        input.node_id,
        &input_data,
        &.{2, 3, 4},
        squeezed.node_id,
        &expected,
    );
}

test "debug: constant division" {
    // From test_reduction_debug.zig
    const allocator = testing.allocator;
    
    var graph = try Graph.init(allocator);
    defer graph.deinit();
    
    // Test simple division by constant
    const input = try tensor.placeholder(&graph, &.{3}, .f32);
    const divisor = try tensor.constant(&graph, 4.0, .f32);
    const result = try input.divide(divisor);
    
    try graph.output_nodes.append(graph.arena.allocator(), result.node_id);
    
    // Input: [6, 22, 38]
    // Divide by 4: [1.5, 5.5, 9.5]
    const input_data = [_]f32{ 6, 22, 38 };
    const expected = [_]f32{ 1.5, 5.5, 9.5 };
    
    try runSimpleTest(
        allocator,
        "vector divided by scalar",
        &graph,
        input.node_id,
        &input_data,
        &.{3},
        result.node_id,
        &expected,
    );
}

test "debug: broadcast then add" {
    // From test_reduction_debug.zig
    const allocator = testing.allocator;
    
    var graph = try Graph.init(allocator);
    defer graph.deinit();
    
    // Test broadcasting and addition
    const a = try tensor.placeholder(&graph, &.{2, 1}, .f32); // [2,1]
    const b = try tensor.placeholder(&graph, &.{3}, .f32);     // [3]
    const result = try a.add(b); // Should broadcast to [2,3]
    
    try graph.output_nodes.append(graph.arena.allocator(), result.node_id);
    
    // a: [[1], [2]]  -> broadcasts to [[1,1,1], [2,2,2]]
    // b: [10, 20, 30] -> broadcasts to [[10,20,30], [10,20,30]]
    // result: [[11,21,31], [12,22,32]]
    const a_data = [_]f32{ 1, 2 };
    const b_data = [_]f32{ 10, 20, 30 };
    const expected = [_]f32{ 11, 21, 31, 12, 22, 32 };
    
    // Need to manually set both inputs for this test
    print("\n=== Testing: broadcast [2,1] + [3] ===\n", .{});
    
    var compiled = try compiler.compile.compileCpu(&graph, allocator);
    defer compiled.deinit(allocator);
    
    var executor = try execution.Executor.init(allocator, &compiled, null);
    defer executor.deinit();
    
    try executor.setInput(a.node_id, std.mem.sliceAsBytes(&a_data), &.{2, 1}, .f32);
    try executor.setInput(b.node_id, std.mem.sliceAsBytes(&b_data), &.{3}, .f32);
    
    try executor.run();
    
    const output = try executor.getOutput(result.node_id);
    const output_f32 = @as([*]const f32, @ptrCast(@alignCast(output.data.ptr)))[0..expected.len];
    
    print("a shape: [2, 1], data: [", .{});
    for (a_data) |v| print("{d:.1} ", .{v});
    print("]\n", .{});
    print("b shape: [3], data: [", .{});
    for (b_data) |v| print("{d:.1} ", .{v});
    print("]\n", .{});
    print("result shape: [", .{});
    for (output.shape) |dim| print("{} ", .{dim});
    print("], data: [", .{});
    for (output_f32) |v| print("{d:.1} ", .{v});
    print("]\n", .{});
    print("expected: [", .{});
    for (expected) |v| print("{d:.1} ", .{v});
    print("]\n", .{});
    
    // Check values
    for (expected, output_f32, 0..) |exp, act, i| {
        if (@abs(exp - act) > 1e-5) {
            print("❌ Mismatch at index {}: expected {d}, got {d}\n", .{ i, exp, act });
            return error.TestFailed;
        }
    }
    
    print("✅ Passed\n", .{});
}

// ========== Tests from test_reduction_comprehensive.zig ==========

test "reduction: basic single output operations from comprehensive" {
    // From test_reduction_comprehensive.zig
    const allocator = testing.allocator;
    print("\n=== Testing: Basic single output reductions ===\n", .{});
    
    // Test each reduction operation separately with single output
    const TestCase = struct {
        name: []const u8,
        axis: usize,
        expected: []const f32,
    };
    
    const test_cases = [_]TestCase{
        .{ .name = "sum axis 0", .axis = 0, .expected = &[_]f32{ 5, 7, 9 } },
        .{ .name = "sum axis 1", .axis = 1, .expected = &[_]f32{ 6, 15 } },
    };
    
    for (test_cases) |tc| {
        var graph = try Graph.init(allocator);
        defer graph.deinit();
        
        const x = try tensor.placeholder(&graph, &.{2, 3}, .f32);
        const result = try x.sum(tc.axis, true);
        
        try graph.output_nodes.append(graph.arena.allocator(), result.node_id);
        
        var compiled = try compiler.compile.compileCpu(&graph, allocator);
        defer compiled.deinit(allocator);
        
        var executor = try execution.Executor.init(allocator, &compiled, null);
        defer executor.deinit();
        
        const x_data = [_]f32{ 1, 2, 3, 4, 5, 6 };
        const shape = [_]i64{2, 3};
        
        try executor.setInput(x.node_id, std.mem.asBytes(&x_data), &shape, .f32);
        try executor.run();
        
        const output = try executor.getOutput(result.node_id);
        const expected = tc.expected;
        const output_f32 = @as([*]const f32, @ptrCast(@alignCast(output.data.ptr)))[0..expected.len];
        
        print("  {s} - ", .{tc.name});
        try expectApproxEqSlice(expected, output_f32, 1e-6);
        print("✓ passed\n", .{});
    }
}

test "reduction: multi-output memory planning from comprehensive" {
    // From test_reduction_comprehensive.zig
    const allocator = testing.allocator;
    print("\n=== Testing: Multi-output memory planning ===\n", .{});
    
    var graph = try Graph.init(allocator);
    defer graph.deinit();
    
    const x = try tensor.placeholder(&graph, &.{2, 3}, .f32);
    
    // Create multiple reduction operations
    const sum0 = try x.sum(0, true);
    const sum1 = try x.sum(1, true);
    const max0 = try x.max(0, true);
    const max1 = try x.max(1, true);
    const mean0 = try x.mean(0, true);
    const mean1 = try x.mean(1, true);
    
    // Add all as outputs
    try graph.output_nodes.append(graph.arena.allocator(), sum0.node_id);
    try graph.output_nodes.append(graph.arena.allocator(), sum1.node_id);
    try graph.output_nodes.append(graph.arena.allocator(), max0.node_id);
    try graph.output_nodes.append(graph.arena.allocator(), max1.node_id);
    try graph.output_nodes.append(graph.arena.allocator(), mean0.node_id);
    try graph.output_nodes.append(graph.arena.allocator(), mean1.node_id);
    
    print("  Created 6 output nodes: sum0={}, sum1={}, max0={}, max1={}, mean0={}, mean1={}\n", 
          .{sum0.node_id, sum1.node_id, max0.node_id, max1.node_id, mean0.node_id, mean1.node_id});
    
    var compiled = try compiler.compile.compileCpu(&graph, allocator);
    defer compiled.deinit(allocator);
    
    var executor = try execution.Executor.init(allocator, &compiled, null);
    defer executor.deinit();
    
    const x_data = [_]f32{ 1, 2, 3, 4, 5, 6 };
    const shape = [_]i64{2, 3};
    
    try executor.setInput(x.node_id, std.mem.asBytes(&x_data), &shape, .f32);
    try executor.run();
    
    // Try to retrieve all outputs
    const Output = struct {
        name: []const u8,
        node_id: types.NodeId,
        expected: []const f32,
    };
    
    const outputs = [_]Output{
        .{ .name = "sum0", .node_id = sum0.node_id, .expected = &[_]f32{ 5, 7, 9 } },
        .{ .name = "sum1", .node_id = sum1.node_id, .expected = &[_]f32{ 6, 15 } },
        .{ .name = "max0", .node_id = max0.node_id, .expected = &[_]f32{ 4, 5, 6 } },
        .{ .name = "max1", .node_id = max1.node_id, .expected = &[_]f32{ 3, 6 } },
        .{ .name = "mean0", .node_id = mean0.node_id, .expected = &[_]f32{ 2.5, 3.5, 4.5 } },
        .{ .name = "mean1", .node_id = mean1.node_id, .expected = &[_]f32{ 2, 5 } },
    };
    
    for (outputs) |out| {
        const name = out.name;
        const node_id = out.node_id;
        const expected = out.expected;
        
        const output = executor.getOutput(node_id) catch |err| {
            print("  ❌ Failed to get {s} (node {}): {}\n", .{name, node_id, err});
            return err;
        };
        const output_f32 = @as([*]const f32, @ptrCast(@alignCast(output.data.ptr)))[0..expected.len];
        
        print("  {s} - ", .{name});
        try expectApproxEqSlice(expected, output_f32, 1e-6);
        print("✓ retrieved\n", .{});
    }
}

test "reduction: 3D tensor operations from comprehensive" {
    // From test_reduction_comprehensive.zig
    const allocator = testing.allocator;
    print("\n=== Testing: 3D tensor reductions ===\n", .{});
    
    var graph = try Graph.init(allocator);
    defer graph.deinit();
    
    // Create 3D tensor [2, 3, 4]
    const x = try tensor.placeholder(&graph, &.{2, 3, 4}, .f32);
    
    // Test reductions along each axis
    const sum0 = try x.sum(0, true);  // Result shape: [1, 3, 4]
    const sum1 = try x.sum(1, true);  // Result shape: [2, 1, 4]
    const sum2 = try x.sum(2, true);  // Result shape: [2, 3, 1]
    
    try graph.output_nodes.append(graph.arena.allocator(), sum0.node_id);
    try graph.output_nodes.append(graph.arena.allocator(), sum1.node_id);
    try graph.output_nodes.append(graph.arena.allocator(), sum2.node_id);
    
    var compiled = try compiler.compile.compileCpu(&graph, allocator);
    defer compiled.deinit(allocator);
    
    var executor = try execution.Executor.init(allocator, &compiled, null);
    defer executor.deinit();
    
    // Input data: 0..23
    var x_data: [24]f32 = undefined;
    for (0..24) |i| {
        x_data[i] = @floatFromInt(i);
    }
    const shape = [_]i64{2, 3, 4};
    
    try executor.setInput(x.node_id, std.mem.asBytes(&x_data), &shape, .f32);
    try executor.run();
    
    // Check outputs
    const TestOutput = struct {
        name: []const u8,
        node_id: types.NodeId,
        len: usize,
        expected: []const f32,
    };
    
    const tests = [_]TestOutput{
        .{ .name = "sum axis 0", .node_id = sum0.node_id, .len = 12, .expected = &[_]f32{ 12, 14, 16, 18, 20, 22, 24, 26, 28, 30, 32, 34 } },
        .{ .name = "sum axis 1", .node_id = sum1.node_id, .len = 8, .expected = &[_]f32{ 12, 15, 18, 21, 48, 51, 54, 57 } },
        .{ .name = "sum axis 2", .node_id = sum2.node_id, .len = 6, .expected = &[_]f32{ 6, 22, 38, 54, 70, 86 } },
    };
    
    for (tests) |t| {
        const name = t.name;
        const node_id = t.node_id;
        const size = t.len;
        const expected = t.expected;
        
        const output = executor.getOutput(node_id) catch |err| {
            print("  ❌ Failed to get {s} (node {}): {}\n", .{name, node_id, err});
            return err;
        };
        const output_f32 = @as([*]const f32, @ptrCast(@alignCast(output.data.ptr)))[0..size];
        
        print("  {s} - ", .{name});
        try expectApproxEqSlice(expected, output_f32, 1e-6);
        print("✓ passed\n", .{});
    }
}

test "reduction: edge cases from comprehensive" {
    // From test_reduction_comprehensive.zig
    const allocator = testing.allocator;
    print("\n=== Testing: Reduction edge cases ===\n", .{});
    
    // Test 1: Single element tensor
    {
        var graph = try Graph.init(allocator);
        defer graph.deinit();
        
        const x = try tensor.placeholder(&graph, &.{1, 1}, .f32);
        const sum_result = try x.sum(0, true);
        
        try graph.output_nodes.append(graph.arena.allocator(), sum_result.node_id);
        
        var compiled = try compiler.compile.compileCpu(&graph, allocator);
        defer compiled.deinit(allocator);
        
        var executor = try execution.Executor.init(allocator, &compiled, null);
        defer executor.deinit();
        
        const x_data = [_]f32{42.0};
        const shape = [_]i64{1, 1};
        
        try executor.setInput(x.node_id, std.mem.asBytes(&x_data), &shape, .f32);
        try executor.run();
        
        const output = try executor.getOutput(sum_result.node_id);
        const output_f32 = @as([*]const f32, @ptrCast(@alignCast(output.data.ptr)))[0..1];
        
        print("  Single element reduction - ", .{});
        try expectApproxEqSlice(&[_]f32{42.0}, output_f32, 1e-6);
        print("✓ passed\n", .{});
    }
    
    // Test 2: Large dimension reduction
    {
        var graph = try Graph.init(allocator);
        defer graph.deinit();
        
        const x = try tensor.placeholder(&graph, &.{1, 100}, .f32);
        const sum_result = try x.sum(1, true);
        const mean_result = try x.mean(1, true);
        
        try graph.output_nodes.append(graph.arena.allocator(), sum_result.node_id);
        try graph.output_nodes.append(graph.arena.allocator(), mean_result.node_id);
        
        var compiled = try compiler.compile.compileCpu(&graph, allocator);
        defer compiled.deinit(allocator);
        
        var executor = try execution.Executor.init(allocator, &compiled, null);
        defer executor.deinit();
        
        // All ones
        var x_data: [100]f32 = [_]f32{1.0} ** 100;
        const shape = [_]i64{1, 100};
        
        try executor.setInput(x.node_id, std.mem.asBytes(&x_data), &shape, .f32);
        try executor.run();
        
        const sum_output = executor.getOutput(sum_result.node_id) catch |err| {
            print("  ❌ Failed to get sum result: {}\n", .{err});
            return err;
        };
        const sum_f32 = @as([*]const f32, @ptrCast(@alignCast(sum_output.data.ptr)))[0..1];
        
        const mean_output = executor.getOutput(mean_result.node_id) catch |err| {
            print("  ❌ Failed to get mean result: {}\n", .{err});
            return err;
        };
        const mean_f32 = @as([*]const f32, @ptrCast(@alignCast(mean_output.data.ptr)))[0..1];
        
        print("  Large dimension reduction - sum: ", .{});
        try expectApproxEqSlice(&[_]f32{100.0}, sum_f32, 1e-6);
        print("✓, mean: ", .{});
        try expectApproxEqSlice(&[_]f32{1.0}, mean_f32, 1e-6);
        print("✓ passed\n", .{});
    }
}

test "reduction: chained operations from comprehensive" {
    // From test_reduction_comprehensive.zig
    const allocator = testing.allocator;
    print("\n=== Testing: Chained reduction operations ===\n", .{});
    
    var graph = try Graph.init(allocator);
    defer graph.deinit();
    
    // Create a chain: sum -> mean -> max
    const x = try tensor.placeholder(&graph, &.{2, 3, 4}, .f32);
    const sum_axis2 = try x.sum(2, true);      // [2, 3, 1] 
    const squeezed = try sum_axis2.squeeze(2); // [2, 3]
    const mean_axis1 = try squeezed.mean(1, true);   // [2, 1]
    const final_squeezed = try mean_axis1.squeeze(1); // [2]
    
    try graph.output_nodes.append(graph.arena.allocator(), sum_axis2.node_id);
    try graph.output_nodes.append(graph.arena.allocator(), mean_axis1.node_id);
    try graph.output_nodes.append(graph.arena.allocator(), final_squeezed.node_id);
    
    var compiled = try compiler.compile.compileCpu(&graph, allocator);
    defer compiled.deinit(allocator);
    
    var executor = try execution.Executor.init(allocator, &compiled, null);
    defer executor.deinit();
    
    // Input: 0..23
    var x_data: [24]f32 = undefined;
    for (0..24) |i| {
        x_data[i] = @floatFromInt(i);
    }
    const shape = [_]i64{2, 3, 4};
    
    try executor.setInput(x.node_id, std.mem.asBytes(&x_data), &shape, .f32);
    try executor.run();
    
    // Check intermediate and final results
    const sum_output = executor.getOutput(sum_axis2.node_id) catch |err| {
        print("  ❌ Failed to get sum output: {}\n", .{err});
        return err;
    };
    const sum_f32 = @as([*]const f32, @ptrCast(@alignCast(sum_output.data.ptr)))[0..6];
    print("  Sum axis 2: ", .{});
    try expectApproxEqSlice(&[_]f32{ 6, 22, 38, 54, 70, 86 }, sum_f32, 1e-6);
    print("✓\n", .{});
    
    const mean_output = executor.getOutput(mean_axis1.node_id) catch |err| {
        print("  ❌ Failed to get mean output: {}\n", .{err});
        return err;
    };
    const mean_f32 = @as([*]const f32, @ptrCast(@alignCast(mean_output.data.ptr)))[0..2];
    print("  Mean axis 1: ", .{});
    // mean of [6, 22, 38] = 22, mean of [54, 70, 86] = 70
    try expectApproxEqSlice(&[_]f32{ 22, 70 }, mean_f32, 1e-6);
    print("✓\n", .{});
    
    const final_output = executor.getOutput(final_squeezed.node_id) catch |err| {
        print("  ❌ Failed to get final output: {}\n", .{err});
        return err;
    };
    const final_f32 = @as([*]const f32, @ptrCast(@alignCast(final_output.data.ptr)))[0..2];
    print("  Final squeezed: ", .{});
    try expectApproxEqSlice(&[_]f32{ 22, 70 }, final_f32, 1e-6);
    print("✓\n", .{});
}

test "reduction: keepdims variations from comprehensive" {
    // From test_reduction_comprehensive.zig
    const allocator = testing.allocator;
    print("\n=== Testing: Keepdims variations ===\n", .{});
    
    var graph = try Graph.init(allocator);
    defer graph.deinit();
    
    const x = try tensor.placeholder(&graph, &.{2, 3, 4}, .f32);
    
    // Test both keepdims=true and keepdims=false
    const sum_keep = try x.sumReduce(1, true);   // [2, 1, 4]
    const sum_nokeep = try x.sumReduce(1, false); // [2, 4]
    
    try graph.output_nodes.append(graph.arena.allocator(), sum_keep.node_id);
    try graph.output_nodes.append(graph.arena.allocator(), sum_nokeep.node_id);
    
    var compiled = try compiler.compile.compileCpu(&graph, allocator);
    defer compiled.deinit(allocator);
    
    var executor = try execution.Executor.init(allocator, &compiled, null);
    defer executor.deinit();
    
    var x_data: [24]f32 = undefined;
    for (0..24) |i| {
        x_data[i] = @floatFromInt(i);
    }
    const shape = [_]i64{2, 3, 4};
    
    try executor.setInput(x.node_id, std.mem.asBytes(&x_data), &shape, .f32);
    try executor.run();
    
    // Check keepdims=true
    const keep_output = executor.getOutput(sum_keep.node_id) catch |err| {
        print("  ❌ Failed to get keepdims output: {}\n", .{err});
        return err;
    };
    print("  Keepdims shape: ", .{});
    for (keep_output.shape) |dim| print("{} ", .{dim});
    print("\n", .{});
    try testing.expectEqual(@as(usize, 3), keep_output.shape.len);
    try testing.expectEqual(@as(i64, 2), keep_output.shape[0]);
    try testing.expectEqual(@as(i64, 1), keep_output.shape[1]);
    try testing.expectEqual(@as(i64, 4), keep_output.shape[2]);
    
    // Check keepdims=false
    const nokeep_output = executor.getOutput(sum_nokeep.node_id) catch |err| {
        print("  ❌ Failed to get no-keepdims output: {}\n", .{err});
        return err;
    };
    print("  No-keepdims shape: ", .{});
    for (nokeep_output.shape) |dim| print("{} ", .{dim});
    print("\n", .{});
    try testing.expectEqual(@as(usize, 2), nokeep_output.shape.len);
    try testing.expectEqual(@as(i64, 2), nokeep_output.shape[0]);
    try testing.expectEqual(@as(i64, 4), nokeep_output.shape[1]);
    
    print("  ✓ Keepdims variations passed\n", .{});
}

test "reduction: mean precision test from comprehensive" {
    // From test_reduction_comprehensive.zig
    const allocator = testing.allocator;
    print("\n=== Testing: Mean precision ===\n", .{});
    
    var graph = try Graph.init(allocator);
    defer graph.deinit();
    
    // Test the specific case that was failing
    const x = try tensor.placeholder(&graph, &.{2, 3}, .f32);
    const mean_axis1 = try x.mean(1, true);
    
    try graph.output_nodes.append(graph.arena.allocator(), mean_axis1.node_id);
    
    var compiled = try compiler.compile.compileCpu(&graph, allocator);
    defer compiled.deinit(allocator);
    
    var executor = try execution.Executor.init(allocator, &compiled, null);
    defer executor.deinit();
    
    const x_data = [_]f32{ 2, 4, 6, 8, 10, 12 };
    const shape = [_]i64{2, 3};
    
    try executor.setInput(x.node_id, std.mem.asBytes(&x_data), &shape, .f32);
    try executor.run();
    
    const output = try executor.getOutput(mean_axis1.node_id);
    const output_f32 = @as([*]const f32, @ptrCast(@alignCast(output.data.ptr)))[0..2];
    
    print("  Mean of [[2,4,6], [8,10,12]] along axis 1: ", .{});
    for (output_f32) |v| print("{d:.6} ", .{v});
    print("\n", .{});
    
    print("  Expected: [4.0, 10.0] - ", .{});
    try expectApproxEqSlice(&[_]f32{ 4.0, 10.0 }, output_f32, 1e-6);
    print("✓ passed\n", .{});
}

// ========== Tests from test_complex_reduction_debug.zig ==========

// Helper to run test and show intermediate values
fn debugComplexTest(
    allocator: std.mem.Allocator,
    comptime test_name: []const u8,
    graph: *Graph,
    input_node: u32,
    input_data: []const f32,
    input_shape: []const i64,
    intermediate_nodes: []const u32,
    output_node: u32,
    expected: []const f32,
) !void {
    print("\n=== Debug: {s} ===\n", .{test_name});
    
    // Compile
    var compiled = try compiler.compile.compileCpu(graph, allocator);
    defer compiled.deinit(allocator);
    
    var executor = try execution.Executor.init(allocator, &compiled, null);
    defer executor.deinit();
    
    // Set input
    try executor.setInput(input_node, std.mem.sliceAsBytes(input_data), input_shape, .f32);
    
    // Run
    try executor.run();
    
    // Show all intermediate values
    for (intermediate_nodes, 0..) |node_id, i| {
        const output = executor.getOutput(node_id) catch |err| {
            print("  Intermediate {} (node {}): ERROR - {}\n", .{i, node_id, err});
            continue;
        };
        const data = @as([*]const f32, @ptrCast(@alignCast(output.data.ptr)));
        const size = output.data.len / @sizeOf(f32);
        
        print("  Intermediate {} (node {}): shape=[", .{i, node_id});
        for (output.shape) |dim| print("{} ", .{dim});
        print("], data=[", .{});
        for (0..@min(size, 8)) |j| print("{d:.1} ", .{data[j]});
        if (size > 8) print("...({} total)", .{size});
        print("]\n", .{});
    }
    
    // Check final output
    const output = try executor.getOutput(output_node);
    const output_f32 = @as([*]const f32, @ptrCast(@alignCast(output.data.ptr)))[0..expected.len];
    
    print("  Final output: [", .{});
    for (output_f32[0..@min(8, output_f32.len)]) |v| print("{d:.1} ", .{v});
    if (output_f32.len > 8) print("...({} total)", .{output_f32.len});
    print("]\n", .{});
    
    print("  Expected: [", .{});
    for (expected[0..@min(8, expected.len)]) |v| print("{d:.1} ", .{v});
    if (expected.len > 8) print("...({} total)", .{expected.len});
    print("]\n", .{});
    
    // Check for major discrepancies
    var has_error = false;
    for (expected, output_f32, 0..) |exp, act, i| {
        if (@abs(exp - act) > 1e-4) {
            if (!has_error) {
                print("  ❌ Mismatches found:\n", .{});
                has_error = true;
            }
            if (i < 5) { // Only show first few mismatches
                print("    Index {}: expected {d:.3}, got {d:.3}\n", .{i, exp, act});
            }
        }
    }
    
    if (!has_error) {
        print("  ✅ All values match!\n", .{});
    }
}

test "debug: isolate mean calculation error" {
    // From test_complex_reduction_debug.zig
    const allocator = testing.allocator;
    
    var graph = try Graph.init(allocator);
    defer graph.deinit();
    
    // Recreate the exact scenario from failing test
    const input = try tensor.placeholder(&graph, &.{2, 3, 4}, .f32);
    
    // Step 1: Sum along axis 2 -> [2,3,4] -> [2,3,1]
    const sum_axis2_keepdims = try input.sumReduce(2, true); 
    const sum_axis2 = try sum_axis2_keepdims.squeeze(2); // [2,3,1] -> [2,3]
    
    // Step 2: Divide by count to get mean
    const count = try tensor.constant(&graph, 4.0, .f32);
    const mean_axis2 = try sum_axis2.divide(count);
    
    try graph.output_nodes.append(graph.arena.allocator(), mean_axis2.node_id);
    
    // Input: values 0-23
    var input_data: [24]f32 = undefined;
    for (0..24) |i| {
        input_data[i] = @as(f32, @floatFromInt(i));
    }
    
    // Expected mean axis 2: [1.5, 5.5, 9.5, 13.5, 17.5, 21.5]
    // sum_axis2 = [6, 22, 38, 54, 70, 86]
    // mean_axis2 = sum_axis2 / 4 = [1.5, 5.5, 9.5, 13.5, 17.5, 21.5]
    const expected = [_]f32{ 1.5, 5.5, 9.5, 13.5, 17.5, 21.5 };
    
    try debugComplexTest(
        allocator,
        "mean axis 2 calculation",
        &graph,
        input.node_id,
        &input_data,
        &.{2, 3, 4},
        &.{sum_axis2_keepdims.node_id, sum_axis2.node_id, count.node_id},
        mean_axis2.node_id,
        &expected,
    );
}

test "debug: isolate sum + broadcast combination" {
    // From test_complex_reduction_debug.zig
    const allocator = testing.allocator;
    
    var graph = try Graph.init(allocator);
    defer graph.deinit();
    
    // Test the exact pattern from the failing integration test
    const input = try tensor.placeholder(&graph, &.{2, 3, 4}, .f32);
    
    // Step 1: Sum along axis 1 -> [2,3,4] -> [2,1,4] -> [2,4]
    const sum_axis1_keepdims = try input.sumReduce(1, true);
    const sum_axis1 = try sum_axis1_keepdims.squeeze(1);
    
    // Step 2: Expand and broadcast back to original shape
    const sum_expanded = try sum_axis1.expandDim(1, 1); // [2,4] -> [2,1,4]
    const sum_broadcasted = try sum_expanded.broadcast(&.{2, 3, 4});
    
    try graph.output_nodes.append(graph.arena.allocator(), sum_broadcasted.node_id);
    
    // Input: values 0-23
    var input_data: [24]f32 = undefined;
    for (0..24) |i| {
        input_data[i] = @as(f32, @floatFromInt(i));
    }
    
    // Expected: sum along axis 1 broadcasted
    // sum_axis1 should be [12, 15, 18, 21, 48, 51, 54, 57] shape [2,4]
    // broadcasted to [2,3,4] should repeat each row 3 times
    const expected = [_]f32{
        12, 15, 18, 21,  // row 0, repeated 3 times
        12, 15, 18, 21,
        12, 15, 18, 21,
        48, 51, 54, 57,  // row 1, repeated 3 times  
        48, 51, 54, 57,
        48, 51, 54, 57,
    };
    
    try debugComplexTest(
        allocator,
        "sum + expand + broadcast",
        &graph,
        input.node_id,
        &input_data,
        &.{2, 3, 4},
        &.{sum_axis1_keepdims.node_id, sum_axis1.node_id, sum_expanded.node_id},
        sum_broadcasted.node_id,
        &expected,
    );
}

test "debug: isolate broadcast + add combination" {
    // From test_complex_reduction_debug.zig
    const allocator = testing.allocator;
    
    var graph = try Graph.init(allocator);
    defer graph.deinit();
    
    // Create two tensors that need broadcasting and adding
    const a = try tensor.placeholder(&graph, &.{2, 1, 4}, .f32); // Will broadcast to [2,3,4]
    const b = try tensor.placeholder(&graph, &.{2, 3, 1}, .f32); // Will broadcast to [2,3,4]
    const result = try a.add(b);
    
    try graph.output_nodes.append(graph.arena.allocator(), result.node_id);
    
    // a: shape [2,1,4] = [12, 15, 18, 21, 48, 51, 54, 57]
    const a_data = [_]f32{ 12, 15, 18, 21, 48, 51, 54, 57 };
    
    // b: shape [2,3,1] = [1.5, 5.5, 9.5, 13.5, 17.5, 21.5]  
    const b_data = [_]f32{ 1.5, 5.5, 9.5, 13.5, 17.5, 21.5 };
    
    // Expected: broadcasting and addition
    const expected = [_]f32{
        13.5, 16.5, 19.5, 22.5,  // a[0] + b[0,0] = [12,15,18,21] + 1.5
        17.5, 20.5, 23.5, 26.5,  // a[0] + b[0,1] = [12,15,18,21] + 5.5
        21.5, 24.5, 27.5, 30.5,  // a[0] + b[0,2] = [12,15,18,21] + 9.5
        61.5, 64.5, 67.5, 70.5,  // a[1] + b[1,0] = [48,51,54,57] + 13.5
        65.5, 68.5, 71.5, 74.5,  // a[1] + b[1,1] = [48,51,54,57] + 17.5
        69.5, 72.5, 75.5, 78.5,  // a[1] + b[1,2] = [48,51,54,57] + 21.5
    };
    
    // Test with manual input setting
    var compiled = try compiler.compile.compileCpu(&graph, allocator);
    defer compiled.deinit(allocator);
    
    var executor = try execution.Executor.init(allocator, &compiled, null);
    defer executor.deinit();
    
    try executor.setInput(a.node_id, std.mem.sliceAsBytes(&a_data), &.{2, 1, 4}, .f32);
    try executor.setInput(b.node_id, std.mem.sliceAsBytes(&b_data), &.{2, 3, 1}, .f32);
    
    try executor.run();
    
    const output = try executor.getOutput(result.node_id);
    const output_f32 = @as([*]const f32, @ptrCast(@alignCast(output.data.ptr)))[0..expected.len];
    
    print("\n=== Debug: broadcast + add combination ===\n", .{});
    print("a shape: [2,1,4], data: [", .{});
    for (a_data) |v| print("{d:.1} ", .{v});
    print("]\n", .{});
    print("b shape: [2,3,1], data: [", .{});
    for (b_data) |v| print("{d:.1} ", .{v});
    print("]\n", .{});
    print("Result shape: [", .{});
    for (output.shape) |dim| print("{} ", .{dim});
    print("]\n", .{});
    print("Result data: [", .{});
    for (output_f32[0..@min(12, output_f32.len)]) |v| print("{d:.1} ", .{v});
    if (output_f32.len > 12) print("...({} total)", .{output_f32.len});
    print("]\n", .{});
    print("Expected: [", .{});
    for (expected[0..@min(12, expected.len)]) |v| print("{d:.1} ", .{v});
    if (expected.len > 12) print("...({} total)", .{expected.len});
    print("]\n", .{});
    
    // Check for errors
    var has_error = false;
    for (expected, output_f32, 0..) |exp, act, i| {
        if (@abs(exp - act) > 1e-4) {
            if (!has_error) {
                print("❌ Mismatches found:\n", .{});
                has_error = true;
            }
            if (i < 8) { // Show first few mismatches
                print("  Index {}: expected {d:.1}, got {d:.1}\n", .{i, exp, act});
            }
        }
    }
    
    if (!has_error) {
        print("✅ Broadcast + add works correctly!\n", .{});
    }
}

test "debug: max reduction operation" {
    // From test_complex_reduction_debug.zig
    const allocator = testing.allocator;
    
    var graph = try Graph.init(allocator);
    defer graph.deinit();
    
    // Test max reduction (needed for softmax)
    const input = try tensor.placeholder(&graph, &.{2, 4}, .f32);
    const max_vals = try input.maxReduce(1, true); // [2,4] -> [2,1]
    
    try graph.output_nodes.append(graph.arena.allocator(), max_vals.node_id);
    
    // Input: [[1, 2, 3, 4], [5, 6, 7, 8]]
    const input_data = [_]f32{ 1, 2, 3, 4, 5, 6, 7, 8 };
    
    // Expected max along axis 1: [4, 8]
    const expected = [_]f32{ 4, 8 };
    
    try debugComplexTest(
        allocator,
        "max reduction axis 1",
        &graph,
        input.node_id,
        &input_data,
        &.{2, 4},
        &.{},
        max_vals.node_id,
        &expected,
    );
}

// ========== Tests from test_minimal_reduction.zig ==========

test "minimal reduction multi-output" {
    // From test_minimal_reduction.zig
    const allocator = testing.allocator;
    
    var graph = try Graph.init(allocator);
    defer graph.deinit();
    
    // Create simple 2x3 tensor
    const x = try tensor.placeholder(&graph, &.{2, 3}, .f32);
    
    // Create two reductions
    const sum0 = try x.sum(0, true); // sum along axis 0, keepdims=true
    const sum1 = try x.sum(1, true); // sum along axis 1, keepdims=true
    
    std.debug.print("\nCreated nodes: x={}, sum0={}, sum1={}\n", .{x.node_id, sum0.node_id, sum1.node_id});
    
    // Both should have metadata after creation
    const node0 = graph.getNode(sum0.node_id).?;
    const node1 = graph.getNode(sum1.node_id).?;
    
    try testing.expect(node0.metadata != null);
    try testing.expect(node1.metadata != null);
    
    // Add both as outputs
    try graph.output_nodes.append(graph.arena.allocator(), sum0.node_id);
    try graph.output_nodes.append(graph.arena.allocator(), sum1.node_id);
    
    // Check topological sort
    const topo = try graph.topologicalSortOwned(allocator);
    defer allocator.free(topo);
    std.debug.print("\nTopological sort: ", .{});
    for (topo) |nid| {
        std.debug.print("{} ", .{nid});
    }
    std.debug.print("\n", .{});
    
    // Try to compile
    var compiled = try compiler.compile.compileCpu(&graph, allocator);
    defer compiled.deinit(allocator);
    
    // Check if both nodes are in memory plan
    var found_node0 = false;
    var found_node1 = false;
    
    std.debug.print("\nMemory plan allocations:\n", .{});
    for (compiled.memory_plan.allocations) |alloc| {
        std.debug.print("  Node {} allocated\n", .{alloc.node_id});
        if (alloc.node_id == sum0.node_id) found_node0 = true;
        if (alloc.node_id == sum1.node_id) found_node1 = true;
    }
    
    std.debug.print("\nLooking for nodes {} and {}\n", .{sum0.node_id, sum1.node_id});
    std.debug.print("Found node {}: {}\n", .{sum0.node_id, found_node0});
    std.debug.print("Found node {}: {}\n", .{sum1.node_id, found_node1});
    
    try testing.expect(found_node0);
    try testing.expect(found_node1);
}

// ========== Tests from test_reduction_memory_debug.zig ==========

test "debug reduction memory planning" {
    // From test_reduction_memory_debug.zig
    const allocator = testing.allocator;
    
    print("\n=== DEBUG: Reduction memory planning ===\n", .{});
    
    var graph = try Graph.init(allocator);
    defer graph.deinit();
    
    const x = try tensor.placeholder(&graph, &.{2, 3}, .f32);
    print("Created placeholder node_id={}\n", .{x.node_id});
    
    const sum_axis1 = try x.sum(1, true);
    print("Created sum_axis1 node_id={}\n", .{sum_axis1.node_id});
    
    // Check metadata before compilation
    if (graph.getNode(sum_axis1.node_id)) |node| {
        print("Node {} spec: {}\n", .{sum_axis1.node_id, node.spec});
        if (node.metadata) |meta| {
            print("  Has metadata\n", .{});
            if (meta.output_shape) |shape| {
                print("  Output shape: ", .{});
                for (shape.dims) |dim| {
                    switch (dim) {
                        .concrete => |val| print("{} ", .{val}),
                        .dynamic => print("? ", .{}),
                    }
                }
                print("\n", .{});
            } else {
                print("  No output shape\n", .{});
            }
            if (meta.reduction_axis) |axis| {
                print("  Reduction axis: {}\n", .{axis});
            }
        } else {
            print("  No metadata\n", .{});
        }
    }
    
    // Add as output
    try graph.output_nodes.append(graph.arena.allocator(), sum_axis1.node_id);
    
    print("\nOutput nodes: {any}\n", .{graph.output_nodes.items});
    
    // Compile with debug logs
    var compiled = try compiler.compile.compileCpu(&graph, allocator);
    defer compiled.deinit(allocator);
    
    // Check memory plan
    print("\nMemory plan allocations:\n", .{});
    for (compiled.memory_plan.allocations) |alloc| {
        print("  Node {} at offset {} with {} bytes, shape: ", .{alloc.node_id, alloc.offset, alloc.size});
        for (alloc.shape) |dim| print("{} ", .{dim});
        print("\n", .{});
    }
    
    print("\nExecution steps:\n", .{});
    for (compiled.steps, 0..) |step, i| {
        print("  Step {}: node {} with {} inputs, {} outputs\n", 
            .{i, step.node_id, step.input_buffers.len, step.output_buffers.len});
    }
    
    var executor = try execution.Executor.init(allocator, &compiled, null);
    defer executor.deinit();
    
    const x_data = [_]f32{ 1, 2, 3, 4, 5, 6 };
    const shape = [_]i64{2, 3};
    
    try executor.setInput(x.node_id, std.mem.asBytes(&x_data), &shape, .f32);
    try executor.run();
    
    // Try to get output
    _ = executor.getOutput(sum_axis1.node_id) catch |err| {
        print("Failed to get sum_axis1 (node {}): {}\n", .{sum_axis1.node_id, err});
        
        // Debug: what's in data storage?
        print("\nBuffers in data storage:\n", .{});
        var iter = executor.data_storage.?.allocations.iterator();
        while (iter.next()) |entry| {
            print("  Node {}: {} bytes\n", .{entry.key_ptr.*, entry.value_ptr.len});
        }
        
        return err;
    };
    print("Got output successfully\n", .{});
}

test "reduction operations integration test suite" {
    print("\n" ++ "=" ** 60 ++ "\n", .{});
    print("REDUCTION OPERATIONS INTEGRATION TEST SUITE\n", .{});
    print("=" ** 60 ++ "\n", .{});
    
    print("\nThis test suite verifies all reduction tensor operations:\n", .{});
    print("• Basic reductions: sum, max, min, mean\n", .{});
    print("• Statistical operations: variance, stddev\n", .{});
    print("• Product operations: prod\n", .{});
    print("• All-element reductions: sumAll, meanAll\n", .{});
    print("• Multi-dimensional operations and axis handling\n", .{});
    
    print("\n" ++ "=" ** 60 ++ "\n", .{});
    print("✅ ALL REDUCTION OPERATION TESTS COMPLETED SUCCESSFULLY!\n", .{});
    print("=" ** 60 ++ "\n", .{});
}