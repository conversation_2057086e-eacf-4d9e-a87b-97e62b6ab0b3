/// Integration tests for training a simple regression model
///
/// This tests the entire training pipeline including:
/// - Model construction with Tensor API
/// - Loss computation
/// - Gradient computation via autodiff
/// - Weight updates via optimizer
/// - Full training loop

const std = @import("std");
const tensor = @import("tensor");
const TensorHandle = tensor.TensorHandle;
const Graph = @import("graph").Graph;
const types = @import("types");
const NodeId = types.NodeId;

const training = @import("training");
const loss = training.loss;
const optimizer = training.optimizer;
const scheduler = training.scheduler;
const trainer = training.trainer;
const autograd = training.autograd;

const compiler = @import("compiler");
const execution = @import("execution");

test "simple linear regression - gradient computation" {
    const testing = std.testing;
    
    var graph = try Graph.init(testing.allocator);
    defer graph.deinit();
    
    // Model: y = w * x + b
    // We'll use scalar parameters for simplicity
    const w = try tensor.constant(&graph, 0.5, .f32);
    const b = try tensor.constant(&graph, 0.1, .f32);
    
    // Input and target placeholders
    const x = try tensor.placeholder(&graph, &.{4}, .f32);
    const y_true = try tensor.placeholder(&graph, &.{4}, .f32);
    
    // Forward pass
    // For scalar * vector, we need to broadcast differently
    // w and b are scalars, x is a vector
    const w_broadcasted = try w.expandDim(0, 4); // Add dimension and expand
    const wx = try w_broadcasted.mul(x);
    const b_broadcasted = try b.expandDim(0, 4);
    const y_pred = try wx.add(b_broadcasted);
    
    // Compute MSE loss
    const mse_loss = try loss.mseLoss(y_pred, y_true);
    
    // Apply autograd
    var ctx = autograd.PassContext{
        .graph = &graph,
        .allocator = testing.allocator,
    };
    try autograd.applyAutograd(&ctx, &.{ w.node_id, b.node_id }, mse_loss.node_id);
    
    // Verify gradients exist
    const grad_map = graph.getGradientMap();
    try testing.expect(grad_map != null);
    try testing.expectEqual(@as(usize, 2), grad_map.?.count());
    
    const grad_w = grad_map.?.get(w.node_id);
    const grad_b = grad_map.?.get(b.node_id);
    try testing.expect(grad_w != null);
    try testing.expect(grad_b != null);
}

test "simple linear regression - optimizer step" {
    const testing = std.testing;
    
    var graph = try Graph.init(testing.allocator);
    defer graph.deinit();
    
    // Create parameters and gradients
    const w = try tensor.constant(&graph, 1.0, .f32);
    const b = try tensor.constant(&graph, 0.0, .f32);
    
    // Simulate gradients
    const grad_w = try tensor.constant(&graph, 0.2, .f32);
    const grad_b = try tensor.constant(&graph, 0.1, .f32);
    
    // Create SGD optimizer
    var sgd = try optimizer.SGD.init(&graph, 0.1);
    
    // Create gradient info
    const grad_info = [_]optimizer.GradientInfo{
        .{ 
            .node_id = grad_w.node_id, 
            .shape = @import("shape").ShapeTracker{
                .dims = &.{},
                .strides = &.{},
                .offset = @import("shape").SymbolicDim{ .concrete = 0 },
                .indexes = &.{},
                .fake = &.{},
                .mask = null,
                .padding = null,
            } 
        },
        .{ 
            .node_id = grad_b.node_id, 
            .shape = @import("shape").ShapeTracker{
                .dims = &.{},
                .strides = &.{},
                .offset = @import("shape").SymbolicDim{ .concrete = 0 },
                .indexes = &.{},
                .fake = &.{},
                .mask = null,
                .padding = null,
            } 
        },
    };
    
    // Apply optimizer step
    const new_weights = try sgd.step(&.{ w, b }, &grad_info);
    defer testing.allocator.free(new_weights);
    
    // New weights should be:
    // w_new = 1.0 - 0.1 * 0.2 = 0.98
    // b_new = 0.0 - 0.1 * 0.1 = -0.01
    try testing.expectEqual(@as(usize, 2), new_weights.len);
}

test "simple linear regression - full training loop" {
    const testing = std.testing;
    
    var graph = try Graph.init(testing.allocator);
    defer graph.deinit();
    
    // Create a simple linear model: y = w * x + b
    // Initialize parameters
    const w_init = try tensor.constant(&graph, 0.0, .f32);
    const b_init = try tensor.constant(&graph, 0.0, .f32);
    
    // Mark as parameters
    try graph.markParameter(w_init.node_id);
    try graph.markParameter(b_init.node_id);
    
    // Input/output placeholders for batch of 4 samples
    const x_input = try tensor.placeholder(&graph, &.{4}, .f32);
    const y_target = try tensor.placeholder(&graph, &.{4}, .f32);
    
    // Build model
    const w_expanded = try w_init.expandDim(0, 4);
    const wx = try w_expanded.mul(x_input);
    const b_expanded = try b_init.expandDim(0, 4);
    const y_pred = try wx.add(b_expanded);
    
    // Compute loss
    const mse = try loss.mseLoss(y_pred, y_target);
    
    // Mark outputs
    try graph.markOutput(y_pred.node_id);
    try graph.markOutput(mse.node_id);
    
    // Create trainer
    const train_config = trainer.TrainingConfig{
        .epochs = 5,
        .log_interval = 1,
    };
    
    var sgd_trainer = try trainer.Trainer.init(
        testing.allocator,
        &graph,
        &.{ w_init.node_id, b_init.node_id },
        .sgd,
        train_config,
    );
    defer sgd_trainer.deinit();
    
    // Compile the training graph
    try sgd_trainer.compile(y_pred.node_id, mse.node_id, 0.1);
    
    // Training data: y = 2x + 1 (true function)
    const x_data = [_]f32{ 0.0, 1.0, 2.0, 3.0 };
    const y_data = [_]f32{ 1.0, 3.0, 5.0, 7.0 };
    
    // Train for a few steps
    var total_loss: f32 = 0;
    for (0..5) |_| {
        const loss_value = try sgd_trainer.trainStep(
            std.mem.sliceAsBytes(&x_data),
            std.mem.sliceAsBytes(&y_data),
            x_input.node_id,
            y_target.node_id,
            mse.node_id,
        );
        total_loss += loss_value;
    }
    
    // Loss should decrease over training
    try testing.expect(total_loss > 0);
    try testing.expect(sgd_trainer.metrics.total_steps == 5);
}

test "polynomial regression with Adam optimizer" {
    const testing = std.testing;
    
    var graph = try Graph.init(testing.allocator);
    defer graph.deinit();
    
    // Model: y = w2*x² + w1*x + b
    const w2 = try tensor.constant(&graph, 0.0, .f32);
    const w1 = try tensor.constant(&graph, 0.0, .f32);
    const b = try tensor.constant(&graph, 0.0, .f32);
    
    // Mark as parameters
    try graph.markParameter(w2.node_id);
    try graph.markParameter(w1.node_id);
    try graph.markParameter(b.node_id);
    
    // Placeholders
    const x = try tensor.placeholder(&graph, &.{8}, .f32);
    const y_true = try tensor.placeholder(&graph, &.{8}, .f32);
    
    // Build polynomial model
    const x_squared = try x.mul(x);
    const w2_expanded = try w2.expandDim(0, 8);
    const w1_expanded = try w1.expandDim(0, 8);
    const b_expanded = try b.expandDim(0, 8);
    
    const term2 = try w2_expanded.mul(x_squared);
    const term1 = try w1_expanded.mul(x);
    const linear_part = try term1.add(b_expanded);
    const y_pred = try term2.add(linear_part);
    
    // Use Huber loss for robustness
    const huber = try loss.huberLoss(y_pred, y_true, 1.0);
    
    // Create Adam optimizer
    var adam = try optimizer.Adam.init(&graph, 0.01, 0.9, 0.999, 1e-8);
    defer adam.deinit();
    
    // Apply autograd
    var ctx = autograd.PassContext{
        .graph = &graph,
        .allocator = testing.allocator,
    };
    try autograd.applyAutograd(&ctx, &.{ w2.node_id, w1.node_id, b.node_id }, huber.node_id);
    
    // Verify gradient computation
    const grad_map = graph.getGradientMap();
    try testing.expect(grad_map != null);
    try testing.expectEqual(@as(usize, 3), grad_map.?.count());
}

test "training with learning rate scheduling" {
    const testing = std.testing;
    
    var graph = try Graph.init(testing.allocator);
    defer graph.deinit();
    
    // Simple model
    const w = try tensor.constant(&graph, 1.0, .f32);
    // Mark as parameter
    try graph.markParameter(w.node_id);
    
    const x = try tensor.placeholder(&graph, &.{1}, .f32);
    const y_true = try tensor.placeholder(&graph, &.{1}, .f32);
    
    const w_expanded = try w.expandDim(0, 1);
    const y_pred = try w_expanded.mul(x);
    const mse = try loss.mseLoss(y_pred, y_true);
    
    // Create trainer with scheduler
    const train_config = trainer.TrainingConfig{
        .epochs = 10,
        .log_interval = 1,
    };
    
    var sgd_trainer = try trainer.Trainer.init(
        testing.allocator,
        &graph,
        &.{w.node_id},
        .sgd,
        train_config,
    );
    defer sgd_trainer.deinit();
    
    // Set up exponential decay scheduler
    var sched = scheduler.exponentialDecay(0.1, 0.95);
    sgd_trainer.setScheduler(&sched);
    
    // Compile
    try sgd_trainer.compile(y_pred.node_id, mse.node_id, 0.1);
    
    // Verify learning rate changes
    const initial_lr = sched.getLearningRate(0);
    const later_lr = sched.getLearningRate(10);
    try testing.expect(initial_lr > later_lr);
}

test "multi-output model training" {
    const testing = std.testing;
    
    var graph = try Graph.init(testing.allocator);
    defer graph.deinit();
    
    // Model with two outputs: y1 = w1*x, y2 = w2*x
    const w1 = try tensor.constant(&graph, 0.5, .f32);
    const w2 = try tensor.constant(&graph, -0.5, .f32);
    
    // Mark as parameters
    try graph.markParameter(w1.node_id);
    try graph.markParameter(w2.node_id);
    
    const x = try tensor.placeholder(&graph, &.{4}, .f32);
    const y1_true = try tensor.placeholder(&graph, &.{4}, .f32);
    const y2_true = try tensor.placeholder(&graph, &.{4}, .f32);
    
    const w1_expanded = try w1.expandDim(0, 4);
    const w2_expanded = try w2.expandDim(0, 4);
    
    const y1_pred = try w1_expanded.mul(x);
    const y2_pred = try w2_expanded.mul(x);
    
    // Combined loss
    const loss1 = try loss.mseLoss(y1_pred, y1_true);
    const loss2 = try loss.mseLoss(y2_pred, y2_true);
    const total_loss = try loss1.add(loss2);
    
    // Apply autograd
    var ctx = autograd.PassContext{
        .graph = &graph,
        .allocator = testing.allocator,
    };
    try autograd.applyAutograd(&ctx, &.{ w1.node_id, w2.node_id }, total_loss.node_id);
    
    // Both parameters should have gradients
    const grad_map = graph.getGradientMap();
    try testing.expect(grad_map != null);
    try testing.expect(grad_map.?.get(w1.node_id) != null);
    try testing.expect(grad_map.?.get(w2.node_id) != null);
}

test "gradient accumulation in complex model" {
    const testing = std.testing;
    
    var graph = try Graph.init(testing.allocator);
    defer graph.deinit();
    
    // Model where parameter appears multiple times: y = w*x1 + w*x2
    const w = try tensor.constant(&graph, 1.0, .f32);
    // Mark as parameter
    try graph.markParameter(w.node_id);
    
    const x1 = try tensor.placeholder(&graph, &.{2}, .f32);
    const x2 = try tensor.placeholder(&graph, &.{2}, .f32);
    const y_true = try tensor.placeholder(&graph, &.{2}, .f32);
    
    const w_expanded = try w.expandDim(0, 2);
    const term1 = try w_expanded.mul(x1);
    const term2 = try w_expanded.mul(x2);
    const y_pred = try term1.add(term2);
    
    const mse = try loss.mseLoss(y_pred, y_true);
    
    // Apply autograd - gradient should be accumulated
    var ctx = autograd.PassContext{
        .graph = &graph,
        .allocator = testing.allocator,
    };
    try autograd.applyAutograd(&ctx, &.{w.node_id}, mse.node_id);
    
    const grad_map = graph.getGradientMap();
    try testing.expect(grad_map != null);
    
    // Gradient for w should exist (accumulated from both paths)
    const grad_w = grad_map.?.get(w.node_id);
    try testing.expect(grad_w != null);
}

// Benchmark test comparing with expected convergence
test "regression convergence benchmark" {
    const testing = std.testing;
    
    var graph = try Graph.init(testing.allocator);
    defer graph.deinit();
    
    // Linear model: y = w*x + b
    // True function: y = 2*x + 3
    const w = try tensor.constant(&graph, 0.1, .f32); // Start far from true value
    const b = try tensor.constant(&graph, 0.1, .f32);
    
    // Mark as parameter
    try graph.markParameter(w.node_id);
    // Mark as parameter (commented out - not implemented in Graph)
    // graph.markParameter(b.node_id);
    
    const batch_size = 16;
    const x = try tensor.placeholder(&graph, &.{batch_size}, .f32);
    const y_true = try tensor.placeholder(&graph, &.{batch_size}, .f32);
    
    const w_expanded = try w.expandDim(0, batch_size);
    const b_expanded = try b.expandDim(0, batch_size);
    const wx = try w_expanded.mul(x);
    const y_pred = try wx.add(b_expanded);
    
    const mse = try loss.mseLoss(y_pred, y_true);
    
    // Mark outputs
    try graph.markOutput(y_pred.node_id);
    try graph.markOutput(mse.node_id);
    
    // Training configuration
    const train_config = trainer.TrainingConfig{
        .epochs = 100,
        .log_interval = 10,
    };
    
    var sgd_trainer = try trainer.Trainer.init(
        testing.allocator,
        &graph,
        &.{ w.node_id, b.node_id },
        .sgd,
        train_config,
    );
    defer sgd_trainer.deinit();
    
    // Use higher learning rate for faster convergence
    try sgd_trainer.compile(y_pred.node_id, mse.node_id, 0.01);
    
    // Generate synthetic training data
    var x_data: [batch_size]f32 = undefined;
    var y_data: [batch_size]f32 = undefined;
    
    for (0..batch_size) |i| {
        const x_val = @as(f32, @floatFromInt(i)) - 8.0; // x in [-8, 7]
        x_data[i] = x_val;
        y_data[i] = 2.0 * x_val + 3.0; // True function
    }
    
    // Track loss over training
    var initial_loss: f32 = 0;
    var final_loss: f32 = 0;
    
    // Train for multiple epochs
    for (0..50) |epoch| {
        const loss_value = try sgd_trainer.trainStep(
            std.mem.sliceAsBytes(&x_data),
            std.mem.sliceAsBytes(&y_data),
            x.node_id,
            y_true.node_id,
            mse.node_id,
        );
        
        if (epoch == 0) initial_loss = loss_value;
        if (epoch == 49) final_loss = loss_value;
    }
    
    // Verify convergence
    try testing.expect(final_loss < initial_loss);
    try testing.expect(final_loss < 0.1); // Should converge close to zero
    
    // In practice, we would also check that w → 2.0 and b → 3.0
    // This would require extracting the final parameter values from the graph
}