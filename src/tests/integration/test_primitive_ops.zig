/// Integration tests for primitive tensor operations
/// Tests each primitive operation in isolation and in combination with others
/// All primitive operations: add, mul, mod, less_than, recip, sqrt, sin, exp2, log2, sum_reduce, max_reduce

const std = @import("std");
const testing = std.testing;
const print = std.debug.print;

// Import Zing components
const types = @import("types");
const Graph = @import("graph").Graph;
const tensor = @import("tensor");
const compiler = @import("compiler");
const execution = @import("execution");

// Helper to compare results with tolerance
fn expectApproxEqSlice(expected: []const f32, actual: []const f32, tolerance: f32) !void {
    try testing.expectEqual(expected.len, actual.len);
    for (expected, actual, 0..) |e, a, i| {
        if (@abs(e - a) > tolerance) {
            print("Mismatch at index {}: expected {d:.6}, got {d:.6}\n", .{ i, e, a });
            return error.ValueMismatch;
        }
    }
}

test "primitive: add operation" {
    const allocator = testing.allocator;
    
    print("\n=== Testing: add operation ===\n", .{});
    
    var graph = try Graph.init(allocator);
    defer graph.deinit();
    
    // Test 1: Simple element-wise add
    const a = try tensor.placeholder(&graph, &.{2, 3}, .f32);
    const b = try tensor.placeholder(&graph, &.{2, 3}, .f32);
    const result = try a.add(b);
    
    try graph.output_nodes.append(graph.arena.allocator(), result.node_id);
    
    var compiled = try compiler.compile.compileCpu(&graph, allocator);
    defer compiled.deinit(allocator);
    
    var executor = try execution.Executor.init(allocator, &compiled, null);
    defer executor.deinit();
    
    const a_data = [_]f32{ 1, 2, 3, 4, 5, 6 };
    const b_data = [_]f32{ 10, 20, 30, 40, 50, 60 };
    const shape = [_]i64{ 2, 3 };
    
    try executor.setInput(a.node_id, std.mem.asBytes(&a_data), &shape, .f32);
    try executor.setInput(b.node_id, std.mem.asBytes(&b_data), &shape, .f32);
    
    try executor.run();
    
    const output = try executor.getOutput(result.node_id);
    const output_f32 = @as([*]const f32, @ptrCast(@alignCast(output.data.ptr)))[0..6];
    
    const expected = [_]f32{ 11, 22, 33, 44, 55, 66 };
    try expectApproxEqSlice(&expected, output_f32, 1e-5);
    
    print("✓ Add operation test passed!\n", .{});
}

test "primitive: mul operation" {
    const allocator = testing.allocator;
    
    print("\n=== Testing: mul operation ===\n", .{});
    
    var graph = try Graph.init(allocator);
    defer graph.deinit();
    
    // Test 1: Simple element-wise multiplication
    const a = try tensor.placeholder(&graph, &.{3, 2}, .f32);
    const b = try tensor.placeholder(&graph, &.{3, 2}, .f32);
    const result = try a.mul(b);
    
    try graph.output_nodes.append(graph.arena.allocator(), result.node_id);
    
    var compiled = try compiler.compile.compileCpu(&graph, allocator);
    defer compiled.deinit(allocator);
    
    var executor = try execution.Executor.init(allocator, &compiled, null);
    defer executor.deinit();
    
    const a_data = [_]f32{ 2, 3, 4, 5, 6, 7 };
    const b_data = [_]f32{ 0.5, 2, 0.25, 3, 0.1, 4 };
    const shape = [_]i64{ 3, 2 };
    
    try executor.setInput(a.node_id, std.mem.asBytes(&a_data), &shape, .f32);
    try executor.setInput(b.node_id, std.mem.asBytes(&b_data), &shape, .f32);
    
    try executor.run();
    
    const output = try executor.getOutput(result.node_id);
    const output_f32 = @as([*]const f32, @ptrCast(@alignCast(output.data.ptr)))[0..6];
    
    const expected = [_]f32{ 1, 6, 1, 15, 0.6, 28 };
    try expectApproxEqSlice(&expected, output_f32, 1e-5);
    
    print("✓ Mul operation test passed!\n", .{});
}

test "primitive: mod operation" {
    const allocator = testing.allocator;
    
    print("\n=== Testing: mod operation ===\n", .{});
    
    var graph = try Graph.init(allocator);
    defer graph.deinit();
    
    const a = try tensor.placeholder(&graph, &.{4}, .f32);
    const b = try tensor.placeholder(&graph, &.{4}, .f32);
    const result = try a.mod(b);
    
    try graph.output_nodes.append(graph.arena.allocator(), result.node_id);
    
    var compiled = try compiler.compile.compileCpu(&graph, allocator);
    defer compiled.deinit(allocator);
    
    var executor = try execution.Executor.init(allocator, &compiled, null);
    defer executor.deinit();
    
    const a_data = [_]f32{ 10, 15, 7, 23 };
    const b_data = [_]f32{ 3, 4, 3, 5 };
    const shape = [_]i64{4};
    
    try executor.setInput(a.node_id, std.mem.asBytes(&a_data), &shape, .f32);
    try executor.setInput(b.node_id, std.mem.asBytes(&b_data), &shape, .f32);
    
    try executor.run();
    
    const output = try executor.getOutput(result.node_id);
    const output_f32 = @as([*]const f32, @ptrCast(@alignCast(output.data.ptr)))[0..4];
    
    const expected = [_]f32{ 1, 3, 1, 3 }; // 10%3=1, 15%4=3, 7%3=1, 23%5=3
    try expectApproxEqSlice(&expected, output_f32, 1e-5);
    
    print("✓ Mod operation test passed!\n", .{});
}

test "primitive: less_than operation" {
    const allocator = testing.allocator;
    
    print("\n=== Testing: less_than operation ===\n", .{});
    
    var graph = try Graph.init(allocator);
    defer graph.deinit();
    
    const a = try tensor.placeholder(&graph, &.{5}, .f32);
    const b = try tensor.placeholder(&graph, &.{5}, .f32);
    const result = try a.lessThan(b);
    
    try graph.output_nodes.append(graph.arena.allocator(), result.node_id);
    
    var compiled = try compiler.compile.compileCpu(&graph, allocator);
    defer compiled.deinit(allocator);
    
    var executor = try execution.Executor.init(allocator, &compiled, null);
    defer executor.deinit();
    
    const a_data = [_]f32{ 1, 5, 3, 7, 2 };
    const b_data = [_]f32{ 2, 4, 3, 8, 1 };
    const shape = [_]i64{5};
    
    try executor.setInput(a.node_id, std.mem.asBytes(&a_data), &shape, .f32);
    try executor.setInput(b.node_id, std.mem.asBytes(&b_data), &shape, .f32);
    
    try executor.run();
    
    const output = try executor.getOutput(result.node_id);
    const output_f32 = @as([*]const f32, @ptrCast(@alignCast(output.data.ptr)))[0..5];
    
    // lessThan returns f32 (1.0 for true, 0.0 for false)
    const expected = [_]f32{ 1.0, 0.0, 0.0, 1.0, 0.0 }; // 1<2, 5!<4, 3!<3, 7<8, 2!<1
    try expectApproxEqSlice(&expected, output_f32, 1e-5);
    
    print("✓ Less than operation test passed!\n", .{});
}

test "primitive: unary operations (recip, sqrt, sin)" {
    const allocator = testing.allocator;
    
    print("\n=== Testing: unary operations (recip, sqrt, sin) ===\n", .{});
    
    var graph = try Graph.init(allocator);
    defer graph.deinit();
    
    const x = try tensor.placeholder(&graph, &.{4}, .f32);
    
    // Test reciprocal
    const recip_x = try x.recip();
    // Test sqrt
    const sqrt_x = try x.sqrt();
    // Test sin
    const sin_x = try x.sin();
    
    try graph.output_nodes.append(graph.arena.allocator(), recip_x.node_id);
    try graph.output_nodes.append(graph.arena.allocator(), sqrt_x.node_id);
    try graph.output_nodes.append(graph.arena.allocator(), sin_x.node_id);
    
    var compiled = try compiler.compile.compileCpu(&graph, allocator);
    defer compiled.deinit(allocator);
    
    var executor = try execution.Executor.init(allocator, &compiled, null);
    defer executor.deinit();
    
    const x_data = [_]f32{ 2.0, 4.0, 0.25, 1.0 };
    const shape = [_]i64{4};
    
    try executor.setInput(x.node_id, std.mem.asBytes(&x_data), &shape, .f32);
    
    try executor.run();
    
    // Check reciprocal
    const recip_output = try executor.getOutput(recip_x.node_id);
    const recip_f32 = @as([*]const f32, @ptrCast(@alignCast(recip_output.data.ptr)))[0..4];
    const expected_recip = [_]f32{ 0.5, 0.25, 4.0, 1.0 };
    try expectApproxEqSlice(&expected_recip, recip_f32, 1e-5);
    
    // Check sqrt
    const sqrt_output = try executor.getOutput(sqrt_x.node_id);
    const sqrt_f32 = @as([*]const f32, @ptrCast(@alignCast(sqrt_output.data.ptr)))[0..4];
    const expected_sqrt = [_]f32{ 1.41421356, 2.0, 0.5, 1.0 };
    try expectApproxEqSlice(&expected_sqrt, sqrt_f32, 1e-5);
    
    // Check sin
    const sin_output = try executor.getOutput(sin_x.node_id);
    const sin_f32 = @as([*]const f32, @ptrCast(@alignCast(sin_output.data.ptr)))[0..4];
    const expected_sin = [_]f32{ 0.9092974, -0.7568025, 0.2474040, 0.8414710 };
    try expectApproxEqSlice(&expected_sin, sin_f32, 1e-5);
    
    print("✓ Unary operations test passed!\n", .{});
}

test "primitive: exp2 and log2 operations" {
    const allocator = testing.allocator;
    
    print("\n=== Testing: exp2 and log2 operations ===\n", .{});
    
    var graph = try Graph.init(allocator);
    defer graph.deinit();
    
    const x = try tensor.placeholder(&graph, &.{5}, .f32);
    
    // Test exp2
    const exp2_x = try x.exp2();
    // Test log2
    const log2_x = try x.log2();
    
    try graph.output_nodes.append(graph.arena.allocator(), exp2_x.node_id);
    try graph.output_nodes.append(graph.arena.allocator(), log2_x.node_id);
    
    var compiled = try compiler.compile.compileCpu(&graph, allocator);
    defer compiled.deinit(allocator);
    
    var executor = try execution.Executor.init(allocator, &compiled, null);
    defer executor.deinit();
    
    const x_data = [_]f32{ 0, 1, 2, 3, -1 };
    const shape = [_]i64{5};
    
    try executor.setInput(x.node_id, std.mem.asBytes(&x_data), &shape, .f32);
    
    try executor.run();
    
    // Check exp2
    const exp2_output = try executor.getOutput(exp2_x.node_id);
    const exp2_f32 = @as([*]const f32, @ptrCast(@alignCast(exp2_output.data.ptr)))[0..5];
    const expected_exp2 = [_]f32{ 1.0, 2.0, 4.0, 8.0, 0.5 };
    try expectApproxEqSlice(&expected_exp2, exp2_f32, 1e-5);
    
    // For log2, use different input (positive values only)
    const x_log_data = [_]f32{ 1, 2, 4, 8, 0.5 };
    try executor.setInput(x.node_id, std.mem.asBytes(&x_log_data), &shape, .f32);
    try executor.run();
    
    // Check log2
    const log2_output = try executor.getOutput(log2_x.node_id);
    const log2_f32 = @as([*]const f32, @ptrCast(@alignCast(log2_output.data.ptr)))[0..5];
    const expected_log2 = [_]f32{ 0.0, 1.0, 2.0, 3.0, -1.0 };
    try expectApproxEqSlice(&expected_log2, log2_f32, 1e-5);
    
    print("✓ exp2 and log2 operations test passed!\n", .{});
}

test "primitive: sum_reduce operation" {
    const allocator = testing.allocator;
    
    print("\n=== Testing: sum_reduce operation ===\n", .{});
    
    var graph = try Graph.init(allocator);
    defer graph.deinit();
    
    // Test 1: Sum along axis 1 of a 2x3x4 tensor
    const x = try tensor.placeholder(&graph, &.{2, 3, 4}, .f32);
    const sum_axis1 = try x.sumReduce(1, true);
    
    try graph.output_nodes.append(graph.arena.allocator(), sum_axis1.node_id);
    
    var compiled = try compiler.compile.compileCpu(&graph, allocator);
    defer compiled.deinit(allocator);
    
    var executor = try execution.Executor.init(allocator, &compiled, null);
    defer executor.deinit();
    
    // Create sequential test data for easy verification
    var x_data: [24]f32 = undefined;
    for (0..24) |i| {
        x_data[i] = @as(f32, @floatFromInt(i + 1));
    }
    const shape = [_]i64{ 2, 3, 4 };
    
    try executor.setInput(x.node_id, std.mem.asBytes(&x_data), &shape, .f32);
    
    try executor.run();
    
    const output = try executor.getOutput(sum_axis1.node_id);
    const output_f32 = @as([*]const f32, @ptrCast(@alignCast(output.data.ptr)))[0..8];
    
    // Sum along axis 1: shape becomes [2, 1, 4]
    // First batch: sum of rows [1,2,3,4], [5,6,7,8], [9,10,11,12] = [15,18,21,24]
    // Second batch: sum of rows [13,14,15,16], [17,18,19,20], [21,22,23,24] = [51,54,57,60]
    const expected = [_]f32{ 15, 18, 21, 24, 51, 54, 57, 60 };
    try expectApproxEqSlice(&expected, output_f32, 1e-5);
    
    print("✓ sum_reduce operation test passed!\n", .{});
}

test "primitive: max_reduce operation" {
    const allocator = testing.allocator;
    
    print("\n=== Testing: max_reduce operation ===\n", .{});
    
    var graph = try Graph.init(allocator);
    defer graph.deinit();
    
    // Test: Max along axis 0 of a 3x4 tensor
    const x = try tensor.placeholder(&graph, &.{3, 4}, .f32);
    const max_axis0 = try x.maxReduce(0, true);
    
    try graph.output_nodes.append(graph.arena.allocator(), max_axis0.node_id);
    
    var compiled = try compiler.compile.compileCpu(&graph, allocator);
    defer compiled.deinit(allocator);
    
    var executor = try execution.Executor.init(allocator, &compiled, null);
    defer executor.deinit();
    
    const x_data = [_]f32{
        1, 5, 3, 2,
        4, 2, 6, 8,
        3, 7, 1, 5,
    };
    const shape = [_]i64{ 3, 4 };
    
    try executor.setInput(x.node_id, std.mem.asBytes(&x_data), &shape, .f32);
    
    try executor.run();
    
    const output = try executor.getOutput(max_axis0.node_id);
    const output_f32 = @as([*]const f32, @ptrCast(@alignCast(output.data.ptr)))[0..4];
    
    // Max along axis 0: shape becomes [1, 4]
    // Max of each column: [4, 7, 6, 8]
    const expected = [_]f32{ 4, 7, 6, 8 };
    try expectApproxEqSlice(&expected, output_f32, 1e-5);
    
    print("✓ max_reduce operation test passed!\n", .{});
}

test "primitive: combined operations - add and mul" {
    const allocator = testing.allocator;
    
    print("\n=== Testing: combined add and mul operations ===\n", .{});
    
    var graph = try Graph.init(allocator);
    defer graph.deinit();
    
    // (a + b) * c
    const a = try tensor.placeholder(&graph, &.{2, 2}, .f32);
    const b = try tensor.placeholder(&graph, &.{2, 2}, .f32);
    const c = try tensor.placeholder(&graph, &.{2, 2}, .f32);
    
    const sum_ab = try a.add(b);
    const result = try sum_ab.mul(c);
    
    try graph.output_nodes.append(graph.arena.allocator(), result.node_id);
    
    var compiled = try compiler.compile.compileCpu(&graph, allocator);
    defer compiled.deinit(allocator);
    
    var executor = try execution.Executor.init(allocator, &compiled, null);
    defer executor.deinit();
    
    const a_data = [_]f32{ 1, 2, 3, 4 };
    const b_data = [_]f32{ 5, 6, 7, 8 };
    const c_data = [_]f32{ 2, 0.5, 3, 0.25 };
    const shape = [_]i64{ 2, 2 };
    
    try executor.setInput(a.node_id, std.mem.asBytes(&a_data), &shape, .f32);
    try executor.setInput(b.node_id, std.mem.asBytes(&b_data), &shape, .f32);
    try executor.setInput(c.node_id, std.mem.asBytes(&c_data), &shape, .f32);
    
    try executor.run();
    
    const output = try executor.getOutput(result.node_id);
    const output_f32 = @as([*]const f32, @ptrCast(@alignCast(output.data.ptr)))[0..4];
    
    // (1+5)*2=12, (2+6)*0.5=4, (3+7)*3=30, (4+8)*0.25=3
    const expected = [_]f32{ 12, 4, 30, 3 };
    try expectApproxEqSlice(&expected, output_f32, 1e-5);
    
    print("✓ Combined add and mul test passed!\n", .{});
}

test "primitive: chain of unary operations" {
    const allocator = testing.allocator;
    
    print("\n=== Testing: chain of unary operations ===\n", .{});
    
    var graph = try Graph.init(allocator);
    defer graph.deinit();
    
    const x = try tensor.placeholder(&graph, &.{3}, .f32);
    
    // Chain: sqrt(recip(x))
    const recip_x = try x.recip();
    const result = try recip_x.sqrt();
    
    try graph.output_nodes.append(graph.arena.allocator(), result.node_id);
    
    var compiled = try compiler.compile.compileCpu(&graph, allocator);
    defer compiled.deinit(allocator);
    
    var executor = try execution.Executor.init(allocator, &compiled, null);
    defer executor.deinit();
    
    const x_data = [_]f32{ 4.0, 16.0, 0.25 };
    const shape = [_]i64{3};
    
    try executor.setInput(x.node_id, std.mem.asBytes(&x_data), &shape, .f32);
    
    try executor.run();
    
    const output = try executor.getOutput(result.node_id);
    const output_f32 = @as([*]const f32, @ptrCast(@alignCast(output.data.ptr)))[0..3];
    
    // sqrt(1/4)=0.5, sqrt(1/16)=0.25, sqrt(1/0.25)=2
    const expected = [_]f32{ 0.5, 0.25, 2.0 };
    try expectApproxEqSlice(&expected, output_f32, 1e-5);
    
    print("✓ Chain of unary operations test passed!\n", .{});
}

test "primitive: reduction with element-wise operations" {
    const allocator = testing.allocator;
    
    print("\n=== Testing: reduction with element-wise operations ===\n", .{});
    
    var graph = try Graph.init(allocator);
    defer graph.deinit();
    
    // Sum(a * b) along axis 1
    const a = try tensor.placeholder(&graph, &.{2, 4}, .f32);
    const b = try tensor.placeholder(&graph, &.{2, 4}, .f32);
    
    const prod = try a.mul(b);
    const sum_prod = try prod.sumReduce(1, true);
    
    try graph.output_nodes.append(graph.arena.allocator(), sum_prod.node_id);
    
    var compiled = try compiler.compile.compileCpu(&graph, allocator);
    defer compiled.deinit(allocator);
    
    var executor = try execution.Executor.init(allocator, &compiled, null);
    defer executor.deinit();
    
    const a_data = [_]f32{ 1, 2, 3, 4, 5, 6, 7, 8 };
    const b_data = [_]f32{ 2, 2, 2, 2, 0.5, 0.5, 0.5, 0.5 };
    const shape = [_]i64{ 2, 4 };
    
    try executor.setInput(a.node_id, std.mem.asBytes(&a_data), &shape, .f32);
    try executor.setInput(b.node_id, std.mem.asBytes(&b_data), &shape, .f32);
    
    try executor.run();
    
    const output = try executor.getOutput(sum_prod.node_id);
    const output_f32 = @as([*]const f32, @ptrCast(@alignCast(output.data.ptr)))[0..2];
    
    // First row: (1*2 + 2*2 + 3*2 + 4*2) = 20
    // Second row: (5*0.5 + 6*0.5 + 7*0.5 + 8*0.5) = 13
    const expected = [_]f32{ 20, 13 };
    try expectApproxEqSlice(&expected, output_f32, 1e-5);
    
    print("✓ Reduction with element-wise operations test passed!\n", .{});
}

test "primitive operations integration test suite" {
    print("\n" ++ "=" ** 60 ++ "\n", .{});
    print("PRIMITIVE OPERATIONS INTEGRATION TEST SUITE\n", .{});
    print("=" ** 60 ++ "\n", .{});
    
    print("\nThis test suite verifies all primitive tensor operations:\n", .{});
    print("• Binary operations: add, mul, mod, less_than\n", .{});
    print("• Unary operations: recip, sqrt, sin, exp2, log2\n", .{});
    print("• Reduction operations: sum_reduce, max_reduce\n", .{});
    print("• Combined operations and chains\n", .{});
    
    print("\n" ++ "=" ** 60 ++ "\n", .{});
    print("✅ ALL PRIMITIVE OPERATION TESTS COMPLETED SUCCESSFULLY!\n", .{});
    print("=" ** 60 ++ "\n", .{});
}