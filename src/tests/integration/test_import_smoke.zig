const std = @import("std");
const testing = std.testing;
const print = std.debug.print;

// Test imports one by one
test "test imports" {
    print("Testing types...\n", .{});
    _ = @import("types");
    
    print("Testing graph...\n", .{});
    _ = @import("graph");
    
    print("Testing shape...\n", .{});
    _ = @import("shape");
    
    print("Testing symbolic...\n", .{});
    _ = @import("symbolic");
    
    print("Testing storage...\n", .{});
    _ = @import("storage");
    
    print("Testing tensor...\n", .{});
    _ = @import("tensor");
    
    print("Testing backends...\n", .{});
    _ = @import("backends");
    
    print("Testing execution...\n", .{});
    _ = @import("execution");
    
    print("Testing compiler...\n", .{});
    _ = @import("compiler");
    
    print("All imports successful!\n", .{});
}