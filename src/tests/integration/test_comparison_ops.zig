/// Integration tests for comparison tensor operations
/// Tests comparison operations: equal, notEqual, lessThan, lessThanOrEqual, greaterThan, greaterThanOrEqual, maximum, minimum

const std = @import("std");
const testing = std.testing;
const print = std.debug.print;

// Import Zing components
const types = @import("types");
const Graph = @import("graph").Graph;
const tensor = @import("tensor");
const compiler = @import("compiler");
const execution = @import("execution");

// Helper to compare results with tolerance
fn expectApproxEqSlice(expected: []const f32, actual: []const f32, tolerance: f32) !void {
    try testing.expectEqual(expected.len, actual.len);
    for (expected, actual, 0..) |e, a, i| {
        if (@abs(e - a) > tolerance) {
            print("Mismatch at index {}: expected {d:.6}, got {d:.6}\n", .{ i, e, a });
            return error.ValueMismatch;
        }
    }
}

test "comparison: lessThan operation" {
    const allocator = testing.allocator;
    
    print("\n=== Testing: lessThan operation ===\n", .{});
    
    var graph = try Graph.init(allocator);
    defer graph.deinit();
    
    const a = try tensor.placeholder(&graph, &.{5}, .f32);
    const b = try tensor.placeholder(&graph, &.{5}, .f32);
    const result = try a.lessThan(b);
    
    try graph.output_nodes.append(graph.arena.allocator(), result.node_id);
    
    var compiled = try compiler.compile.compileCpu(&graph, allocator);
    defer compiled.deinit(allocator);
    
    var executor = try execution.Executor.init(allocator, &compiled, null);
    defer executor.deinit();
    
    // Test data: a = [1, 3, 5, 7, 9], b = [2, 3, 4, 8, 8]
    const a_data = [_]f32{ 1, 3, 5, 7, 9 };
    const b_data = [_]f32{ 2, 3, 4, 8, 8 };
    const shape = [_]i64{5};
    
    try executor.setInput(a.node_id, std.mem.asBytes(&a_data), &shape, .f32);
    try executor.setInput(b.node_id, std.mem.asBytes(&b_data), &shape, .f32);
    try executor.run();
    
    const output = try executor.getOutput(result.node_id);
    const output_f32 = @as([*]const f32, @ptrCast(@alignCast(output.data.ptr)))[0..5];
    
    // Expected: [1<2, 3<3, 5<4, 7<8, 9<8] = [1, 0, 0, 1, 0] (using 1.0/0.0 for true/false)
    const expected = [_]f32{ 1.0, 0.0, 0.0, 1.0, 0.0 };
    try expectApproxEqSlice(&expected, output_f32, 1e-6);
    
    print("✓ lessThan operation test passed!\n", .{});
}

test "comparison: equal operation" {
    const allocator = testing.allocator;
    
    print("\n=== Testing: equal operation ===\n", .{});
    
    var graph = try Graph.init(allocator);
    defer graph.deinit();
    
    const a = try tensor.placeholder(&graph, &.{4}, .f32);
    const b = try tensor.placeholder(&graph, &.{4}, .f32);
    const result = try a.equal(b);
    
    try graph.output_nodes.append(graph.arena.allocator(), result.node_id);
    
    var compiled = try compiler.compile.compileCpu(&graph, allocator);
    defer compiled.deinit(allocator);
    
    var executor = try execution.Executor.init(allocator, &compiled, null);
    defer executor.deinit();
    
    // Test data: a = [1, 2, 3, 4], b = [1, 5, 3, 7]
    const a_data = [_]f32{ 1, 2, 3, 4 };
    const b_data = [_]f32{ 1, 5, 3, 7 };
    const shape = [_]i64{4};
    
    try executor.setInput(a.node_id, std.mem.asBytes(&a_data), &shape, .f32);
    try executor.setInput(b.node_id, std.mem.asBytes(&b_data), &shape, .f32);
    try executor.run();
    
    const output = try executor.getOutput(result.node_id);
    const output_f32 = @as([*]const f32, @ptrCast(@alignCast(output.data.ptr)))[0..4];
    
    // Expected: [1==1, 2==5, 3==3, 4==7] = [1, 0, 1, 0]
    const expected = [_]f32{ 1.0, 0.0, 1.0, 0.0 };
    try expectApproxEqSlice(&expected, output_f32, 1e-6);
    
    print("✓ equal operation test passed!\n", .{});
}

test "comparison: notEqual operation" {
    const allocator = testing.allocator;
    
    print("\n=== Testing: notEqual operation ===\n", .{});
    
    var graph = try Graph.init(allocator);
    defer graph.deinit();
    
    const a = try tensor.placeholder(&graph, &.{4}, .f32);
    const b = try tensor.placeholder(&graph, &.{4}, .f32);
    const result = try a.notEqual(b);
    
    try graph.output_nodes.append(graph.arena.allocator(), result.node_id);
    
    var compiled = try compiler.compile.compileCpu(&graph, allocator);
    defer compiled.deinit(allocator);
    
    var executor = try execution.Executor.init(allocator, &compiled, null);
    defer executor.deinit();
    
    // Test data: a = [1, 2, 3, 4], b = [1, 5, 3, 7]
    const a_data = [_]f32{ 1, 2, 3, 4 };
    const b_data = [_]f32{ 1, 5, 3, 7 };
    const shape = [_]i64{4};
    
    try executor.setInput(a.node_id, std.mem.asBytes(&a_data), &shape, .f32);
    try executor.setInput(b.node_id, std.mem.asBytes(&b_data), &shape, .f32);
    try executor.run();
    
    const output = try executor.getOutput(result.node_id);
    const output_f32 = @as([*]const f32, @ptrCast(@alignCast(output.data.ptr)))[0..4];
    
    // Expected: [1!=1, 2!=5, 3!=3, 4!=7] = [0, 1, 0, 1]
    const expected = [_]f32{ 0.0, 1.0, 0.0, 1.0 };
    try expectApproxEqSlice(&expected, output_f32, 1e-6);
    
    print("✓ notEqual operation test passed!\n", .{});
}

test "comparison: greaterThan operation" {
    const allocator = testing.allocator;
    
    print("\n=== Testing: greaterThan operation ===\n", .{});
    
    var graph = try Graph.init(allocator);
    defer graph.deinit();
    
    const a = try tensor.placeholder(&graph, &.{5}, .f32);
    const b = try tensor.placeholder(&graph, &.{5}, .f32);
    const result = try a.greaterThan(b);
    
    try graph.output_nodes.append(graph.arena.allocator(), result.node_id);
    
    var compiled = try compiler.compile.compileCpu(&graph, allocator);
    defer compiled.deinit(allocator);
    
    var executor = try execution.Executor.init(allocator, &compiled, null);
    defer executor.deinit();
    
    // Test data: a = [1, 3, 5, 7, 9], b = [2, 3, 4, 8, 8]
    const a_data = [_]f32{ 1, 3, 5, 7, 9 };
    const b_data = [_]f32{ 2, 3, 4, 8, 8 };
    const shape = [_]i64{5};
    
    try executor.setInput(a.node_id, std.mem.asBytes(&a_data), &shape, .f32);
    try executor.setInput(b.node_id, std.mem.asBytes(&b_data), &shape, .f32);
    try executor.run();
    
    const output = try executor.getOutput(result.node_id);
    const output_f32 = @as([*]const f32, @ptrCast(@alignCast(output.data.ptr)))[0..5];
    
    // Expected: [1>2, 3>3, 5>4, 7>8, 9>8] = [0, 0, 1, 0, 1]
    const expected = [_]f32{ 0.0, 0.0, 1.0, 0.0, 1.0 };
    try expectApproxEqSlice(&expected, output_f32, 1e-6);
    
    print("✓ greaterThan operation test passed!\n", .{});
}

test "comparison: lessThanOrEqual operation" {
    const allocator = testing.allocator;
    
    print("\n=== Testing: lessThanOrEqual operation ===\n", .{});
    
    var graph = try Graph.init(allocator);
    defer graph.deinit();
    
    const a = try tensor.placeholder(&graph, &.{5}, .f32);
    const b = try tensor.placeholder(&graph, &.{5}, .f32);
    const result = try a.lessThanOrEqual(b);
    
    try graph.output_nodes.append(graph.arena.allocator(), result.node_id);
    
    var compiled = try compiler.compile.compileCpu(&graph, allocator);
    defer compiled.deinit(allocator);
    
    var executor = try execution.Executor.init(allocator, &compiled, null);
    defer executor.deinit();
    
    // Test data: a = [1, 3, 5, 7, 9], b = [2, 3, 4, 8, 8]
    const a_data = [_]f32{ 1, 3, 5, 7, 9 };
    const b_data = [_]f32{ 2, 3, 4, 8, 8 };
    const shape = [_]i64{5};
    
    try executor.setInput(a.node_id, std.mem.asBytes(&a_data), &shape, .f32);
    try executor.setInput(b.node_id, std.mem.asBytes(&b_data), &shape, .f32);
    try executor.run();
    
    const output = try executor.getOutput(result.node_id);
    const output_f32 = @as([*]const f32, @ptrCast(@alignCast(output.data.ptr)))[0..5];
    
    // Expected: [1<=2, 3<=3, 5<=4, 7<=8, 9<=8] = [1, 1, 0, 1, 0]
    const expected = [_]f32{ 1.0, 1.0, 0.0, 1.0, 0.0 };
    try expectApproxEqSlice(&expected, output_f32, 1e-6);
    
    print("✓ lessThanOrEqual operation test passed!\n", .{});
}

test "comparison: greaterThanOrEqual operation" {
    const allocator = testing.allocator;
    
    print("\n=== Testing: greaterThanOrEqual operation ===\n", .{});
    
    var graph = try Graph.init(allocator);
    defer graph.deinit();
    
    const a = try tensor.placeholder(&graph, &.{5}, .f32);
    const b = try tensor.placeholder(&graph, &.{5}, .f32);
    const result = try a.greaterThanOrEqual(b);
    
    try graph.output_nodes.append(graph.arena.allocator(), result.node_id);
    
    var compiled = try compiler.compile.compileCpu(&graph, allocator);
    defer compiled.deinit(allocator);
    
    var executor = try execution.Executor.init(allocator, &compiled, null);
    defer executor.deinit();
    
    // Test data: a = [1, 3, 5, 7, 9], b = [2, 3, 4, 8, 8]
    const a_data = [_]f32{ 1, 3, 5, 7, 9 };
    const b_data = [_]f32{ 2, 3, 4, 8, 8 };
    const shape = [_]i64{5};
    
    try executor.setInput(a.node_id, std.mem.asBytes(&a_data), &shape, .f32);
    try executor.setInput(b.node_id, std.mem.asBytes(&b_data), &shape, .f32);
    try executor.run();
    
    const output = try executor.getOutput(result.node_id);
    const output_f32 = @as([*]const f32, @ptrCast(@alignCast(output.data.ptr)))[0..5];
    
    // Expected: [1>=2, 3>=3, 5>=4, 7>=8, 9>=8] = [0, 1, 1, 0, 1]
    const expected = [_]f32{ 0.0, 1.0, 1.0, 0.0, 1.0 };
    try expectApproxEqSlice(&expected, output_f32, 1e-6);
    
    print("✓ greaterThanOrEqual operation test passed!\n", .{});
}

test "comparison: maximum operation" {
    const allocator = testing.allocator;
    
    print("\n=== Testing: maximum operation ===\n", .{});
    
    var graph = try Graph.init(allocator);
    defer graph.deinit();
    
    const a = try tensor.placeholder(&graph, &.{4}, .f32);
    const b = try tensor.placeholder(&graph, &.{4}, .f32);
    const result = try a.maximum(b);
    
    try graph.output_nodes.append(graph.arena.allocator(), result.node_id);
    
    var compiled = try compiler.compile.compileCpu(&graph, allocator);
    defer compiled.deinit(allocator);
    
    var executor = try execution.Executor.init(allocator, &compiled, null);
    defer executor.deinit();
    
    // Test data: a = [-2, 1, 3, 0], b = [0, -1, 2, 4]
    const a_data = [_]f32{ -2, 1, 3, 0 };
    const b_data = [_]f32{ 0, -1, 2, 4 };
    const shape = [_]i64{4};
    
    try executor.setInput(a.node_id, std.mem.asBytes(&a_data), &shape, .f32);
    try executor.setInput(b.node_id, std.mem.asBytes(&b_data), &shape, .f32);
    try executor.run();
    
    const output = try executor.getOutput(result.node_id);
    const output_f32 = @as([*]const f32, @ptrCast(@alignCast(output.data.ptr)))[0..4];
    
    // Expected: max(a, b) = [0, 1, 3, 4]
    const expected = [_]f32{ 0, 1, 3, 4 };
    try expectApproxEqSlice(&expected, output_f32, 1e-6);
    
    print("✓ maximum operation test passed!\n", .{});
}

test "comparison: minimum operation" {
    const allocator = testing.allocator;
    
    print("\n=== Testing: minimum operation ===\n", .{});
    
    var graph = try Graph.init(allocator);
    defer graph.deinit();
    
    const a = try tensor.placeholder(&graph, &.{4}, .f32);
    const b = try tensor.placeholder(&graph, &.{4}, .f32);
    const result = try a.minimum(b);
    
    try graph.output_nodes.append(graph.arena.allocator(), result.node_id);
    
    var compiled = try compiler.compile.compileCpu(&graph, allocator);
    defer compiled.deinit(allocator);
    
    var executor = try execution.Executor.init(allocator, &compiled, null);
    defer executor.deinit();
    
    // Test data: a = [-2, 1, 3, 0], b = [0, -1, 2, 4]
    const a_data = [_]f32{ -2, 1, 3, 0 };
    const b_data = [_]f32{ 0, -1, 2, 4 };
    const shape = [_]i64{4};
    
    try executor.setInput(a.node_id, std.mem.asBytes(&a_data), &shape, .f32);
    try executor.setInput(b.node_id, std.mem.asBytes(&b_data), &shape, .f32);
    try executor.run();
    
    const output = try executor.getOutput(result.node_id);
    const output_f32 = @as([*]const f32, @ptrCast(@alignCast(output.data.ptr)))[0..4];
    
    // Expected: min(a, b) = [-2, -1, 2, 0]
    const expected = [_]f32{ -2, -1, 2, 0 };
    try expectApproxEqSlice(&expected, output_f32, 1e-6);
    
    print("✓ minimum operation test passed!\n", .{});
}

test "comparison: 2D and 3D operations" {
    const allocator = testing.allocator;
    
    print("\n=== Testing: 2D comparison operations ===\n", .{});
    
    var graph = try Graph.init(allocator);
    defer graph.deinit();
    
    // Test 2D operations
    const a_2d = try tensor.placeholder(&graph, &.{2, 3}, .f32);
    const b_2d = try tensor.placeholder(&graph, &.{2, 3}, .f32);
    const less_than_2d = try a_2d.lessThan(b_2d);
    const max_2d = try a_2d.maximum(b_2d);
    
    try graph.output_nodes.append(graph.arena.allocator(), less_than_2d.node_id);
    try graph.output_nodes.append(graph.arena.allocator(), max_2d.node_id);
    
    var compiled = try compiler.compile.compileCpu(&graph, allocator);
    defer compiled.deinit(allocator);
    
    var executor = try execution.Executor.init(allocator, &compiled, null);
    defer executor.deinit();
    
    // Test data for 2D: a = [[1,2,3],[4,5,6]], b = [[2,1,4],[3,6,5]]
    const a_data = [_]f32{ 1, 2, 3, 4, 5, 6 };
    const b_data = [_]f32{ 2, 1, 4, 3, 6, 5 };
    const shape_2d = [_]i64{2, 3};
    
    try executor.setInput(a_2d.node_id, std.mem.asBytes(&a_data), &shape_2d, .f32);
    try executor.setInput(b_2d.node_id, std.mem.asBytes(&b_data), &shape_2d, .f32);
    try executor.run();
    
    const lt_output = try executor.getOutput(less_than_2d.node_id);
    const lt_f32 = @as([*]const f32, @ptrCast(@alignCast(lt_output.data.ptr)))[0..6];
    
    const max_output = try executor.getOutput(max_2d.node_id);
    const max_f32 = @as([*]const f32, @ptrCast(@alignCast(max_output.data.ptr)))[0..6];
    
    // Expected less than: [1<2, 2<1, 3<4, 4<3, 5<6, 6<5] = [1,0,1,0,1,0]
    const expected_lt = [_]f32{ 1, 0, 1, 0, 1, 0 };
    try expectApproxEqSlice(&expected_lt, lt_f32, 1e-6);
    
    // Expected maximum: [max(1,2), max(2,1), max(3,4), max(4,3), max(5,6), max(6,5)] = [2,2,4,4,6,6]
    const expected_max = [_]f32{ 2, 2, 4, 4, 6, 6 };
    try expectApproxEqSlice(&expected_max, max_f32, 1e-6);
    
    print("✓ 2D comparison operations test passed!\n", .{});
}

test "comparison: complex comparison chains" {
    const allocator = testing.allocator;
    
    print("\n=== Testing: complex comparison chains ===\n", .{});
    
    var graph = try Graph.init(allocator);
    defer graph.deinit();
    
    // Test complex logical chain: (a > b) AND (a < c)
    const a = try tensor.placeholder(&graph, &.{4}, .f32);
    const b = try tensor.placeholder(&graph, &.{4}, .f32);
    const c = try tensor.placeholder(&graph, &.{4}, .f32);
    
    const a_gt_b = try a.greaterThan(b);
    const a_lt_c = try a.lessThan(c);
    const result = try a_gt_b.logicalAnd(a_lt_c);
    
    try graph.output_nodes.append(graph.arena.allocator(), result.node_id);
    
    var compiled = try compiler.compile.compileCpu(&graph, allocator);
    defer compiled.deinit(allocator);
    
    var executor = try execution.Executor.init(allocator, &compiled, null);
    defer executor.deinit();
    
    // Test data: a = [2, 5, 3, 8], b = [1, 6, 2, 7], c = [4, 7, 5, 9]
    const a_data = [_]f32{ 2, 5, 3, 8 };
    const b_data = [_]f32{ 1, 6, 2, 7 };
    const c_data = [_]f32{ 4, 7, 5, 9 };
    const shape = [_]i64{4};
    
    try executor.setInput(a.node_id, std.mem.asBytes(&a_data), &shape, .f32);
    try executor.setInput(b.node_id, std.mem.asBytes(&b_data), &shape, .f32);
    try executor.setInput(c.node_id, std.mem.asBytes(&c_data), &shape, .f32);
    try executor.run();
    
    const output = try executor.getOutput(result.node_id);
    const output_f32 = @as([*]const f32, @ptrCast(@alignCast(output.data.ptr)))[0..4];
    
    // Expected: (2>1)&(2<4), (5>6)&(5<7), (3>2)&(3<5), (8>7)&(8<9) = [1&1, 0&1, 1&1, 1&1] = [1,0,1,1]
    const expected = [_]f32{ 1.0, 0.0, 1.0, 1.0 };
    try expectApproxEqSlice(&expected, output_f32, 1e-6);
    
    print("✓ complex comparison chains test passed!\n", .{});
}

test "comparison operations integration test suite" {
    print("\n" ++ "=" ** 60 ++ "\n", .{});
    print("COMPARISON OPERATIONS INTEGRATION TEST SUITE\n", .{});
    print("=" ** 60 ++ "\n", .{});
    
    print("\nThis test suite verifies all comparison tensor operations:\n", .{});
    print("• Basic comparisons: lessThan, equal, notEqual\n", .{});
    print("• Extended comparisons: greaterThan, lessThanOrEqual, greaterThanOrEqual\n", .{});
    print("• Element-wise selection: maximum, minimum\n", .{});
    print("• Complex logical chains and multi-dimensional operations\n", .{});
    
    print("\n" ++ "=" ** 60 ++ "\n", .{});
    print("✅ ALL COMPARISON OPERATION TESTS COMPLETED SUCCESSFULLY!\n", .{});
    print("=" ** 60 ++ "\n", .{});
}