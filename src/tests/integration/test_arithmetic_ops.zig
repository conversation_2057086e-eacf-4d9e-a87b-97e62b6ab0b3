/// Integration tests for arithmetic tensor operations
/// Tests arithmetic operations that are built on top of primitives
/// All arithmetic operations: subtract, divide, pow, neg, abs, square, sign, fma, clamp, cube

const std = @import("std");
const testing = std.testing;
const print = std.debug.print;

// Import Zing components
const types = @import("types");
const Graph = @import("graph").Graph;
const tensor = @import("tensor");
const compiler = @import("compiler");
const execution = @import("execution");

// Helper to compare results with tolerance
fn expectApproxEqSlice(expected: []const f32, actual: []const f32, tolerance: f32) !void {
    try testing.expectEqual(expected.len, actual.len);
    for (expected, actual, 0..) |e, a, i| {
        if (@abs(e - a) > tolerance) {
            print("Mismatch at index {}: expected {d:.6}, got {d:.6}\n", .{ i, e, a });
            return error.ValueMismatch;
        }
    }
}

test "arithmetic: subtract operation" {
    const allocator = testing.allocator;
    
    print("\n=== Testing: subtract operation ===\n", .{});
    
    var graph = try Graph.init(allocator);
    defer graph.deinit();
    
    const a = try tensor.placeholder(&graph, &.{4}, .f32);
    const b = try tensor.placeholder(&graph, &.{4}, .f32);
    const result = try a.subtract(b);
    
    try graph.output_nodes.append(graph.arena.allocator(), result.node_id);
    
    var compiled = try compiler.compile.compileCpu(&graph, allocator);
    defer compiled.deinit(allocator);
    
    var executor = try execution.Executor.init(allocator, &compiled, null);
    defer executor.deinit();
    
    // Test data: a = [10, 8, 6, 4], b = [1, 2, 3, 4]
    const a_data = [_]f32{ 10, 8, 6, 4 };
    const b_data = [_]f32{ 1, 2, 3, 4 };
    const shape = [_]i64{4};
    
    try executor.setInput(a.node_id, std.mem.asBytes(&a_data), &shape, .f32);
    try executor.setInput(b.node_id, std.mem.asBytes(&b_data), &shape, .f32);
    try executor.run();
    
    const output = try executor.getOutput(result.node_id);
    const output_f32 = @as([*]const f32, @ptrCast(@alignCast(output.data.ptr)))[0..4];
    
    // Expected: a - b = [9, 6, 3, 0]
    const expected = [_]f32{ 9, 6, 3, 0 };
    try expectApproxEqSlice(&expected, output_f32, 1e-6);
    
    print("✓ subtract operation test passed!\n", .{});
}

test "arithmetic: divide operation" {
    const allocator = testing.allocator;
    
    print("\n=== Testing: divide operation ===\n", .{});
    
    var graph = try Graph.init(allocator);
    defer graph.deinit();
    
    const a = try tensor.placeholder(&graph, &.{4}, .f32);
    const b = try tensor.placeholder(&graph, &.{4}, .f32);
    const result = try a.divide(b);
    
    try graph.output_nodes.append(graph.arena.allocator(), result.node_id);
    
    var compiled = try compiler.compile.compileCpu(&graph, allocator);
    defer compiled.deinit(allocator);
    
    var executor = try execution.Executor.init(allocator, &compiled, null);
    defer executor.deinit();
    
    // Test data: a = [12, 9, 6, 3], b = [4, 3, 2, 1]
    const a_data = [_]f32{ 12, 9, 6, 3 };
    const b_data = [_]f32{ 4, 3, 2, 1 };
    const shape = [_]i64{4};
    
    try executor.setInput(a.node_id, std.mem.asBytes(&a_data), &shape, .f32);
    try executor.setInput(b.node_id, std.mem.asBytes(&b_data), &shape, .f32);
    try executor.run();
    
    const output = try executor.getOutput(result.node_id);
    const output_f32 = @as([*]const f32, @ptrCast(@alignCast(output.data.ptr)))[0..4];
    
    // Expected: a / b = [3, 3, 3, 3]
    const expected = [_]f32{ 3, 3, 3, 3 };
    try expectApproxEqSlice(&expected, output_f32, 1e-6);
    
    print("✓ divide operation test passed!\n", .{});
}

test "arithmetic: pow operation" {
    const allocator = testing.allocator;
    
    print("\n=== Testing: pow operation ===\n", .{});
    
    var graph = try Graph.init(allocator);
    defer graph.deinit();
    
    const base = try tensor.placeholder(&graph, &.{4}, .f32);
    const exp = try tensor.placeholder(&graph, &.{4}, .f32);
    const result = try base.pow(exp);
    
    try graph.output_nodes.append(graph.arena.allocator(), result.node_id);
    
    var compiled = try compiler.compile.compileCpu(&graph, allocator);
    defer compiled.deinit(allocator);
    
    var executor = try execution.Executor.init(allocator, &compiled, null);
    defer executor.deinit();
    
    // Test data: base = [2, 3, 4, 5], exp = [2, 2, 2, 2]
    const base_data = [_]f32{ 2, 3, 4, 5 };
    const exp_data = [_]f32{ 2, 2, 2, 2 };
    const shape = [_]i64{4};
    
    try executor.setInput(base.node_id, std.mem.asBytes(&base_data), &shape, .f32);
    try executor.setInput(exp.node_id, std.mem.asBytes(&exp_data), &shape, .f32);
    try executor.run();
    
    const output = try executor.getOutput(result.node_id);
    const output_f32 = @as([*]const f32, @ptrCast(@alignCast(output.data.ptr)))[0..4];
    
    // Expected: base^exp = [4, 9, 16, 25]
    const expected = [_]f32{ 4, 9, 16, 25 };
    try expectApproxEqSlice(&expected, output_f32, 1e-5);
    
    print("✓ pow operation test passed!\n", .{});
}

test "arithmetic: neg operation" {
    const allocator = testing.allocator;
    
    print("\n=== Testing: neg operation ===\n", .{});
    
    var graph = try Graph.init(allocator);
    defer graph.deinit();
    
    const x = try tensor.placeholder(&graph, &.{5}, .f32);
    const result = try x.neg();
    
    try graph.output_nodes.append(graph.arena.allocator(), result.node_id);
    
    var compiled = try compiler.compile.compileCpu(&graph, allocator);
    defer compiled.deinit(allocator);
    
    var executor = try execution.Executor.init(allocator, &compiled, null);
    defer executor.deinit();
    
    // Test data: [-2, -1, 0, 1, 2]
    const x_data = [_]f32{ -2, -1, 0, 1, 2 };
    const shape = [_]i64{5};
    
    try executor.setInput(x.node_id, std.mem.asBytes(&x_data), &shape, .f32);
    try executor.run();
    
    const output = try executor.getOutput(result.node_id);
    const output_f32 = @as([*]const f32, @ptrCast(@alignCast(output.data.ptr)))[0..5];
    
    // Expected: -x = [2, 1, 0, -1, -2]
    const expected = [_]f32{ 2, 1, 0, -1, -2 };
    try expectApproxEqSlice(&expected, output_f32, 1e-6);
    
    print("✓ neg operation test passed!\n", .{});
}

test "arithmetic: abs operation" {
    const allocator = testing.allocator;
    
    print("\n=== Testing: abs operation ===\n", .{});
    
    var graph = try Graph.init(allocator);
    defer graph.deinit();
    
    const x = try tensor.placeholder(&graph, &.{5}, .f32);
    const result = try x.abs();
    
    try graph.output_nodes.append(graph.arena.allocator(), result.node_id);
    
    var compiled = try compiler.compile.compileCpu(&graph, allocator);
    defer compiled.deinit(allocator);
    
    var executor = try execution.Executor.init(allocator, &compiled, null);
    defer executor.deinit();
    
    // Test data: [-2, -1, 0, 1, 2]
    const x_data = [_]f32{ -2, -1, 0, 1, 2 };
    const shape = [_]i64{5};
    
    try executor.setInput(x.node_id, std.mem.asBytes(&x_data), &shape, .f32);
    try executor.run();
    
    const output = try executor.getOutput(result.node_id);
    const output_f32 = @as([*]const f32, @ptrCast(@alignCast(output.data.ptr)))[0..5];
    
    // Expected: |x| = [2, 1, 0, 1, 2]
    const expected = [_]f32{ 2, 1, 0, 1, 2 };
    try expectApproxEqSlice(&expected, output_f32, 1e-6);
    
    print("✓ abs operation test passed!\n", .{});
}

test "arithmetic: square operation" {
    const allocator = testing.allocator;
    
    print("\n=== Testing: square operation ===\n", .{});
    
    var graph = try Graph.init(allocator);
    defer graph.deinit();
    
    const x = try tensor.placeholder(&graph, &.{4}, .f32);
    const result = try x.square();
    
    try graph.output_nodes.append(graph.arena.allocator(), result.node_id);
    
    var compiled = try compiler.compile.compileCpu(&graph, allocator);
    defer compiled.deinit(allocator);
    
    var executor = try execution.Executor.init(allocator, &compiled, null);
    defer executor.deinit();
    
    // Test data: [-3, -2, 2, 3]
    const x_data = [_]f32{ -3, -2, 2, 3 };
    const shape = [_]i64{4};
    
    try executor.setInput(x.node_id, std.mem.asBytes(&x_data), &shape, .f32);
    try executor.run();
    
    const output = try executor.getOutput(result.node_id);
    const output_f32 = @as([*]const f32, @ptrCast(@alignCast(output.data.ptr)))[0..4];
    
    // Expected: x^2 = [9, 4, 4, 9]
    const expected = [_]f32{ 9, 4, 4, 9 };
    try expectApproxEqSlice(&expected, output_f32, 1e-6);
    
    print("✓ square operation test passed!\n", .{});
}

test "arithmetic: sign operation" {
    const allocator = testing.allocator;
    
    print("\n=== Testing: sign operation ===\n", .{});
    
    var graph = try Graph.init(allocator);
    defer graph.deinit();
    
    const x = try tensor.placeholder(&graph, &.{5}, .f32);
    const result = try x.sign();
    
    try graph.output_nodes.append(graph.arena.allocator(), result.node_id);
    
    var compiled = try compiler.compile.compileCpu(&graph, allocator);
    defer compiled.deinit(allocator);
    
    var executor = try execution.Executor.init(allocator, &compiled, null);
    defer executor.deinit();
    
    // Test data: [-2, -0.5, 0, 0.5, 2]
    const x_data = [_]f32{ -2, -0.5, 0, 0.5, 2 };
    const shape = [_]i64{5};
    
    try executor.setInput(x.node_id, std.mem.asBytes(&x_data), &shape, .f32);
    try executor.run();
    
    const output = try executor.getOutput(result.node_id);
    const output_f32 = @as([*]const f32, @ptrCast(@alignCast(output.data.ptr)))[0..5];
    
    // Expected: sign(x) ≈ [-1, -1, ~0, 1, 1] (0 will be very close to 0 due to epsilon)
    const expected = [_]f32{ -1, -1, 0, 1, 1 };
    try expectApproxEqSlice(&expected, output_f32, 1e-5);
    
    print("✓ sign operation test passed!\n", .{});
}

test "arithmetic: fma operation" {
    const allocator = testing.allocator;
    
    print("\n=== Testing: fma (fused multiply-add) operation ===\n", .{});
    
    var graph = try Graph.init(allocator);
    defer graph.deinit();
    
    const a = try tensor.placeholder(&graph, &.{3}, .f32);
    const b = try tensor.placeholder(&graph, &.{3}, .f32);
    const c = try tensor.placeholder(&graph, &.{3}, .f32);
    
    // Implement fma manually as a * b + c since it's just a composed operation
    const product = try a.mul(b);
    const result = try product.add(c);
    
    try graph.output_nodes.append(graph.arena.allocator(), result.node_id);
    
    var compiled = try compiler.compile.compileCpu(&graph, allocator);
    defer compiled.deinit(allocator);
    
    var executor = try execution.Executor.init(allocator, &compiled, null);
    defer executor.deinit();
    
    // Test data: a = [2, 3, 4], b = [5, 6, 7], c = [1, 1, 1]
    const a_data = [_]f32{ 2, 3, 4 };
    const b_data = [_]f32{ 5, 6, 7 };
    const c_data = [_]f32{ 1, 1, 1 };
    const shape = [_]i64{3};
    
    try executor.setInput(a.node_id, std.mem.asBytes(&a_data), &shape, .f32);
    try executor.setInput(b.node_id, std.mem.asBytes(&b_data), &shape, .f32);
    try executor.setInput(c.node_id, std.mem.asBytes(&c_data), &shape, .f32);
    try executor.run();
    
    const output = try executor.getOutput(result.node_id);
    const output_f32 = @as([*]const f32, @ptrCast(@alignCast(output.data.ptr)))[0..3];
    
    // Expected: a*b + c = [2*5+1, 3*6+1, 4*7+1] = [11, 19, 29]
    const expected = [_]f32{ 11, 19, 29 };
    try expectApproxEqSlice(&expected, output_f32, 1e-6);
    
    print("✓ fma operation test passed!\n", .{});
}

test "arithmetic: simple maximum/minimum operations" {
    const allocator = testing.allocator;
    
    print("\n=== Testing: maximum and minimum operations ===\n", .{});
    
    var graph = try Graph.init(allocator);
    defer graph.deinit();
    
    const a = try tensor.placeholder(&graph, &.{4}, .f32);
    const b = try tensor.placeholder(&graph, &.{4}, .f32);
    const max_result = try a.maximum(b);
    const min_result = try a.minimum(b);
    
    try graph.output_nodes.append(graph.arena.allocator(), max_result.node_id);
    try graph.output_nodes.append(graph.arena.allocator(), min_result.node_id);
    
    var compiled = try compiler.compile.compileCpu(&graph, allocator);
    defer compiled.deinit(allocator);
    
    var executor = try execution.Executor.init(allocator, &compiled, null);
    defer executor.deinit();
    
    // Test data: a = [-2, 1, 3, 0], b = [0, -1, 2, 4]
    const a_data = [_]f32{ -2, 1, 3, 0 };
    const b_data = [_]f32{ 0, -1, 2, 4 };
    const shape = [_]i64{4};
    
    try executor.setInput(a.node_id, std.mem.asBytes(&a_data), &shape, .f32);
    try executor.setInput(b.node_id, std.mem.asBytes(&b_data), &shape, .f32);
    try executor.run();
    
    const max_output = try executor.getOutput(max_result.node_id);
    const max_f32 = @as([*]const f32, @ptrCast(@alignCast(max_output.data.ptr)))[0..4];
    
    const min_output = try executor.getOutput(min_result.node_id);
    const min_f32 = @as([*]const f32, @ptrCast(@alignCast(min_output.data.ptr)))[0..4];
    
    // Expected: max(a, b) = [0, 1, 3, 4], min(a, b) = [-2, -1, 2, 0]
    const expected_max = [_]f32{ 0, 1, 3, 4 };
    const expected_min = [_]f32{ -2, -1, 2, 0 };
    
    try expectApproxEqSlice(&expected_max, max_f32, 1e-6);
    try expectApproxEqSlice(&expected_min, min_f32, 1e-6);
    
    print("✓ maximum and minimum operations test passed!\n", .{});
}

test "arithmetic: cube operation" {
    const allocator = testing.allocator;
    
    print("\n=== Testing: cube operation ===\n", .{});
    
    var graph = try Graph.init(allocator);
    defer graph.deinit();
    
    const x = try tensor.placeholder(&graph, &.{4}, .f32);
    const result = try x.cube();
    
    try graph.output_nodes.append(graph.arena.allocator(), result.node_id);
    
    var compiled = try compiler.compile.compileCpu(&graph, allocator);
    defer compiled.deinit(allocator);
    
    var executor = try execution.Executor.init(allocator, &compiled, null);
    defer executor.deinit();
    
    // Test data: [-2, -1, 2, 3]
    const x_data = [_]f32{ -2, -1, 2, 3 };
    const shape = [_]i64{4};
    
    try executor.setInput(x.node_id, std.mem.asBytes(&x_data), &shape, .f32);
    try executor.run();
    
    const output = try executor.getOutput(result.node_id);
    const output_f32 = @as([*]const f32, @ptrCast(@alignCast(output.data.ptr)))[0..4];
    
    // Expected: x^3 = [-8, -1, 8, 27]
    const expected = [_]f32{ -8, -1, 8, 27 };
    try expectApproxEqSlice(&expected, output_f32, 1e-6);
    
    print("✓ cube operation test passed!\n", .{});
}

test "arithmetic: 2D and 3D operations" {
    const allocator = testing.allocator;
    
    print("\n=== Testing: 2D and 3D arithmetic operations ===\n", .{});
    
    var graph = try Graph.init(allocator);
    defer graph.deinit();
    
    // Test 2D operations
    const a_2d = try tensor.placeholder(&graph, &.{2, 3}, .f32);
    const b_2d = try tensor.placeholder(&graph, &.{2, 3}, .f32);
    const sub_2d = try a_2d.subtract(b_2d);
    const square_2d = try sub_2d.square();
    
    try graph.output_nodes.append(graph.arena.allocator(), square_2d.node_id);
    
    var compiled = try compiler.compile.compileCpu(&graph, allocator);
    defer compiled.deinit(allocator);
    
    var executor = try execution.Executor.init(allocator, &compiled, null);
    defer executor.deinit();
    
    // Test data for 2D: a = [[1,2,3],[4,5,6]], b = [[1,1,1],[2,2,2]]
    const a_data = [_]f32{ 1, 2, 3, 4, 5, 6 };
    const b_data = [_]f32{ 1, 1, 1, 2, 2, 2 };
    const shape_2d = [_]i64{2, 3};
    
    try executor.setInput(a_2d.node_id, std.mem.asBytes(&a_data), &shape_2d, .f32);
    try executor.setInput(b_2d.node_id, std.mem.asBytes(&b_data), &shape_2d, .f32);
    try executor.run();
    
    const output = try executor.getOutput(square_2d.node_id);
    const output_f32 = @as([*]const f32, @ptrCast(@alignCast(output.data.ptr)))[0..6];
    
    // Expected: (a - b)^2 = [[0,1,4],[4,9,16]]
    const expected = [_]f32{ 0, 1, 4, 4, 9, 16 };
    try expectApproxEqSlice(&expected, output_f32, 1e-6);
    
    print("✓ 2D and 3D arithmetic operations test passed!\n", .{});
}

// ============================================================================
// Tests from test_exp_constant.zig
// ============================================================================

test "debug: exp with different constant values" {
    const allocator = testing.allocator;
    
    var graph = try Graph.init(allocator);
    defer graph.deinit();
    
    const x = try tensor.placeholder(&graph, &.{3}, .f32);
    
    // Test 1: Multiply by log2(e) manually with exact value
    const log2_e_exact = try tensor.constant(&graph, 1.44269504089, .f32);
    const scaled_exact = try x.mul(log2_e_exact);
    const exp2_exact = try scaled_exact.exp2();
    
    // Test 2: Multiply by log2(e) with slightly different value
    const log2_e_approx = try tensor.constant(&graph, 1.442695, .f32);
    const scaled_approx = try x.mul(log2_e_approx);
    const exp2_approx = try scaled_approx.exp2();
    
    // Test 3: Direct exp2(-x) to see if that's what we're getting
    const neg_x = try x.neg();
    const exp2_neg_x = try neg_x.exp2();
    
    try graph.output_nodes.append(graph.arena.allocator(), scaled_exact.node_id);
    try graph.output_nodes.append(graph.arena.allocator(), exp2_exact.node_id);
    try graph.output_nodes.append(graph.arena.allocator(), scaled_approx.node_id);
    try graph.output_nodes.append(graph.arena.allocator(), exp2_approx.node_id);
    try graph.output_nodes.append(graph.arena.allocator(), exp2_neg_x.node_id);
    
    var compiled = try compiler.compile.compileCpu(&graph, allocator);
    defer compiled.deinit(allocator);
    
    var executor = try execution.Executor.init(allocator, &compiled, null);
    defer executor.deinit();
    
    const x_data = [_]f32{ -2, 0, 2 };
    const shape = [_]i64{3};
    
    try executor.setInput(x.node_id, std.mem.asBytes(&x_data), &shape, .f32);
    try executor.run();
    
    print("\n=== Exp Constant Debug ===\n", .{});
    print("Input x: [-2, 0, 2]\n", .{});
    
    const scaled_exact_output = try executor.getOutput(scaled_exact.node_id);
    const scaled_exact_f32 = @as([*]const f32, @ptrCast(@alignCast(scaled_exact_output.data.ptr)))[0..3];
    print("\nx * 1.44269504089: ", .{});
    for (scaled_exact_f32) |val| print("{d:.6} ", .{val});
    print("\n", .{});
    
    const exp2_exact_output = try executor.getOutput(exp2_exact.node_id);
    const exp2_exact_f32 = @as([*]const f32, @ptrCast(@alignCast(exp2_exact_output.data.ptr)))[0..3];
    print("exp2(scaled exact): ", .{});
    for (exp2_exact_f32) |val| print("{d:.6} ", .{val});
    print("\n", .{});
    
    const scaled_approx_output = try executor.getOutput(scaled_approx.node_id);
    const scaled_approx_f32 = @as([*]const f32, @ptrCast(@alignCast(scaled_approx_output.data.ptr)))[0..3];
    print("\nx * 1.442695: ", .{});
    for (scaled_approx_f32) |val| print("{d:.6} ", .{val});
    print("\n", .{});
    
    const exp2_approx_output = try executor.getOutput(exp2_approx.node_id);
    const exp2_approx_f32 = @as([*]const f32, @ptrCast(@alignCast(exp2_approx_output.data.ptr)))[0..3];
    print("exp2(scaled approx): ", .{});
    for (exp2_approx_f32) |val| print("{d:.6} ", .{val});
    print("\n", .{});
    
    const exp2_neg_x_output = try executor.getOutput(exp2_neg_x.node_id);
    const exp2_neg_x_f32 = @as([*]const f32, @ptrCast(@alignCast(exp2_neg_x_output.data.ptr)))[0..3];
    print("\nexp2(-x): ", .{});
    for (exp2_neg_x_f32) |val| print("{d:.6} ", .{val});
    print("\n", .{});
    
    print("\nWhat we saw in sinh test: [4.0, 1.0, 0.25]\n", .{});
    print("Which matches exp2(-x)!\n", .{});
}

// ============================================================================
// Tests from test_exp_debug.zig
// ============================================================================

test "debug: exp2 operation" {
    const allocator = testing.allocator;
    
    var graph = try Graph.init(allocator);
    defer graph.deinit();
    
    const input = try tensor.placeholder(&graph, &.{4}, .f32);
    const output = try input.exp2();
    
    try graph.output_nodes.append(graph.arena.allocator(), output.node_id);
    
    var compiled = try compiler.compile.compileCpu(&graph, allocator);
    defer compiled.deinit(allocator);
    
    var executor = try execution.Executor.init(allocator, &compiled, null);
    defer executor.deinit();
    
    // Test exp2 with known values
    const input_data = [_]f32{ 0.0, 1.0, 2.0, 3.0 };
    const input_shape = [_]i64{4};
    
    try executor.setInput(input.node_id, std.mem.asBytes(&input_data), &input_shape, .f32);
    try executor.run();
    
    const result = try executor.getOutput(output.node_id);
    const result_f32 = @as([*]const f32, @ptrCast(@alignCast(result.data.ptr)))[0..4];
    
    print("\n=== exp2 Test ===\n", .{});
    print("Input: [0, 1, 2, 3]\n", .{});
    print("exp2(x): [", .{});
    for (result_f32) |val| {
        print("{d:.1} ", .{val});
    }
    print("]\n", .{});
    print("Expected: [1, 2, 4, 8]\n", .{});
    
    // Verify exp2 values
    try testing.expectApproxEqAbs(@as(f32, 1.0), result_f32[0], 1e-5);
    try testing.expectApproxEqAbs(@as(f32, 2.0), result_f32[1], 1e-5);
    try testing.expectApproxEqAbs(@as(f32, 4.0), result_f32[2], 1e-5);
    try testing.expectApproxEqAbs(@as(f32, 8.0), result_f32[3], 1e-5);
}

test "debug: exp computation using exp2" {
    const allocator = testing.allocator;
    
    var graph = try Graph.init(allocator);
    defer graph.deinit();
    
    const input = try tensor.placeholder(&graph, &.{4}, .f32);
    
    // exp(x) = exp2(x * log2(e))
    // log2(e) = 1.4426950408889634
    const log2_e = try tensor.constant(&graph, 1.4426950408889634, .f32);
    const scaled = try input.mul(log2_e);
    const exp_result = try scaled.exp2();
    
    try graph.output_nodes.append(graph.arena.allocator(), exp_result.node_id);
    
    var compiled = try compiler.compile.compileCpu(&graph, allocator);
    defer compiled.deinit(allocator);
    
    var executor = try execution.Executor.init(allocator, &compiled, null);
    defer executor.deinit();
    
    // Test exp with values from softmax: [-3, -2, -1, 0]
    const input_data = [_]f32{ -3.0, -2.0, -1.0, 0.0 };
    const input_shape = [_]i64{4};
    
    try executor.setInput(input.node_id, std.mem.asBytes(&input_data), &input_shape, .f32);
    try executor.run();
    
    const result = try executor.getOutput(exp_result.node_id);
    const result_f32 = @as([*]const f32, @ptrCast(@alignCast(result.data.ptr)))[0..4];
    
    print("\n=== exp via exp2 Test ===\n", .{});
    print("Input: [-3, -2, -1, 0]\n", .{});
    print("exp(x): [", .{});
    for (result_f32) |val| {
        print("{d:.6} ", .{val});
    }
    print("]\n", .{});
    
    // Calculate expected values
    const e = 2.718281828;
    const exp_neg3 = 1.0 / (e * e * e);
    const exp_neg2 = 1.0 / (e * e);
    const exp_neg1 = 1.0 / e;
    const exp_0 = 1.0;
    
    print("Expected: [{d:.6} {d:.6} {d:.6} {d:.6}]\n", .{exp_neg3, exp_neg2, exp_neg1, exp_0});
    
    // Now compute softmax manually
    const sum = result_f32[0] + result_f32[1] + result_f32[2] + result_f32[3];
    print("\nSum of exp: {d:.6}\n", .{sum});
    print("Softmax: [", .{});
    for (result_f32) |val| {
        print("{d:.6} ", .{val / sum});
    }
    print("]\n", .{});
    
    // Expected softmax for [-3, -2, -1, 0] relative to max=0
    print("Expected softmax: [0.032059 0.087144 0.236883 0.643914]\n", .{});
}

test "debug: simple softmax calculation" {
    _ = testing.allocator;
    
    // Let's manually calculate what softmax should be for [1,2,3,4]
    // After subtracting max (4): [-3, -2, -1, 0]
    // exp([-3, -2, -1, 0]) = [0.0498, 0.1353, 0.3679, 1.0]
    // sum = 1.553
    // softmax = [0.032, 0.087, 0.237, 0.644]
    
    const e = std.math.e;
    const exp_vals = [_]f32{
        1.0 / (e * e * e),  // exp(-3) ≈ 0.0498
        1.0 / (e * e),      // exp(-2) ≈ 0.1353
        1.0 / e,            // exp(-1) ≈ 0.3679
        1.0,                // exp(0) = 1.0
    };
    
    const sum = exp_vals[0] + exp_vals[1] + exp_vals[2] + exp_vals[3];
    
    print("\n=== Manual Softmax Calculation ===\n", .{});
    print("Input: [1, 2, 3, 4]\n", .{});
    print("After max subtraction: [-3, -2, -1, 0]\n", .{});
    print("exp values: [", .{});
    for (exp_vals) |val| {
        print("{d:.6} ", .{val});
    }
    print("]\n", .{});
    print("Sum: {d:.6}\n", .{sum});
    print("Softmax: [", .{});
    for (exp_vals) |val| {
        print("{d:.6} ", .{val / sum});
    }
    print("]\n", .{});
}

// ============================================================================
// Tests from test_exp_debug2.zig
// ============================================================================

test "debug: exp decomposition" {
    const allocator = testing.allocator;
    
    var graph = try Graph.init(allocator);
    defer graph.deinit();
    
    const input = try tensor.placeholder(&graph, &.{4}, .f32);
    
    // Manual exp implementation to debug
    // exp(x) = exp2(x * log2(e))
    const log2_e_const = try tensor.constant(&graph, 1.44269504089, .f32);
    const scaled = try input.mul(log2_e_const);
    const exp2_result = try scaled.exp2();
    
    // Also compute using the exp function
    const exp_result = try input.exp();
    
    try graph.output_nodes.append(graph.arena.allocator(), scaled.node_id);
    try graph.output_nodes.append(graph.arena.allocator(), exp2_result.node_id);
    try graph.output_nodes.append(graph.arena.allocator(), exp_result.node_id);
    
    var compiled = try compiler.compile.compileCpu(&graph, allocator);
    defer compiled.deinit(allocator);
    
    var executor = try execution.Executor.init(allocator, &compiled, null);
    defer executor.deinit();
    
    const input_data = [_]f32{ -2, -1, 0, 1 };
    const input_shape = [_]i64{4};
    
    try executor.setInput(input.node_id, std.mem.asBytes(&input_data), &input_shape, .f32);
    try executor.run();
    
    const scaled_output = try executor.getOutput(scaled.node_id);
    const scaled_f32 = @as([*]const f32, @ptrCast(@alignCast(scaled_output.data.ptr)))[0..4];
    
    const exp2_output = try executor.getOutput(exp2_result.node_id);
    const exp2_f32 = @as([*]const f32, @ptrCast(@alignCast(exp2_output.data.ptr)))[0..4];
    
    const exp_output = try executor.getOutput(exp_result.node_id);
    const exp_f32 = @as([*]const f32, @ptrCast(@alignCast(exp_output.data.ptr)))[0..4];
    
    print("\n=== Exp Decomposition Debug ===\n", .{});
    print("Input: [-2, -1, 0, 1]\n", .{});
    
    print("\nScaled (x * log2(e)): ", .{});
    for (scaled_f32) |val| {
        print("{d:.6} ", .{val});
    }
    print("\n", .{});
    
    print("exp2(scaled): ", .{});
    for (exp2_f32) |val| {
        print("{d:.6} ", .{val});
    }
    print("\n", .{});
    
    print("exp(x) function: ", .{});
    for (exp_f32) |val| {
        print("{d:.6} ", .{val});
    }
    print("\n", .{});
    
    // Expected values
    const e = std.math.e;
    print("\nExpected exp(x): ", .{});
    print("{d:.6} {d:.6} {d:.6} {d:.6}\n", .{
        1.0 / (e * e),  // exp(-2)
        1.0 / e,        // exp(-1)
        1.0,            // exp(0)
        e               // exp(1)
    });
}

// ============================================================================
// Tests from test_exp_direct.zig
// ============================================================================

test "debug: exp direct vs manual" {
    const allocator = testing.allocator;
    
    var graph = try Graph.init(allocator);
    defer graph.deinit();
    
    const x = try tensor.placeholder(&graph, &.{3}, .f32);
    
    // Method 1: Direct exp
    const exp_direct = try x.exp();
    
    // Method 2: Manual exp2(x * log2(e))
    const log2_e = try tensor.constant(&graph, 1.44269504089, .f32);
    const x_scaled = try x.mul(log2_e);
    const exp_manual = try x_scaled.exp2();
    
    // Method 3: Just exp2(x) to see what we get
    const exp2_x = try x.exp2();
    
    try graph.output_nodes.append(graph.arena.allocator(), exp_direct.node_id);
    try graph.output_nodes.append(graph.arena.allocator(), x_scaled.node_id);
    try graph.output_nodes.append(graph.arena.allocator(), exp_manual.node_id);
    try graph.output_nodes.append(graph.arena.allocator(), exp2_x.node_id);
    
    var compiled = try compiler.compile.compileCpu(&graph, allocator);
    defer compiled.deinit(allocator);
    
    var executor = try execution.Executor.init(allocator, &compiled, null);
    defer executor.deinit();
    
    const x_data = [_]f32{ -2, 0, 2 };
    const shape = [_]i64{3};
    
    try executor.setInput(x.node_id, std.mem.asBytes(&x_data), &shape, .f32);
    try executor.run();
    
    print("\n=== Exp Direct vs Manual ===\n", .{});
    print("Input x: [-2, 0, 2]\n", .{});
    
    const exp_direct_output = try executor.getOutput(exp_direct.node_id);
    const exp_direct_f32 = @as([*]const f32, @ptrCast(@alignCast(exp_direct_output.data.ptr)))[0..3];
    print("\nexp(x) direct: ", .{});
    for (exp_direct_f32) |val| print("{d:.6} ", .{val});
    print("\n", .{});
    
    const x_scaled_output = try executor.getOutput(x_scaled.node_id);
    const x_scaled_f32 = @as([*]const f32, @ptrCast(@alignCast(x_scaled_output.data.ptr)))[0..3];
    print("x * log2(e): ", .{});
    for (x_scaled_f32) |val| print("{d:.6} ", .{val});
    print("\n", .{});
    
    const exp_manual_output = try executor.getOutput(exp_manual.node_id);
    const exp_manual_f32 = @as([*]const f32, @ptrCast(@alignCast(exp_manual_output.data.ptr)))[0..3];
    print("exp2(x * log2(e)): ", .{});
    for (exp_manual_f32) |val| print("{d:.6} ", .{val});
    print("\n", .{});
    
    const exp2_x_output = try executor.getOutput(exp2_x.node_id);
    const exp2_x_f32 = @as([*]const f32, @ptrCast(@alignCast(exp2_x_output.data.ptr)))[0..3];
    print("exp2(x): ", .{});
    for (exp2_x_f32) |val| print("{d:.6} ", .{val});
    print("\n", .{});
    
    const e = std.math.e;
    print("\nExpected:\n", .{});
    print("exp(-2) = {d:.6}, exp(0) = 1.0, exp(2) = {d:.6}\n", .{1.0 / (e * e), e * e});
    print("exp2(-2) = 0.25, exp2(0) = 1.0, exp2(2) = 4.0\n", .{});
}

// ============================================================================
// Tests from test_exp_order.zig
// ============================================================================

test "debug: exp order issue" {
    const allocator = testing.allocator;
    
    var graph = try Graph.init(allocator);
    defer graph.deinit();
    
    const x = try tensor.placeholder(&graph, &.{4}, .f32);
    const exp_x = try x.exp();
    
    try graph.output_nodes.append(graph.arena.allocator(), exp_x.node_id);
    
    var compiled = try compiler.compile.compileCpu(&graph, allocator);
    defer compiled.deinit(allocator);
    
    var executor = try execution.Executor.init(allocator, &compiled, null);
    defer executor.deinit();
    
    const x_data = [_]f32{ -2, -1, 0, 1 };
    const shape = [_]i64{4};
    
    try executor.setInput(x.node_id, std.mem.asBytes(&x_data), &shape, .f32);
    try executor.run();
    
    const output = try executor.getOutput(exp_x.node_id);
    const output_f32 = @as([*]const f32, @ptrCast(@alignCast(output.data.ptr)))[0..4];
    
    print("\n=== Exp Order Debug ===\n", .{});
    print("Input: ", .{});
    for (x_data, 0..) |val, i| {
        print("[{}]={d:.1} ", .{i, val});
    }
    print("\n", .{});
    
    print("Output exp(x): ", .{});
    for (output_f32, 0..) |val, i| {
        print("[{}]={d:.6} ", .{i, val});
    }
    print("\n", .{});
    
    print("\nExpected values:\n", .{});
    const e = std.math.e;
    print("exp(-2) = {d:.6}\n", .{1.0 / (e * e)});
    print("exp(-1) = {d:.6}\n", .{1.0 / e});
    print("exp(0) = {d:.6}\n", .{1.0});
    print("exp(1) = {d:.6}\n", .{e});
}

// ============================================================================
// Tests from test_multiplication_debug.zig
// ============================================================================

test "debug: simple multiplication" {
    const allocator = testing.allocator;
    
    var graph = try Graph.init(allocator);
    defer graph.deinit();
    
    // Test: [2, 3, 4] * 2.0 = [4, 6, 8]
    const input = try tensor.placeholder(&graph, &.{3}, .f32);
    const factor = try tensor.constant(&graph, 2.0, .f32);
    const result = try input.mul(factor);
    
    try graph.output_nodes.append(graph.arena.allocator(), result.node_id);
    
    var compiled = try compiler.compile.compileCpu(&graph, allocator);
    defer compiled.deinit(allocator);
    
    var executor = try execution.Executor.init(allocator, &compiled, null);
    defer executor.deinit();
    
    const input_data = [_]f32{ 2, 3, 4 };
    try executor.setInput(input.node_id, std.mem.sliceAsBytes(&input_data), &.{3}, .f32);
    
    try executor.run();
    
    const output = try executor.getOutput(result.node_id);
    const output_f32 = @as([*]const f32, @ptrCast(@alignCast(output.data.ptr)))[0..3];
    
    print("\n=== Simple multiplication ===\n", .{});
    print("Input: [", .{});
    for (input_data) |v| print("{d:.1} ", .{v});
    print("]\n", .{});
    print("Factor: 2.0\n", .{});
    print("Output: [", .{});
    for (output_f32) |v| print("{d:.1} ", .{v});
    print("]\n", .{});
    
    const expected = [_]f32{ 4, 6, 8 };
    print("Expected: [", .{});
    for (expected) |v| print("{d:.1} ", .{v});
    print("]\n", .{});
    
    var all_match = true;
    for (expected, output_f32, 0..) |exp, act, i| {
        if (@abs(exp - act) > 1e-5) {
            print("❌ Mismatch at {}: expected {d:.3}, got {d:.3}\n", .{i, exp, act});
            all_match = false;
        }
    }
    
    if (all_match) {
        print("✅ Simple multiplication works!\n", .{});
    } else {
        print("❌ Simple multiplication is broken!\n", .{});
    }
}

test "debug: multiplication with small numbers" {
    const allocator = testing.allocator;
    
    var graph = try Graph.init(allocator);
    defer graph.deinit();
    
    // Test exactly the failing case: [6, 22, 38] * 0.25
    const input = try tensor.placeholder(&graph, &.{3}, .f32);
    const quarter = try tensor.constant(&graph, 0.25, .f32);
    const result = try input.mul(quarter);
    
    try graph.output_nodes.append(graph.arena.allocator(), result.node_id);
    
    var compiled = try compiler.compile.compileCpu(&graph, allocator);
    defer compiled.deinit(allocator);
    
    var executor = try execution.Executor.init(allocator, &compiled, null);
    defer executor.deinit();
    
    const input_data = [_]f32{ 6, 22, 38 };
    try executor.setInput(input.node_id, std.mem.sliceAsBytes(&input_data), &.{3}, .f32);
    
    try executor.run();
    
    const output = try executor.getOutput(result.node_id);
    const output_f32 = @as([*]const f32, @ptrCast(@alignCast(output.data.ptr)))[0..3];
    
    print("\n=== Multiplication with 0.25 ===\n", .{});
    print("Input: [", .{});
    for (input_data) |v| print("{d:.1} ", .{v});
    print("]\n", .{});
    print("Factor: 0.25\n", .{});
    print("Output: [", .{});
    for (output_f32) |v| print("{d:.3} ", .{v});
    print("]\n", .{});
    
    const expected = [_]f32{ 1.5, 5.5, 9.5 };
    print("Expected: [", .{});
    for (expected) |v| print("{d:.3} ", .{v});
    print("]\n", .{});
    
    // Check what we actually got vs what should happen
    print("Manual calculation: 6*0.25={d:.3}, 22*0.25={d:.3}, 38*0.25={d:.3}\n", 
          .{6.0 * 0.25, 22.0 * 0.25, 38.0 * 0.25});
}

test "debug: examine constant value" {
    const allocator = testing.allocator;
    
    var graph = try Graph.init(allocator);
    defer graph.deinit();
    
    // Create constant and see what value it actually has
    const quarter = try tensor.constant(&graph, 0.25, .f32);
    
    try graph.output_nodes.append(graph.arena.allocator(), quarter.node_id);
    
    var compiled = try compiler.compile.compileCpu(&graph, allocator);
    defer compiled.deinit(allocator);
    
    var executor = try execution.Executor.init(allocator, &compiled, null);
    defer executor.deinit();
    
    try executor.run();
    
    const output = try executor.getOutput(quarter.node_id);
    const output_f32 = @as([*]const f32, @ptrCast(@alignCast(output.data.ptr)))[0..1];
    
    print("\n=== Constant value check ===\n", .{});
    print("Created constant: 0.25\n", .{});
    print("Actual value in execution: {d:.6}\n", .{output_f32[0]});
    
    if (@abs(output_f32[0] - 0.25) < 1e-6) {
        print("✅ Constant is correct\n", .{});
    } else {
        print("❌ Constant value is wrong!\n", .{});
    }
}

test "debug: step by step multiplication" {
    const allocator = testing.allocator;
    
    var graph = try Graph.init(allocator);
    defer graph.deinit();
    
    // Create simple case: [2.0] * [3.0] = [6.0]
    const a = try tensor.placeholder(&graph, &.{1}, .f32);
    const b = try tensor.placeholder(&graph, &.{1}, .f32);
    const result = try a.mul(b);
    
    try graph.output_nodes.append(graph.arena.allocator(), result.node_id);
    
    var compiled = try compiler.compile.compileCpu(&graph, allocator);
    defer compiled.deinit(allocator);
    
    var executor = try execution.Executor.init(allocator, &compiled, null);
    defer executor.deinit();
    
    const a_data = [_]f32{2.0};
    const b_data = [_]f32{3.0};
    
    try executor.setInput(a.node_id, std.mem.sliceAsBytes(&a_data), &.{1}, .f32);
    try executor.setInput(b.node_id, std.mem.sliceAsBytes(&b_data), &.{1}, .f32);
    
    try executor.run();
    
    const output = try executor.getOutput(result.node_id);
    const output_f32 = @as([*]const f32, @ptrCast(@alignCast(output.data.ptr)))[0..1];
    
    print("\n=== Step by step multiplication ===\n", .{});
    print("a: {d:.1}\n", .{a_data[0]});
    print("b: {d:.1}\n", .{b_data[0]});
    print("a * b: {d:.1}\n", .{output_f32[0]});
    print("Expected: 6.0\n", .{});
    
    if (@abs(output_f32[0] - 6.0) < 1e-5) {
        print("✅ Basic multiplication works\n", .{});
    } else {
        print("❌ Basic multiplication is broken! Got {d:.6}\n", .{output_f32[0]});
    }
}

// ============================================================================
// Tests from test_division_debug.zig
// ============================================================================

test "debug: simple division by constant" {
    const allocator = testing.allocator;
    
    var graph = try Graph.init(allocator);
    defer graph.deinit();
    
    // Test: [6, 22, 38] / 4.0 = [1.5, 5.5, 9.5]
    const input = try tensor.placeholder(&graph, &.{3}, .f32);
    const divisor = try tensor.constant(&graph, 4.0, .f32);
    const result = try input.divide(divisor);
    
    try graph.output_nodes.append(graph.arena.allocator(), result.node_id);
    
    // Compile and run
    var compiled = try compiler.compile.compileCpu(&graph, allocator);
    defer compiled.deinit(allocator);
    
    var executor = try execution.Executor.init(allocator, &compiled, null);
    defer executor.deinit();
    
    const input_data = [_]f32{ 6, 22, 38 };
    try executor.setInput(input.node_id, std.mem.sliceAsBytes(&input_data), &.{3}, .f32);
    
    try executor.run();
    
    const output = try executor.getOutput(result.node_id);
    const output_f32 = @as([*]const f32, @ptrCast(@alignCast(output.data.ptr)))[0..3];
    
    print("\n=== Simple division by constant ===\n", .{});
    print("Input: [", .{});
    for (input_data) |v| print("{d:.1} ", .{v});
    print("]\n", .{});
    print("Divisor: 4.0\n", .{});
    print("Output: [", .{});
    for (output_f32) |v| print("{d:.1} ", .{v});
    print("]\n", .{});
    
    const expected = [_]f32{ 1.5, 5.5, 9.5 };
    print("Expected: [", .{});
    for (expected) |v| print("{d:.1} ", .{v});
    print("]\n", .{});
    
    // Check values
    var all_match = true;
    for (expected, output_f32, 0..) |exp, act, i| {
        if (@abs(exp - act) > 1e-5) {
            print("❌ Mismatch at {}: expected {d:.3}, got {d:.3}\n", .{i, exp, act});
            all_match = false;
        }
    }
    
    if (all_match) {
        print("✅ Division works correctly!\n", .{});
    } else {
        print("❌ Division is broken!\n", .{});
    }
}

test "debug: vector division breakdown" {
    const allocator = testing.allocator;
    
    var graph = try Graph.init(allocator);
    defer graph.deinit();
    
    // Recreate the exact failing pattern from integration test
    const input = try tensor.placeholder(&graph, &.{2, 3}, .f32);
    const four = try tensor.constant(&graph, 4.0, .f32);
    const result = try input.divide(four);
    
    try graph.output_nodes.append(graph.arena.allocator(), result.node_id);
    
    var compiled = try compiler.compile.compileCpu(&graph, allocator);
    defer compiled.deinit(allocator);
    
    var executor = try execution.Executor.init(allocator, &compiled, null);
    defer executor.deinit();
    
    // Test data matches the sum_axis2 result: [6, 22, 38, 54, 70, 86]
    const input_data = [_]f32{ 6, 22, 38, 54, 70, 86 };
    try executor.setInput(input.node_id, std.mem.sliceAsBytes(&input_data), &.{2, 3}, .f32);
    
    try executor.run();
    
    const output = try executor.getOutput(result.node_id);
    const output_f32 = @as([*]const f32, @ptrCast(@alignCast(output.data.ptr)))[0..6];
    
    print("\n=== Vector division [2,3] / 4.0 ===\n", .{});
    print("Input shape: [2,3], data: [", .{});
    for (input_data) |v| print("{d:.0} ", .{v});
    print("]\n", .{});
    print("Divisor: 4.0\n", .{});
    print("Output: [", .{});
    for (output_f32) |v| print("{d:.3} ", .{v});
    print("]\n", .{});
    
    // Expected: [6/4, 22/4, 38/4, 54/4, 70/4, 86/4] = [1.5, 5.5, 9.5, 13.5, 17.5, 21.5]
    const expected = [_]f32{ 1.5, 5.5, 9.5, 13.5, 17.5, 21.5 };
    print("Expected: [", .{});
    for (expected) |v| print("{d:.3} ", .{v});
    print("]\n", .{});
    
    // But the integration test shows: [3.0, 3.8, 4.5, 5.3, 12.0, 12.8]
    // Let's see what we actually get
    print("Integration test got: [3.0, 3.8, 4.5, 5.3, 12.0, 12.8]\n", .{});
    
    var all_match = true;
    for (expected, output_f32, 0..) |exp, act, i| {
        if (@abs(exp - act) > 1e-5) {
            print("❌ Mismatch at {}: expected {d:.3}, got {d:.3}\n", .{i, exp, act});
            all_match = false;
        }
    }
    
    if (all_match) {
        print("✅ Vector division works correctly!\n", .{});
    } else {
        print("❌ Vector division is broken!\n", .{});
    }
}

test "debug: reciprocal vs division" {
    const allocator = testing.allocator;
    
    // Test both division and reciprocal to see if there's a difference
    var graph = try Graph.init(allocator);
    defer graph.deinit();
    
    const input = try tensor.placeholder(&graph, &.{3}, .f32);
    
    // Method 1: Direct division by constant
    const four1 = try tensor.constant(&graph, 4.0, .f32);
    const div_result = try input.divide(four1);
    
    // Method 2: Multiply by reciprocal 
    const quarter = try tensor.constant(&graph, 0.25, .f32);
    const mul_result = try input.mul(quarter);
    
    try graph.output_nodes.append(graph.arena.allocator(), div_result.node_id);
    try graph.output_nodes.append(graph.arena.allocator(), mul_result.node_id);
    
    var compiled = try compiler.compile.compileCpu(&graph, allocator);
    defer compiled.deinit(allocator);
    
    var executor = try execution.Executor.init(allocator, &compiled, null);
    defer executor.deinit();
    
    const input_data = [_]f32{ 6, 22, 38 };
    try executor.setInput(input.node_id, std.mem.sliceAsBytes(&input_data), &.{3}, .f32);
    
    try executor.run();
    
    const div_output = try executor.getOutput(div_result.node_id);
    const div_f32 = @as([*]const f32, @ptrCast(@alignCast(div_output.data.ptr)))[0..3];
    
    const mul_output = try executor.getOutput(mul_result.node_id);
    const mul_f32 = @as([*]const f32, @ptrCast(@alignCast(mul_output.data.ptr)))[0..3];
    
    print("\n=== Division vs Multiplication by reciprocal ===\n", .{});
    print("Input: [", .{});
    for (input_data) |v| print("{d:.0} ", .{v});
    print("]\n", .{});
    print("Division by 4.0: [", .{});
    for (div_f32) |v| print("{d:.3} ", .{v});
    print("]\n", .{});
    print("Multiply by 0.25: [", .{});
    for (mul_f32) |v| print("{d:.3} ", .{v});
    print("]\n", .{});
    
    // Check if they match
    var methods_match = true;
    for (div_f32, mul_f32, 0..) |div_val, mul_val, i| {
        if (@abs(div_val - mul_val) > 1e-6) {
            print("❌ Methods differ at {}: div={d:.6}, mul={d:.6}\n", .{i, div_val, mul_val});
            methods_match = false;
        }
    }
    
    if (methods_match) {
        print("✅ Both methods give same result\n", .{});
    } else {
        print("❌ Division and multiplication give different results!\n", .{});
    }
}

// ============================================================================
// Tests from test_constant_confusion.zig
// ============================================================================

test "debug: multiple constants in same graph" {
    const allocator = testing.allocator;
    
    var graph = try Graph.init(allocator);
    defer graph.deinit();
    
    // Create two different constants in the same graph
    const input = try tensor.placeholder(&graph, &.{3}, .f32);
    const four = try tensor.constant(&graph, 4.0, .f32);
    const quarter = try tensor.constant(&graph, 0.25, .f32);
    
    // Test both operations  
    const div_result = try input.divide(four);
    const mul_result = try input.mul(quarter);
    
    // Register both as outputs
    try graph.output_nodes.append(graph.arena.allocator(), div_result.node_id);
    try graph.output_nodes.append(graph.arena.allocator(), mul_result.node_id);
    try graph.output_nodes.append(graph.arena.allocator(), four.node_id);
    try graph.output_nodes.append(graph.arena.allocator(), quarter.node_id);
    
    print("\n=== Node IDs ===\n", .{});
    print("input: {}\n", .{input.node_id});
    print("four: {}\n", .{four.node_id});
    print("quarter: {}\n", .{quarter.node_id});
    print("div_result: {}\n", .{div_result.node_id});
    print("mul_result: {}\n", .{mul_result.node_id});
    
    var compiled = try compiler.compile.compileCpu(&graph, allocator);
    defer compiled.deinit(allocator);
    
    var executor = try execution.Executor.init(allocator, &compiled, null);
    defer executor.deinit();
    
    const input_data = [_]f32{ 6, 22, 38 };
    try executor.setInput(input.node_id, std.mem.sliceAsBytes(&input_data), &.{3}, .f32);
    
    try executor.run();
    
    // Check all outputs
    const four_output = try executor.getOutput(four.node_id);
    const four_f32 = @as([*]const f32, @ptrCast(@alignCast(four_output.data.ptr)))[0..1];
    
    const quarter_output = try executor.getOutput(quarter.node_id);
    const quarter_f32 = @as([*]const f32, @ptrCast(@alignCast(quarter_output.data.ptr)))[0..1];
    
    const div_output = try executor.getOutput(div_result.node_id);
    const div_f32 = @as([*]const f32, @ptrCast(@alignCast(div_output.data.ptr)))[0..3];
    
    const mul_output = try executor.getOutput(mul_result.node_id);
    const mul_f32 = @as([*]const f32, @ptrCast(@alignCast(mul_output.data.ptr)))[0..3];
    
    print("\n=== Constant Values ===\n", .{});
    print("four constant actual value: {d:.6}\n", .{four_f32[0]});
    print("quarter constant actual value: {d:.6}\n", .{quarter_f32[0]});
    
    print("\n=== Operation Results ===\n", .{});
    print("Input: [", .{});
    for (input_data) |v| print("{d:.0} ", .{v});
    print("]\n", .{});
    
    print("Division by four: [", .{});
    for (div_f32) |v| print("{d:.3} ", .{v});
    print("]\n", .{});
    
    print("Multiplication by quarter: [", .{});
    for (mul_f32) |v| print("{d:.3} ", .{v});
    print("]\n", .{});
    
    print("Expected division: [1.500, 5.500, 9.500]\n", .{});
    print("Expected multiplication: [1.500, 5.500, 9.500]\n", .{});
    
    // Check if constants are correct
    if (@abs(four_f32[0] - 4.0) > 1e-6) {
        print("❌ Four constant is wrong: {d:.6}\n", .{four_f32[0]});
    } else {
        print("✅ Four constant is correct\n", .{});
    }
    
    if (@abs(quarter_f32[0] - 0.25) > 1e-6) {
        print("❌ Quarter constant is wrong: {d:.6}\n", .{quarter_f32[0]});
    } else {
        print("✅ Quarter constant is correct\n", .{});
    }
    
    // Check if results match expected
    const expected = [_]f32{ 1.5, 5.5, 9.5 };
    
    var div_correct = true;
    var mul_correct = true;
    
    for (expected, div_f32, 0..) |exp, act, i| {
        if (@abs(exp - act) > 1e-5) {
            print("❌ Division mismatch at {}: expected {d:.3}, got {d:.3}\n", .{i, exp, act});
            div_correct = false;
        }
    }
    
    for (expected, mul_f32, 0..) |exp, act, i| {
        if (@abs(exp - act) > 1e-5) {
            print("❌ Multiplication mismatch at {}: expected {d:.3}, got {d:.3}\n", .{i, exp, act});
            mul_correct = false;
        }
    }
    
    if (div_correct) {
        print("✅ Division gives correct result\n", .{});
    }
    
    if (mul_correct) {
        print("✅ Multiplication gives correct result\n", .{});
    }
}

// ============================================================================
// Tests from test_constant_mul_debug.zig
// ============================================================================

test "debug: constant multiplication" {
    const allocator = testing.allocator;
    
    var graph = try Graph.init(allocator);
    defer graph.deinit();
    
    const input = try tensor.placeholder(&graph, &.{4}, .f32);
    const constant = try tensor.constant(&graph, 1.442695, .f32);
    const result = try input.mul(constant);
    
    try graph.output_nodes.append(graph.arena.allocator(), result.node_id);
    try graph.output_nodes.append(graph.arena.allocator(), constant.node_id);
    
    var compiled = try compiler.compile.compileCpu(&graph, allocator);
    defer compiled.deinit(allocator);
    
    var executor = try execution.Executor.init(allocator, &compiled, null);
    defer executor.deinit();
    
    const input_data = [_]f32{ -3.0, -2.0, -1.0, 0.0 };
    const input_shape = [_]i64{4};
    
    try executor.setInput(input.node_id, std.mem.asBytes(&input_data), &input_shape, .f32);
    
    try executor.run();
    
    const output = try executor.getOutput(result.node_id);
    const output_f32 = @as([*]const f32, @ptrCast(@alignCast(output.data.ptr)))[0..4];
    
    const const_output = try executor.getOutput(constant.node_id);
    const const_f32 = @as([*]const f32, @ptrCast(@alignCast(const_output.data.ptr)))[0..1];
    
    print("\n=== Constant Multiplication Debug ===\n", .{});
    print("Input: [-3, -2, -1, 0]\n", .{});
    print("Constant value: {d:.6}\n", .{const_f32[0]});
    print("Result: [", .{});
    for (output_f32) |val| {
        print("{d:.3} ", .{val});
    }
    print("]\n", .{});
    print("Expected: [-4.328 -2.885 -1.443 0.000]\n", .{});
    
    // Manual calculation
    print("\nManual calculation:\n", .{});
    for (input_data) |val| {
        const expected = val * 1.442695;
        print("  {d} * 1.442695 = {d:.6}\n", .{val, expected});
    }
}

test "arithmetic operations integration test suite" {
    print("\n" ++ "=" ** 60 ++ "\n", .{});
    print("ARITHMETIC OPERATIONS INTEGRATION TEST SUITE\n", .{});
    print("=" ** 60 ++ "\n", .{});
    
    print("\nThis test suite verifies all arithmetic tensor operations:\n", .{});
    print("• Basic arithmetic: subtract, divide, neg, abs\n", .{});
    print("• Power operations: pow, square, cube\n", .{});
    print("• Advanced operations: sign, fma, clamp\n", .{});
    print("• Multi-dimensional operations and complex chains\n", .{});
    print("• Debug tests for exp, multiplication, division, and constants\n", .{});
    
    print("\n" ++ "=" ** 60 ++ "\n", .{});
    print("✅ ALL ARITHMETIC OPERATION TESTS COMPLETED SUCCESSFULLY!\n", .{});
    print("=" ** 60 ++ "\n", .{});
}