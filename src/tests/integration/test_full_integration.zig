/// Zing Integration Tests
/// 
/// Complete end-to-end tests that demonstrate the full Zing pipeline:
/// Graph Creation → Compilation → Execution → Results

const std = @import("std");
const testing = std.testing;
const print = std.debug.print;

// Import Zing core components
const types = @import("types");
const Graph = @import("graph").Graph;
const TensorHandle = @import("tensor").TensorHandle;
const tensor = @import("tensor");
const compile = @import("compiler").compile;
const shape_mod = @import("shape");
const execution = @import("execution");
const NodeId = types.NodeId;
const DataType = types.DataType;

test "Complete matrix multiplication integration test" {
    const allocator = testing.allocator;
    
    print("\n=== COMPLETE ZING INTEGRATION TEST ===\n", .{});
    print("Testing: Graph Creation → Compilation → Execution → Results\n", .{});
    
    // ===== STEP 1: Create Computation Graph =====
    print("\nStep 1: Creating computation graph...\n", .{});
    
    var graph = try Graph.init(allocator);
    // NOTE: Graph must be deinitialized LAST since CompiledGraph and Executor reference it
    
    // Create 2x2 matrix placeholders
    const matrix_a = try tensor.placeholder(&graph, &.{ 2, 2 }, .f32);
    const matrix_b = try tensor.placeholder(&graph, &.{ 2, 2 }, .f32);
    
    print("  ✓ Matrix A: node {} [2x2]\n", .{matrix_a.node_id});
    print("  ✓ Matrix B: node {} [2x2]\n", .{matrix_b.node_id});
    
    // Create matrix multiplication
    print("  Creating matmul operation...\n", .{});
    print("  matrix_a shape: dims={}, strides={}\n", .{matrix_a.shape.dims.len, matrix_a.shape.strides.len});
    print("  matrix_b shape: dims={}, strides={}\n", .{matrix_b.shape.dims.len, matrix_b.shape.strides.len});
    
    // Remove debug operations that interfere with execution
    // const simple_mul = try matrix_a.mul(matrix_b);
    // print("  DEBUG: simple mul created node_id={}\n", .{simple_mul.node_id});
    // const simple_sum = try simple_mul.sumReduce(0);
    // print("  DEBUG: simple sum created node_id={}\n", .{simple_sum.node_id});
    
    const result_matrix = try matrix_a.matmul(matrix_b);
    print("  Matmul created, result node_id={}\n", .{result_matrix.node_id});
    
    // Mark the result as a graph output to prevent dead code elimination
    try graph.output_nodes.append(graph.arena.allocator(), result_matrix.node_id);
    
    print("  ✓ Result matrix: node {}\n", .{result_matrix.node_id});
    print("  ✓ Graph contains {} nodes total\n", .{graph.nodes.items.len});
    
    // Debug: print all nodes in the graph
    print("\n  Graph nodes:\n", .{});
    for (graph.nodes.items, 0..) |node, i| {
        if (node.is_valid) {
            print("    Node {}: spec={any}\n", .{i, node.spec});
            if (node.spec == .compute) {
                print("      Inputs: {any}\n", .{node.inputs});
            }
            if (node.metadata) |metadata| {
                if (metadata.output_shape) |output_shape| {
                    print("      Output shape: dims=", .{});
                    for (output_shape.dims) |d| print("{} ", .{d.concrete});
                    print("\n", .{});
                }
            }
        }
    }
    
    // Also show node substitutions if any
    print("\n  Node substitutions:\n", .{});
    var it = graph.substitution_map.iterator();
    while (it.next()) |entry| {
        print("    Node {} -> {}\n", .{entry.key_ptr.*, entry.value_ptr.*});
    }
    
    
    
    // ===== STEP 2: Backends are now compile-time =====
    print("\nStep 2: Backend selection (compile-time)...\n", .{});
    print("  ✓ CPU backend ready (no registration needed)\n", .{});
    
    // ===== STEP 3: Compile Graph =====
    print("\nStep 3: Compiling computation graph...\n", .{});
    print("  (Using simplified compilation API that extracts shapes from TensorHandles)\n", .{});
    
    print("  Requesting output: node {} with shape [{}, {}]\n", .{
        result_matrix.node_id, 
        result_matrix.shape.dims[0].concrete,
        result_matrix.shape.dims[1].concrete
    });
    
    // Compile with CPU backend (runs optimization passes internally)
    var compiled = try compile.compileCpu(result_matrix.graph, allocator);
    
    print("  ✓ Compilation successful!\n", .{});
    print("  ✓ Execution steps: {}\n", .{compiled.steps.len});
    print("  ✓ Memory required: {} bytes\n", .{compiled.memory_plan.total_memory});
    
    // ===== STEP 4: Create Executor =====
    print("\nStep 4: Creating executor...\n", .{});
    
    var executor = try execution.Executor.init(allocator, &compiled, null);
    
    print("  ✓ Executor ready for execution\n", .{});
    
    // ===== STEP 5: Prepare Test Data =====
    print("\nStep 5: Preparing test matrices...\n", .{});
    
    // Test matrices:
    // A = [[1, 2],     B = [[5, 6],
    //      [3, 4]]          [7, 8]]
    //
    // Expected A @ B = [[1*5+2*7, 1*6+2*8],   = [[19, 22],
    //                   [3*5+4*7, 3*6+4*8]]      [43, 50]]
    
    const matrix_a_data = [_]f32{ 1.0, 2.0, 3.0, 4.0 };
    const matrix_b_data = [_]f32{ 5.0, 6.0, 7.0, 8.0 };
    const shape_2x2 = [_]i64{ 2, 2 };
    
    print("  Input A = [[1, 2], [3, 4]]\n", .{});
    print("  Input B = [[5, 6], [7, 8]]\n", .{});
    print("  Expected A @ B = [[19, 22], [43, 50]]\n", .{});
    
    // Set input data
    try executor.setInput(
        matrix_a.node_id,
        std.mem.asBytes(&matrix_a_data),
        &shape_2x2,
        .f32
    );
    try executor.setInput(
        matrix_b.node_id,
        std.mem.asBytes(&matrix_b_data),
        &shape_2x2,
        .f32
    );
    
    print("  ✓ Input data loaded into executor\n", .{});
    
    // ===== STEP 6: EXECUTE THE COMPUTATION =====
    print("\nStep 6: EXECUTING matrix multiplication...\n", .{});
    
    const start_time = std.time.nanoTimestamp();
    try executor.run();
    const end_time = std.time.nanoTimestamp();
    
    const execution_time_us = @as(f64, @floatFromInt(end_time - start_time)) / 1000.0;
    print("  ✓ Execution completed in {d:.2} μs\n", .{execution_time_us});
    
    // ===== STEP 7: RETRIEVE AND VERIFY RESULTS =====
    print("\nStep 7: Retrieving and verifying results...\n", .{});
    
    // Get output from the original result matrix node
    print("  Trying to get output from node {}\n", .{result_matrix.node_id});
    const output = try executor.getOutput(result_matrix.node_id);
    
    // Cast output data to f32 slice
    const output_f32 = @as([*]const f32, @ptrCast(@alignCast(output.data.ptr)))[0..4];
    
    // Show the actual results
    print("  ===== ZING MATRIX MULTIPLICATION RESULTS =====\n", .{});
    print("  A @ B = [[{d:.0}, {d:.0}],\n", .{ output_f32[0], output_f32[1] });
    print("           [{d:.0}, {d:.0}]]\n", .{ output_f32[2], output_f32[3] });
    
    // Verify correctness
    const expected_result = [_]f32{ 19.0, 22.0, 43.0, 50.0 };
    
    print("  Verification: ", .{});
    var all_correct = true;
    for (output_f32, expected_result, 0..) |actual, expected, i| {
        const error_abs = @abs(actual - expected);
        if (error_abs > 1e-6) {
            print("\n  ❌ ERROR at position {}: got {d:.6}, expected {d:.6}", .{ i, actual, expected });
            all_correct = false;
        }
    }
    
    if (all_correct) {
        print("✅ PERFECT!\n", .{});
    } else {
        print("\n  ❌ FAILED!\n", .{});
        std.log.err("Matrix multiplication integration test failed - incorrect results", .{});
        return error.TestFailed;
    }
    
    // Use testing.expect for proper test result
    try testing.expect(all_correct);
    
    // ===== FINAL SUMMARY =====
    print("\n=== COMPLETE INTEGRATION TEST PASSED! ===\n", .{});
    print("✅ Successfully demonstrated FULL Zing pipeline:\n", .{});
    print("  • Graph creation with tensor operations ✓\n", .{});
    print("  • CPU backend compilation ✓\n", .{});
    print("  • Graph execution ✓\n", .{});
    print("  • Correct numerical results ✓\n", .{});
    print("  • Performance: {d:.2} μs execution time ✓\n", .{execution_time_us});
    
    const theoretical_ops = 16.0; // 2x2 @ 2x2 = 8 multiply-adds = 16 ops
    const gflops = theoretical_ops / (execution_time_us / 1_000_000.0) / 1_000_000_000.0;
    print("  • Throughput: {d:.3} GFLOPS ✓\n", .{gflops});
    
    print("\n🎉 ZING CPU BACKEND MATRIX MULTIPLICATION WORKING! 🎉\n", .{});
    
    // Cleanup in order that respects memory ownership
    // CompiledGraph borrows references from Graph, so free CompiledGraph BEFORE Graph
    const enable_debug = @import("build_options").enable_debug_logs;
    
    if (enable_debug) {
        print("DEBUG: Starting cleanup sequence...\n", .{});
        print("DEBUG: About to call executor.deinit()...\n", .{});
    }
    executor.deinit();
    if (enable_debug) {
        print("DEBUG: ✓ executor.deinit() completed\n", .{});
        print("DEBUG: About to call compiled.deinit(allocator)...\n", .{});
    }
    compiled.deinit(allocator);  // Free CompiledGraph first (it has borrowed references)
    if (enable_debug) {
        print("DEBUG: ✓ compiled.deinit(allocator) completed\n", .{});
        print("DEBUG: About to call graph.deinit()...\n", .{});
    }
    graph.deinit();              // Free Graph last (it owns the memory)
    if (enable_debug) {
        print("DEBUG: ✓ graph.deinit() completed\n", .{});
        print("DEBUG: All cleanup completed successfully!\n", .{});
    }
}

test "Matrix multiplication with different values" {
    const allocator = testing.allocator;
    
    print("\n=== ZING INTEGRATION TEST: DIFFERENT VALUES ===\n", .{});
    
    var graph = try Graph.init(allocator);
    
    const matrix_a = try tensor.placeholder(&graph, &.{ 2, 2 }, .f32);
    const matrix_b = try tensor.placeholder(&graph, &.{ 2, 2 }, .f32);
    const result_matrix = try matrix_a.matmul(matrix_b);
    
    // Mark the result as a graph output to prevent dead code elimination
    try graph.output_nodes.append(graph.arena.allocator(), result_matrix.node_id);
    
    // Compile using new unified system
    var compiled = try compile.compileCpu(result_matrix.graph, allocator);
    
    var executor = try execution.Executor.init(allocator, &compiled, null);
    
    // Test with different values: A = [[10, 20], [30, 40]], B = [[0.1, 0.2], [0.3, 0.4]]
    // Expected: [[10*0.1+20*0.3, 10*0.2+20*0.4], [30*0.1+40*0.3, 30*0.2+40*0.4]]
    //         = [[1+6, 2+8], [3+12, 6+16]] = [[7, 10], [15, 22]]
    
    const matrix_a_data = [_]f32{ 10.0, 20.0, 30.0, 40.0 };
    const matrix_b_data = [_]f32{ 0.1, 0.2, 0.3, 0.4 };
    const shape_2x2 = [_]i64{ 2, 2 };
    
    try executor.setInput(matrix_a.node_id, std.mem.asBytes(&matrix_a_data), &shape_2x2, .f32);
    try executor.setInput(matrix_b.node_id, std.mem.asBytes(&matrix_b_data), &shape_2x2, .f32);
    
    try executor.run();
    
    // Get output from the original result matrix node
    const output = try executor.getOutput(result_matrix.node_id);
    const output_f32 = @as([*]const f32, @ptrCast(@alignCast(output.data.ptr)))[0..4];
    
    print("A = [[10, 20], [30, 40]], B = [[0.1, 0.2], [0.3, 0.4]]\n", .{});
    print("Result = [[{d:.0}, {d:.0}], [{d:.0}, {d:.0}]]\n", .{ output_f32[0], output_f32[1], output_f32[2], output_f32[3] });
    
    const expected_result = [_]f32{ 7.0, 10.0, 15.0, 22.0 };
    for (output_f32, expected_result) |actual, expected| {
        try testing.expectApproxEqAbs(expected, actual, 1e-6);
    }
    
    print("✅ Different values test passed!\n", .{});
    
    // Cleanup in order that respects memory ownership
    const enable_debug = @import("build_options").enable_debug_logs;
    
    if (enable_debug) {
        print("DEBUG: Test 2 - Starting cleanup sequence...\n", .{});
        print("DEBUG: Test 2 - About to call executor.deinit()...\n", .{});
    }
    executor.deinit();
    if (enable_debug) {
        print("DEBUG: Test 2 - ✓ executor.deinit() completed\n", .{});
        print("DEBUG: Test 2 - About to call compiled.deinit(allocator)...\n", .{});
    }
    compiled.deinit(allocator);  // Free CompiledGraph first (it has borrowed references)
    if (enable_debug) {
        print("DEBUG: Test 2 - ✓ compiled.deinit(allocator) completed\n", .{});
        print("DEBUG: Test 2 - About to call graph.deinit()...\n", .{});
    }
    graph.deinit();              // Free Graph last (it owns the memory)
    if (enable_debug) {
        print("DEBUG: Test 2 - ✓ graph.deinit() completed\n", .{});
        print("DEBUG: Test 2 - All cleanup completed successfully!\n", .{});
    }
}

test "matrix multiplication demo" {
    const allocator = testing.allocator;
    
    print("\n=== MATRIX MULTIPLICATION DEMO ===\n", .{});
    print("This demo shows how to use Zing for matrix multiplication\n\n", .{});
    
    // Step 1: Create computation graph
    var graph = try Graph.init(allocator);
    
    print("Step 1: Create input placeholders\n", .{});
    const matrix_a = try tensor.placeholder(&graph, &.{ 2, 2 }, .f32);
    const matrix_b = try tensor.placeholder(&graph, &.{ 2, 2 }, .f32);
    print("  ✓ Created matrix A placeholder (2x2)\n", .{});
    print("  ✓ Created matrix B placeholder (2x2)\n", .{});
    
    print("\nStep 2: Define matrix multiplication operation\n", .{});
    const result = try tensor.matmul(matrix_a, matrix_b);
    print("  ✓ Created matmul operation: C = A @ B\n", .{});
    
    // Mark the result as a graph output to prevent dead code elimination
    try graph.output_nodes.append(graph.arena.allocator(), result.node_id);
    
    print("\nStep 3: Compile the computation graph\n", .{});
    var compiled = try compile.compileCpu(result.graph, allocator);
    print("  ✓ Graph compiled with optimizations\n", .{});
    
    print("\nStep 4: Create executor and run computation\n", .{});
    var executor = try execution.Executor.init(allocator, &compiled, null);
    
    // Example 1: Simple integers
    print("\nExample 1: Simple integer matrices\n", .{});
    const a_data = [_]f32{ 1.0, 2.0, 3.0, 4.0 };
    const b_data = [_]f32{ 5.0, 6.0, 7.0, 8.0 };
    const shape = [_]i64{ 2, 2 };
    
    print("  A = [[1, 2],     B = [[5, 6],\n", .{});
    print("       [3, 4]]          [7, 8]]\n", .{});
    
    try executor.setInput(matrix_a.node_id, std.mem.asBytes(&a_data), &shape, .f32);
    try executor.setInput(matrix_b.node_id, std.mem.asBytes(&b_data), &shape, .f32);
    try executor.run();
    
    // Get output from the result node
    const output = try executor.getOutput(result.node_id);
    const result_data = @as([*]const f32, @ptrCast(@alignCast(output.data.ptr)))[0..4];
    
    print("\n  C = A @ B = [[{d:.0}, {d:.0}],\n", .{ result_data[0], result_data[1] });
    print("               [{d:.0}, {d:.0}]]\n", .{ result_data[2], result_data[3] });
    
    // Verify
    const expected = [_]f32{ 19.0, 22.0, 43.0, 50.0 };
    for (result_data, expected) |actual, exp| {
        try testing.expectApproxEqAbs(exp, actual, 1e-6);
    }
    print("  ✓ Results verified!\n", .{});
    
    // Example 2: Decimal values
    print("\nExample 2: Decimal matrices\n", .{});
    const a_data2 = [_]f32{ 1.5, 2.5, 3.5, 4.5 };
    const b_data2 = [_]f32{ 0.5, 1.0, 1.5, 2.0 };
    
    print("  A = [[1.5, 2.5],   B = [[0.5, 1.0],\n", .{});
    print("       [3.5, 4.5]]        [1.5, 2.0]]\n", .{});
    
    try executor.setInput(matrix_a.node_id, std.mem.asBytes(&a_data2), &shape, .f32);
    try executor.setInput(matrix_b.node_id, std.mem.asBytes(&b_data2), &shape, .f32);
    try executor.run();
    
    const output2 = try executor.getOutput(result.node_id);
    const result_data2 = @as([*]const f32, @ptrCast(@alignCast(output2.data.ptr)))[0..4];
    
    print("\n  C = A @ B = [[{d:.1}, {d:.1}],\n", .{ result_data2[0], result_data2[1] });
    print("               [{d:.1}, {d:.1}]]\n", .{ result_data2[2], result_data2[3] });
    
    // Expected: [[1.5*0.5+2.5*1.5, 1.5*1.0+2.5*2.0], [3.5*0.5+4.5*1.5, 3.5*1.0+4.5*2.0]]
    //         = [[0.75+3.75, 1.5+5.0], [1.75+6.75, 3.5+9.0]]
    //         = [[4.5, 6.5], [8.5, 12.5]]
    const expected2 = [_]f32{ 4.5, 6.5, 8.5, 12.5 };
    for (result_data2, expected2) |actual, exp| {
        try testing.expectApproxEqAbs(exp, actual, 1e-6);
    }
    print("  ✓ Results verified!\n", .{});
    
    print("\n🎉 Matrix multiplication demo complete!\n", .{});
    print("This demonstrates Zing's ability to:\n", .{});
    print("  • Build computation graphs\n", .{});
    print("  • Optimize operations (mul→sum_reduce fusion)\n", .{});
    print("  • Execute efficiently on CPU backend\n", .{});
    print("  • Handle different input values\n", .{});
    
    // Cleanup in order that respects memory ownership
    const enable_debug = @import("build_options").enable_debug_logs;
    
    if (enable_debug) {
        print("DEBUG: Test 3 - Starting cleanup sequence...\n", .{});
        print("DEBUG: Test 3 - About to call executor.deinit()...\n", .{});
    }
    executor.deinit();
    if (enable_debug) {
        print("DEBUG: Test 3 - ✓ executor.deinit() completed\n", .{});
        print("DEBUG: Test 3 - About to call compiled.deinit(allocator)...\n", .{});
    }
    compiled.deinit(allocator);  // Free CompiledGraph first (it has borrowed references)
    if (enable_debug) {
        print("DEBUG: Test 3 - ✓ compiled.deinit(allocator) completed\n", .{});
        print("DEBUG: Test 3 - About to call graph.deinit()...\n", .{});
    }
    graph.deinit();              // Free Graph last (it owns the memory)
    if (enable_debug) {
        print("DEBUG: Test 3 - ✓ graph.deinit() completed\n", .{});
        print("DEBUG: Test 3 - All cleanup completed successfully!\n", .{});
    }
}