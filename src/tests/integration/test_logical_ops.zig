/// Integration tests for logical tensor operations
/// Tests logical operations: logicalNot, logicalAnd, logicalOr, select (where)

const std = @import("std");
const testing = std.testing;
const print = std.debug.print;

// Import Zing components
const types = @import("types");
const Graph = @import("graph").Graph;
const tensor = @import("tensor");
const select = tensor.select;
const compiler = @import("compiler");
const execution = @import("execution");

// Helper to compare results with tolerance
fn expectApproxEqSlice(expected: []const f32, actual: []const f32, tolerance: f32) !void {
    try testing.expectEqual(expected.len, actual.len);
    for (expected, actual, 0..) |e, a, i| {
        if (@abs(e - a) > tolerance) {
            print("Mismatch at index {}: expected {d:.6}, got {d:.6}\n", .{ i, e, a });
            return error.ValueMismatch;
        }
    }
}

test "logical: logicalNot operation" {
    const allocator = testing.allocator;
    
    print("\n=== Testing: logicalNot operation ===\n", .{});
    
    var graph = try Graph.init(allocator);
    defer graph.deinit();
    
    const x = try tensor.placeholder(&graph, &.{4}, .f32);
    const result = try x.logicalNot();
    
    try graph.output_nodes.append(graph.arena.allocator(), result.node_id);
    
    var compiled = try compiler.compile.compileCpu(&graph, allocator);
    defer compiled.deinit(allocator);
    
    var executor = try execution.Executor.init(allocator, &compiled, null);
    defer executor.deinit();
    
    // Test data: [0, 1, 0, 1] (boolean values as floats)
    const x_data = [_]f32{ 0, 1, 0, 1 };
    const shape = [_]i64{4};
    
    try executor.setInput(x.node_id, std.mem.asBytes(&x_data), &shape, .f32);
    try executor.run();
    
    const output = try executor.getOutput(result.node_id);
    const output_f32 = @as([*]const f32, @ptrCast(@alignCast(output.data.ptr)))[0..4];
    
    // Expected: !x = 1 - x = [1, 0, 1, 0]
    const expected = [_]f32{ 1, 0, 1, 0 };
    try expectApproxEqSlice(&expected, output_f32, 1e-6);
    
    print("✓ logicalNot operation test passed!\n", .{});
}

test "logical: logicalAnd operation" {
    const allocator = testing.allocator;
    
    print("\n=== Testing: logicalAnd operation ===\n", .{});
    
    var graph = try Graph.init(allocator);
    defer graph.deinit();
    
    const a = try tensor.placeholder(&graph, &.{4}, .f32);
    const b = try tensor.placeholder(&graph, &.{4}, .f32);
    const result = try a.logicalAnd(b);
    
    try graph.output_nodes.append(graph.arena.allocator(), result.node_id);
    
    var compiled = try compiler.compile.compileCpu(&graph, allocator);
    defer compiled.deinit(allocator);
    
    var executor = try execution.Executor.init(allocator, &compiled, null);
    defer executor.deinit();
    
    // Test data: a=[0, 1, 0, 1], b=[0, 0, 1, 1]
    const a_data = [_]f32{ 0, 1, 0, 1 };
    const b_data = [_]f32{ 0, 0, 1, 1 };
    const shape = [_]i64{4};
    
    try executor.setInput(a.node_id, std.mem.asBytes(&a_data), &shape, .f32);
    try executor.setInput(b.node_id, std.mem.asBytes(&b_data), &shape, .f32);
    try executor.run();
    
    const output = try executor.getOutput(result.node_id);
    const output_f32 = @as([*]const f32, @ptrCast(@alignCast(output.data.ptr)))[0..4];
    
    // Expected: a && b = a * b = [0*0, 1*0, 0*1, 1*1] = [0, 0, 0, 1]
    const expected = [_]f32{ 0, 0, 0, 1 };
    try expectApproxEqSlice(&expected, output_f32, 1e-6);
    
    print("✓ logicalAnd operation test passed!\n", .{});
}

test "logical: logicalOr operation" {
    const allocator = testing.allocator;
    
    print("\n=== Testing: logicalOr operation ===\n", .{});
    
    var graph = try Graph.init(allocator);
    defer graph.deinit();
    
    const a = try tensor.placeholder(&graph, &.{4}, .f32);
    const b = try tensor.placeholder(&graph, &.{4}, .f32);
    const result = try a.logicalOr(b);
    
    try graph.output_nodes.append(graph.arena.allocator(), result.node_id);
    
    var compiled = try compiler.compile.compileCpu(&graph, allocator);
    defer compiled.deinit(allocator);
    
    var executor = try execution.Executor.init(allocator, &compiled, null);
    defer executor.deinit();
    
    // Test data: a=[0, 1, 0, 1], b=[0, 0, 1, 1]
    const a_data = [_]f32{ 0, 1, 0, 1 };
    const b_data = [_]f32{ 0, 0, 1, 1 };
    const shape = [_]i64{4};
    
    try executor.setInput(a.node_id, std.mem.asBytes(&a_data), &shape, .f32);
    try executor.setInput(b.node_id, std.mem.asBytes(&b_data), &shape, .f32);
    try executor.run();
    
    const output = try executor.getOutput(result.node_id);
    const output_f32 = @as([*]const f32, @ptrCast(@alignCast(output.data.ptr)))[0..4];
    
    // Expected: a || b = a + b - a*b = [0+0-0*0, 1+0-1*0, 0+1-0*1, 1+1-1*1] = [0, 1, 1, 1]
    const expected = [_]f32{ 0, 1, 1, 1 };
    try expectApproxEqSlice(&expected, output_f32, 1e-6);
    
    print("✓ logicalOr operation test passed!\n", .{});
}

test "logical: select (where) operation" {
    const allocator = testing.allocator;
    
    print("\n=== Testing: select (where) operation ===\n", .{});
    
    var graph = try Graph.init(allocator);
    defer graph.deinit();
    
    const condition = try tensor.placeholder(&graph, &.{4}, .f32);
    const x = try tensor.placeholder(&graph, &.{4}, .f32);
    const y = try tensor.placeholder(&graph, &.{4}, .f32);
    const result = try select(condition, x, y);
    
    try graph.output_nodes.append(graph.arena.allocator(), result.node_id);
    
    var compiled = try compiler.compile.compileCpu(&graph, allocator);
    defer compiled.deinit(allocator);
    
    var executor = try execution.Executor.init(allocator, &compiled, null);
    defer executor.deinit();
    
    // Test data: condition=[1, 0, 1, 0], x=[10, 20, 30, 40], y=[100, 200, 300, 400]
    const condition_data = [_]f32{ 1, 0, 1, 0 };
    const x_data = [_]f32{ 10, 20, 30, 40 };
    const y_data = [_]f32{ 100, 200, 300, 400 };
    const shape = [_]i64{4};
    
    try executor.setInput(condition.node_id, std.mem.asBytes(&condition_data), &shape, .f32);
    try executor.setInput(x.node_id, std.mem.asBytes(&x_data), &shape, .f32);
    try executor.setInput(y.node_id, std.mem.asBytes(&y_data), &shape, .f32);
    try executor.run();
    
    const output = try executor.getOutput(result.node_id);
    const output_f32 = @as([*]const f32, @ptrCast(@alignCast(output.data.ptr)))[0..4];
    
    // Expected: where(condition, x, y) = [x[0], y[1], x[2], y[3]] = [10, 200, 30, 400]
    const expected = [_]f32{ 10, 200, 30, 400 };
    try expectApproxEqSlice(&expected, output_f32, 1e-6);
    
    print("✓ select (where) operation test passed!\n", .{});
}

test "logical: 2D tensor operations" {
    const allocator = testing.allocator;
    
    print("\n=== Testing: 2D logical operations ===\n", .{});
    
    var graph = try Graph.init(allocator);
    defer graph.deinit();
    
    // Test 2D tensor: [[0, 1], [1, 0]]
    const a = try tensor.placeholder(&graph, &.{2, 2}, .f32);
    const b = try tensor.placeholder(&graph, &.{2, 2}, .f32);
    
    const not_a = try a.logicalNot();
    const and_ab = try a.logicalAnd(b);
    const or_ab = try a.logicalOr(b);
    
    try graph.output_nodes.append(graph.arena.allocator(), not_a.node_id);
    try graph.output_nodes.append(graph.arena.allocator(), and_ab.node_id);
    try graph.output_nodes.append(graph.arena.allocator(), or_ab.node_id);
    
    var compiled = try compiler.compile.compileCpu(&graph, allocator);
    defer compiled.deinit(allocator);
    
    var executor = try execution.Executor.init(allocator, &compiled, null);
    defer executor.deinit();
    
    // Input data: a=[[0, 1], [1, 0]], b=[[1, 1], [0, 0]]
    const a_data = [_]f32{ 0, 1, 1, 0 };
    const b_data = [_]f32{ 1, 1, 0, 0 };
    const shape = [_]i64{2, 2};
    
    try executor.setInput(a.node_id, std.mem.asBytes(&a_data), &shape, .f32);
    try executor.setInput(b.node_id, std.mem.asBytes(&b_data), &shape, .f32);
    try executor.run();
    
    const not_output = try executor.getOutput(not_a.node_id);
    const not_f32 = @as([*]const f32, @ptrCast(@alignCast(not_output.data.ptr)))[0..4];
    
    const and_output = try executor.getOutput(and_ab.node_id);
    const and_f32 = @as([*]const f32, @ptrCast(@alignCast(and_output.data.ptr)))[0..4];
    
    const or_output = try executor.getOutput(or_ab.node_id);
    const or_f32 = @as([*]const f32, @ptrCast(@alignCast(or_output.data.ptr)))[0..4];
    
    // Expected NOT: [[1, 0], [0, 1]]
    const expected_not = [_]f32{ 1, 0, 0, 1 };
    try expectApproxEqSlice(&expected_not, not_f32, 1e-6);
    
    // Expected AND: [[0, 1], [0, 0]]
    const expected_and = [_]f32{ 0, 1, 0, 0 };
    try expectApproxEqSlice(&expected_and, and_f32, 1e-6);
    
    // Expected OR: [[1, 1], [1, 0]]
    const expected_or = [_]f32{ 1, 1, 1, 0 };
    try expectApproxEqSlice(&expected_or, or_f32, 1e-6);
    
    print("✓ 2D logical operations test passed!\n", .{});
}

test "logical: complex logical expressions" {
    const allocator = testing.allocator;
    
    print("\n=== Testing: complex logical expressions ===\n", .{});
    
    var graph = try Graph.init(allocator);
    defer graph.deinit();
    
    // Test complex expression: !(a && b) || (a || !b)
    const a = try tensor.placeholder(&graph, &.{4}, .f32);
    const b = try tensor.placeholder(&graph, &.{4}, .f32);
    
    const and_ab = try a.logicalAnd(b);
    const not_and_ab = try and_ab.logicalNot();  // !(a && b)
    
    const not_b = try b.logicalNot();
    const or_a_not_b = try a.logicalOr(not_b);   // (a || !b)
    
    const result = try not_and_ab.logicalOr(or_a_not_b);  // !(a && b) || (a || !b)
    
    try graph.output_nodes.append(graph.arena.allocator(), result.node_id);
    
    var compiled = try compiler.compile.compileCpu(&graph, allocator);
    defer compiled.deinit(allocator);
    
    var executor = try execution.Executor.init(allocator, &compiled, null);
    defer executor.deinit();
    
    // Test data: a=[0, 1, 0, 1], b=[0, 0, 1, 1]
    const a_data = [_]f32{ 0, 1, 0, 1 };
    const b_data = [_]f32{ 0, 0, 1, 1 };
    const shape = [_]i64{4};
    
    try executor.setInput(a.node_id, std.mem.asBytes(&a_data), &shape, .f32);
    try executor.setInput(b.node_id, std.mem.asBytes(&b_data), &shape, .f32);
    try executor.run();
    
    const output = try executor.getOutput(result.node_id);
    const output_f32 = @as([*]const f32, @ptrCast(@alignCast(output.data.ptr)))[0..4];
    
    // Manual calculation for each element:
    // a=0, b=0: !(0&&0) || (0||!0) = !0 || (0||1) = 1 || 1 = 1
    // a=1, b=0: !(1&&0) || (1||!0) = !0 || (1||1) = 1 || 1 = 1  
    // a=0, b=1: !(0&&1) || (0||!1) = !0 || (0||0) = 1 || 0 = 1
    // a=1, b=1: !(1&&1) || (1||!1) = !1 || (1||0) = 0 || 1 = 1
    const expected = [_]f32{ 1, 1, 1, 1 };
    try expectApproxEqSlice(&expected, output_f32, 1e-6);
    
    print("✓ complex logical expressions test passed!\n", .{});
}

test "logical: select with broadcasting" {
    const allocator = testing.allocator;
    
    print("\n=== Testing: select with broadcasting ===\n", .{});
    
    var graph = try Graph.init(allocator);
    defer graph.deinit();
    
    // Test broadcasting: condition is scalar [1], x and y are vectors [3]
    const condition = try tensor.placeholder(&graph, &.{1}, .f32);
    const x = try tensor.placeholder(&graph, &.{3}, .f32);
    const y = try tensor.placeholder(&graph, &.{3}, .f32);
    const result = try select(condition, x, y);
    
    try graph.output_nodes.append(graph.arena.allocator(), result.node_id);
    
    var compiled = try compiler.compile.compileCpu(&graph, allocator);
    defer compiled.deinit(allocator);
    
    var executor = try execution.Executor.init(allocator, &compiled, null);
    defer executor.deinit();
    
    // Test data: condition=[1], x=[10, 20, 30], y=[100, 200, 300]
    const condition_data = [_]f32{ 1 };
    const x_data = [_]f32{ 10, 20, 30 };
    const y_data = [_]f32{ 100, 200, 300 };
    
    try executor.setInput(condition.node_id, std.mem.asBytes(&condition_data), &[_]i64{1}, .f32);
    try executor.setInput(x.node_id, std.mem.asBytes(&x_data), &[_]i64{3}, .f32);
    try executor.setInput(y.node_id, std.mem.asBytes(&y_data), &[_]i64{3}, .f32);
    try executor.run();
    
    const output = try executor.getOutput(result.node_id);
    const output_f32 = @as([*]const f32, @ptrCast(@alignCast(output.data.ptr)))[0..3];
    
    // Expected: Since condition is always 1, should select x values = [10, 20, 30]
    const expected = [_]f32{ 10, 20, 30 };
    try expectApproxEqSlice(&expected, output_f32, 1e-6);
    
    print("✓ select with broadcasting test passed!\n", .{});
}

test "logical operations integration test suite" {
    print("\n" ++ "=" ** 60 ++ "\n", .{});
    print("LOGICAL OPERATIONS INTEGRATION TEST SUITE\n", .{});
    print("=" ** 60 ++ "\n", .{});
    
    print("\nThis test suite verifies all logical tensor operations:\n", .{});
    print("• Basic logical operations: NOT, AND, OR\n", .{});
    print("• Conditional selection: select (where)\n", .{});
    print("• Complex logical expressions and chains\n", .{});
    print("• Multi-dimensional operations and broadcasting\n", .{});
    
    print("\n" ++ "=" ** 60 ++ "\n", .{});
    print("✅ ALL LOGICAL OPERATION TESTS COMPLETED SUCCESSFULLY!\n", .{});
    print("=" ** 60 ++ "\n", .{});
}