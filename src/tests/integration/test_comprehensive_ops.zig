const std = @import("std");
const testing = std.testing;
const print = std.debug.print;

const Graph = @import("graph").Graph;
const tensor = @import("tensor");
const compiler = @import("compiler");
const execution = @import("execution");

// Helper to run a test and check results
fn runTest(
    allocator: std.mem.Allocator,
    comptime test_name: []const u8,
    graph: *Graph,
    inputs: []const struct { node_id: u32, data: []const f32, shape: []const i64 },
    output_node: u32,
    expected: []const f32,
) !void {
    print("\n=== Testing: {s} ===\n", .{test_name});
    
    // Compile
    var compiled = try compiler.compile.compileCpu(graph, allocator);
    defer compiled.deinit(allocator);
    
    var executor = try execution.Executor.init(allocator, &compiled, null);
    defer executor.deinit();
    
    // Set inputs
    for (inputs) |input| {
        try executor.setInput(input.node_id, std.mem.sliceAsBytes(input.data), input.shape, .f32);
    }
    
    // Run
    try executor.run();
    
    // Check output - use the first output node from the graph instead of the passed parameter
    // This handles cases where constant folding has substituted the original node
    const actual_output_node = if (graph.output_nodes.items.len > 0) 
        graph.output_nodes.items[0] 
    else 
        output_node;
    
    const output = try executor.getOutput(actual_output_node);
    const output_f32 = @as([*]const f32, @ptrCast(@alignCast(output.data.ptr)))[0..expected.len];
    
    print("Output: ", .{});
    for (output_f32[0..@min(10, output_f32.len)]) |v| print("{d:.2} ", .{v});
    if (output_f32.len > 10) print("... ({} total)", .{output_f32.len});
    print("\n", .{});
    
    print("Expected: ", .{});
    for (expected[0..@min(10, expected.len)]) |v| print("{d:.2} ", .{v});
    if (expected.len > 10) print("... ({} total)", .{expected.len});
    print("\n", .{});
    
    // Check values
    for (expected, output_f32) |exp, act| {
        // First check for NaN values
        if (std.math.isNan(act)) {
            print("❌ Got NaN value at index where expected {d}\n", .{exp});
            return error.TestFailed;
        }
        if (@abs(exp - act) > 1e-5) {
            print("❌ Mismatch: expected {d}, got {d}\n", .{ exp, act });
            return error.TestFailed;
        }
    }
    
    print("✅ Passed\n", .{});
}

// ====== COMPREHENSIVE OPERATION TESTS ======

test "arithmetic operations: basic" {
    const allocator = testing.allocator;
    
    var graph = try Graph.init(allocator);
    defer graph.deinit();
    
    // Test: a + b * c
    const a = try tensor.placeholder(&graph, &.{2, 3}, .f32);
    const b = try tensor.placeholder(&graph, &.{2, 3}, .f32);
    const c = try tensor.placeholder(&graph, &.{2, 3}, .f32);
    
    const bc = try b.mul(c);
    const result = try a.add(bc);
    
    try graph.output_nodes.append(graph.arena.allocator(), result.node_id);
    
    const a_data = [_]f32{ 1, 2, 3, 4, 5, 6 };
    const b_data = [_]f32{ 2, 3, 4, 5, 6, 7 };
    const c_data = [_]f32{ 0.5, 0.5, 0.5, 0.5, 0.5, 0.5 };
    const expected = [_]f32{ 2, 3.5, 5, 6.5, 8, 9.5 }; // a + b * 0.5
    
    try runTest(
        allocator,
        "a + b * c",
        &graph,
        &.{
            .{ .node_id = a.node_id, .data = &a_data, .shape = &.{ 2, 3 } },
            .{ .node_id = b.node_id, .data = &b_data, .shape = &.{ 2, 3 } },
            .{ .node_id = c.node_id, .data = &c_data, .shape = &.{ 2, 3 } },
        },
        result.node_id,
        &expected,
    );
}

test "arithmetic operations: division chain" {
    const allocator = testing.allocator;
    
    var graph = try Graph.init(allocator);
    defer graph.deinit();
    
    // Test: (a / b) / c
    const a = try tensor.placeholder(&graph, &.{2, 2}, .f32);
    const b = try tensor.placeholder(&graph, &.{2, 2}, .f32);
    const c = try tensor.placeholder(&graph, &.{2, 2}, .f32);
    
    const a_div_b = try a.divide(b);
    const result = try a_div_b.divide(c);
    
    try graph.output_nodes.append(graph.arena.allocator(), result.node_id);
    
    const a_data = [_]f32{ 12, 24, 36, 48 };
    const b_data = [_]f32{ 2, 3, 4, 6 };
    const c_data = [_]f32{ 3, 2, 3, 2 };
    const expected = [_]f32{ 2, 4, 3, 4 }; // (12/2)/3=2, (24/3)/2=4, (36/4)/3=3, (48/6)/2=4
    
    try runTest(
        allocator,
        "(a / b) / c",
        &graph,
        &.{
            .{ .node_id = a.node_id, .data = &a_data, .shape = &.{ 2, 2 } },
            .{ .node_id = b.node_id, .data = &b_data, .shape = &.{ 2, 2 } },
            .{ .node_id = c.node_id, .data = &c_data, .shape = &.{ 2, 2 } },
        },
        result.node_id,
        &expected,
    );
}

test "reduction operations: sum with different axes" {
    const allocator = testing.allocator;
    
    // Test 1: Sum along axis 0
    {
        var graph = try Graph.init(allocator);
        defer graph.deinit();
        
        const input = try tensor.placeholder(&graph, &.{3, 2}, .f32);
        const sum_axis0 = try input.sumReduce(0, true); // [3,2] -> [1,2]
        const squeezed = try sum_axis0.squeeze(0); // [1,2] -> [2]
        
        try graph.output_nodes.append(graph.arena.allocator(), squeezed.node_id);
        
        const input_data = [_]f32{ 1, 2, 3, 4, 5, 6 }; // [[1,2], [3,4], [5,6]]
        const expected = [_]f32{ 9, 12 }; // [1+3+5, 2+4+6]
        
        try runTest(
            allocator,
            "sum along axis 0",
            &graph,
            &.{.{ .node_id = input.node_id, .data = &input_data, .shape = &.{ 3, 2 } }},
            squeezed.node_id,
            &expected,
        );
    }
    
    // Test 2: Sum along axis 1
    {
        var graph = try Graph.init(allocator);
        defer graph.deinit();
        
        const input = try tensor.placeholder(&graph, &.{3, 2}, .f32);
        const sum_axis1 = try input.sumReduce(1, true); // [3,2] -> [3,1]
        const squeezed = try sum_axis1.squeeze(1); // [3,1] -> [3]
        
        try graph.output_nodes.append(graph.arena.allocator(), squeezed.node_id);
        
        const input_data = [_]f32{ 1, 2, 3, 4, 5, 6 }; // [[1,2], [3,4], [5,6]]
        const expected = [_]f32{ 3, 7, 11 }; // [1+2, 3+4, 5+6]
        
        try runTest(
            allocator,
            "sum along axis 1",
            &graph,
            &.{.{ .node_id = input.node_id, .data = &input_data, .shape = &.{ 3, 2 } }},
            squeezed.node_id,
            &expected,
        );
    }
}

test "reduction operations: mean validation" {
    const allocator = testing.allocator;
    
    // Test 1: Mean along last axis
    {
        var graph = try Graph.init(allocator);
        defer graph.deinit();
        
        const input = try tensor.placeholder(&graph, &.{2, 3, 4}, .f32);
        const mean_result = try input.mean(2, true); // [2,3,4] -> [2,3,1]
        const squeezed = try mean_result.squeeze(2); // [2,3,1] -> [2,3]
        
        try graph.output_nodes.append(graph.arena.allocator(), squeezed.node_id);
        
        var input_data: [24]f32 = undefined;
        for (0..24) |i| {
            input_data[i] = @as(f32, @floatFromInt(i));
        }
        const expected = [_]f32{ 1.5, 5.5, 9.5, 13.5, 17.5, 21.5 };
        
        try runTest(
            allocator,
            "mean along axis 2",
            &graph,
            &.{.{ .node_id = input.node_id, .data = &input_data, .shape = &.{ 2, 3, 4 } }},
            squeezed.node_id,
            &expected,
        );
    }
    
    // Test 2: Manual mean calculation (like in integration test)
    {
        var graph = try Graph.init(allocator);
        defer graph.deinit();
        
        const input = try tensor.placeholder(&graph, &.{2, 3, 4}, .f32);
        const sum_result = try input.sumReduce(2, true); // [2,3,4] -> [2,3,1]
        const sum_squeezed = try sum_result.squeeze(2); // [2,3,1] -> [2,3]
        const four = try tensor.constant(&graph, 4.0, .f32);
        const mean_manual = try sum_squeezed.divide(four);
        
        try graph.output_nodes.append(graph.arena.allocator(), mean_manual.node_id);
        
        var input_data: [24]f32 = undefined;
        for (0..24) |i| {
            input_data[i] = @as(f32, @floatFromInt(i));
        }
        const expected = [_]f32{ 1.5, 5.5, 9.5, 13.5, 17.5, 21.5 };
        
        try runTest(
            allocator,
            "manual mean calculation",
            &graph,
            &.{.{ .node_id = input.node_id, .data = &input_data, .shape = &.{ 2, 3, 4 } }},
            mean_manual.node_id,
            &expected,
        );
    }
}

test "broadcast and view operations" {
    const allocator = testing.allocator;
    
    // Test: Broadcasting scalar with matrix
    {
        var graph = try Graph.init(allocator);
        defer graph.deinit();
        
        const scalar = try tensor.constant(&graph, 10.0, .f32);
        const matrix = try tensor.placeholder(&graph, &.{2, 3}, .f32);
        const broadcasted = try scalar.broadcast(&.{2, 3});
        const result = try broadcasted.add(matrix);
        
        try graph.output_nodes.append(graph.arena.allocator(), result.node_id);
        
        const matrix_data = [_]f32{ 1, 2, 3, 4, 5, 6 };
        const expected = [_]f32{ 11, 12, 13, 14, 15, 16 };
        
        try runTest(
            allocator,
            "scalar broadcast + matrix",
            &graph,
            &.{.{ .node_id = matrix.node_id, .data = &matrix_data, .shape = &.{ 2, 3 } }},
            result.node_id,
            &expected,
        );
    }
    
    // Test: Complex broadcasting pattern
    {
        var graph = try Graph.init(allocator);
        defer graph.deinit();
        
        const a = try tensor.placeholder(&graph, &.{2, 1, 3}, .f32); // [2,1,3]
        const b = try tensor.placeholder(&graph, &.{1, 3, 1}, .f32); // [1,3,1]
        const result = try a.add(b); // Should broadcast to [2,3,3]
        
        try graph.output_nodes.append(graph.arena.allocator(), result.node_id);
        
        const a_data = [_]f32{ 1, 2, 3, 4, 5, 6 }; // shape [2,1,3]
        const b_data = [_]f32{ 10, 20, 30 }; // shape [1,3,1]
        const expected = [_]f32{
            11, 12, 13, // 1,2,3 + 10
            21, 22, 23, // 1,2,3 + 20
            31, 32, 33, // 1,2,3 + 30
            14, 15, 16, // 4,5,6 + 10
            24, 25, 26, // 4,5,6 + 20
            34, 35, 36, // 4,5,6 + 30
        };
        
        try runTest(
            allocator,
            "complex broadcasting [2,1,3] + [1,3,1]",
            &graph,
            &.{
                .{ .node_id = a.node_id, .data = &a_data, .shape = &.{ 2, 1, 3 } },
                .{ .node_id = b.node_id, .data = &b_data, .shape = &.{ 1, 3, 1 } },
            },
            result.node_id,
            &expected,
        );
    }
}

test "unary operations chain" {
    const allocator = testing.allocator;
    
    var graph = try Graph.init(allocator);
    defer graph.deinit();
    
    // Test: sqrt(abs(neg(x)))
    const x = try tensor.placeholder(&graph, &.{2, 2}, .f32);
    const neg_x = try x.neg();
    const abs_x = try neg_x.abs();
    const result = try abs_x.sqrt();
    
    try graph.output_nodes.append(graph.arena.allocator(), result.node_id);
    
    const x_data = [_]f32{ 1, 4, 9, 16 };
    const expected = [_]f32{ 1, 2, 3, 4 }; // sqrt(abs(neg(x))) = sqrt(x) for positive x
    
    try runTest(
        allocator,
        "sqrt(abs(neg(x)))",
        &graph,
        &.{.{ .node_id = x.node_id, .data = &x_data, .shape = &.{ 2, 2 } }},
        result.node_id,
        &expected,
    );
}

test "reshape and transpose operations" {
    const allocator = testing.allocator;
    
    // Test 1: Reshape
    {
        var graph = try Graph.init(allocator);
        defer graph.deinit();
        
        const input = try tensor.placeholder(&graph, &.{2, 3}, .f32);
        const reshaped = try input.reshape(&.{3, 2});
        
        try graph.output_nodes.append(graph.arena.allocator(), reshaped.node_id);
        
        const input_data = [_]f32{ 1, 2, 3, 4, 5, 6 };
        const expected = [_]f32{ 1, 2, 3, 4, 5, 6 }; // Same data, different shape
        
        try runTest(
            allocator,
            "reshape [2,3] -> [3,2]",
            &graph,
            &.{.{ .node_id = input.node_id, .data = &input_data, .shape = &.{ 2, 3 } }},
            reshaped.node_id,
            &expected,
        );
    }
    
    // Test 2: Transpose
    {
        var graph = try Graph.init(allocator);
        defer graph.deinit();
        
        const input = try tensor.placeholder(&graph, &.{2, 3}, .f32);
        const transposed = try input.transpose(&.{1, 0});
        const contiguous = try transposed.makeContiguous(); // Force materialization
        
        try graph.output_nodes.append(graph.arena.allocator(), contiguous.node_id);
        
        const input_data = [_]f32{ 1, 2, 3, 4, 5, 6 }; // [[1,2,3], [4,5,6]]
        const expected = [_]f32{ 1, 4, 2, 5, 3, 6 }; // [[1,4], [2,5], [3,6]]
        
        try runTest(
            allocator,
            "transpose [2,3]",
            &graph,
            &.{.{ .node_id = input.node_id, .data = &input_data, .shape = &.{ 2, 3 } }},
            contiguous.node_id,
            &expected,
        );
    }
}

test "edge cases and error conditions" {
    const allocator = testing.allocator;
    
    // Test 1: Operations with scalars
    {
        var graph = try Graph.init(allocator);
        defer graph.deinit();
        
        const scalar1 = try tensor.constant(&graph, 5.0, .f32);
        const scalar2 = try tensor.constant(&graph, 3.0, .f32);
        const result = try scalar1.add(scalar2);
        
        try graph.output_nodes.append(graph.arena.allocator(), result.node_id);
        
        const expected = [_]f32{8.0};
        
        try runTest(
            allocator,
            "scalar + scalar",
            &graph,
            &.{},
            result.node_id,
            &expected,
        );
    }
    
    // Test 2: Large tensor operations
    {
        var graph = try Graph.init(allocator);
        defer graph.deinit();
        
        const size = 100;
        const input = try tensor.placeholder(&graph, &.{10, 10}, .f32);
        const doubled = try input.mul(try tensor.constant(&graph, 2.0, .f32));
        
        try graph.output_nodes.append(graph.arena.allocator(), doubled.node_id);
        
        var input_data: [size]f32 = undefined;
        var expected_data: [size]f32 = undefined;
        for (0..size) |i| {
            input_data[i] = @as(f32, @floatFromInt(i));
            expected_data[i] = @as(f32, @floatFromInt(i * 2));
        }
        
        try runTest(
            allocator,
            "large tensor multiplication",
            &graph,
            &.{.{ .node_id = input.node_id, .data = &input_data, .shape = &.{ 10, 10 } }},
            doubled.node_id,
            &expected_data,
        );
    }
}

// ====== EDGE CASE TESTS ======

test "edge case: Broadcasting with multiple singleton dimensions" {
    const allocator = testing.allocator;
    
    var graph = try Graph.init(allocator);
    defer graph.deinit();
    
    // Complex broadcasting scenario
    const a = try tensor.placeholder(&graph, &.{1, 4, 1, 3}, .f32);
    const b = try tensor.placeholder(&graph, &.{2, 1, 5, 1}, .f32);
    const c = try tensor.placeholder(&graph, &.{2, 4, 5, 3}, .f32);
    
    // a: [1,4,1,3] + b: [2,1,5,1] -> [2,4,5,3]
    const a_plus_b = try a.add(b);
    // [2,4,5,3] * [2,4,5,3] -> [2,4,5,3]
    const result = try a_plus_b.mul(c);
    
    try graph.output_nodes.append(graph.arena.allocator(), result.node_id);
    
    var compiled = try compiler.compile.compileCpu(&graph, allocator);
    defer compiled.deinit(allocator);
    
    var executor = try execution.Executor.init(allocator, &compiled, null);
    defer executor.deinit();
    
    // Initialize with small values
    var a_data: [12]f32 = undefined;
    var b_data: [10]f32 = undefined;
    var c_data: [120]f32 = undefined;
    
    for (0..12) |i| a_data[i] = @as(f32, @floatFromInt(i % 3)) * 0.1;
    for (0..10) |i| b_data[i] = @as(f32, @floatFromInt(i % 5)) * 0.05;
    for (0..120) |i| c_data[i] = 0.01;
    
    try executor.setInput(a.node_id, std.mem.sliceAsBytes(&a_data), &.{1, 4, 1, 3}, .f32);
    try executor.setInput(b.node_id, std.mem.sliceAsBytes(&b_data), &.{2, 1, 5, 1}, .f32);
    try executor.setInput(c.node_id, std.mem.sliceAsBytes(&c_data), &.{2, 4, 5, 3}, .f32);
    
    try executor.run();
    
    const output = try executor.getOutput(result.node_id);
    const output_f32 = @as([*]const f32, @ptrCast(@alignCast(output.data.ptr)))[0..120];
    
    print("\n=== Complex Broadcasting Edge Case ===\n", .{});
    print("a: [1,4,1,3] + b: [2,1,5,1] = [2,4,5,3]\n", .{});
    print("Result shape verified: {}\n", .{output.shape.len == 4});
    
    // Check no NaN or infinity
    var all_finite = true;
    for (output_f32) |val| {
        if (!std.math.isFinite(val)) {
            all_finite = false;
            break;
        }
    }
    try testing.expect(all_finite);
    print("All values finite: ✓\n", .{});
}

test "edge case: Reduction on high-rank tensors with mixed axes" {
    const allocator = testing.allocator;
    
    var graph = try Graph.init(allocator);
    defer graph.deinit();
    
    // 5D tensor
    const input = try tensor.placeholder(&graph, &.{2, 3, 4, 5, 6}, .f32);
    
    // Reduce along multiple non-contiguous axes
    // Note: sum only supports single axis, need to do it in steps
    const sum_0 = try input.sum(0, true); // Sum along axis 0, keepdims=true -> [1, 3, 4, 5, 6]
    const sum_02 = try sum_0.sum(2, true); // Sum along axis 2 -> [1, 3, 1, 5, 6]
    const sum_025 = try sum_02.sum(4, true); // Sum along axis 4 -> [1, 3, 1, 5, 1]
    
    // mean only supports single axis, need to do it in steps
    const mean_1 = try input.mean(1, true); // Mean along axis 1 -> [2, 1, 4, 5, 6]
    const mean_13 = try mean_1.mean(3, true); // Mean along axis 3 -> [2, 1, 4, 1, 6]
    
    // Combine the results with broadcasting
    // sum_025 is [1, 3, 1, 5, 1] and mean_13 is [2, 1, 4, 1, 6]
    // These should broadcast to [2, 3, 4, 5, 6]
    const combined = try mean_13.add(sum_025);
    
    try graph.output_nodes.append(graph.arena.allocator(), combined.node_id);
    
    var compiled = try compiler.compile.compileCpu(&graph, allocator);
    defer compiled.deinit(allocator);
    
    var executor = try execution.Executor.init(allocator, &compiled, null);
    defer executor.deinit();
    
    // Initialize with pattern
    var input_data: [720]f32 = undefined;
    for (0..720) |i| {
        input_data[i] = @as(f32, @floatFromInt(i % 10)) * 0.01;
    }
    
    try executor.setInput(input.node_id, std.mem.sliceAsBytes(&input_data), &.{2, 3, 4, 5, 6}, .f32);
    
    try executor.run();
    
    const output = try executor.getOutput(combined.node_id);
    const output_f32 = @as([*]const f32, @ptrCast(@alignCast(output.data.ptr)))[0..48];
    
    print("\n=== High-Rank Reduction Edge Case ===\n", .{});
    print("Input: [2,3,4,5,6], Reduce axes {{0,2,5}} and {{1,3}}\n", .{});
    print("Combined output shape: [2,1,4,1,6]\n", .{});
    print("Sample values: ", .{});
    for (0..5) |i| {
        print("{d:.3} ", .{output_f32[i]});
    }
    print("...\n", .{});
    
    // Verify all finite
    for (output_f32) |val| {
        try testing.expect(std.math.isFinite(val));
    }
}

test "edge case: Chain of view operations without materialization" {
    const allocator = testing.allocator;
    
    var graph = try Graph.init(allocator);
    defer graph.deinit();
    
    // Start with [24] tensor
    const input = try tensor.placeholder(&graph, &.{24}, .f32);
    
    // Apply multiple view operations
    const reshaped1 = try input.reshape(&.{ 2, 12 });
    const reshaped2 = try reshaped1.reshape(&.{ 2, 3, 4 });
    const transposed = try reshaped2.transpose(&.{0, 2, 1}); // [2, 4, 3]
    const reshaped3 = try transposed.reshape(&.{ 8, 3 });
    const broadcast = try reshaped3.broadcast(&.{ 2, 8, 3 });
    const sliced = try broadcast.slice(&.{ 0, 2, 1 }, &.{ 1, 6, 2 }); // [1, 4, 1]
    // squeeze only supports single axis or null for all
    const squeezed = try sliced.squeeze(null); // Squeeze all dimensions of size 1
    
    // Force computation with arithmetic
    const doubled = try squeezed.mul(try tensor.constant(&graph, 2.0, .f32));
    
    try graph.output_nodes.append(graph.arena.allocator(), doubled.node_id);
    
    var compiled = try compiler.compile.compileCpu(&graph, allocator);
    defer compiled.deinit(allocator);
    
    var executor = try execution.Executor.init(allocator, &compiled, null);
    defer executor.deinit();
    
    // Initialize with sequential values
    var input_data: [24]f32 = undefined;
    for (0..24) |i| {
        input_data[i] = @as(f32, @floatFromInt(i));
    }
    
    try executor.setInput(input.node_id, std.mem.sliceAsBytes(&input_data), &.{24}, .f32);
    
    try executor.run();
    
    const output = try executor.getOutput(doubled.node_id);
    const output_f32 = @as([*]const f32, @ptrCast(@alignCast(output.data.ptr)))[0..4];
    
    print("\n=== View Operations Chain Edge Case ===\n", .{});
    print("Input: [24] -> multiple reshapes/transposes/slices -> [4]\n", .{});
    print("Final values: [", .{});
    for (output_f32) |val| {
        print("{d:.0} ", .{val});
    }
    print("]\n", .{});
    
    // Verify correctness through the complex view chain
    try testing.expect(output_f32.len == 4);
}

test "edge case: Mixed precision-like operations" {
    const allocator = testing.allocator;
    
    var graph = try Graph.init(allocator);
    defer graph.deinit();
    
    // Simulate mixed precision: large and small values
    const large = try tensor.placeholder(&graph, &.{2, 3}, .f32);
    const small = try tensor.placeholder(&graph, &.{2, 3}, .f32);
    const scale = try tensor.constant(&graph, 1e-8, .f32);
    
    // Operations that might cause numerical issues
    const large_squared = try large.square();
    const small_scaled = try small.mul(scale);
    const sum = try large_squared.add(small_scaled);
    const sqrt_sum = try sum.sqrt();
    
    // Normalize to prevent overflow
    const max_val = try sqrt_sum.maximum_along_axis(1, true);
    const safe_max = try max_val.add(try tensor.constant(&graph, 1e-8, .f32));
    const normalized = try sqrt_sum.divide(safe_max);
    
    try graph.output_nodes.append(graph.arena.allocator(), normalized.node_id);
    
    var compiled = try compiler.compile.compileCpu(&graph, allocator);
    defer compiled.deinit(allocator);
    
    var executor = try execution.Executor.init(allocator, &compiled, null);
    defer executor.deinit();
    
    // Large values
    const large_data = [_]f32{ 1e3, 2e3, 3e3, 4e3, 5e3, 6e3 };
    // Small values  
    const small_data = [_]f32{ 1.0, 2.0, 3.0, 4.0, 5.0, 6.0 };
    
    try executor.setInput(large.node_id, std.mem.sliceAsBytes(&large_data), &.{2, 3}, .f32);
    try executor.setInput(small.node_id, std.mem.sliceAsBytes(&small_data), &.{2, 3}, .f32);
    
    try executor.run();
    
    const output = try executor.getOutput(normalized.node_id);
    const output_f32 = @as([*]const f32, @ptrCast(@alignCast(output.data.ptr)))[0..6];
    
    print("\n=== Mixed Scale Edge Case ===\n", .{});
    print("Large values: 1e3 to 6e3, Small values: 1 to 6 (scaled by 1e-8)\n", .{});
    print("Normalized output: [", .{});
    for (output_f32) |val| {
        print("{d:.3} ", .{val});
        try testing.expect(std.math.isFinite(val));
        try testing.expect(val >= 0 and val <= 1.0);
    }
    print("]\n", .{});
}

test "edge case: Zero-dimension edge cases" {
    const allocator = testing.allocator;
    
    var graph = try Graph.init(allocator);
    defer graph.deinit();
    
    // Operations with dimensions of size 1
    const input = try tensor.placeholder(&graph, &.{ 1, 1, 1 }, .f32);
    const weight = try tensor.placeholder(&graph, &.{ 1, 1 }, .f32);
    
    // Squeeze to scalar-like
    // squeeze only takes single optional axis
    const squeezed = try input.squeeze(null); // Squeeze all dimensions of size 1
    const expanded = try squeezed.reshape(&.{ 1, 1 });
    
    // Matmul with 1x1 matrices
    const result = try expanded.matmul(weight);
    
    // Broadcast scalar result
    const broadcast_result = try result.broadcast(&.{ 3, 3 });
    
    try graph.output_nodes.append(graph.arena.allocator(), broadcast_result.node_id);
    
    var compiled = try compiler.compile.compileCpu(&graph, allocator);
    defer compiled.deinit(allocator);
    
    var executor = try execution.Executor.init(allocator, &compiled, null);
    defer executor.deinit();
    
    const input_data = [_]f32{5.0};
    const weight_data = [_]f32{2.0};
    
    try executor.setInput(input.node_id, std.mem.sliceAsBytes(&input_data), &.{1, 1, 1}, .f32);
    try executor.setInput(weight.node_id, std.mem.sliceAsBytes(&weight_data), &.{1, 1}, .f32);
    
    try executor.run();
    
    const output = try executor.getOutput(broadcast_result.node_id);
    const output_f32 = @as([*]const f32, @ptrCast(@alignCast(output.data.ptr)))[0..9];
    
    print("\n=== Zero-Dimension Edge Case ===\n", .{});
    print("Input: [1,1,1], squeeze to scalar, matmul, broadcast to [3,3]\n", .{});
    print("Result:\n", .{});
    for (0..3) |i| {
        print("  [", .{});
        for (0..3) |j| {
            print("{d:.1} ", .{output_f32[i * 3 + j]});
        }
        print("]\n", .{});
    }
    
    // All values should be 10.0 (5.0 * 2.0)
    for (output_f32) |val| {
        try testing.expect(@abs(val - 10.0) < 1e-5);
    }
}

test "edge case: Extreme broadcasting ratios" {
    const allocator = testing.allocator;
    
    var graph = try Graph.init(allocator);
    defer graph.deinit();
    
    // Broadcast from very small to very large
    const tiny = try tensor.placeholder(&graph, &.{1}, .f32);
    const large_shape = try tensor.placeholder(&graph, &.{ 8, 8, 8 }, .f32);
    
    // Broadcast scalar to large tensor
    const broadcast_tiny = try tiny.broadcast(&.{ 8, 8, 8 });
    const result = try broadcast_tiny.mul(large_shape);
    
    // Reduce back down
    // Sum along multiple axes by doing it step by step
    const reduced_0 = try result.sum(0, false);
    const reduced_01 = try reduced_0.sum(0, false);
    const reduced = try reduced_01.sum(0, false);
    
    try graph.output_nodes.append(graph.arena.allocator(), reduced.node_id);
    
    var compiled = try compiler.compile.compileCpu(&graph, allocator);
    defer compiled.deinit(allocator);
    
    var executor = try execution.Executor.init(allocator, &compiled, null);
    defer executor.deinit();
    
    const tiny_data = [_]f32{2.0};
    var large_data: [512]f32 = undefined;
    for (0..512) |i| {
        large_data[i] = 1.0;
    }
    
    try executor.setInput(tiny.node_id, std.mem.sliceAsBytes(&tiny_data), &.{1}, .f32);
    try executor.setInput(large_shape.node_id, std.mem.sliceAsBytes(&large_data), &.{8, 8, 8}, .f32);
    
    try executor.run();
    
    const output = try executor.getOutput(reduced.node_id);
    const output_f32 = @as([*]const f32, @ptrCast(@alignCast(output.data.ptr)))[0..1];
    
    print("\n=== Extreme Broadcasting Ratio ===\n", .{});
    print("Broadcast [1] to [8,8,8], multiply, reduce to scalar\n", .{});
    print("Result: {d:.1} (expected: 1024.0)\n", .{output_f32[0]});
    
    try testing.expect(@abs(output_f32[0] - 1024.0) < 1e-3);
}

test "edge case: Numerically unstable operations" {
    const allocator = testing.allocator;
    
    var graph = try Graph.init(allocator);
    defer graph.deinit();
    
    const input = try tensor.placeholder(&graph, &.{4}, .f32);
    
    // Operations that can be numerically unstable
    // 1. log(exp(x)) - should equal x but can overflow
    const exp_x = try input.exp();
    const log_exp_x = try exp_x.log();
    
    // 2. sqrt(x^2) - should equal |x| but can have issues near 0
    const squared = try input.square();
    const sqrt_squared = try squared.sqrt();
    
    // 3. x - x (should be exactly 0)
    const self_diff = try input.subtract(input);
    
    // 4. x / x (should be 1, except at 0)
    const epsilon = try tensor.constant(&graph, 1e-8, .f32);
    const safe_input = try input.add(epsilon);
    const self_div = try safe_input.divide(safe_input);
    
    try graph.output_nodes.append(graph.arena.allocator(), log_exp_x.node_id);
    try graph.output_nodes.append(graph.arena.allocator(), sqrt_squared.node_id);
    try graph.output_nodes.append(graph.arena.allocator(), self_diff.node_id);
    try graph.output_nodes.append(graph.arena.allocator(), self_div.node_id);
    
    var compiled = try compiler.compile.compileCpu(&graph, allocator);
    defer compiled.deinit(allocator);
    
    var executor = try execution.Executor.init(allocator, &compiled, null);
    defer executor.deinit();
    
    // Test with potentially problematic values
    const input_data = [_]f32{ -10.0, 0.0, 10.0, 100.0 };
    
    try executor.setInput(input.node_id, std.mem.sliceAsBytes(&input_data), &.{4}, .f32);
    
    try executor.run();
    
    print("\n=== Numerically Unstable Operations ===\n", .{});
    print("Input: [-10, 0, 10, 100]\n", .{});
    
    const log_exp_out = try executor.getOutput(log_exp_x.node_id);
    const log_exp_f32 = @as([*]const f32, @ptrCast(@alignCast(log_exp_out.data.ptr)))[0..4];
    print("log(exp(x)): [", .{});
    for (log_exp_f32, 0..) |val, i| {
        print("{d:.1} ", .{val});
        // For large values like 100, exp(100) overflows to inf, which is expected
        if (i < 3) {
            try testing.expect(std.math.isFinite(val));
        } else {
            // exp(100) = inf, log(inf) = inf is expected behavior
            try testing.expect(std.math.isInf(val));
        }
    }
    print("]\n", .{});
    
    const sqrt_sq_out = try executor.getOutput(sqrt_squared.node_id);
    const sqrt_sq_f32 = @as([*]const f32, @ptrCast(@alignCast(sqrt_sq_out.data.ptr)))[0..4];
    print("sqrt(x²): [", .{});
    for (sqrt_sq_f32) |val| {
        print("{d:.1} ", .{val});
        try testing.expect(std.math.isFinite(val));
    }
    print("]\n", .{});
    
    const self_diff_out = try executor.getOutput(self_diff.node_id);
    const self_diff_f32 = @as([*]const f32, @ptrCast(@alignCast(self_diff_out.data.ptr)))[0..4];
    print("x - x: [", .{});
    for (self_diff_f32) |val| {
        print("{d:.1} ", .{val});
        try testing.expect(@abs(val) < 1e-5);
    }
    print("]\n", .{});
}

test "edge case: Graph with multiple paths and dependencies" {
    const allocator = testing.allocator;
    
    var graph = try Graph.init(allocator);
    defer graph.deinit();
    
    // Create a diamond-shaped computation graph
    const input = try tensor.placeholder(&graph, &.{3, 3}, .f32);
    
    // Two parallel paths
    const path1_1 = try input.add(try tensor.constant(&graph, 1.0, .f32));
    const path1_2 = try path1_1.square();
    
    const path2_1 = try input.mul(try tensor.constant(&graph, 2.0, .f32));
    const path2_2 = try path2_1.relu();
    
    // Merge paths
    const merged = try path1_2.add(path2_2);
    
    // Split again
    const final1 = try merged.mean(1, false);
    const final2 = try merged.sum(0, false);
    
    // Final combination
    const final1_broadcast = try final1.broadcast(&.{3, 3});
    const result = try final1_broadcast.add(final2);
    
    try graph.output_nodes.append(graph.arena.allocator(), result.node_id);
    
    var compiled = try compiler.compile.compileCpu(&graph, allocator);
    defer compiled.deinit(allocator);
    
    var executor = try execution.Executor.init(allocator, &compiled, null);
    defer executor.deinit();
    
    const input_data = [_]f32{
        -1, 0, 1,
        -2, 0, 2,
        -3, 0, 3,
    };
    
    try executor.setInput(input.node_id, std.mem.sliceAsBytes(&input_data), &.{3, 3}, .f32);
    
    try executor.run();
    
    const output = try executor.getOutput(result.node_id);
    const output_f32 = @as([*]const f32, @ptrCast(@alignCast(output.data.ptr)))[0..9];
    
    print("\n=== Multi-Path Graph Edge Case ===\n", .{});
    print("Diamond-shaped computation graph with parallel paths\n", .{});
    print("Result:\n", .{});
    for (0..3) |i| {
        print("  [", .{});
        for (0..3) |j| {
            print("{d:.1} ", .{output_f32[i * 3 + j]});
        }
        print("]\n", .{});
    }
    
    // Verify all finite
    for (output_f32) |val| {
        try testing.expect(std.math.isFinite(val));
    }
}

test "edge case: Consecutive reductions with different keepdims" {
    const allocator = testing.allocator;
    
    var graph = try Graph.init(allocator);
    defer graph.deinit();
    
    // Start with 4D tensor
    const input = try tensor.placeholder(&graph, &.{2, 3, 4, 5}, .f32);
    
    // Chain of reductions with mixed keepdims
    const sum1 = try input.sum(1, true);    // [2, 1, 4, 5]
    const mean1 = try sum1.mean(2, false);  // [2, 1, 5]
    const sum2 = try mean1.sum(2, false);   // [2, 1]
    const mean2 = try sum2.mean(0, true);   // [1, 1]
    
    // Expand back and combine with original
    const expanded = try mean2.broadcast(&.{2, 3, 4, 5});
    const result = try input.add(expanded);
    
    try graph.output_nodes.append(graph.arena.allocator(), result.node_id);
    
    var compiled = try compiler.compile.compileCpu(&graph, allocator);
    defer compiled.deinit(allocator);
    
    var executor = try execution.Executor.init(allocator, &compiled, null);
    defer executor.deinit();
    
    var input_data: [120]f32 = undefined;
    for (0..120) |i| {
        input_data[i] = @as(f32, @floatFromInt(i % 5)) * 0.1;
    }
    
    try executor.setInput(input.node_id, std.mem.sliceAsBytes(&input_data), &.{2, 3, 4, 5}, .f32);
    
    try executor.run();
    
    const output = try executor.getOutput(result.node_id);
    const output_f32 = @as([*]const f32, @ptrCast(@alignCast(output.data.ptr)))[0..120];
    
    print("\n=== Consecutive Reductions Edge Case ===\n", .{});
    print("Input: [2,3,4,5], chain of reductions with mixed keepdims\n", .{});
    print("Sample output values: ", .{});
    for (0..5) |i| {
        print("{d:.3} ", .{output_f32[i]});
    }
    print("...\n", .{});
    
    // Verify shape and values
    try testing.expect(output.shape.len == 4);
    for (output_f32) |val| {
        try testing.expect(std.math.isFinite(val));
    }
}

test "comprehensive operation test summary" {
    print("\n", .{});
    print("=" ** 60 ++ "\n", .{});
    print("COMPREHENSIVE OPERATION TEST SUITE\n", .{});
    print("=" ** 60 ++ "\n", .{});
    print("Tested:\n", .{});
    print("  ✓ Arithmetic operations (add, mul, div chains)\n", .{});
    print("  ✓ Reduction operations (sum, mean on different axes)\n", .{});
    print("  ✓ Broadcasting operations (scalar, complex patterns)\n", .{});
    print("  ✓ View operations (reshape, transpose)\n", .{});
    print("  ✓ Unary operations (neg, abs, sqrt chains)\n", .{});
    print("  ✓ Edge cases (scalars, large tensors)\n", .{});
    print("  ✓ Complex broadcasting with singleton dimensions\n", .{});
    print("  ✓ High-rank tensor reductions\n", .{});
    print("  ✓ View operation chains\n", .{});
    print("  ✓ Mixed scale operations\n", .{});
    print("  ✓ Zero-dimension edge cases\n", .{});
    print("  ✓ Extreme broadcasting ratios\n", .{});
    print("  ✓ Numerically unstable operations\n", .{});
    print("  ✓ Multi-path graph dependencies\n", .{});
    print("  ✓ Consecutive reductions with keepdims\n", .{});
    print("=" ** 60 ++ "\n", .{});
}