/// Integration tests for view tensor operations
/// Tests view operations that manipulate tensor shapes and memory layout
/// All view operations: reshape, transpose, slice, expand, squeeze/unsqueeze, flatten, broadcast

const std = @import("std");
const testing = std.testing;
const print = std.debug.print;

// Import Zing components
const types = @import("types");
const Graph = @import("graph").Graph;
const tensor = @import("tensor");
const compiler = @import("compiler");
const execution = @import("execution");

// Helper to compare results with tolerance
fn expectApproxEqSlice(expected: []const f32, actual: []const f32, tolerance: f32) !void {
    try testing.expectEqual(expected.len, actual.len);
    for (expected, actual, 0..) |e, a, i| {
        if (@abs(e - a) > tolerance) {
            print("Mismatch at index {}: expected {d:.6}, got {d:.6}\n", .{ i, e, a });
            return error.ValueMismatch;
        }
    }
}

test "view: reshape operation" {
    const allocator = testing.allocator;
    
    print("\n=== Testing: reshape operation ===\n", .{});
    
    var graph = try Graph.init(allocator);
    defer graph.deinit();
    
    const x = try tensor.placeholder(&graph, &.{2, 3, 4}, .f32);
    const reshaped = try x.reshape(&.{6, 4});
    
    try graph.output_nodes.append(graph.arena.allocator(), reshaped.node_id);
    
    var compiled = try compiler.compile.compileCpu(&graph, allocator);
    defer compiled.deinit(allocator);
    
    var executor = try execution.Executor.init(allocator, &compiled, null);
    defer executor.deinit();
    
    // Test data: [0, 1, 2, ..., 23]
    const x_data = blk: {
        var data: [24]f32 = undefined;
        for (0..24) |i| {
            data[i] = @as(f32, @floatFromInt(i));
        }
        break :blk data;
    };
    const shape = [_]i64{2, 3, 4};
    
    try executor.setInput(x.node_id, std.mem.asBytes(&x_data), &shape, .f32);
    try executor.run();
    
    const output = try executor.getOutput(reshaped.node_id);
    const output_f32 = @as([*]const f32, @ptrCast(@alignCast(output.data.ptr)))[0..24];
    
    // Reshape should preserve data order
    try expectApproxEqSlice(&x_data, output_f32, 1e-6);
    
    print("✓ reshape operation test passed!\n", .{});
}

test "view: reshape with auto-infer (-1)" {
    const allocator = testing.allocator;
    
    print("\n=== Testing: reshape with auto-infer (-1) ===\n", .{});
    
    var graph = try Graph.init(allocator);
    defer graph.deinit();
    
    const x = try tensor.placeholder(&graph, &.{2, 3, 4}, .f32);
    const reshaped = try x.reshape(&.{-1, 6}); // Should infer 4
    
    try graph.output_nodes.append(graph.arena.allocator(), reshaped.node_id);
    
    var compiled = try compiler.compile.compileCpu(&graph, allocator);
    defer compiled.deinit(allocator);
    
    var executor = try execution.Executor.init(allocator, &compiled, null);
    defer executor.deinit();
    
    // Test data
    const x_data = blk: {
        var data: [24]f32 = undefined;
        for (0..24) |i| {
            data[i] = @as(f32, @floatFromInt(i));
        }
        break :blk data;
    };
    const shape = [_]i64{2, 3, 4};
    
    try executor.setInput(x.node_id, std.mem.asBytes(&x_data), &shape, .f32);
    try executor.run();
    
    const output = try executor.getOutput(reshaped.node_id);
    const output_f32 = @as([*]const f32, @ptrCast(@alignCast(output.data.ptr)))[0..24];
    
    // Data should be preserved
    try expectApproxEqSlice(&x_data, output_f32, 1e-6);
    
    print("✓ reshape with auto-infer test passed!\n", .{});
}

test "view: transpose operation" {
    const allocator = testing.allocator;
    
    print("\n=== Testing: transpose operation ===\n", .{});
    
    var graph = try Graph.init(allocator);
    defer graph.deinit();
    
    const x = try tensor.placeholder(&graph, &.{2, 3}, .f32);
    const transposed = try x.transpose(&.{1, 0}); // Swap dimensions
    
    // Following Luminal's approach: view operations don't materialize
    // When a view is an output, we get the original data layout
    // To test transpose actually works, we need a compute operation
    const scalar_one = try tensor.constant(&graph, 1.0, .f32);
    const result = try transposed.add(scalar_one); // This materializes the transpose
    
    try graph.output_nodes.append(graph.arena.allocator(), result.node_id);
    
    var compiled = try compiler.compile.compileCpu(&graph, allocator);
    defer compiled.deinit(allocator);
    
    var executor = try execution.Executor.init(allocator, &compiled, null);
    defer executor.deinit();
    
    // Input matrix: [[0, 1, 2], [3, 4, 5]]
    const x_data = [_]f32{ 0, 1, 2, 3, 4, 5 };
    const shape = [_]i64{2, 3};
    
    try executor.setInput(x.node_id, std.mem.asBytes(&x_data), &shape, .f32);
    try executor.run();
    
    const output = try executor.getOutput(result.node_id);
    const output_f32 = @as([*]const f32, @ptrCast(@alignCast(output.data.ptr)))[0..6];
    
    // Transposed [[0, 3], [1, 4], [2, 5]] + 1 = [[1, 4], [2, 5], [3, 6]]
    const expected = [_]f32{ 1, 4, 2, 5, 3, 6 };
    try expectApproxEqSlice(&expected, output_f32, 1e-6);
    
    print("✓ transpose operation test passed!\n", .{});
}

test "view: expand/broadcasting operation" {
    const allocator = testing.allocator;
    
    print("\n=== Testing: expand/broadcasting operation ===\n", .{});
    
    var graph = try Graph.init(allocator);
    defer graph.deinit();
    
    const x = try tensor.placeholder(&graph, &.{1, 3}, .f32);
    const expanded = try x.expand(&.{2, 3}); // Broadcast first dimension
    
    try graph.output_nodes.append(graph.arena.allocator(), expanded.node_id);
    
    var compiled = try compiler.compile.compileCpu(&graph, allocator);
    defer compiled.deinit(allocator);
    
    var executor = try execution.Executor.init(allocator, &compiled, null);
    defer executor.deinit();
    
    // Input: [1, 2, 3] (shape [1, 3])
    const x_data = [_]f32{ 1, 2, 3 };
    const shape = [_]i64{1, 3};
    
    try executor.setInput(x.node_id, std.mem.asBytes(&x_data), &shape, .f32);
    try executor.run();
    
    const output = try executor.getOutput(expanded.node_id);
    const output_f32 = @as([*]const f32, @ptrCast(@alignCast(output.data.ptr)))[0..6];
    
    // Expected: [[1, 2, 3], [1, 2, 3]]
    const expected = [_]f32{ 1, 2, 3, 1, 2, 3 };
    try expectApproxEqSlice(&expected, output_f32, 1e-6);
    
    print("✓ expand/broadcasting operation test passed!\n", .{});
}

// Tests from test_broadcast_issue_debug.zig
test "view: broadcast - simple broadcast operation" {
    const allocator = testing.allocator;
    
    print("\n=== Testing: simple broadcast [2,1] -> [2,3] ===\n", .{});
    
    var graph = try Graph.init(allocator);
    defer graph.deinit();
    
    // Test simple broadcast: [2,1] -> [2,3]
    const input = try tensor.placeholder(&graph, &.{2, 1}, .f32);
    const broadcasted = try input.broadcast(&.{2, 3});
    
    try graph.output_nodes.append(graph.arena.allocator(), broadcasted.node_id);
    
    // Compile and run
    var compiled = try compiler.compile.compileCpu(&graph, allocator);
    defer compiled.deinit(allocator);
    
    var executor = try execution.Executor.init(allocator, &compiled, null);
    defer executor.deinit();
    
    // Input: [[1], [2]]
    const input_data = [_]f32{ 1, 2 };
    try executor.setInput(input.node_id, std.mem.sliceAsBytes(&input_data), &.{2, 1}, .f32);
    
    try executor.run();
    
    const output = try executor.getOutput(broadcasted.node_id);
    const output_f32 = @as([*]const f32, @ptrCast(@alignCast(output.data.ptr)))[0..6];
    
    // Expected: [[1, 1, 1], [2, 2, 2]] = [1, 1, 1, 2, 2, 2]
    const expected = [_]f32{ 1, 1, 1, 2, 2, 2 };
    try expectApproxEqSlice(&expected, output_f32, 1e-5);
    
    print("✓ simple broadcast test passed!\n", .{});
}

test "view: broadcast - expand_dim then broadcast" {
    const allocator = testing.allocator;
    
    print("\n=== Testing: expand + broadcast [2] -> [2,1] -> [2,3] ===\n", .{});
    
    var graph = try Graph.init(allocator);
    defer graph.deinit();
    
    // Test the exact pattern: [2] -> [2,1] -> [2,3]
    const input = try tensor.placeholder(&graph, &.{2}, .f32);
    const expanded = try input.expandDim(1, 1); // [2] -> [2,1]  
    const broadcasted = try expanded.broadcast(&.{2, 3}); // [2,1] -> [2,3]
    
    try graph.output_nodes.append(graph.arena.allocator(), broadcasted.node_id);
    
    // Compile and run
    var compiled = try compiler.compile.compileCpu(&graph, allocator);
    defer compiled.deinit(allocator);
    
    var executor = try execution.Executor.init(allocator, &compiled, null);
    defer executor.deinit();
    
    // Input: [10, 20]
    const input_data = [_]f32{ 10, 20 };
    try executor.setInput(input.node_id, std.mem.sliceAsBytes(&input_data), &.{2}, .f32);
    
    try executor.run();
    
    const output = try executor.getOutput(broadcasted.node_id);
    const output_f32 = @as([*]const f32, @ptrCast(@alignCast(output.data.ptr)))[0..6];
    
    // Expected: [[10, 10, 10], [20, 20, 20]] = [10, 10, 10, 20, 20, 20]
    const expected = [_]f32{ 10, 10, 10, 20, 20, 20 };
    try expectApproxEqSlice(&expected, output_f32, 1e-5);
    
    print("✓ expand + broadcast test passed!\n", .{});
}

test "view: broadcast - 3D broadcast operation" {
    const allocator = testing.allocator;
    
    print("\n=== Testing: 3D broadcast [2,1,4] -> [2,3,4] ===\n", .{});
    
    var graph = try Graph.init(allocator);
    defer graph.deinit();
    
    // Test exact failing pattern: [2,1,4] -> [2,3,4]
    const input = try tensor.placeholder(&graph, &.{2, 1, 4}, .f32);
    const broadcasted = try input.broadcast(&.{2, 3, 4});
    
    try graph.output_nodes.append(graph.arena.allocator(), broadcasted.node_id);
    
    // Compile and run
    var compiled = try compiler.compile.compileCpu(&graph, allocator);
    defer compiled.deinit(allocator);
    
    var executor = try execution.Executor.init(allocator, &compiled, null);
    defer executor.deinit();
    
    // Input: shape [2,1,4] = [12, 15, 18, 21, 48, 51, 54, 57]
    const input_data = [_]f32{ 12, 15, 18, 21, 48, 51, 54, 57 };
    try executor.setInput(input.node_id, std.mem.sliceAsBytes(&input_data), &.{2, 1, 4}, .f32);
    
    try executor.run();
    
    const output = try executor.getOutput(broadcasted.node_id);
    const output_f32 = @as([*]const f32, @ptrCast(@alignCast(output.data.ptr)))[0..24];
    
    // Expected: each [1,4] slice repeated 3 times
    // [12,15,18,21] repeated 3 times, then [48,51,54,57] repeated 3 times
    const expected = [_]f32{
        12, 15, 18, 21,  // First slice, first repeat
        12, 15, 18, 21,  // First slice, second repeat  
        12, 15, 18, 21,  // First slice, third repeat
        48, 51, 54, 57,  // Second slice, first repeat
        48, 51, 54, 57,  // Second slice, second repeat
        48, 51, 54, 57,  // Second slice, third repeat
    };
    
    try expectApproxEqSlice(&expected, output_f32, 1e-5);
    
    print("✓ 3D broadcast test passed!\n", .{});
}

test "view: squeeze operation" {
    const allocator = testing.allocator;
    
    print("\n=== Testing: squeeze operation ===\n", .{});
    
    var graph = try Graph.init(allocator);
    defer graph.deinit();
    
    const x = try tensor.placeholder(&graph, &.{2, 1, 3}, .f32);
    const squeezed = try x.squeeze(1); // Remove dimension at index 1
    
    try graph.output_nodes.append(graph.arena.allocator(), squeezed.node_id);
    
    var compiled = try compiler.compile.compileCpu(&graph, allocator);
    defer compiled.deinit(allocator);
    
    var executor = try execution.Executor.init(allocator, &compiled, null);
    defer executor.deinit();
    
    // Input data
    const x_data = [_]f32{ 1, 2, 3, 4, 5, 6 };
    const shape = [_]i64{2, 1, 3};
    
    try executor.setInput(x.node_id, std.mem.asBytes(&x_data), &shape, .f32);
    try executor.run();
    
    const output = try executor.getOutput(squeezed.node_id);
    const output_f32 = @as([*]const f32, @ptrCast(@alignCast(output.data.ptr)))[0..6];
    
    // Data should be preserved, just shape changes from [2,1,3] to [2,3]
    try expectApproxEqSlice(&x_data, output_f32, 1e-6);
    
    print("✓ squeeze operation test passed!\n", .{});
}

test "view: unsqueeze operation" {
    const allocator = testing.allocator;
    
    print("\n=== Testing: unsqueeze operation ===\n", .{});
    
    var graph = try Graph.init(allocator);
    defer graph.deinit();
    
    const x = try tensor.placeholder(&graph, &.{2, 3}, .f32);
    const unsqueezed = try x.unsqueeze(1); // Add dimension at index 1
    
    try graph.output_nodes.append(graph.arena.allocator(), unsqueezed.node_id);
    
    var compiled = try compiler.compile.compileCpu(&graph, allocator);
    defer compiled.deinit(allocator);
    
    var executor = try execution.Executor.init(allocator, &compiled, null);
    defer executor.deinit();
    
    // Input data
    const x_data = [_]f32{ 1, 2, 3, 4, 5, 6 };
    const shape = [_]i64{2, 3};
    
    try executor.setInput(x.node_id, std.mem.asBytes(&x_data), &shape, .f32);
    try executor.run();
    
    const output = try executor.getOutput(unsqueezed.node_id);
    const output_f32 = @as([*]const f32, @ptrCast(@alignCast(output.data.ptr)))[0..6];
    
    // Data should be preserved, just shape changes from [2,3] to [2,1,3]
    try expectApproxEqSlice(&x_data, output_f32, 1e-6);
    
    print("✓ unsqueeze operation test passed!\n", .{});
}

test "view: flatten operation" {
    const allocator = testing.allocator;
    
    print("\n=== Testing: flatten operation ===\n", .{});
    
    var graph = try Graph.init(allocator);
    defer graph.deinit();
    
    const x = try tensor.placeholder(&graph, &.{2, 3, 4}, .f32);
    const flattened = try x.flatten();
    
    try graph.output_nodes.append(graph.arena.allocator(), flattened.node_id);
    
    var compiled = try compiler.compile.compileCpu(&graph, allocator);
    defer compiled.deinit(allocator);
    
    var executor = try execution.Executor.init(allocator, &compiled, null);
    defer executor.deinit();
    
    // Test data
    const x_data = blk: {
        var data: [24]f32 = undefined;
        for (0..24) |i| {
            data[i] = @as(f32, @floatFromInt(i));
        }
        break :blk data;
    };
    const shape = [_]i64{2, 3, 4};
    
    try executor.setInput(x.node_id, std.mem.asBytes(&x_data), &shape, .f32);
    try executor.run();
    
    const output = try executor.getOutput(flattened.node_id);
    const output_f32 = @as([*]const f32, @ptrCast(@alignCast(output.data.ptr)))[0..24];
    
    // Flatten should preserve data order
    try expectApproxEqSlice(&x_data, output_f32, 1e-6);
    
    print("✓ flatten operation test passed!\n", .{});
}

test "view: slice operation" {
    const allocator = testing.allocator;
    
    print("\n=== Testing: slice operation ===\n", .{});
    
    var graph = try Graph.init(allocator);
    defer graph.deinit();
    
    const x = try tensor.placeholder(&graph, &.{3, 4}, .f32);
    const sliced = try x.slice(&.{1, 1}, &.{3, 3}); // [1:3, 1:3]
    
    try graph.output_nodes.append(graph.arena.allocator(), sliced.node_id);
    
    var compiled = try compiler.compile.compileCpu(&graph, allocator);
    defer compiled.deinit(allocator);
    
    var executor = try execution.Executor.init(allocator, &compiled, null);
    defer executor.deinit();
    
    // Input matrix: [[0,1,2,3], [4,5,6,7], [8,9,10,11]]
    const x_data = [_]f32{ 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11 };
    const shape = [_]i64{3, 4};
    
    try executor.setInput(x.node_id, std.mem.asBytes(&x_data), &shape, .f32);
    try executor.run();
    
    const output = try executor.getOutput(sliced.node_id);
    const output_f32 = @as([*]const f32, @ptrCast(@alignCast(output.data.ptr)))[0..4];
    
    // Expected slice: [[5,6], [9,10]]
    const expected = [_]f32{ 5, 6, 9, 10 };
    try expectApproxEqSlice(&expected, output_f32, 1e-6);
    
    print("✓ slice operation test passed!\n", .{});
}

test "view: complex view chain" {
    const allocator = testing.allocator;
    
    print("\n=== Testing: complex view chain ===\n", .{});
    
    var graph = try Graph.init(allocator);
    defer graph.deinit();
    
    const x = try tensor.placeholder(&graph, &.{2, 6}, .f32);
    // Chain: reshape -> transpose -> squeeze
    const reshaped = try x.reshape(&.{2, 2, 3});
    const transposed = try reshaped.transpose(&.{1, 0, 2});
    const unsqueezed = try transposed.unsqueeze(3);
    
    try graph.output_nodes.append(graph.arena.allocator(), unsqueezed.node_id);
    
    var compiled = try compiler.compile.compileCpu(&graph, allocator);
    defer compiled.deinit(allocator);
    
    var executor = try execution.Executor.init(allocator, &compiled, null);
    defer executor.deinit();
    
    // Input data
    const x_data = [_]f32{ 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11 };
    const shape = [_]i64{2, 6};
    
    try executor.setInput(x.node_id, std.mem.asBytes(&x_data), &shape, .f32);
    try executor.run();
    
    const output = try executor.getOutput(unsqueezed.node_id);
    const output_f32 = @as([*]const f32, @ptrCast(@alignCast(output.data.ptr)))[0..12];
    
    // After reshape [2,6] -> [2,2,3]: [[[0,1,2],[3,4,5]], [[6,7,8],[9,10,11]]]
    // After transpose [1,0,2]: [[[0,1,2],[6,7,8]], [[3,4,5],[9,10,11]]]
    // After unsqueeze at axis 3: same data, different shape
    const expected = [_]f32{ 0, 1, 2, 6, 7, 8, 3, 4, 5, 9, 10, 11 };
    try expectApproxEqSlice(&expected, output_f32, 1e-6);
    
    print("✓ complex view chain test passed!\n", .{});
}

test "view: 3D operations" {
    const allocator = testing.allocator;
    
    print("\n=== Testing: 3D view operations ===\n", .{});
    
    var graph = try Graph.init(allocator);
    defer graph.deinit();
    
    const x = try tensor.placeholder(&graph, &.{2, 3, 4}, .f32);
    // Test 3D transpose
    const transposed = try x.transpose(&.{2, 1, 0});
    const flattened = try transposed.flatten();
    
    try graph.output_nodes.append(graph.arena.allocator(), flattened.node_id);
    
    var compiled = try compiler.compile.compileCpu(&graph, allocator);
    defer compiled.deinit(allocator);
    
    var executor = try execution.Executor.init(allocator, &compiled, null);
    defer executor.deinit();
    
    // Test data
    const x_data = blk: {
        var data: [24]f32 = undefined;
        for (0..24) |i| {
            data[i] = @as(f32, @floatFromInt(i));
        }
        break :blk data;
    };
    const shape = [_]i64{2, 3, 4};
    
    try executor.setInput(x.node_id, std.mem.asBytes(&x_data), &shape, .f32);
    try executor.run();
    
    const output = try executor.getOutput(flattened.node_id);
    const output_f32 = @as([*]const f32, @ptrCast(@alignCast(output.data.ptr)))[0..24];
    
    // 3D transpose [2,3,4] -> [4,3,2] should reorder elements
    // Verify we get all original elements (though reordered)
    var sum_original: f32 = 0;
    var sum_result: f32 = 0;
    for (x_data) |val| sum_original += val;
    for (output_f32) |val| sum_result += val;
    
    try testing.expectApproxEqAbs(sum_original, sum_result, 1e-5);
    
    print("✓ 3D view operations test passed!\n", .{});
}

test "view operations integration test suite" {
    print("\n" ++ "=" ** 60 ++ "\n", .{});
    print("VIEW OPERATIONS INTEGRATION TEST SUITE\n", .{});
    print("=" ** 60 ++ "\n", .{});
    
    print("\nThis test suite verifies all view tensor operations:\n", .{});
    print("• Shape manipulation: reshape, flatten\n", .{});
    print("• Dimension manipulation: transpose, squeeze, unsqueeze\n", .{});
    print("• Broadcasting: expand, broadcast\n", .{});
    print("• Data access: slice\n", .{});
    print("• Complex view chains and 3D operations\n", .{});
    
    print("\n" ++ "=" ** 60 ++ "\n", .{});
    print("✅ ALL VIEW OPERATION TESTS COMPLETED SUCCESSFULLY!\n", .{});
    print("=" ** 60 ++ "\n", .{});
}