/// Session API Integration Tests
///
/// This module tests the high-level Session API that provides automatic
/// lifecycle management and PyTorch-like method chaining for tensor operations.

const std = @import("std");
const testing = std.testing;

// Import modules
const types = @import("types");
const session = @import("session");
const DataType = types.DataType;
const Session = session.Session;
const SessionTensor = session.SessionTensor;

test "Session API - Basic tensor creation and properties" {
    var sess = try Session.init(testing.allocator);
    defer sess.deinit();
    
    // Test zeros tensor
    const zeros_tensor = try sess.zeros(&.{2, 3}, .f32);
    try testing.expectEqual(@as(usize, 2), zeros_tensor.rank());
    try testing.expectEqual(@as(i64, 6), zeros_tensor.size());
    try testing.expectEqual(@as(i64, 2), zeros_tensor.shape[0]);
    try testing.expectEqual(@as(i64, 3), zeros_tensor.shape[1]);
    
    // Test ones tensor
    const ones_tensor = try sess.ones(&.{3, 2}, .f32);
    try testing.expectEqual(@as(usize, 2), ones_tensor.rank());
    try testing.expectEqual(@as(i64, 6), ones_tensor.size());
    try testing.expectEqual(@as(i64, 3), ones_tensor.shape[0]);
    try testing.expectEqual(@as(i64, 2), ones_tensor.shape[1]);
}

test "Session API - Basic arithmetic operations" {
    var sess = try Session.init(testing.allocator);
    defer sess.deinit();
    
    // Create test tensors
    const a = try sess.ones(&.{2, 2}, .f32);
    const b = try sess.ones(&.{2, 2}, .f32);
    
    // Test addition
    const c = try a.add(b);
    try testing.expectEqual(@as(usize, 2), c.rank());
    try testing.expectEqual(@as(i64, 4), c.size());
    
    // Test multiplication  
    const d = try a.mul(b);
    try testing.expectEqual(@as(usize, 2), d.rank());
    try testing.expectEqual(@as(i64, 4), d.size());
    
    // Test subtraction
    const e = try a.subtract(b);
    try testing.expectEqual(@as(usize, 2), e.rank());
    try testing.expectEqual(@as(i64, 4), e.size());
    
    // Test division
    const f = try a.divide(b);
    try testing.expectEqual(@as(usize, 2), f.rank());
    try testing.expectEqual(@as(i64, 4), f.size());
}

test "Session API - Method chaining" {
    var sess = try Session.init(testing.allocator);
    defer sess.deinit();
    
    // Create initial tensor
    const a = try sess.ones(&.{2, 2}, .f32);
    const b = try sess.ones(&.{2, 2}, .f32);
    
    // Test method chaining: (a + b) * a
    const result = try (try a.add(b)).mul(a);
    
    try testing.expectEqual(@as(usize, 2), result.rank());
    try testing.expectEqual(@as(i64, 4), result.size());
    try testing.expectEqual(@as(i64, 2), result.shape[0]);
    try testing.expectEqual(@as(i64, 2), result.shape[1]);
}

test "Session API - Activation functions" {
    var sess = try Session.init(testing.allocator);
    defer sess.deinit();
    
    // Create test tensor
    const a = try sess.ones(&.{2, 3}, .f32);
    
    // Test ReLU
    const relu_result = try a.relu();
    try testing.expectEqual(@as(usize, 2), relu_result.rank());
    try testing.expectEqual(@as(i64, 6), relu_result.size());
}

test "Session API - Shape operations" {
    var sess = try Session.init(testing.allocator);
    defer sess.deinit();
    
    // Create test tensor [2, 3]
    const a = try sess.ones(&.{2, 3}, .f32);
    
    // Test reshape to [3, 2]
    const reshaped = try a.reshape(&.{3, 2});
    try testing.expectEqual(@as(usize, 2), reshaped.rank());
    try testing.expectEqual(@as(i64, 6), reshaped.size());
    try testing.expectEqual(@as(i64, 3), reshaped.shape[0]);
    try testing.expectEqual(@as(i64, 2), reshaped.shape[1]);
}

test "Session API - Matrix multiplication" {
    var sess = try Session.init(testing.allocator);
    defer sess.deinit();
    
    // Create compatible matrices for matmul
    const a = try sess.ones(&.{2, 3}, .f32);  // [2, 3]
    const b = try sess.ones(&.{3, 4}, .f32);  // [3, 4]
    
    // Test matrix multiplication: [2, 3] @ [3, 4] = [2, 4]
    const result = try a.matmul(b);
    try testing.expectEqual(@as(usize, 2), result.rank());
    try testing.expectEqual(@as(i64, 8), result.size());
    try testing.expectEqual(@as(i64, 2), result.shape[0]);
    try testing.expectEqual(@as(i64, 4), result.shape[1]);
}

test "Session API - Execution modes" {
    var sess = try Session.init(testing.allocator);
    defer sess.deinit();
    
    // Test default mode
    try testing.expect(sess.getMode() == .eager);
    
    // Test mode switching
    sess.setMode(.lazy);
    try testing.expect(sess.getMode() == .lazy);
    
    sess.setMode(.eager);
    try testing.expect(sess.getMode() == .eager);
}

test "Session API - Lazy execution mode" {
    var sess = try Session.init(testing.allocator);
    defer sess.deinit();
    
    // Switch to lazy mode
    sess.setMode(.lazy);
    
    // Create operations without executing
    const a = try sess.ones(&.{2, 2}, .f32);
    const b = try sess.ones(&.{2, 2}, .f32);
    const c = try a.add(b);
    
    // Should not be executed yet
    try testing.expectEqual(@as(usize, 2), c.rank());
    try testing.expectEqual(@as(i64, 4), c.size());
    
    // Explicitly run computation
    try sess.run();
}

test "Session API - Complex computation graph" {
    var sess = try Session.init(testing.allocator);
    defer sess.deinit();
    
    // Create a more complex computation: relu((a + b) * c)
    const a = try sess.ones(&.{2, 2}, .f32);
    const b = try sess.ones(&.{2, 2}, .f32);
    const c = try sess.ones(&.{2, 2}, .f32);
    
    const sum = try a.add(b);
    const product = try sum.mul(c);
    const result = try product.relu();
    
    try testing.expectEqual(@as(usize, 2), result.rank());
    try testing.expectEqual(@as(i64, 4), result.size());
}

test "Session API - Broadcasting operations" {
    var sess = try Session.init(testing.allocator);
    defer sess.deinit();
    
    // Test broadcasting: [2, 1] + [1, 3] = [2, 3]
    const a = try sess.ones(&.{2, 1}, .f32);
    const b = try sess.ones(&.{1, 3}, .f32);
    
    const result = try a.add(b);
    try testing.expectEqual(@as(usize, 2), result.rank());
    try testing.expectEqual(@as(i64, 6), result.size());
    try testing.expectEqual(@as(i64, 2), result.shape[0]);
    try testing.expectEqual(@as(i64, 3), result.shape[1]);
}

test "Session API - Incompatible tensor operations" {
    var sess = try Session.init(testing.allocator);
    defer sess.deinit();
    
    // Create tensors from different sessions (should fail)
    var sess2 = try Session.init(testing.allocator);
    defer sess2.deinit();
    
    const a = try sess.ones(&.{2, 2}, .f32);
    const b = try sess2.ones(&.{2, 2}, .f32);
    
    // This should fail with IncompatibleTensors
    const result = a.add(b);
    try testing.expectError(session.SessionError.IncompatibleTensors, result);
}

test "Session API - Memory cleanup" {
    // Test that Session properly cleans up all allocated memory
    // This test passes if there are no memory leaks detected by the test allocator
    
    var sess = try Session.init(testing.allocator);
    defer sess.deinit();
    
    // Create many tensors and operations to test memory management
    for (0..10) |_| {
        const a = try sess.ones(&.{10, 10}, .f32);
        const b = try sess.zeros(&.{10, 10}, .f32);
        const c = try a.add(b);
        const d = try c.mul(a);
        const e = try d.relu();
        const f = try e.reshape(&.{100});
        
        // Just verify the tensors are created properly
        try testing.expectEqual(@as(usize, 1), f.rank());
        try testing.expectEqual(@as(i64, 100), f.size());
    }
    
    // Session.deinit() should clean up all allocated shapes automatically
}

test "Session API - Performance with large operations" {
    var sess = try Session.init(testing.allocator);
    defer sess.deinit();
    
    // Test with larger tensors to ensure the API scales
    const a = try sess.ones(&.{100, 100}, .f32);
    const b = try sess.ones(&.{100, 100}, .f32);
    
    const result = try a.add(b);
    
    try testing.expectEqual(@as(usize, 2), result.rank());
    try testing.expectEqual(@as(i64, 10000), result.size());
    try testing.expectEqual(@as(i64, 100), result.shape[0]);
    try testing.expectEqual(@as(i64, 100), result.shape[1]);
}

test "Session API - Backend selection integration" {
    // Test default backend configuration
    {
        var sess = try Session.init(testing.allocator);
        defer sess.deinit();
        
        // Verify default backend is CPU
        try testing.expect(sess.backend_config.device == .cpu);
        
        // Test basic operations with default backend
        const a = try sess.ones(&.{2, 2}, .f32);
        const b = try sess.zeros(&.{2, 2}, .f32);
        const result = try a.add(b);
        
        try testing.expectEqual(@as(usize, 2), result.rank());
        try testing.expectEqual(@as(i64, 4), result.size());
    }
}

test "Session API - Explicit CPU backend configuration" {
    const backend_config = session.BackendConfig{ .device = .cpu };
    var sess = try Session.initWithBackend(testing.allocator, backend_config);
    defer sess.deinit();
    
    // Verify backend configuration
    try testing.expect(sess.backend_config.device == .cpu);
    
    // Test tensor operations with explicit CPU backend
    const a = try sess.ones(&.{3, 2}, .f32);
    const b = try sess.ones(&.{3, 2}, .f32);
    const c = try a.mul(b);
    const result = try c.relu();
    
    try testing.expectEqual(@as(usize, 2), result.rank());
    try testing.expectEqual(@as(i64, 6), result.size());
    try testing.expectEqual(@as(i64, 3), result.shape[0]);
    try testing.expectEqual(@as(i64, 2), result.shape[1]);
}

test "Session API - Unsupported backend fallback" {
    // Test with CUDA backend (should fallback to CPU with warning)
    const backend_config = session.BackendConfig{ .device = .cuda };
    var sess = try Session.initWithBackend(testing.allocator, backend_config);
    defer sess.deinit();
    
    // Backend config should still reflect the requested backend
    try testing.expect(sess.backend_config.device == .cuda);
    
    // Operations should still work (will use CPU backend internally)
    const a = try sess.ones(&.{2, 3}, .f32);
    const b = try sess.ones(&.{2, 3}, .f32);
    const sum = try a.add(b);
    const product = try sum.mul(a);
    const result = try product.relu();
    
    try testing.expectEqual(@as(usize, 2), result.rank());
    try testing.expectEqual(@as(i64, 6), result.size());
}

test "Session API - Backend configuration with complex operations" {
    // Test backend selection with more complex computational graphs
    const backend_config = session.BackendConfig{ .device = .cpu };
    var sess = try Session.initWithBackend(testing.allocator, backend_config);
    defer sess.deinit();
    
    // Create a neural network-like computation
    const input = try sess.ones(&.{4, 8}, .f32);  // Input layer
    const weight1 = try sess.ones(&.{8, 16}, .f32); // Hidden layer weights
    const bias1 = try sess.ones(&.{1, 16}, .f32);   // Hidden layer bias
    const weight2 = try sess.ones(&.{16, 4}, .f32); // Output layer weights
    
    // Forward pass: input -> hidden -> output
    const hidden_linear = try input.matmul(weight1);
    const hidden_bias = try hidden_linear.add(bias1);
    const hidden_activated = try hidden_bias.relu();
    const output = try hidden_activated.matmul(weight2);
    const final_result = try output.relu();
    
    // Verify the computation completed successfully
    try testing.expectEqual(@as(usize, 2), final_result.rank());
    try testing.expectEqual(@as(i64, 16), final_result.size());
    try testing.expectEqual(@as(i64, 4), final_result.shape[0]);
    try testing.expectEqual(@as(i64, 4), final_result.shape[1]);
}

test "Session API - Backend selection across execution modes" {
    // Test backend selection works with both eager and lazy execution
    const backend_config = session.BackendConfig{ .device = .cpu };
    var sess = try Session.initWithBackend(testing.allocator, backend_config);
    defer sess.deinit();
    
    // Test with eager mode (default)
    try testing.expect(sess.getMode() == .eager);
    {
        const a = try sess.ones(&.{2, 2}, .f32);
        const b = try sess.ones(&.{2, 2}, .f32);
        const result = try a.add(b);
        try testing.expectEqual(@as(i64, 4), result.size());
    }
    
    // Test with lazy mode
    sess.setMode(.lazy);
    try testing.expect(sess.getMode() == .lazy);
    {
        const a = try sess.ones(&.{3, 3}, .f32);
        const b = try sess.ones(&.{3, 3}, .f32);
        const result = try a.mul(b);
        
        // Run the computation
        try sess.run();
        
        try testing.expectEqual(@as(i64, 9), result.size());
    }
    
    // Switch back to eager mode
    sess.setMode(.eager);
    try testing.expect(sess.getMode() == .eager);
}

test "Session API - Mixed eager/lazy execution with incremental graph building" {
    var sess = try Session.init(testing.allocator);
    defer sess.deinit();
    
    // Start in eager mode and create some tensors
    const a = try sess.ones(&.{2, 3}, .f32);
    const b = try sess.ones(&.{2, 3}, .f32);
    
    // Perform operation in eager mode
    const c = try a.add(b); // c = 2
    
    // Get data - this should trigger compilation of just what's needed
    const c_data = try c.data(f32);
    try testing.expectEqual(@as(f32, 2.0), c_data[0]);
    try testing.expectEqual(@as(f32, 2.0), c_data[5]); // Last element
    
    // Switch to lazy mode and build on top of eager results
    sess.setMode(.lazy);
    
    // Build more complex graph using previous results
    const d = try sess.ones(&.{2, 3}, .f32);
    const e = try c.mul(d); // e = 2 * 1 = 2
    const f = try e.add(c); // f = 2 + 2 = 4
    
    // Get data from the lazy computation
    const f_data = try f.data(f32);
    try testing.expectEqual(@as(f32, 4.0), f_data[0]);
    try testing.expectEqual(@as(f32, 4.0), f_data[5]);
    
    // Verify that we can still access intermediate results efficiently
    const e_data = try e.data(f32);
    try testing.expectEqual(@as(f32, 2.0), e_data[0]);
}

test "Session API - Mixed execution with selective evaluation" {
    var sess = try Session.init(testing.allocator);
    defer sess.deinit();
    
    // Build a computation graph with multiple branches in lazy mode
    sess.setMode(.lazy);
    
    const x = try sess.ones(&.{4, 4}, .f32);
    const y = try sess.ones(&.{4, 4}, .f32);
    
    // Create multiple independent branches
    const branch1 = try x.add(y);      // branch1 = 2
    const branch2 = try x.mul(y);      // branch2 = 1
    const branch3 = try branch1.subtract(branch2); // branch3 = 2 - 1 = 1
    
    // Only evaluate branch3 - should compile only necessary nodes
    const branch3_data = try branch3.data(f32);
    try testing.expectEqual(@as(f32, 1.0), branch3_data[0]);
    
    // Now evaluate branch1 - should reuse already computed result
    const branch1_data = try branch1.data(f32);
    try testing.expectEqual(@as(f32, 2.0), branch1_data[0]);
    
    // Create a new branch after partial evaluation
    const branch4 = try branch2.add(branch3); // 1 + 1 = 2
    const branch4_data = try branch4.data(f32);
    try testing.expectEqual(@as(f32, 2.0), branch4_data[0]);
}

test "Session API - Mixed execution mode workflow simulation" {
    var sess = try Session.init(testing.allocator);
    defer sess.deinit();
    
    // Simulate a real ML workflow with mixed execution modes
    
    // 1. Data preprocessing in eager mode
    const raw_data = try sess.ones(&.{10, 5}, .f32);
    const scale = try sess.tensor(&.{}, .f32, @as(f32, 0.5));
    const normalized = try raw_data.mul(scale); // Immediate execution
    
    // Verify preprocessing
    const norm_data = try normalized.data(f32);
    try testing.expectEqual(@as(f32, 0.5), norm_data[0]);
    
    // 2. Switch to lazy mode for model building
    sess.setMode(.lazy);
    
    // Build a simple "neural network"
    const weights1 = try sess.ones(&.{5, 8}, .f32);
    const hidden = try normalized.matmul(weights1); // [10, 5] @ [5, 8] = [10, 8]
    const activated = try hidden.relu();
    
    const weights2 = try sess.ones(&.{8, 3}, .f32);
    const output = try activated.matmul(weights2); // [10, 8] @ [8, 3] = [10, 3]
    
    // 3. Switch back to eager for postprocessing
    sess.setMode(.eager);
    
    // Apply postprocessing
    const bias = try sess.tensor(&.{}, .f32, @as(f32, 0.1));
    const final_output = try output.add(bias);
    
    // Get final results - triggers efficient compilation of entire pipeline
    const result_data = try final_output.data(f32);
    // Each element should be: 0.5 * 5 * 8 + 0.1 = 20.1
    try testing.expectApproxEqAbs(@as(f32, 20.1), result_data[0], 0.001);
}

test "Session API - No redundant recompilations" {
    var sess = try Session.init(testing.allocator);
    defer sess.deinit();
    
    // Create initial computation
    const a = try sess.ones(&.{3, 3}, .f32);
    const b = try sess.ones(&.{3, 3}, .f32);
    const c = try a.add(b);
    
    // First data access - triggers compilation
    _ = try c.data(f32);
    const first_compiled_version = sess.last_compiled_version;
    
    // Second data access - should NOT recompile
    _ = try c.data(f32);
    try testing.expectEqual(first_compiled_version, sess.last_compiled_version);
    
    // Access data from a different tensor that was already computed
    _ = try a.data(f32); // 'a' is an input, should not trigger recompilation
    try testing.expectEqual(first_compiled_version, sess.last_compiled_version);
    
    // Create new computation
    const d = try c.mul(a);
    
    // Accessing new computation should trigger recompilation
    _ = try d.data(f32);
    try testing.expect(sess.last_compiled_version > first_compiled_version);
    
    // But accessing old results should still work without recompilation
    const second_compiled_version = sess.last_compiled_version;
    _ = try c.data(f32);
    try testing.expectEqual(second_compiled_version, sess.last_compiled_version);
}

test "Session API - Graph modification detection" {
    var sess = try Session.init(testing.allocator);
    defer sess.deinit();
    
    // Build initial graph
    const a = try sess.ones(&.{2, 2}, .f32);
    const b = try sess.ones(&.{2, 2}, .f32);
    const c = try a.add(b);
    
    // Compile by accessing data
    _ = try c.data(f32);
    const initial_version = sess.graph.modifications_count;
    
    // Add more operations - should increase modification count
    const d = try c.relu();
    try testing.expect(sess.graph.modifications_count > initial_version);
    
    // Access new data - should trigger recompilation
    const result = try d.data(f32);
    try testing.expectEqual(@as(f32, 2.0), result[0]); // ReLU(2) = 2
}

test "Session API - Edge case: Empty graph compilation" {
    var sess = try Session.init(testing.allocator);
    defer sess.deinit();
    
    // Try to run empty session - should handle gracefully
    try sess.run();
    
    // Add a single tensor and access it
    const a = try sess.ones(&.{2, 2}, .f32);
    const data = try a.data(f32);
    try testing.expectEqual(@as(f32, 1.0), data[0]);
}

test "Session API - Edge case: Interleaved mode switches" {
    var sess = try Session.init(testing.allocator);
    defer sess.deinit();
    
    // Rapidly switch modes while building graph
    const a = try sess.ones(&.{3, 3}, .f32); // eager
    
    sess.setMode(.lazy);
    const b = try sess.ones(&.{3, 3}, .f32); // lazy
    const c = try a.add(b); // lazy
    
    sess.setMode(.eager);
    const d = try sess.ones(&.{3, 3}, .f32); // eager
    const e = try c.mul(d); // eager
    
    sess.setMode(.lazy);
    const f = try e.relu(); // lazy
    
    // Access final result - should handle all mode switches correctly
    const result = try f.data(f32);
    try testing.expectEqual(@as(f32, 2.0), result[0]); // (1+1)*1 = 2, ReLU(2) = 2
    
    // Verify intermediate results are still accessible
    const c_data = try c.data(f32);
    try testing.expectEqual(@as(f32, 2.0), c_data[0]);
}

test "Session API - Stress test: Large graph with mixed modes" {
    var sess = try Session.init(testing.allocator);
    defer sess.deinit();
    
    // Build a larger computation graph mixing modes
    var tensors: [10]SessionTensor = undefined;
    
    // Create initial tensors in eager mode
    tensors[0] = try sess.ones(&.{5, 5}, .f32);
    tensors[1] = try sess.ones(&.{5, 5}, .f32);
    
    // Switch to lazy and build chain
    sess.setMode(.lazy);
    for (2..10) |i| {
        tensors[i] = try tensors[i-1].add(tensors[i-2]);
    }
    
    // Access only the final result
    const final_data = try tensors[9].data(f32);
    // This is like Fibonacci: 1,1,2,3,5,8,13,21,34,55
    try testing.expectEqual(@as(f32, 55.0), final_data[0]);
    
    // Verify compilation happened only once for all nodes
    const version = sess.last_compiled_version;
    
    // Access intermediate result - should not recompile
    const mid_data = try tensors[5].data(f32); 
    try testing.expectEqual(@as(f32, 8.0), mid_data[0]);
    try testing.expectEqual(version, sess.last_compiled_version);
}