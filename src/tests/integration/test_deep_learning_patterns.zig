const std = @import("std");
const testing = std.testing;
const print = std.debug.print;

const Graph = @import("graph").Graph;
const tensor = @import("tensor");
const compiler = @import("compiler");
const execution = @import("execution");

// Test common deep learning patterns with realistic combinations of operations

test "pattern: Linear Layer (Dense/FC) - y = Wx + b" {
    const allocator = testing.allocator;
    
    var graph = try Graph.init(allocator);
    defer graph.deinit();
    
    // Typical linear layer: input[batch=2, features=4] @ weights[4, 3] + bias[3]
    const input = try tensor.placeholder(&graph, &.{2, 4}, .f32);
    const weights = try tensor.placeholder(&graph, &.{4, 3}, .f32);
    const bias = try tensor.placeholder(&graph, &.{3}, .f32);
    
    // Forward pass: y = x @ W + b
    const matmul_result = try input.matmul(weights);
    const output = try matmul_result.add(bias); // Broadcasting bias across batch
    
    try graph.output_nodes.append(graph.arena.allocator(), output.node_id);
    
    var compiled = try compiler.compile.compileCpu(&graph, allocator);
    defer compiled.deinit(allocator);
    
    var executor = try execution.Executor.init(allocator, &compiled, null);
    defer executor.deinit();
    
    // Realistic input values
    const input_data = [_]f32{
        1.0, 0.5, -0.3, 0.8,  // batch item 1
        -0.5, 1.2, 0.3, -0.7, // batch item 2
    };
    const weight_data = [_]f32{
        0.1, 0.2, -0.3,  // weights for feature 1
        -0.4, 0.5, 0.6,  // weights for feature 2
        0.7, -0.8, 0.9,  // weights for feature 3
        -0.1, 0.2, -0.3, // weights for feature 4
    };
    const bias_data = [_]f32{ 0.1, -0.2, 0.3 };
    
    try executor.setInput(input.node_id, std.mem.sliceAsBytes(&input_data), &.{2, 4}, .f32);
    try executor.setInput(weights.node_id, std.mem.sliceAsBytes(&weight_data), &.{4, 3}, .f32);
    try executor.setInput(bias.node_id, std.mem.sliceAsBytes(&bias_data), &.{3}, .f32);
    
    try executor.run();
    
    const result = try executor.getOutput(output.node_id);
    const result_f32 = @as([*]const f32, @ptrCast(@alignCast(result.data.ptr)))[0..6];
    
    print("\n=== Linear Layer Pattern ===\n", .{});
    print("Input shape: [2, 4], Weights shape: [4, 3], Bias shape: [3]\n", .{});
    print("Output shape: [2, 3]\n", .{});
    print("Results:\n", .{});
    for (0..2) |batch| {
        print("  Batch {}: [", .{batch});
        for (0..3) |i| {
            print("{d:.3} ", .{result_f32[batch * 3 + i]});
        }
        print("]\n", .{});
    }
    
    // Verify no NaN values
    for (result_f32) |val| {
        try testing.expect(!std.math.isNan(val));
    }
}

test "pattern: Batch Normalization - normalized = (x - mean) / sqrt(var + eps) * gamma + beta" {
    const allocator = testing.allocator;
    
    var graph = try Graph.init(allocator);
    defer graph.deinit();
    
    // Input: [batch=4, features=3]
    const input = try tensor.placeholder(&graph, &.{4, 3}, .f32);
    const gamma = try tensor.placeholder(&graph, &.{3}, .f32); // scale
    const beta = try tensor.placeholder(&graph, &.{3}, .f32);  // shift
    const epsilon = try tensor.constant(&graph, 1e-5, .f32);
    
    // Compute mean and variance along batch dimension
    const mean = try input.mean(0, true);
    const x_centered = try input.subtract(mean);
    const x_squared = try x_centered.square();
    const variance = try x_squared.mean(0, true);
    
    // Normalize: (x - mean) / sqrt(var + eps)
    const var_plus_eps = try variance.add(epsilon);
    const std_dev = try var_plus_eps.sqrt();
    const normalized = try x_centered.divide(std_dev);
    
    // Scale and shift: normalized * gamma + beta
    const scaled = try normalized.mul(gamma);
    const output = try scaled.add(beta);
    
    try graph.output_nodes.append(graph.arena.allocator(), output.node_id);
    
    var compiled = try compiler.compile.compileCpu(&graph, allocator);
    defer compiled.deinit(allocator);
    
    var executor = try execution.Executor.init(allocator, &compiled, null);
    defer executor.deinit();
    
    // Test data
    const input_data = [_]f32{
        1.0, 2.0, 3.0,
        2.0, 3.0, 4.0,
        3.0, 4.0, 5.0,
        4.0, 5.0, 6.0,
    };
    const gamma_data = [_]f32{ 1.0, 1.0, 1.0 };
    const beta_data = [_]f32{ 0.0, 0.0, 0.0 };
    
    try executor.setInput(input.node_id, std.mem.sliceAsBytes(&input_data), &.{4, 3}, .f32);
    try executor.setInput(gamma.node_id, std.mem.sliceAsBytes(&gamma_data), &.{3}, .f32);
    try executor.setInput(beta.node_id, std.mem.sliceAsBytes(&beta_data), &.{3}, .f32);
    
    try executor.run();
    
    const result = try executor.getOutput(output.node_id);
    const result_f32 = @as([*]const f32, @ptrCast(@alignCast(result.data.ptr)))[0..12];
    
    print("\n=== Batch Normalization Pattern ===\n", .{});
    print("Input shape: [4, 3]\n", .{});
    print("Normalized output:\n", .{});
    for (0..4) |batch| {
        print("  Batch {}: [", .{batch});
        for (0..3) |i| {
            print("{d:.3} ", .{result_f32[batch * 3 + i]});
        }
        print("]\n", .{});
    }
    
    // Verify normalization (mean ~0, std ~1)
    for (0..3) |feature| {
        var sum: f32 = 0;
        for (0..4) |batch| {
            sum += result_f32[batch * 3 + feature];
        }
        const feature_mean = sum / 4.0;
        print("  Feature {} mean: {d:.6}\n", .{feature, feature_mean});
        try testing.expect(@abs(feature_mean) < 1e-5); // Mean should be ~0
    }
}

test "pattern: Softmax - exp(x) / sum(exp(x))" {
    const allocator = testing.allocator;
    
    var graph = try Graph.init(allocator);
    defer graph.deinit();
    
    // Input: [batch=2, classes=4]
    const logits = try tensor.placeholder(&graph, &.{2, 4}, .f32);
    
    // Numerically stable softmax: exp(x - max(x)) / sum(exp(x - max(x)))
    const max_logits = try logits.maxReduce(1, true);
    const shifted = try logits.subtract(max_logits);
    const exp_shifted = try shifted.exp();
    const sum_exp = try exp_shifted.sumReduce(1, true);
    const softmax = try exp_shifted.divide(sum_exp);
    
    try graph.output_nodes.append(graph.arena.allocator(), softmax.node_id);
    
    var compiled = try compiler.compile.compileCpu(&graph, allocator);
    defer compiled.deinit(allocator);
    
    var executor = try execution.Executor.init(allocator, &compiled, null);
    defer executor.deinit();
    
    // Test with typical logit values
    const logit_data = [_]f32{
        2.0, 1.0, 0.1, 3.0,   // batch 1: class 3 should have highest prob
        -1.0, 2.0, 3.0, 0.5, // batch 2: class 2 should have highest prob
    };
    
    try executor.setInput(logits.node_id, std.mem.sliceAsBytes(&logit_data), &.{2, 4}, .f32);
    
    try executor.run();
    
    const result = try executor.getOutput(softmax.node_id);
    const result_f32 = @as([*]const f32, @ptrCast(@alignCast(result.data.ptr)))[0..8];
    
    print("\n=== Softmax Pattern ===\n", .{});
    print("Logits shape: [2, 4]\n", .{});
    print("Softmax probabilities:\n", .{});
    
    for (0..2) |batch| {
        print("  Batch {}: [", .{batch});
        var sum: f32 = 0;
        for (0..4) |i| {
            const prob = result_f32[batch * 4 + i];
            print("{d:.3} ", .{prob});
            sum += prob;
        }
        print("] sum={d:.6}\n", .{sum});
        
        // Verify probabilities sum to 1
        try testing.expect(@abs(sum - 1.0) < 1e-5);
        
        // Verify all probabilities are positive
        for (0..4) |i| {
            try testing.expect(result_f32[batch * 4 + i] > 0);
            try testing.expect(result_f32[batch * 4 + i] < 1);
        }
    }
}

test "pattern: ReLU activation with Dropout simulation" {
    const allocator = testing.allocator;
    
    var graph = try Graph.init(allocator);
    defer graph.deinit();
    
    // Input: [batch=3, features=8]
    const input = try tensor.placeholder(&graph, &.{3, 8}, .f32);
    const dropout_mask = try tensor.placeholder(&graph, &.{3, 8}, .f32);
    const dropout_scale = try tensor.constant(&graph, 1.0 / 0.8, .f32); // p=0.8 keep prob
    
    // ReLU: max(0, x)
    const zero = try tensor.constant(&graph, 0.0, .f32);
    const relu = try input.maximum(zero);
    
    // Dropout: relu * mask * scale
    const masked = try relu.mul(dropout_mask);
    const output = try masked.mul(dropout_scale);
    
    try graph.output_nodes.append(graph.arena.allocator(), output.node_id);
    
    var compiled = try compiler.compile.compileCpu(&graph, allocator);
    defer compiled.deinit(allocator);
    
    var executor = try execution.Executor.init(allocator, &compiled, null);
    defer executor.deinit();
    
    // Test data with positive and negative values
    const input_data = [_]f32{
        // Mix of positive and negative values
        1.0, -0.5, 0.3, -1.2, 0.8, -0.3, 1.5, -2.0,
        -0.5, 1.2, -0.8, 0.4, -1.0, 0.7, -0.2, 1.8,
        0.2, -0.9, 1.1, -0.4, 0.6, -1.5, 0.9, -0.1,
    };
    
    // Dropout mask (1 = keep, 0 = drop)
    const mask_data = [_]f32{
        1, 1, 0, 1, 1, 0, 1, 1,
        0, 1, 1, 1, 0, 1, 1, 0,
        1, 0, 1, 1, 0, 1, 0, 1,
    };
    
    try executor.setInput(input.node_id, std.mem.sliceAsBytes(&input_data), &.{3, 8}, .f32);
    try executor.setInput(dropout_mask.node_id, std.mem.sliceAsBytes(&mask_data), &.{3, 8}, .f32);
    
    try executor.run();
    
    const result = try executor.getOutput(output.node_id);
    const result_f32 = @as([*]const f32, @ptrCast(@alignCast(result.data.ptr)))[0..24];
    
    print("\n=== ReLU + Dropout Pattern ===\n", .{});
    print("Input shape: [3, 8]\n", .{});
    
    var active_count: u32 = 0;
    var dropped_count: u32 = 0;
    
    for (0..3) |batch| {
        print("  Batch {}:\n", .{batch});
        print("    Input:  [", .{});
        for (0..8) |i| {
            print("{d:.1} ", .{input_data[batch * 8 + i]});
        }
        print("]\n    Output: [", .{});
        for (0..8) |i| {
            const val = result_f32[batch * 8 + i];
            print("{d:.3} ", .{val});
            
            // Count active/dropped neurons
            if (val > 0) {
                active_count += 1;
            } else {
                dropped_count += 1;
            }
        }
        print("]\n", .{});
    }
    
    print("  Active neurons: {}, Dropped neurons: {}\n", .{active_count, dropped_count});
}

test "pattern: Layer Normalization - used in Transformers" {
    const allocator = testing.allocator;
    
    var graph = try Graph.init(allocator);
    defer graph.deinit();
    
    // Input: [batch=2, seq_len=4, hidden=6]
    const input = try tensor.placeholder(&graph, &.{2, 4, 6}, .f32);
    const gamma = try tensor.placeholder(&graph, &.{6}, .f32);
    const beta = try tensor.placeholder(&graph, &.{6}, .f32);
    const epsilon = try tensor.constant(&graph, 1e-5, .f32);
    
    // Normalize over hidden dimension (axis=2)
    const mean = try input.mean(2, true);
    const x_centered = try input.subtract(mean);
    const x_squared = try x_centered.square();
    const variance = try x_squared.mean(2, true);
    
    // Normalize
    const var_plus_eps = try variance.add(epsilon);
    const std_dev = try var_plus_eps.sqrt();
    const normalized = try x_centered.divide(std_dev);
    
    // Scale and shift
    const scaled = try normalized.mul(gamma);
    const output = try scaled.add(beta);
    
    try graph.output_nodes.append(graph.arena.allocator(), output.node_id);
    
    var compiled = try compiler.compile.compileCpu(&graph, allocator);
    defer compiled.deinit(allocator);
    
    var executor = try execution.Executor.init(allocator, &compiled, null);
    defer executor.deinit();
    
    // Simulated hidden states
    var input_data: [48]f32 = undefined;
    for (0..48) |i| {
        input_data[i] = @as(f32, @floatFromInt(i % 6)) * 0.5 - 1.5;
    }
    
    const gamma_data = [_]f32{ 1.0, 1.0, 1.0, 1.0, 1.0, 1.0 };
    const beta_data = [_]f32{ 0.0, 0.0, 0.0, 0.0, 0.0, 0.0 };
    
    try executor.setInput(input.node_id, std.mem.sliceAsBytes(&input_data), &.{2, 4, 6}, .f32);
    try executor.setInput(gamma.node_id, std.mem.sliceAsBytes(&gamma_data), &.{6}, .f32);
    try executor.setInput(beta.node_id, std.mem.sliceAsBytes(&beta_data), &.{6}, .f32);
    
    try executor.run();
    
    const result = try executor.getOutput(output.node_id);
    const result_f32 = @as([*]const f32, @ptrCast(@alignCast(result.data.ptr)))[0..48];
    
    print("\n=== Layer Normalization Pattern ===\n", .{});
    print("Input shape: [2, 4, 6] (batch, sequence, hidden)\n", .{});
    print("Normalizing over hidden dimension\n", .{});
    
    // Verify layer norm properties
    for (0..2) |batch| {
        for (0..4) |seq| {
            var sum: f32 = 0;
            var sum_sq: f32 = 0;
            
            print("  Batch {}, Seq {}: [", .{batch, seq});
            for (0..6) |hidden| {
                const idx = batch * 24 + seq * 6 + hidden;
                const val = result_f32[idx];
                print("{d:.3} ", .{val});
                sum += val;
                sum_sq += val * val;
            }
            
            const local_mean = sum / 6.0;
            const local_variance = sum_sq / 6.0 - local_mean * local_mean;
            print("] mean={d:.6}, var={d:.6}\n", .{local_mean, local_variance});
            
            // Verify normalization
            try testing.expect(@abs(local_mean) < 1e-5);
            try testing.expect(@abs(local_variance - 1.0) < 0.1); // Approximate unit variance
        }
    }
}

test "pattern: Attention Score Computation - Q @ K^T / sqrt(d)" {
    const allocator = testing.allocator;
    
    var graph = try Graph.init(allocator);
    defer graph.deinit();
    
    // Simplified attention: [batch=1, heads=2, seq=3, dim=4]
    const queries = try tensor.placeholder(&graph, &.{1, 2, 3, 4}, .f32);
    const keys = try tensor.placeholder(&graph, &.{1, 2, 3, 4}, .f32);
    const scale = try tensor.constant(&graph, 1.0 / @sqrt(4.0), .f32); // 1/sqrt(dim)
    
    // Transpose keys: [1, 2, 3, 4] -> [1, 2, 4, 3]
    const keys_T = try keys.transpose(&.{0, 1, 3, 2});
    
    // Compute scores: Q @ K^T
    const scores = try queries.matmul(keys_T);
    
    // Scale scores
    const scaled_scores = try scores.mul(scale);
    
    // Apply softmax (simplified - just exp and normalize)
    const exp_scores = try scaled_scores.exp();
    const sum_exp = try exp_scores.sumReduce(3, true);
    const attention_weights = try exp_scores.divide(sum_exp);
    
    try graph.output_nodes.append(graph.arena.allocator(), attention_weights.node_id);
    
    var compiled = try compiler.compile.compileCpu(&graph, allocator);
    defer compiled.deinit(allocator);
    
    var executor = try execution.Executor.init(allocator, &compiled, null);
    defer executor.deinit();
    
    // Initialize Q and K with small values
    var q_data: [24]f32 = undefined;
    var k_data: [24]f32 = undefined;
    for (0..24) |i| {
        q_data[i] = @as(f32, @floatFromInt(i % 4)) * 0.1;
        k_data[i] = @as(f32, @floatFromInt((i + 2) % 4)) * 0.1;
    }
    
    try executor.setInput(queries.node_id, std.mem.sliceAsBytes(&q_data), &.{1, 2, 3, 4}, .f32);
    try executor.setInput(keys.node_id, std.mem.sliceAsBytes(&k_data), &.{1, 2, 3, 4}, .f32);
    
    try executor.run();
    
    const result = try executor.getOutput(attention_weights.node_id);
    const result_f32 = @as([*]const f32, @ptrCast(@alignCast(result.data.ptr)))[0..18];
    
    print("\n=== Attention Score Pattern ===\n", .{});
    print("Q shape: [1, 2, 3, 4], K shape: [1, 2, 3, 4]\n", .{});
    print("Attention weights shape: [1, 2, 3, 3]\n", .{});
    
    for (0..2) |head| {
        print("  Head {}:\n", .{head});
        for (0..3) |query_pos| {
            print("    Query {} attends to: [", .{query_pos});
            var sum: f32 = 0;
            for (0..3) |key_pos| {
                const idx = head * 9 + query_pos * 3 + key_pos;
                const weight = result_f32[idx];
                print("{d:.3} ", .{weight});
                sum += weight;
            }
            print("] sum={d:.6}\n", .{sum});
            
            // Verify attention weights sum to 1
            try testing.expect(@abs(sum - 1.0) < 1e-5);
        }
    }
}

test "pattern: Residual Connection - x + sublayer(x)" {
    const allocator = testing.allocator;
    
    var graph = try Graph.init(allocator);
    defer graph.deinit();
    
    // Input: [batch=2, features=8]
    const input = try tensor.placeholder(&graph, &.{2, 8}, .f32);
    const weight1 = try tensor.placeholder(&graph, &.{8, 8}, .f32);
    const weight2 = try tensor.placeholder(&graph, &.{8, 8}, .f32);
    
    // First layer with residual
    const hidden1 = try input.matmul(weight1);
    const relu1 = try hidden1.relu();
    const residual1 = try input.add(relu1); // Residual connection
    
    // Second layer with residual
    const hidden2 = try residual1.matmul(weight2);
    const relu2 = try hidden2.relu();
    const output = try residual1.add(relu2); // Residual connection
    
    try graph.output_nodes.append(graph.arena.allocator(), output.node_id);
    
    var compiled = try compiler.compile.compileCpu(&graph, allocator);
    defer compiled.deinit(allocator);
    
    var executor = try execution.Executor.init(allocator, &compiled, null);
    defer executor.deinit();
    
    // Initialize with small weights to prevent gradient explosion
    var input_data: [16]f32 = undefined;
    var weight1_data: [64]f32 = undefined;
    var weight2_data: [64]f32 = undefined;
    
    for (0..16) |i| {
        input_data[i] = @as(f32, @floatFromInt(i % 4)) * 0.25 - 0.5;
    }
    for (0..64) |i| {
        weight1_data[i] = if (i % 9 == 0) 0.2 else 0.05; // Mostly small weights
        weight2_data[i] = if (i % 9 == 0) 0.15 else 0.03;
    }
    
    try executor.setInput(input.node_id, std.mem.sliceAsBytes(&input_data), &.{2, 8}, .f32);
    try executor.setInput(weight1.node_id, std.mem.sliceAsBytes(&weight1_data), &.{8, 8}, .f32);
    try executor.setInput(weight2.node_id, std.mem.sliceAsBytes(&weight2_data), &.{8, 8}, .f32);
    
    try executor.run();
    
    const result = try executor.getOutput(output.node_id);
    const result_f32 = @as([*]const f32, @ptrCast(@alignCast(result.data.ptr)))[0..16];
    
    print("\n=== Residual Connection Pattern ===\n", .{});
    print("Input shape: [2, 8]\n", .{});
    print("Two residual blocks\n", .{});
    
    for (0..2) |batch| {
        print("  Batch {}:\n", .{batch});
        print("    Input:  [", .{});
        for (0..8) |i| {
            print("{d:.3} ", .{input_data[batch * 8 + i]});
        }
        print("]\n    Output: [", .{});
        for (0..8) |i| {
            print("{d:.3} ", .{result_f32[batch * 8 + i]});
        }
        print("]\n", .{});
    }
    
    // Verify no gradient explosion (values should remain reasonable)
    for (result_f32) |val| {
        try testing.expect(@abs(val) < 10.0);
        try testing.expect(!std.math.isNan(val));
    }
}

test "pattern: Conv-like operation using matmul (im2col style)" {
    const allocator = testing.allocator;
    
    var graph = try Graph.init(allocator);
    defer graph.deinit();
    
    // Simulate a linear layer instead of convolution for simplicity
    // Input: [batch=2, features=9]
    // Weights: [features=9, out_features=4]
    
    // For simplicity, we'll use a standard linear layer
    const input = try tensor.placeholder(&graph, &.{2, 9}, .f32); // batch_size=2, features=9
    const kernel = try tensor.placeholder(&graph, &.{9, 4}, .f32); // in_features=9, out_features=4
    const bias = try tensor.placeholder(&graph, &.{4}, .f32);
    
    // Apply linear transformation
    const conv_out = try input.matmul(kernel);
    const biased = try conv_out.add(bias);
    const activated = try biased.relu();
    
    // Global average pooling simulation
    const pooled = try activated.mean(0, true); // Average across batch
    
    try graph.output_nodes.append(graph.arena.allocator(), pooled.node_id);
    
    var compiled = try compiler.compile.compileCpu(&graph, allocator);
    defer compiled.deinit(allocator);
    
    var executor = try execution.Executor.init(allocator, &compiled, null);
    defer executor.deinit();
    
    // Initialize data
    var input_data: [18]f32 = undefined; // 2 batches * 9 features
    var kernel_data: [36]f32 = undefined; // 9 features * 4 outputs
    const bias_data = [_]f32{ 0.1, -0.1, 0.2, -0.2 };
    
    for (0..18) |i| {
        input_data[i] = @as(f32, @floatFromInt(i % 8)) * 0.1 - 0.4;
    }
    for (0..36) |i| {
        kernel_data[i] = if (i % 5 == 0) 0.3 else -0.1;
    }
    
    try executor.setInput(input.node_id, std.mem.sliceAsBytes(&input_data), &.{2, 9}, .f32);
    try executor.setInput(kernel.node_id, std.mem.sliceAsBytes(&kernel_data), &.{9, 4}, .f32);
    try executor.setInput(bias.node_id, std.mem.sliceAsBytes(&bias_data), &.{4}, .f32);
    
    try executor.run();
    
    const result = try executor.getOutput(pooled.node_id);
    const result_f32 = @as([*]const f32, @ptrCast(@alignCast(result.data.ptr)))[0..4];
    
    print("\n=== Conv-like Pattern with Global Pooling ===\n", .{});
    print("Input shape: [2, 24] (flattened from [2, 8, 3])\n", .{});
    print("Kernel shape: [9, 4] (3x3 conv, 3->4 channels)\n", .{});
    print("Global average pooled output: [", .{});
    for (result_f32) |val| {
        print("{d:.3} ", .{val});
        try testing.expect(!std.math.isNan(val));
    }
    print("]\n", .{});
}

test "pattern: Skip connection with different dimensions" {
    const allocator = testing.allocator;
    
    var graph = try Graph.init(allocator);
    defer graph.deinit();
    
    // Common pattern in ResNet when dimensions change
    // Input: [batch=2, features=8]
    const input = try tensor.placeholder(&graph, &.{2, 8}, .f32);
    const main_weight = try tensor.placeholder(&graph, &.{8, 16}, .f32);
    const skip_weight = try tensor.placeholder(&graph, &.{8, 16}, .f32);
    
    // Main path: transform to higher dimension
    const main_path = try input.matmul(main_weight);
    const main_relu = try main_path.relu();
    
    // Skip path: project to match dimensions
    const skip_projection = try input.matmul(skip_weight);
    
    // Combine paths
    const output = try main_relu.add(skip_projection);
    
    try graph.output_nodes.append(graph.arena.allocator(), output.node_id);
    
    var compiled = try compiler.compile.compileCpu(&graph, allocator);
    defer compiled.deinit(allocator);
    
    var executor = try execution.Executor.init(allocator, &compiled, null);
    defer executor.deinit();
    
    // Test data
    var input_data: [16]f32 = undefined;
    var main_weight_data: [128]f32 = undefined;
    var skip_weight_data: [128]f32 = undefined;
    
    for (0..16) |i| {
        input_data[i] = @as(f32, @floatFromInt(i)) * 0.1 - 0.8;
    }
    for (0..128) |i| {
        main_weight_data[i] = if (i % 17 == 0) 0.1 else 0.01;
        skip_weight_data[i] = if (i % 17 == 0) 0.05 else 0.005;
    }
    
    try executor.setInput(input.node_id, std.mem.sliceAsBytes(&input_data), &.{2, 8}, .f32);
    try executor.setInput(main_weight.node_id, std.mem.sliceAsBytes(&main_weight_data), &.{8, 16}, .f32);
    try executor.setInput(skip_weight.node_id, std.mem.sliceAsBytes(&skip_weight_data), &.{8, 16}, .f32);
    
    try executor.run();
    
    const result = try executor.getOutput(output.node_id);
    const result_f32 = @as([*]const f32, @ptrCast(@alignCast(result.data.ptr)))[0..32];
    
    print("\n=== Skip Connection with Projection ===\n", .{});
    print("Input: [2, 8] -> Output: [2, 16]\n", .{});
    
    for (0..2) |batch| {
        var min_val: f32 = std.math.inf(f32);
        var max_val: f32 = -std.math.inf(f32);
        
        for (0..16) |i| {
            const val = result_f32[batch * 16 + i];
            min_val = @min(min_val, val);
            max_val = @max(max_val, val);
        }
        
        print("  Batch {}: min={d:.3}, max={d:.3}\n", .{batch, min_val, max_val});
    }
    
    // Verify all values are finite
    for (result_f32) |val| {
        try testing.expect(std.math.isFinite(val));
    }
}