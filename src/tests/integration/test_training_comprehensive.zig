/// Comprehensive Training and Autograd Integration Tests
///
/// This file contains all training-related tests including:
/// - Autograd functionality verification
/// - Gradient computation correctness
/// - Optimizer parameter updates
/// - End-to-end training scenarios
///
/// These tests verify the complete training pipeline works correctly
/// with the new autograd-as-compilation-pass design.

const std = @import("std");
const testing = std.testing;

const Graph = @import("graph").Graph;
const tensor = @import("tensor");
const types = @import("types");
const compiler = @import("compiler");
const execution = @import("execution");
const training = @import("training");

// ============================================================================
// Autograd Correctness Tests
// ============================================================================

test "autograd - simple addition gradient verification" {
    var graph = try Graph.init(testing.allocator);
    defer graph.deinit();

    // Create parameters with gradients
    const x = try tensor.placeholderWithOptions(&graph, &.{1}, .f32, .{ .requires_grad = true });
    const y = try tensor.placeholderWithOptions(&graph, &.{1}, .f32, .{ .requires_grad = true });
    
    // z = x + y
    const z = try x.add(y);
    
    // Mark z as output and loss
    try graph.markOutput(z.node_id);
    try graph.setLossOutput(z.node_id);

    // Compile with autograd
    var compiled = try compiler.compile.compileCpu(&graph, testing.allocator);
    defer compiled.deinit(testing.allocator);

    // Create executor
    var executor = try execution.Executor.init(testing.allocator, &compiled, null);
    defer executor.deinit();

    // Set input values
    const x_data = [_]f32{2.0};
    const y_data = [_]f32{3.0};
    try executor.setInput(x.node_id, std.mem.sliceAsBytes(&x_data), &.{1}, .f32);
    try executor.setInput(y.node_id, std.mem.sliceAsBytes(&y_data), &.{1}, .f32);

    // Execute forward and backward pass
    try executor.run();

    // Verify forward pass: z = x + y = 2 + 3 = 5
    const z_output = try executor.getOutput(z.node_id);
    const z_value = @as(*const f32, @ptrCast(@alignCast(z_output.data.ptr))).*;
    try testing.expectApproxEqAbs(@as(f32, 5.0), z_value, 1e-6);

    // Verify gradients: ∂z/∂x = 1, ∂z/∂y = 1
    const x_grad = try executor.getGradient(x.node_id);
    const y_grad = try executor.getGradient(y.node_id);
    
    const x_grad_value = @as(*const f32, @ptrCast(@alignCast(x_grad.data.ptr))).*;
    const y_grad_value = @as(*const f32, @ptrCast(@alignCast(y_grad.data.ptr))).*;
    
    try testing.expectApproxEqAbs(@as(f32, 1.0), x_grad_value, 1e-6);
    try testing.expectApproxEqAbs(@as(f32, 1.0), y_grad_value, 1e-6);
}

test "autograd - multiplication gradient verification" {
    var graph = try Graph.init(testing.allocator);
    defer graph.deinit();

    // Create parameters with gradients
    const x = try tensor.placeholderWithOptions(&graph, &.{1}, .f32, .{ .requires_grad = true });
    const y = try tensor.placeholderWithOptions(&graph, &.{1}, .f32, .{ .requires_grad = true });
    
    // z = x * y
    const z = try x.mul(y);
    
    try graph.markOutput(z.node_id);
    try graph.setLossOutput(z.node_id);

    var compiled = try compiler.compile.compileCpu(&graph, testing.allocator);
    defer compiled.deinit(testing.allocator);

    var executor = try execution.Executor.init(testing.allocator, &compiled, null);
    defer executor.deinit();

    const x_data = [_]f32{3.0};
    const y_data = [_]f32{4.0};
    try executor.setInput(x.node_id, std.mem.sliceAsBytes(&x_data), &.{1}, .f32);
    try executor.setInput(y.node_id, std.mem.sliceAsBytes(&y_data), &.{1}, .f32);

    try executor.run();

    // Verify forward pass: z = x * y = 3 * 4 = 12
    const z_output = try executor.getOutput(z.node_id);
    const z_value = @as(*const f32, @ptrCast(@alignCast(z_output.data.ptr))).*;
    try testing.expectApproxEqAbs(@as(f32, 12.0), z_value, 1e-6);

    // Verify gradients: ∂z/∂x = y = 4, ∂z/∂y = x = 3
    const x_grad = try executor.getGradient(x.node_id);
    const y_grad = try executor.getGradient(y.node_id);
    
    const x_grad_value = @as(*const f32, @ptrCast(@alignCast(x_grad.data.ptr))).*;
    const y_grad_value = @as(*const f32, @ptrCast(@alignCast(y_grad.data.ptr))).*;
    
    try testing.expectApproxEqAbs(@as(f32, 4.0), x_grad_value, 1e-6);
    try testing.expectApproxEqAbs(@as(f32, 3.0), y_grad_value, 1e-6);
}

test "autograd - chain rule verification" {
    var graph = try Graph.init(testing.allocator);
    defer graph.deinit();

    const x = try tensor.placeholderWithOptions(&graph, &.{1}, .f32, .{ .requires_grad = true });
    
    // y = x * x (x²)
    const y = try x.mul(x);
    // z = y * x (x³)
    const z = try y.mul(x);
    
    try graph.markOutput(z.node_id);
    try graph.setLossOutput(z.node_id);

    var compiled = try compiler.compile.compileCpu(&graph, testing.allocator);
    defer compiled.deinit(testing.allocator);

    var executor = try execution.Executor.init(testing.allocator, &compiled, null);
    defer executor.deinit();

    const x_data = [_]f32{2.0};
    try executor.setInput(x.node_id, std.mem.sliceAsBytes(&x_data), &.{1}, .f32);

    try executor.run();

    // Verify forward pass: z = x³ = 2³ = 8
    const z_output = try executor.getOutput(z.node_id);
    const z_value = @as(*const f32, @ptrCast(@alignCast(z_output.data.ptr))).*;
    try testing.expectApproxEqAbs(@as(f32, 8.0), z_value, 1e-6);

    // Verify gradient: ∂z/∂x = 3x² = 3 * 4 = 12
    const x_grad = try executor.getGradient(x.node_id);
    const x_grad_value = @as(*const f32, @ptrCast(@alignCast(x_grad.data.ptr))).*;
    try testing.expectApproxEqAbs(@as(f32, 12.0), x_grad_value, 1e-6);
}

test "autograd - MSE loss gradient flow" {
    var graph = try Graph.init(testing.allocator);
    defer graph.deinit();

    // Model: y = w * x + b
    const w = try tensor.constant(&graph, 0.5, .f32);
    const b = try tensor.constant(&graph, 0.1, .f32);
    try graph.markRequiresGrad(w.node_id);
    try graph.markRequiresGrad(b.node_id);
    const x = try tensor.placeholder(&graph, &.{1}, .f32);
    const target = try tensor.placeholder(&graph, &.{1}, .f32);

    // Forward pass
    const wx = try w.mul(x);
    const y_pred = try wx.add(b);
    
    // MSE loss: (y_pred - target)²
    const diff = try y_pred.subtract(target);
    const loss = try diff.mul(diff);
    
    try graph.markOutput(loss.node_id);
    try graph.setLossOutput(loss.node_id);

    var compiled = try compiler.compile.compileCpu(&graph, testing.allocator);
    defer compiled.deinit(testing.allocator);

    var executor = try execution.Executor.init(testing.allocator, &compiled, null);
    defer executor.deinit();

    // Training example: x=2, target=1
    // y_pred = 0.5 * 2 + 0.1 = 1.1
    // loss = (1.1 - 1)² = 0.01
    const x_data = [_]f32{2.0};
    const target_data = [_]f32{1.0};
    try executor.setInput(x.node_id, std.mem.sliceAsBytes(&x_data), &.{1}, .f32);
    try executor.setInput(target.node_id, std.mem.sliceAsBytes(&target_data), &.{1}, .f32);

    try executor.run();

    // Verify loss
    const loss_output = try executor.getOutput(loss.node_id);
    const loss_value = @as(*const f32, @ptrCast(@alignCast(loss_output.data.ptr))).*;
    try testing.expectApproxEqAbs(@as(f32, 0.01), loss_value, 1e-6);

    // Verify gradients
    // ∂loss/∂w = 2 * (y_pred - target) * x = 2 * 0.1 * 2 = 0.4
    // ∂loss/∂b = 2 * (y_pred - target) = 2 * 0.1 = 0.2
    const w_grad = try executor.getGradient(w.node_id);
    const b_grad = try executor.getGradient(b.node_id);
    
    const w_grad_value = @as(*const f32, @ptrCast(@alignCast(w_grad.data.ptr))).*;
    const b_grad_value = @as(*const f32, @ptrCast(@alignCast(b_grad.data.ptr))).*;
    
    try testing.expectApproxEqAbs(@as(f32, 0.4), w_grad_value, 1e-6);
    try testing.expectApproxEqAbs(@as(f32, 0.2), b_grad_value, 1e-6);
}

// ============================================================================
// Optimizer Integration Tests
// ============================================================================

test "SGD optimizer - parameter update verification" {
    var graph = try Graph.init(testing.allocator);
    defer graph.deinit();

    // Simple linear model
    const w = try tensor.constant(&graph, 1.0, .f32);
    try graph.markRequiresGrad(w.node_id);
    const x = try tensor.placeholder(&graph, &.{1}, .f32);
    const target = try tensor.placeholder(&graph, &.{1}, .f32);

    const y_pred = try w.mul(x);
    const diff = try y_pred.subtract(target);
    const loss = try diff.mul(diff);
    
    try graph.markOutput(loss.node_id);
    try graph.setLossOutput(loss.node_id);

    // Create trainer with SGD
    var trainer = try training.Trainer.init(
        testing.allocator,
        &graph,
        &[_]types.NodeId{w.node_id},
        .sgd,
        .{ .epochs = 1 },
    );
    defer trainer.deinit();

    // Compile with learning rate 0.1
    try trainer.compile(y_pred.node_id, loss.node_id, 0.1);

    // Training data: x=2, target=4
    // Initial: w=1, y_pred=2, loss=4
    // Gradient: ∂loss/∂w = 2*(y_pred-target)*x = 2*(-2)*2 = -8
    // Update: w_new = w - lr*grad = 1 - 0.1*(-8) = 1.8
    const x_data = [_]f32{2.0};
    const target_data = [_]f32{4.0};

    const loss_value = try trainer.trainStep(
        std.mem.sliceAsBytes(&x_data),
        std.mem.sliceAsBytes(&target_data),
        x.node_id,
        target.node_id,
        loss.node_id,
    );

    try testing.expectApproxEqAbs(@as(f32, 4.0), loss_value, 1e-5);

    // Verify parameter was updated
    // After update, w should be closer to optimal value of 2
    // With w=1.8: y_pred=3.6, loss=(3.6-4)²=0.16
    // Run forward pass again to check
    try trainer.forward_executor.?.setInput(x.node_id, std.mem.sliceAsBytes(&x_data), &.{1}, .f32);
    try trainer.forward_executor.?.setInput(target.node_id, std.mem.sliceAsBytes(&target_data), &.{1}, .f32);
    try trainer.forward_executor.?.run();
    
    const new_loss_output = try trainer.forward_executor.?.getOutput(loss.node_id);
    const new_loss_value = @as(*const f32, @ptrCast(@alignCast(new_loss_output.data.ptr))).*;
    try testing.expectApproxEqAbs(@as(f32, 0.16), new_loss_value, 1e-5);
}

test "training - multi-step convergence" {
    var graph = try Graph.init(testing.allocator);
    defer graph.deinit();

    // Linear regression: y = w * x
    // We'll train to find w=2
    const w = try tensor.constant(&graph, 0.5, .f32);
    try graph.markRequiresGrad(w.node_id);
    const x = try tensor.placeholder(&graph, &.{1}, .f32);
    const target = try tensor.placeholder(&graph, &.{1}, .f32);

    const y_pred = try w.mul(x);
    const diff = try y_pred.subtract(target);
    const loss = try diff.mul(diff);
    
    try graph.markOutput(loss.node_id);
    try graph.setLossOutput(loss.node_id);

    var trainer = try training.Trainer.init(
        testing.allocator,
        &graph,
        &[_]types.NodeId{w.node_id},
        .sgd,
        .{ .epochs = 1, .log_interval = 5 },
    );
    defer trainer.deinit();

    try trainer.compile(y_pred.node_id, loss.node_id, 0.01);

    // Training data: x=1, target=2 (so optimal w=2)
    const x_data = [_]f32{1.0};
    const target_data = [_]f32{2.0};

    // Train for multiple steps
    var prev_loss: f32 = std.math.inf(f32);
    for (0..20) |_| {
        const loss_value = try trainer.trainStep(
            std.mem.sliceAsBytes(&x_data),
            std.mem.sliceAsBytes(&target_data),
            x.node_id,
            target.node_id,
            loss.node_id,
        );
        
        // Verify loss is decreasing
        try testing.expect(loss_value < prev_loss);
        prev_loss = loss_value;
    }

    // After 20 steps, loss should be very small
    try testing.expect(prev_loss < 0.1);
}

// ============================================================================
// Edge Cases and Error Handling
// ============================================================================

test "autograd - no gradients when not requested" {
    var graph = try Graph.init(testing.allocator);
    defer graph.deinit();

    // Create tensors WITHOUT requires_grad
    const x = try tensor.placeholder(&graph, &.{1}, .f32);
    const y = try tensor.placeholder(&graph, &.{1}, .f32);
    const z = try x.add(y);
    
    try graph.markOutput(z.node_id);

    var compiled = try compiler.compile.compileCpu(&graph, testing.allocator);
    defer compiled.deinit(testing.allocator);

    var executor = try execution.Executor.init(testing.allocator, &compiled, null);
    defer executor.deinit();

    const x_data = [_]f32{1.0};
    const y_data = [_]f32{2.0};
    try executor.setInput(x.node_id, std.mem.sliceAsBytes(&x_data), &.{1}, .f32);
    try executor.setInput(y.node_id, std.mem.sliceAsBytes(&y_data), &.{1}, .f32);

    try executor.run();

    // Forward pass should work
    const z_output = try executor.getOutput(z.node_id);
    const z_value = @as(*const f32, @ptrCast(@alignCast(z_output.data.ptr))).*;
    try testing.expectApproxEqAbs(@as(f32, 3.0), z_value, 1e-6);

    // But gradients should not be available
    try testing.expectError(error.GradientsNotEnabled, executor.getGradient(x.node_id));
}

test "autograd - gradient accumulation for shared parameters" {
    var graph = try Graph.init(testing.allocator);
    defer graph.deinit();

    const w = try tensor.constant(&graph, 2.0, .f32);
    try graph.markRequiresGrad(w.node_id);
    const x1 = try tensor.placeholder(&graph, &.{1}, .f32);
    const x2 = try tensor.placeholder(&graph, &.{1}, .f32);
    
    // Use w in two different paths
    const y1 = try w.mul(x1);
    const y2 = try w.mul(x2);
    const z = try y1.add(y2);
    
    try graph.markOutput(z.node_id);
    try graph.setLossOutput(z.node_id);

    var compiled = try compiler.compile.compileCpu(&graph, testing.allocator);
    defer compiled.deinit(testing.allocator);

    var executor = try execution.Executor.init(testing.allocator, &compiled, null);
    defer executor.deinit();

    const x1_data = [_]f32{3.0};
    const x2_data = [_]f32{4.0};
    try executor.setInput(x1.node_id, std.mem.sliceAsBytes(&x1_data), &.{1}, .f32);
    try executor.setInput(x2.node_id, std.mem.sliceAsBytes(&x2_data), &.{1}, .f32);

    try executor.run();

    // z = w*x1 + w*x2 = 2*3 + 2*4 = 14
    const z_output = try executor.getOutput(z.node_id);
    const z_value = @as(*const f32, @ptrCast(@alignCast(z_output.data.ptr))).*;
    try testing.expectApproxEqAbs(@as(f32, 14.0), z_value, 1e-6);

    // ∂z/∂w = x1 + x2 = 3 + 4 = 7 (accumulated)
    const w_grad = try executor.getGradient(w.node_id);
    const w_grad_value = @as(*const f32, @ptrCast(@alignCast(w_grad.data.ptr))).*;
    try testing.expectApproxEqAbs(@as(f32, 7.0), w_grad_value, 1e-6);
}

// ============================================================================
// Real-world Patterns
// ============================================================================

test "training - neural network layer with bias" {
    var graph = try Graph.init(testing.allocator);
    defer graph.deinit();

    // Single neuron: y = sigmoid(w*x + b)
    const w = try tensor.constant(&graph, 0.5, .f32);
    const b = try tensor.constant(&graph, 0.0, .f32);
    try graph.markRequiresGrad(w.node_id);
    try graph.markRequiresGrad(b.node_id);
    const x = try tensor.placeholder(&graph, &.{1}, .f32);
    const target = try tensor.placeholder(&graph, &.{1}, .f32);

    const wx = try w.mul(x);
    const linear = try wx.add(b);
    const y_pred = try linear.sigmoid();
    
    // Binary cross-entropy loss (simplified)
    // For single sample: -target*log(y_pred) - (1-target)*log(1-y_pred)
    const diff = try y_pred.subtract(target);
    const loss = try diff.mul(diff); // Using MSE for simplicity
    
    try graph.markOutput(loss.node_id);
    try graph.setLossOutput(loss.node_id);

    var trainer = try training.Trainer.init(
        testing.allocator,
        &graph,
        &[_]types.NodeId{ w.node_id, b.node_id },
        .sgd,
        .{ .epochs = 1 },
    );
    defer trainer.deinit();

    try trainer.compile(y_pred.node_id, loss.node_id, 0.5);

    // Train on a simple example
    const x_data = [_]f32{1.0};
    const target_data = [_]f32{1.0}; // Target output 1

    var loss_history = std.ArrayList(f32).init(testing.allocator);
    defer loss_history.deinit();

    // Train for a few steps
    for (0..10) |_| {
        const loss_value = try trainer.trainStep(
            std.mem.sliceAsBytes(&x_data),
            std.mem.sliceAsBytes(&target_data),
            x.node_id,
            target.node_id,
            loss.node_id,
        );
        try loss_history.append(loss_value);
    }

    // Verify training is improving
    const initial_loss = loss_history.items[0];
    const final_loss = loss_history.items[loss_history.items.len - 1];
    try testing.expect(final_loss < initial_loss);
}

test "autograd - sum reduction gradient broadcasting" {
    var graph = try Graph.init(testing.allocator);
    defer graph.deinit();

    // Test gradient broadcasting for sum reduction
    const x = try tensor.placeholderWithOptions(&graph, &.{2, 3}, .f32, .{ .requires_grad = true });
    const sum_all = try x.sumAll();
    
    try graph.markOutput(sum_all.node_id);
    try graph.setLossOutput(sum_all.node_id);

    var compiled = try compiler.compile.compileCpu(&graph, testing.allocator);
    defer compiled.deinit(testing.allocator);

    var executor = try execution.Executor.init(testing.allocator, &compiled, null);
    defer executor.deinit();

    const x_data = [_]f32{ 1, 2, 3, 4, 5, 6 };
    try executor.setInput(x.node_id, std.mem.sliceAsBytes(&x_data), &.{2, 3}, .f32);

    try executor.run();

    // Verify forward pass: sum = 21
    const sum_output = try executor.getOutput(sum_all.node_id);
    const sum_value = @as(*const f32, @ptrCast(@alignCast(sum_output.data.ptr))).*;
    try testing.expectApproxEqAbs(@as(f32, 21.0), sum_value, 1e-6);

    // Gradient of sum w.r.t each element is 1
    // But this test will fail until we implement broadcasting in autograd
    if (executor.getGradient(x.node_id)) |x_grad| {
        // Each element should have gradient 1
        const grad_data = x_grad.data;
        for (0..6) |i| {
            const grad_value = @as(*const f32, @ptrCast(@alignCast(grad_data.ptr + i * @sizeOf(f32)))).*;
            try testing.expectApproxEqAbs(@as(f32, 1.0), grad_value, 1e-6);
        }
    } else |_| {
        // Expected until broadcasting is implemented
    }
}