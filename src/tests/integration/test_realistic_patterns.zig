const std = @import("std");
const testing = std.testing;
const print = std.debug.print;

const Graph = @import("graph").Graph;
const tensor = @import("tensor");
const compiler = @import("compiler");
const execution = @import("execution");

// Test realistic deep learning patterns with correct API usage

test "pattern: Simple linear layer" {
    const allocator = testing.allocator;
    
    var graph = try Graph.init(allocator);
    defer graph.deinit();
    
    // Linear layer: y = Wx + b
    // Input: [batch=2, features=4]
    // Weights: [4, 3]
    // Bias: [3]
    const input = try tensor.placeholder(&graph, &.{2, 4}, .f32);
    const weights = try tensor.placeholder(&graph, &.{4, 3}, .f32);
    const bias = try tensor.placeholder(&graph, &.{3}, .f32);
    
    // Forward pass
    const output = try input.matmul(weights);
    const biased = try output.add(bias);
    
    try graph.output_nodes.append(graph.arena.allocator(), biased.node_id);
    
    var compiled = try compiler.compile.compileCpu(&graph, allocator);
    defer compiled.deinit(allocator);
    
    var executor = try execution.Executor.init(allocator, &compiled, null);
    defer executor.deinit();
    
    // Test data
    const input_data = [_]f32{
        1.0, 0.5, -0.3, 0.8,
        -0.5, 1.2, 0.3, -0.7,
    };
    const weight_data = [_]f32{
        0.1, 0.2, -0.3,
        -0.4, 0.5, 0.6,
        0.7, -0.8, 0.9,
        -0.1, 0.2, -0.3,
    };
    const bias_data = [_]f32{ 0.1, -0.2, 0.3 };
    
    try executor.setInput(input.node_id, std.mem.sliceAsBytes(&input_data), &.{2, 4}, .f32);
    try executor.setInput(weights.node_id, std.mem.sliceAsBytes(&weight_data), &.{4, 3}, .f32);
    try executor.setInput(bias.node_id, std.mem.sliceAsBytes(&bias_data), &.{3}, .f32);
    
    try executor.run();
    
    const result = try executor.getOutput(biased.node_id);
    const result_f32 = @as([*]const f32, @ptrCast(@alignCast(result.data.ptr)))[0..6];
    
    print("\n=== Linear Layer ===\n", .{});
    print("Output shape: [2, 3]\n", .{});
    for (0..2) |batch| {
        print("  Batch {}: [", .{batch});
        for (0..3) |i| {
            print("{d:.3} ", .{result_f32[batch * 3 + i]});
        }
        print("]\n", .{});
    }
    
    // Verify all values are finite
    for (result_f32) |val| {
        try testing.expect(std.math.isFinite(val));
    }
}

test "pattern: ReLU activation chain" {
    const allocator = testing.allocator;
    
    var graph = try Graph.init(allocator);
    defer graph.deinit();
    
    // Input -> ReLU -> Linear -> ReLU
    const input = try tensor.placeholder(&graph, &.{3, 8}, .f32);
    const w1 = try tensor.placeholder(&graph, &.{8, 8}, .f32);
    const w2 = try tensor.placeholder(&graph, &.{8, 4}, .f32);
    
    // First layer with ReLU
    const h1 = try input.matmul(w1);
    const a1 = try h1.relu();
    
    // Second layer with ReLU
    const h2 = try a1.matmul(w2);
    const output = try h2.relu();
    
    // Also output the intermediate values for debugging
    try graph.output_nodes.append(graph.arena.allocator(), h1.node_id);
    try graph.output_nodes.append(graph.arena.allocator(), a1.node_id);
    try graph.output_nodes.append(graph.arena.allocator(), h2.node_id);
    try graph.output_nodes.append(graph.arena.allocator(), output.node_id);
    
    var compiled = try compiler.compile.compileCpu(&graph, allocator);
    defer compiled.deinit(allocator);
    
    var executor = try execution.Executor.init(allocator, &compiled, null);
    defer executor.deinit();
    
    // Initialize with mix of positive and negative values
    var input_data: [24]f32 = undefined;
    var w1_data: [64]f32 = undefined;
    var w2_data: [32]f32 = undefined;
    
    for (0..24) |i| {
        input_data[i] = @as(f32, @floatFromInt(@as(i32, @intCast(i)) - 12)) * 0.1;
    }
    for (0..64) |i| {
        w1_data[i] = if (i % 9 == 0) 0.2 else 0.02;
    }
    for (0..32) |i| {
        w2_data[i] = if (i % 5 == 0) 0.3 else 0.03;
    }
    
    try executor.setInput(input.node_id, std.mem.sliceAsBytes(&input_data), &.{3, 8}, .f32);
    try executor.setInput(w1.node_id, std.mem.sliceAsBytes(&w1_data), &.{8, 8}, .f32);
    try executor.setInput(w2.node_id, std.mem.sliceAsBytes(&w2_data), &.{8, 4}, .f32);
    
    try executor.run();
    
    // Get intermediate results
    const h1_result = try executor.getOutput(h1.node_id);
    const h1_f32 = @as([*]const f32, @ptrCast(@alignCast(h1_result.data.ptr)))[0..24];
    
    const a1_result = try executor.getOutput(a1.node_id);
    const a1_f32 = @as([*]const f32, @ptrCast(@alignCast(a1_result.data.ptr)))[0..24];
    
    const h2_result = try executor.getOutput(h2.node_id);
    const h2_f32 = @as([*]const f32, @ptrCast(@alignCast(h2_result.data.ptr)))[0..12];
    
    const result = try executor.getOutput(output.node_id);
    const result_f32 = @as([*]const f32, @ptrCast(@alignCast(result.data.ptr)))[0..12];
    
    print("\n=== ReLU Chain ===\n", .{});
    print("Output shape: [3, 4]\n", .{});
    
    // Debug: Check first ReLU
    print("\nFirst ReLU check:\n", .{});
    for (0..8) |i| {
        if (a1_f32[i] < 0) {
            print("  ERROR: a1[{}] = {d:.6} is negative after ReLU! h1[{}] = {d:.6}\n", 
                  .{i, a1_f32[i], i, h1_f32[i]});
        }
    }
    
    // Debug: Check second layer inputs/outputs
    print("\nSecond layer check:\n", .{});
    for (0..4) |i| {
        print("  h2[{}] = {d:.6} -> output[{}] = {d:.6}\n", 
              .{i, h2_f32[i], i, result_f32[i]});
    }
    
    var num_zeros: u32 = 0;
    for (result_f32, 0..) |val, i| {
        if (val == 0.0) num_zeros += 1;
        if (val < 0.0) {
            print("ERROR: ReLU output[{}] = {d:.6} is negative!\n", .{i, val});
        }
        try testing.expect(val >= 0.0); // ReLU ensures non-negative
        try testing.expect(std.math.isFinite(val));
    }
    
    print("Active neurons: {}/12\n", .{12 - num_zeros});
}

test "pattern: Simple mean pooling" {
    const allocator = testing.allocator;
    
    var graph = try Graph.init(allocator);
    defer graph.deinit();
    
    // Mean pooling along last dimension
    // Input: [batch=2, seq=4, hidden=6]
    const input = try tensor.placeholder(&graph, &.{2, 4, 6}, .f32);
    
    // Mean pool over sequence dimension (axis=1)
    const pooled = try input.mean(1, true);
    
    try graph.output_nodes.append(graph.arena.allocator(), pooled.node_id);
    
    var compiled = try compiler.compile.compileCpu(&graph, allocator);
    defer compiled.deinit(allocator);
    
    var executor = try execution.Executor.init(allocator, &compiled, null);
    defer executor.deinit();
    
    // Test data
    var input_data: [48]f32 = undefined;
    for (0..48) |i| {
        input_data[i] = @as(f32, @floatFromInt(i % 6));
    }
    
    try executor.setInput(input.node_id, std.mem.sliceAsBytes(&input_data), &.{2, 4, 6}, .f32);
    
    try executor.run();
    
    const result = try executor.getOutput(pooled.node_id);
    const result_f32 = @as([*]const f32, @ptrCast(@alignCast(result.data.ptr)))[0..12];
    
    print("\n=== Mean Pooling ===\n", .{});
    print("Input: [2, 4, 6] -> Output: [2, 6]\n", .{});
    
    for (0..2) |batch| {
        print("  Batch {}: [", .{batch});
        for (0..6) |i| {
            print("{d:.1} ", .{result_f32[batch * 6 + i]});
        }
        print("]\n", .{});
    }
    
    // Verify values are correct (should be same as input since all sequences have same values)
    for (0..12) |i| {
        const expected = @as(f32, @floatFromInt(i % 6));
        try testing.expect(@abs(result_f32[i] - expected) < 1e-5);
    }
}

test "pattern: Residual connection" {
    const allocator = testing.allocator;
    
    var graph = try Graph.init(allocator);
    defer graph.deinit();
    
    // Simple residual: output = x + f(x)
    const input = try tensor.placeholder(&graph, &.{2, 8}, .f32);
    const weight = try tensor.placeholder(&graph, &.{8, 8}, .f32);
    
    // Transform
    const transformed = try input.matmul(weight);
    const activated = try transformed.relu();
    
    // Residual connection
    const output = try input.add(activated);
    
    try graph.output_nodes.append(graph.arena.allocator(), output.node_id);
    
    var compiled = try compiler.compile.compileCpu(&graph, allocator);
    defer compiled.deinit(allocator);
    
    var executor = try execution.Executor.init(allocator, &compiled, null);
    defer executor.deinit();
    
    // Initialize with small values to prevent explosion
    var input_data: [16]f32 = undefined;
    var weight_data: [64]f32 = undefined;
    
    for (0..16) |i| {
        input_data[i] = @as(f32, @floatFromInt(i % 4)) * 0.25 - 0.5;
    }
    for (0..64) |i| {
        weight_data[i] = if (i % 9 == 0) 0.1 else 0.01;
    }
    
    try executor.setInput(input.node_id, std.mem.sliceAsBytes(&input_data), &.{2, 8}, .f32);
    try executor.setInput(weight.node_id, std.mem.sliceAsBytes(&weight_data), &.{8, 8}, .f32);
    
    try executor.run();
    
    const result = try executor.getOutput(output.node_id);
    const result_f32 = @as([*]const f32, @ptrCast(@alignCast(result.data.ptr)))[0..16];
    
    print("\n=== Residual Connection ===\n", .{});
    print("Output = input + ReLU(input @ weight)\n", .{});
    
    // Check that residual helped preserve gradient flow
    var num_changed: u32 = 0;
    for (0..16) |i| {
        if (@abs(result_f32[i] - input_data[i]) > 1e-6) {
            num_changed += 1;
        }
        try testing.expect(std.math.isFinite(result_f32[i]));
    }
    
    print("Values modified by residual: {}/16\n", .{num_changed});
}

test "pattern: Simple softmax" {
    const allocator = testing.allocator;
    
    var graph = try Graph.init(allocator);
    defer graph.deinit();
    
    // Softmax over logits
    const logits = try tensor.placeholder(&graph, &.{2, 5}, .f32);
    
    // Softmax: exp(x) / sum(exp(x))
    const exp_logits = try logits.exp();
    const sum_exp = try exp_logits.sumReduce(1, true); // Sum over classes with keepdims
    
    // No need to manually broadcast - keepdims preserves the shape for broadcasting
    const softmax = try exp_logits.divide(sum_exp);
    
    try graph.output_nodes.append(graph.arena.allocator(), softmax.node_id);
    
    var compiled = try compiler.compile.compileCpu(&graph, allocator);
    defer compiled.deinit(allocator);
    
    var executor = try execution.Executor.init(allocator, &compiled, null);
    defer executor.deinit();
    
    // Test with different magnitudes
    const logit_data = [_]f32{
        1.0, 2.0, 3.0, 2.0, 1.0,    // batch 1
        -1.0, 0.0, 1.0, 0.0, -1.0,  // batch 2
    };
    
    try executor.setInput(logits.node_id, std.mem.sliceAsBytes(&logit_data), &.{2, 5}, .f32);
    
    try executor.run();
    
    const result = try executor.getOutput(softmax.node_id);
    const result_f32 = @as([*]const f32, @ptrCast(@alignCast(result.data.ptr)))[0..10];
    
    print("\n=== Softmax ===\n", .{});
    print("Logits -> Probabilities\n", .{});
    
    for (0..2) |batch| {
        print("  Batch {}: [", .{batch});
        var sum: f32 = 0;
        var max_idx: usize = 0;
        var max_prob: f32 = 0;
        
        for (0..5) |i| {
            const prob = result_f32[batch * 5 + i];
            print("{d:.3} ", .{prob});
            sum += prob;
            
            if (prob > max_prob) {
                max_prob = prob;
                max_idx = i;
            }
        }
        print("] sum={d:.6}, argmax={}\n", .{sum, max_idx});
        
        // Verify probabilities sum to 1
        try testing.expect(@abs(sum - 1.0) < 1e-4);
    }
}

test "pattern: Batch matrix multiplication" {
    const allocator = testing.allocator;
    
    var graph = try Graph.init(allocator);
    defer graph.deinit();
    
    // Batch matmul: [batch, m, k] @ [batch, k, n] = [batch, m, n]
    // Simulated with 2D for now
    const a = try tensor.placeholder(&graph, &.{6, 4}, .f32); // Flattened from [2, 3, 4]
    const b = try tensor.placeholder(&graph, &.{4, 5}, .f32); // Shared weights
    
    const output = try a.matmul(b);
    
    try graph.output_nodes.append(graph.arena.allocator(), output.node_id);
    
    var compiled = try compiler.compile.compileCpu(&graph, allocator);
    defer compiled.deinit(allocator);
    
    var executor = try execution.Executor.init(allocator, &compiled, null);
    defer executor.deinit();
    
    // Initialize
    var a_data: [24]f32 = undefined;
    var b_data: [20]f32 = undefined;
    
    for (0..24) |i| {
        a_data[i] = @as(f32, @floatFromInt(i % 4)) * 0.25;
    }
    for (0..20) |i| {
        b_data[i] = if (i % 6 == 0) 0.5 else 0.1;
    }
    
    try executor.setInput(a.node_id, std.mem.sliceAsBytes(&a_data), &.{6, 4}, .f32);
    try executor.setInput(b.node_id, std.mem.sliceAsBytes(&b_data), &.{4, 5}, .f32);
    
    try executor.run();
    
    const result = try executor.getOutput(output.node_id);
    const result_f32 = @as([*]const f32, @ptrCast(@alignCast(result.data.ptr)))[0..30];
    
    print("\n=== Batch MatMul ===\n", .{});
    print("[6, 4] @ [4, 5] = [6, 5]\n", .{});
    
    // Verify all finite
    var min_val: f32 = std.math.inf(f32);
    var max_val: f32 = -std.math.inf(f32);
    
    for (result_f32) |val| {
        try testing.expect(std.math.isFinite(val));
        min_val = @min(min_val, val);
        max_val = @max(max_val, val);
    }
    
    print("Value range: [{d:.3}, {d:.3}]\n", .{min_val, max_val});
}

test "pattern: Elementwise operations chain" {
    const allocator = testing.allocator;
    
    var graph = try Graph.init(allocator);
    defer graph.deinit();
    
    // Chain: (a + b) * c - d
    const a = try tensor.placeholder(&graph, &.{2, 3, 4}, .f32);
    const b = try tensor.placeholder(&graph, &.{2, 3, 4}, .f32);
    const c = try tensor.placeholder(&graph, &.{4}, .f32);     // Will broadcast
    const d = try tensor.placeholder(&graph, &.{2, 1, 4}, .f32); // Will broadcast
    
    const sum_ab = try a.add(b);
    const prod = try sum_ab.mul(c);
    const output = try prod.subtract(d);
    
    try graph.output_nodes.append(graph.arena.allocator(), output.node_id);
    
    var compiled = try compiler.compile.compileCpu(&graph, allocator);
    defer compiled.deinit(allocator);
    
    var executor = try execution.Executor.init(allocator, &compiled, null);
    defer executor.deinit();
    
    // Initialize
    var a_data: [24]f32 = undefined;
    var b_data: [24]f32 = undefined;
    const c_data = [_]f32{ 2.0, 3.0, 4.0, 5.0 };
    const d_data = [_]f32{
        1.0, 1.0, 1.0, 1.0,
        2.0, 2.0, 2.0, 2.0,
    };
    
    for (0..24) |i| {
        a_data[i] = @as(f32, @floatFromInt(i % 4));
        b_data[i] = 1.0;
    }
    
    try executor.setInput(a.node_id, std.mem.sliceAsBytes(&a_data), &.{2, 3, 4}, .f32);
    try executor.setInput(b.node_id, std.mem.sliceAsBytes(&b_data), &.{2, 3, 4}, .f32);
    try executor.setInput(c.node_id, std.mem.sliceAsBytes(&c_data), &.{4}, .f32);
    try executor.setInput(d.node_id, std.mem.sliceAsBytes(&d_data), &.{2, 1, 4}, .f32);
    
    try executor.run();
    
    const result = try executor.getOutput(output.node_id);
    const result_f32 = @as([*]const f32, @ptrCast(@alignCast(result.data.ptr)))[0..24];
    
    print("\n=== Elementwise Chain with Broadcasting ===\n", .{});
    print("(a + b) * c - d with shapes:\n", .{});
    print("  a,b: [2,3,4], c: [4], d: [2,1,4]\n", .{});
    
    // Verify computation
    for (0..2) |batch| {
        for (0..3) |row| {
            for (0..4) |col| {
                const idx = batch * 12 + row * 4 + col;
                const expected = (a_data[idx] + 1.0) * c_data[col] - d_data[batch * 4 + col];
                const actual = result_f32[idx];
                
                try testing.expect(@abs(actual - expected) < 1e-5);
            }
        }
    }
    
    print("All computations verified ✓\n", .{});
}

test "pattern: Common activation functions" {
    const allocator = testing.allocator;
    
    var graph = try Graph.init(allocator);
    defer graph.deinit();
    
    // Test various activations
    const input = try tensor.placeholder(&graph, &.{2, 8}, .f32);
    
    // Different activation paths
    const relu_out = try input.relu();
    const sigmoid_out = try input.sigmoid();
    const tanh_out = try input.tanh();
    const swish_out = try input.swish(); // x * sigmoid(x)
    
    // Get the current node IDs after any substitutions
    const relu_id = relu_out.getCurrentNodeId();
    const sigmoid_id = sigmoid_out.getCurrentNodeId(); 
    const tanh_id = tanh_out.getCurrentNodeId();
    const swish_id = swish_out.getCurrentNodeId();
    
    print("\nInitial node IDs:\n", .{});
    print("  Input: {}\n", .{input.node_id});
    print("  ReLU: {} (current: {})\n", .{relu_out.node_id, relu_id});
    print("  Sigmoid: {} (current: {})\n", .{sigmoid_out.node_id, sigmoid_id});
    print("  Tanh: {} (current: {})\n", .{tanh_out.node_id, tanh_id});
    print("  Swish: {} (current: {})\n", .{swish_out.node_id, swish_id});
    
    try graph.output_nodes.append(graph.arena.allocator(), relu_id);
    try graph.output_nodes.append(graph.arena.allocator(), sigmoid_id);
    try graph.output_nodes.append(graph.arena.allocator(), tanh_id);
    try graph.output_nodes.append(graph.arena.allocator(), swish_id);
    
    var compiled = try compiler.compile.compileCpu(&graph, allocator);
    defer compiled.deinit(allocator);
    
    var executor = try execution.Executor.init(allocator, &compiled, null);
    defer executor.deinit();
    
    // Test with range of values
    const input_data = [_]f32{
        -3.0, -2.0, -1.0, -0.5, 0.0, 0.5, 1.0, 2.0,
        -2.5, -1.5, -0.8, -0.2, 0.2, 0.8, 1.5, 2.5,
    };
    
    try executor.setInput(input.node_id, std.mem.sliceAsBytes(&input_data), &.{2, 8}, .f32);
    
    try executor.run();
    
    print("\nDebug: Output node IDs:\n", .{});
    print("  ReLU: node_id={}\n", .{relu_out.node_id});
    print("  Sigmoid: node_id={}\n", .{sigmoid_out.node_id});
    print("  Tanh: node_id={}\n", .{tanh_out.node_id});
    print("  Swish: node_id={}\n", .{swish_out.node_id});
    
    const relu = try executor.getOutput(relu_out.node_id);
    const sigmoid = try executor.getOutput(sigmoid_out.node_id);
    const tanh_result = try executor.getOutput(tanh_out.node_id);
    const swish = try executor.getOutput(swish_out.node_id);
    
    const relu_f32 = @as([*]const f32, @ptrCast(@alignCast(relu.data.ptr)))[0..16];
    const sigmoid_f32 = @as([*]const f32, @ptrCast(@alignCast(sigmoid.data.ptr)))[0..16];
    const tanh_f32 = @as([*]const f32, @ptrCast(@alignCast(tanh_result.data.ptr)))[0..16];
    const swish_f32 = @as([*]const f32, @ptrCast(@alignCast(swish.data.ptr)))[0..16];
    
    print("\n=== Activation Functions ===\n", .{});
    print("Testing ReLU, Sigmoid, Tanh, Swish\n", .{});
    
    // Print all ReLU outputs to understand the pattern
    print("ReLU outputs:\n", .{});
    for (0..16) |i| {
        print("  input[{}]={d:.3} -> relu[{}]={d:.3}\n", .{i, input_data[i], i, relu_f32[i]});
    }
    
    // Verify properties
    for (0..16) |i| {
        // ReLU: max(0, x)
        const expected_relu = @max(0, input_data[i]);
        if (relu_f32[i] < 0) {
            print("ERROR: ReLU[{}] = {d:.6} is negative! Input was {d:.6}, expected {d:.6}\n", 
                  .{i, relu_f32[i], input_data[i], expected_relu});
        }
        // Debug: print all values to see what's happening
        if (@abs(expected_relu - relu_f32[i]) > 1e-6) {
            print("ReLU mismatch at index {}: input={d:.6}, expected={d:.6}, actual={d:.6}\n",
                  .{i, input_data[i], expected_relu, relu_f32[i]});
        }
        try testing.expect(relu_f32[i] >= 0);
        // Use approximate equality due to floating point precision in decomposed operations
        try testing.expectApproxEqAbs(expected_relu, relu_f32[i], 1e-6);
        
        // Sigmoid: 0 < output < 1
        try testing.expect(sigmoid_f32[i] > 0 and sigmoid_f32[i] < 1);
        
        // Tanh: -1 < output < 1
        try testing.expect(tanh_f32[i] > -1 and tanh_f32[i] < 1);
        
        // All should be finite
        try testing.expect(std.math.isFinite(swish_f32[i]));
    }
    
    print("All activation properties verified ✓\n", .{});
}