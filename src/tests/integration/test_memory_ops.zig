/// Integrated test suite for memory-related operations
/// This file merges tests from:
/// - test_multi_output_debug.zig
/// - test_multi_output_memory_debug.zig
/// - test_metadata_issue.zig
/// - test_shape_metadata_debug.zig
const std = @import("std");
const testing = std.testing;
const print = std.debug.print;

// Import Zing components
const types = @import("types");
const Graph = @import("graph").Graph;
const tensor = @import("tensor");
const compiler = @import("compiler");
const execution = @import("execution");
const shape_mod = @import("shape");

// ========== From test_multi_output_debug.zig ==========

test "debug multi-output issue" {
    const allocator = testing.allocator;
    
    print("\n=== Debug: Multi-Output Issue ===\n", .{});
    
    // Test 1: Single output (should work)
    {
        var graph = try Graph.init(allocator);
        defer graph.deinit();
        
        const x = try tensor.placeholder(&graph, &.{2, 3}, .f32);
        const sum0 = try x.sum(0, true);
        
        try graph.output_nodes.append(graph.arena.allocator(), sum0.node_id);
        
        var compiled = try compiler.compile.compileCpu(&graph, allocator);
        defer compiled.deinit(allocator);
        
        var executor = try execution.Executor.init(allocator, &compiled, null);
        defer executor.deinit();
        
        const x_data = [_]f32{ 1, 2, 3, 4, 5, 6 };
        const shape = [_]i64{2, 3};
        
        try executor.setInput(x.node_id, std.mem.asBytes(&x_data), &shape, .f32);
        try executor.run();
        
        const output = try executor.getOutput(sum0.node_id);
        const output_f32 = @as([*]const f32, @ptrCast(@alignCast(output.data.ptr)))[0..3];
        
        print("Test 1 - Single output: ", .{});
        for (output_f32) |v| print("{d:.1} ", .{v});
        print("(expected: 5 7 9)\n", .{});
    }
    
    // Test 2: Two outputs (might fail)
    {
        var graph = try Graph.init(allocator);
        defer graph.deinit();
        
        const x = try tensor.placeholder(&graph, &.{2, 3}, .f32);
        const sum0 = try x.sum(0, true);
        const sum1 = try x.sum(1, true);
        
        print("\nNode IDs: x={}, sum0={}, sum1={}\n", .{x.node_id, sum0.node_id, sum1.node_id});
        
        try graph.output_nodes.append(graph.arena.allocator(), sum0.node_id);
        try graph.output_nodes.append(graph.arena.allocator(), sum1.node_id);
        
        print("Output nodes in graph: ", .{});
        for (graph.output_nodes.items) |node_id| {
            print("{} ", .{node_id});
        }
        print("\n", .{});
        
        var compiled = try compiler.compile.compileCpu(&graph, allocator);
        defer compiled.deinit(allocator);
        
        var executor = try execution.Executor.init(allocator, &compiled, null);
        defer executor.deinit();
        
        const x_data = [_]f32{ 1, 2, 3, 4, 5, 6 };
        const shape = [_]i64{2, 3};
        
        try executor.setInput(x.node_id, std.mem.asBytes(&x_data), &shape, .f32);
        try executor.run();
        
        print("\nTest 2 - Two outputs:\n", .{});
        
        // Try to get first output
        const output0 = executor.getOutput(sum0.node_id) catch |err| {
            print("  Failed to get sum0 (node {}): {}\n", .{sum0.node_id, err});
            return err;
        };
        const output0_f32 = @as([*]const f32, @ptrCast(@alignCast(output0.data.ptr)))[0..3];
        print("  sum0: ", .{});
        for (output0_f32) |v| print("{d:.1} ", .{v});
        print("(expected: 5 7 9)\n", .{});
        
        // Try to get second output
        const output1 = executor.getOutput(sum1.node_id) catch |err| {
            print("  Failed to get sum1 (node {}): {}\n", .{sum1.node_id, err});
            return err;
        };
        const output1_f32 = @as([*]const f32, @ptrCast(@alignCast(output1.data.ptr)))[0..2];
        print("  sum1: ", .{});
        for (output1_f32) |v| print("{d:.1} ", .{v});
        print("(expected: 6 15)\n", .{});
    }
    
    print("\nConclusion: Multi-output issue isolated\n", .{});
}

// ========== From test_multi_output_memory_debug.zig ==========

test "debug multi-output memory planning" {
    const allocator = testing.allocator;
    
    print("\n=== DEBUG: Multi-output memory planning ===\n", .{});
    
    var graph = try Graph.init(allocator);
    defer graph.deinit();
    
    const x = try tensor.placeholder(&graph, &.{2, 3}, .f32);
    print("Created placeholder node_id={}\n", .{x.node_id});
    
    const sum_axis0 = try x.sum(0, true);
    print("Created sum_axis0 node_id={}\n", .{sum_axis0.node_id});
    
    const sum_axis1 = try x.sum(1, true);
    print("Created sum_axis1 node_id={}\n", .{sum_axis1.node_id});
    
    // Add both as outputs
    try graph.output_nodes.append(graph.arena.allocator(), sum_axis0.node_id);
    try graph.output_nodes.append(graph.arena.allocator(), sum_axis1.node_id);
    
    print("Output nodes: {any}\n", .{graph.output_nodes.items});
    print("Output nodes detailed:\n", .{});
    for (graph.output_nodes.items, 0..) |out_id, i| {
        print("  output_nodes[{}] = {}\n", .{i, out_id});
    }
    
    // Check that both nodes exist
    print("\nVerifying nodes exist:\n", .{});
    print("  Node 0 exists: {}\n", .{graph.hasNode(x.node_id)});
    print("  Node 1 exists: {}\n", .{graph.hasNode(sum_axis0.node_id)});
    print("  Node 2 exists: {}\n", .{graph.hasNode(sum_axis1.node_id)});
    
    // Debug node_map
    print("\nNode map contents:\n", .{});
    var iter = graph.node_map.iterator();
    while (iter.next()) |entry| {
        print("  node_id {} -> index {}\n", .{entry.key_ptr.*, entry.value_ptr.*});
    }
    print("  Graph has {} nodes in array\n", .{graph.nodes.items.len});
    
    // Get topological order manually to debug
    const topo_order = try graph.topologicalSortOwned(allocator);
    defer allocator.free(topo_order);
    print("\nTopological order: {any}\n", .{topo_order});
    
    // Check metadata before compilation
    print("\nChecking metadata before compilation:\n", .{});
    for ([_]types.NodeId{x.node_id, sum_axis0.node_id, sum_axis1.node_id}) |check_id| {
        if (graph.getNode(check_id)) |node| {
            print("  Node {} ({}): ", .{check_id, node.spec});
            if (node.metadata) |meta| {
                if (meta.output_shape) |shape| {
                    print("has shape [", .{});
                    for (shape.dims, 0..) |dim, i| {
                        if (i > 0) print(", ", .{});
                        switch (dim) {
                            .concrete => |val| print("{}", .{val}),
                            .dynamic => print("?", .{}),
                        }
                    }
                    print("]\n", .{});
                } else {
                    print("has metadata but no output_shape\n", .{});
                }
            } else {
                print("NO METADATA\n", .{});
            }
        }
    }
    
    // Check output nodes before compilation
    print("\nOutput nodes before compilation:\n", .{});
    for (graph.output_nodes.items, 0..) |out_id, i| {
        print("  output_nodes[{}] = {}\n", .{i, out_id});
    }
    
    // Check node validity before compilation
    print("\nNode validity before compilation:\n", .{});
    for (0..graph.next_node_id) |i| {
        const nid: types.NodeId = @intCast(i);
        print("  Node {} is_valid = {}\n", .{nid, graph.hasNode(nid)});
    }
    
    // Compile with debug logs
    var compiled = try compiler.compile.compileCpu(&graph, allocator);
    defer compiled.deinit(allocator);
    
    // Check memory plan
    print("\nMemory plan allocations:\n", .{});
    for (compiled.memory_plan.allocations) |alloc| {
        print("  Node {} at offset {} with {} bytes\n", .{alloc.node_id, alloc.offset, alloc.size});
    }
    
    print("\nExecution steps:\n", .{});
    for (compiled.steps, 0..) |step, i| {
        print("  Step {}: node {} with {} inputs, {} outputs\n", 
            .{i, step.node_id, step.input_buffers.len, step.output_buffers.len});
        print("    Input buffers: {any}\n", .{step.input_buffers});
        print("    Output buffers: {any}\n", .{step.output_buffers});
    }
    
    var executor = try execution.Executor.init(allocator, &compiled, null);
    defer executor.deinit();
    
    const x_data = [_]f32{ 1, 2, 3, 4, 5, 6 };
    const shape = [_]i64{2, 3};
    
    try executor.setInput(x.node_id, std.mem.asBytes(&x_data), &shape, .f32);
    try executor.run();
    
    // Try to get both outputs
    _ = executor.getOutput(sum_axis0.node_id) catch |err| {
        print("Failed to get sum_axis0 (node {}): {}\n", .{sum_axis0.node_id, err});
        return err;
    };
    print("Got output0 successfully\n", .{});
    
    _ = executor.getOutput(sum_axis1.node_id) catch |err| {
        print("Failed to get sum_axis1 (node {}): {}\n", .{sum_axis1.node_id, err});
        return err;
    };
    print("Got output1 successfully\n", .{});
}

// ========== From test_metadata_issue.zig ==========

test "metadata issue investigation" {
    const allocator = testing.allocator;
    
    std.debug.print("\n=== Metadata Issue Investigation ===\n", .{});
    
    var graph = try Graph.init(allocator);
    defer graph.deinit();
    
    // Create a simple graph with two reductions
    const x = try tensor.placeholder(&graph, &.{2, 3}, .f32);
    std.debug.print("Created placeholder node_id={}\n", .{x.node_id});
    
    const sum0 = try x.sum(0, true);
    std.debug.print("Created sum0 node_id={}\n", .{sum0.node_id});
    
    const sum1 = try x.sum(1, true);
    std.debug.print("Created sum1 node_id={}\n", .{sum1.node_id});
    
    // Check metadata immediately after creation
    std.debug.print("\nChecking metadata after creation:\n", .{});
    for ([_]types.NodeId{x.node_id, sum0.node_id, sum1.node_id}) |nid| {
        if (graph.getNode(nid)) |node| {
            std.debug.print("  Node {}: ", .{nid});
            if (node.metadata) |meta| {
                std.debug.print("has metadata", .{});
                if (meta.output_shape) |_| {
                    std.debug.print(" with output_shape", .{});
                }
                if (meta.reduction_axis) |axis| {
                    std.debug.print(" (reduction axis {})", .{axis});
                }
                std.debug.print("\n", .{});
            } else {
                std.debug.print("NO METADATA\n", .{});
            }
        }
    }
    
    // Check what sumReduce actually does
    const node1 = graph.getNode(sum1.node_id).?;
    std.debug.print("\nNode {} details:\n", .{sum1.node_id});
    std.debug.print("  spec: {}\n", .{node1.spec});
    std.debug.print("  inputs: {any}\n", .{node1.inputs});
    std.debug.print("  metadata pointer: {?}\n", .{node1.metadata});
}

// ========== From test_shape_metadata_debug.zig ==========

test "debug reduction shape metadata" {
    const allocator = testing.allocator;
    
    std.debug.print("\n=== DEBUG: Reduction shape metadata ===\n", .{});
    
    var graph = try Graph.init(allocator);
    defer graph.deinit();
    
    const x = try tensor.placeholder(&graph, &.{2, 3}, .f32);
    print("Created placeholder node_id={}\n", .{x.node_id});
    
    // Check TensorHandle shape
    print("TensorHandle x.shape: ", .{});
    for (x.shape.dims) |dim| {
        switch (dim) {
            .concrete => |val| print("{} ", .{val}),
            .dynamic => print("? ", .{}),
        }
    }
    print("\n", .{});
    
    // Create reduction
    const sum_axis1 = try x.sum(1, true); // keepdims=true
    print("\nCreated sum_axis1 node_id={}\n", .{sum_axis1.node_id});
    
    // Check TensorHandle shape
    print("TensorHandle sum_axis1.shape: ", .{});
    for (sum_axis1.shape.dims) |dim| {
        switch (dim) {
            .concrete => |val| print("{} ", .{val}),
            .dynamic => print("? ", .{}),
        }
    }
    print("\n", .{});
    
    // Check if node has metadata
    print("\nChecking node metadata:\n", .{});
    if (graph.getNode(sum_axis1.node_id)) |node| {
        print("  Node {} exists\n", .{sum_axis1.node_id});
        print("  Node spec: {}\n", .{node.spec});
        
        if (node.metadata) |meta| {
            print("  Has metadata\n", .{});
            
            if (meta.output_shape) |shape| {
                print("    Output shape: ", .{});
                for (shape.dims) |dim| {
                    switch (dim) {
                        .concrete => |val| print("{} ", .{val}),
                        .dynamic => print("? ", .{}),
                    }
                }
                print("\n", .{});
            } else {
                print("    No output shape in metadata\n", .{});
            }
            
            if (meta.input_shapes) |shapes| {
                print("    Input shapes count: {}\n", .{shapes.len});
            } else {
                print("    No input shapes in metadata\n", .{});
            }
            
            if (meta.reduction_axis) |axis| {
                print("    Reduction axis: {}\n", .{axis});
            } else {
                print("    No reduction axis in metadata\n", .{});
            }
        } else {
            print("  No metadata\n", .{});
        }
    } else {
        print("  Node {} not found!\n", .{sum_axis1.node_id});
    }
    
    // Let's also check what setNodeShapeMetadata does
    print("\nChecking setNodeShapeMetadata behavior:\n", .{});
    
    // The primitive operations call setNodeShapeMetadata
    // Let's trace the call from sumReduce:
    // 1. sumReduce creates the node
    // 2. Calls setReductionAxis
    // 3. Then modifies metadata to add shapes
    
    print("Success - test completed\n", .{});
}