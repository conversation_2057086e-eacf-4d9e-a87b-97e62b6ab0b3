/// Gradient verification against PyTorch ground truth values
/// This test compares <PERSON><PERSON>'s gradient computations with PyTorch reference values

const std = @import("std");
const testing = std.testing;

const Graph = @import("graph").Graph;
const tensor = @import("tensor");
const TensorHandle = tensor.TensorHandle;
const training = @import("training");
const compiler = @import("compiler");
const PassContext = compiler.PassContext;
const execution = @import("execution");
const Executor = execution.Executor;
const CompiledGraph = execution.CompiledGraph;

// PyTorch gradient test case structure
const GradientTestCase = struct {
    operation: []const u8,
    x: f32,
    y: ?f32 = null,
    expected: f32,
    tolerance: f32 = 0.0001,
};

// PyTorch ground truth test cases (subset for demonstration)
const pytorch_test_cases = [_]GradientTestCase{
    // Addition gradients - all should be 1.0
    .{ .operation = "add", .x = 1.0, .y = 2.0, .expected = 1.0 },
    .{ .operation = "add", .x = -3.5, .y = 1.5, .expected = 1.0 },
    .{ .operation = "add", .x = 0.0, .y = 5.0, .expected = 1.0 },
    
    // Multiplication gradients - should be y
    .{ .operation = "mul", .x = 2.0, .y = 3.0, .expected = 3.0 },
    .{ .operation = "mul", .x = -1.5, .y = 4.0, .expected = 4.0 },
    .{ .operation = "mul", .x = 0.5, .y = -2.0, .expected = -2.0 },
    
    // Reciprocal gradients - should be -1/x^2
    .{ .operation = "recip", .x = 0.5, .y = null, .expected = -4.0 },
    .{ .operation = "recip", .x = 1.0, .y = null, .expected = -1.0 },
    .{ .operation = "recip", .x = 2.0, .y = null, .expected = -0.25 },
    
    // Square root gradients - should be 1/(2*sqrt(x))
    .{ .operation = "sqrt", .x = 0.25, .y = null, .expected = 1.0 },
    .{ .operation = "sqrt", .x = 1.0, .y = null, .expected = 0.5 },
    .{ .operation = "sqrt", .x = 4.0, .y = null, .expected = 0.25 },
    .{ .operation = "sqrt", .x = 9.0, .y = null, .expected = 0.16666666666666666 },
    
    // Exponential base 2 gradients - should be 2^x * ln(2)
    .{ .operation = "exp2", .x = -2.0, .y = null, .expected = 0.17328679513998632 },
    .{ .operation = "exp2", .x = 0.0, .y = null, .expected = 0.6931471805599453 },
    .{ .operation = "exp2", .x = 1.0, .y = null, .expected = 1.3862943611198906 },
    .{ .operation = "exp2", .x = 2.0, .y = null, .expected = 2.772588722239781 },
    
    // Logarithm base 2 gradients - should be 1/(x * ln(2))
    .{ .operation = "log2", .x = 0.5, .y = null, .expected = 2.8853900817779268 },
    .{ .operation = "log2", .x = 1.0, .y = null, .expected = 1.4426950408889634 },
    .{ .operation = "log2", .x = 2.0, .y = null, .expected = 0.7213475204444817 },
    .{ .operation = "log2", .x = 4.0, .y = null, .expected = 0.36067376022224085 },
};

test "verify gradients against PyTorch - scalar operations" {
    var arena = std.heap.ArenaAllocator.init(testing.allocator);
    defer arena.deinit();
    const allocator = arena.allocator();
    
    for (pytorch_test_cases) |test_case| {
        // Create new graph for each test
        var graph = try Graph.init(allocator);
        
        // Create parameter
        const x = try tensor.placeholder(&graph, &.{}, .f32);
        try graph.markParameter(x.node_id);
        
        // Apply operation
        const y = if (std.mem.eql(u8, test_case.operation, "add")) blk: {
            const y_const = try tensor.constant(&graph, test_case.y.?, .f32);
            break :blk try x.add(y_const);
        } else if (std.mem.eql(u8, test_case.operation, "mul")) blk: {
            const y_const = try tensor.constant(&graph, test_case.y.?, .f32);
            break :blk try x.mul(y_const);
        } else if (std.mem.eql(u8, test_case.operation, "recip")) blk: {
            break :blk try x.recip();
        } else if (std.mem.eql(u8, test_case.operation, "sqrt")) blk: {
            break :blk try x.sqrt();
        } else if (std.mem.eql(u8, test_case.operation, "exp2")) blk: {
            break :blk try x.exp2();
        } else if (std.mem.eql(u8, test_case.operation, "log2")) blk: {
            break :blk try x.log2();
        } else {
            std.log.warn("Skipping unsupported operation: {s}", .{test_case.operation});
            continue;
        };
        
        // Mark output
        try graph.markOutput(y.node_id);
        
        // Apply autograd
        var ctx = PassContext{
            .graph = &graph,
            .allocator = allocator,
        };
        try training.autograd.applyAutograd(&ctx, &.{x.node_id}, y.node_id);
        
        // Get gradient node
        const grad_map = graph.getGradientMap();
        const grad_x_id = grad_map.?.get(x.node_id) orelse {
            std.log.err("No gradient found for {s} operation", .{test_case.operation});
            return error.GradientNotFound;
        };
        
        // Mark gradient as output
        try graph.markOutput(grad_x_id);
        
        // Compile graph
        const compiled = try compiler.compile.compileCpu(&graph, allocator);
        var compiled_heap = try allocator.create(CompiledGraph);
        compiled_heap.* = compiled;
        defer {
            compiled_heap.deinit(allocator);
            allocator.destroy(compiled_heap);
        }
        
        // Create executor
        var executor = try Executor.init(allocator, compiled_heap, null);
        defer executor.deinit();
        
        // Set input value
        const x_input = [_]f32{test_case.x};
        try executor.setInput(x.node_id, std.mem.sliceAsBytes(&x_input), &.{}, .f32);
        
        // Execute
        try executor.run();
        
        // Get gradient
        const grad_view = try executor.getOutput(grad_x_id);
        const computed_grad = std.mem.bytesAsSlice(f32, grad_view.data)[0];
        
        // Compare with PyTorch
        const diff = @abs(computed_grad - test_case.expected);
        const passed = diff < test_case.tolerance;
        
        if (passed) {
            std.log.info("✓ {s}({d:.4f}): grad={d:.6f} (expected: {d:.6f})", .{
                test_case.operation,
                test_case.x,
                computed_grad,
                test_case.expected,
            });
        } else {
            std.log.err("✗ {s}({d:.4f}): grad={d:.6f} (expected: {d:.6f}, diff: {d:.6f})", .{
                test_case.operation,
                test_case.x,
                computed_grad,
                test_case.expected,
                diff,
            });
            return error.GradientMismatch;
        }
    }
    
    std.log.info("✓ All gradient tests match PyTorch ground truth!", .{});
}

test "verify gradients - matrix operations" {
    var arena = std.heap.ArenaAllocator.init(testing.allocator);
    defer arena.deinit();
    const allocator = arena.allocator();
    
    var graph = try Graph.init(allocator);
    
    // Test matmul gradient: A[2,3] @ B[3,2]
    const A = try tensor.placeholder(&graph, &.{2, 3}, .f32);
    const B = try tensor.placeholder(&graph, &.{3, 2}, .f32);
    try graph.markParameter(A.node_id);
    try graph.markParameter(B.node_id);
    
    // C = A @ B
    const C = try A.matmul(B);
    const loss = try C.sumAll();
    
    // Mark loss as output
    try graph.markOutput(loss.node_id);
    
    // Apply autograd
    var ctx = PassContext{
        .graph = &graph,
        .allocator = allocator,
    };
    try training.autograd.applyAutograd(&ctx, &.{A.node_id, B.node_id}, loss.node_id);
    
    // Get gradients
    const grad_map = graph.getGradientMap();
    const grad_A_id = grad_map.?.get(A.node_id).?;
    const grad_B_id = grad_map.?.get(B.node_id).?;
    
    // Mark gradients as outputs
    try graph.markOutput(grad_A_id);
    try graph.markOutput(grad_B_id);
    
    // Compile
    const compiled = try compiler.compile.compileCpu(&graph, allocator);
    var compiled_heap = try allocator.create(CompiledGraph);
    compiled_heap.* = compiled;
    defer {
        compiled_heap.deinit(allocator);
        allocator.destroy(compiled_heap);
    }
    
    // Execute
    var executor = try Executor.init(allocator, compiled_heap, null);
    defer executor.deinit();
    
    // Test values matching PyTorch test
    const A_val = [_]f32{1.0, 2.0, 3.0, 4.0, 5.0, 6.0};
    const B_val = [_]f32{7.0, 8.0, 9.0, 10.0, 11.0, 12.0};
    try executor.setInput(A.node_id, std.mem.sliceAsBytes(&A_val), &.{2, 3}, .f32);
    try executor.setInput(B.node_id, std.mem.sliceAsBytes(&B_val), &.{3, 2}, .f32);
    
    try executor.run();
    
    // Get gradients
    const grad_A_view = try executor.getOutput(grad_A_id);
    const grad_B_view = try executor.getOutput(grad_B_id);
    const grad_A_vals = std.mem.bytesAsSlice(f32, grad_A_view.data);
    const grad_B_vals = std.mem.bytesAsSlice(f32, grad_B_view.data);
    
    // PyTorch reference values
    const expected_grad_A = [_]f32{15.0, 19.0, 23.0, 15.0, 19.0, 23.0};
    const expected_grad_B = [_]f32{5.0, 5.0, 7.0, 7.0, 9.0, 9.0};
    
    // Verify
    for (grad_A_vals, expected_grad_A) |actual, expected| {
        try testing.expectApproxEqAbs(expected, actual, 0.001);
    }
    for (grad_B_vals, expected_grad_B) |actual, expected| {
        try testing.expectApproxEqAbs(expected, actual, 0.001);
    }
    
    std.log.info("✓ Matmul gradients match PyTorch reference!", .{});
}