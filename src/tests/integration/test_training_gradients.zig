/// Integration test for training gradients
/// Verifies gradient computation against ground truth values

const std = @import("std");
const testing = std.testing;

const Graph = @import("graph").Graph;
const tensor = @import("tensor");
const TensorHandle = tensor.TensorHandle;
const session = @import("session");
const Session = session.Session;
const training = @import("training");
const compiler = @import("compiler");
const PassContext = compiler.PassContext;
const execution = @import("execution");
const Executor = execution.Executor;
const CompiledGraph = execution.CompiledGraph;
const storage = @import("storage");
const DataStorage = storage.DataStorage;

test "gradient of x squared" {
    // Test simple gradient computation: f(x) = x^2, df/dx = 2x
    var arena = std.heap.ArenaAllocator.init(testing.allocator);
    defer arena.deinit();
    const allocator = arena.allocator();
    
    // Create computation graph
    var graph = try Graph.init(allocator);
    
    // Create x as parameter
    const x = try tensor.placeholder(&graph, &.{}, .f32);
    try graph.markParameter(x.node_id);
    
    // Compute y = x * x
    const y = try x.mul(x);
    
    // Mark y as output
    try graph.markOutput(y.node_id);
    
    // Apply autograd
    var ctx = PassContext{
        .graph = &graph,
        .allocator = allocator,
    };
    try training.autograd.applyAutograd(&ctx, &.{x.node_id}, y.node_id);
    
    // Get gradient computation
    const grad_map = graph.getGradientMap();
    try testing.expect(grad_map != null);
    const grad_x_id = grad_map.?.get(x.node_id);
    try testing.expect(grad_x_id != null);
    
    // Mark gradient as output too
    try graph.markOutput(grad_x_id.?);
    
    // Compile graph using new API
    const compiled = try compiler.compile.compileCpu(&graph, allocator);
    var compiled_heap = try allocator.create(CompiledGraph);
    compiled_heap.* = compiled;
    defer {
        compiled_heap.deinit(allocator);
        allocator.destroy(compiled_heap);
    }
    
    // Create executor
    var executor = try Executor.init(allocator, compiled_heap, null);
    defer executor.deinit();
    
    // Test at x = 3.0, expected gradient = 2 * 3 = 6.0
    const x_val = [_]f32{3.0};
    try executor.setInput(x.node_id, std.mem.sliceAsBytes(&x_val), &.{}, .f32);
    
    // Execute to compute gradient
    try executor.run();
    
    // Get gradient value
    const grad_view = try executor.getOutput(grad_x_id.?);
    const grad_val = std.mem.bytesAsSlice(f32, grad_view.data)[0];
    
    // Check gradient: df/dx = 2x = 2*3 = 6
    try testing.expectApproxEqAbs(@as(f32, 6.0), grad_val, 0.001);
    
    std.log.info("✓ Gradient of x^2 at x=3: {d:.4f} (expected: 6.0)", .{grad_val});
}

test "gradient with chain rule" {
    // Test chain rule: f(x) = (x + 1)^2, df/dx = 2(x + 1)
    var arena = std.heap.ArenaAllocator.init(testing.allocator);
    defer arena.deinit();
    const allocator = arena.allocator();
    
    var graph = try Graph.init(allocator);
    
    // Create x as parameter
    const x = try tensor.placeholder(&graph, &.{}, .f32);
    try graph.markParameter(x.node_id);
    
    // Compute y = (x + 1)^2
    const one = try tensor.constant(&graph, 1.0, .f32);
    const x_plus_one = try x.add(one);
    const y = try x_plus_one.mul(x_plus_one);
    
    // Mark y as output
    try graph.markOutput(y.node_id);
    
    // Apply autograd
    var ctx = PassContext{
        .graph = &graph,
        .allocator = allocator,
    };
    try training.autograd.applyAutograd(&ctx, &.{x.node_id}, y.node_id);
    
    // Get gradient
    const grad_map = graph.getGradientMap();
    try testing.expect(grad_map != null);
    const grad_x_id = grad_map.?.get(x.node_id);
    try testing.expect(grad_x_id != null);
    
    // Mark gradient as output
    try graph.markOutput(grad_x_id.?);
    
    // Compile graph
    const compiled = try compiler.compile.compileCpu(&graph, allocator);
    var compiled_heap = try allocator.create(CompiledGraph);
    compiled_heap.* = compiled;
    defer {
        compiled_heap.deinit(allocator);
        allocator.destroy(compiled_heap);
    }
    
    // Create executor
    var executor = try Executor.init(allocator, compiled_heap, null);
    defer executor.deinit();
    
    // Test at x = 2.0, expected gradient = 2*(2+1) = 6.0
    const x_val = [_]f32{2.0};
    try executor.setInput(x.node_id, std.mem.sliceAsBytes(&x_val), &.{}, .f32);
    
    // Execute
    try executor.run();
    
    // Get gradient value
    const grad_view = try executor.getOutput(grad_x_id.?);
    const grad_val = std.mem.bytesAsSlice(f32, grad_view.data)[0];
    
    try testing.expectApproxEqAbs(@as(f32, 6.0), grad_val, 0.001);
    
    std.log.info("✓ Gradient of (x+1)^2 at x=2: {d:.4f} (expected: 6.0)", .{grad_val});
}

test "gradient with multiple parameters" {
    // Test multiple parameters: f(a,b) = a*b + a^2, df/da = b + 2a, df/db = a
    var arena = std.heap.ArenaAllocator.init(testing.allocator);
    defer arena.deinit();
    const allocator = arena.allocator();
    
    var graph = try Graph.init(allocator);
    
    // Create parameters
    const a = try tensor.placeholder(&graph, &.{}, .f32);
    const b = try tensor.placeholder(&graph, &.{}, .f32);
    try graph.markParameter(a.node_id);
    try graph.markParameter(b.node_id);
    
    // Compute y = a*b + a^2
    const ab = try a.mul(b);
    const a_squared = try a.mul(a);
    const y = try ab.add(a_squared);
    
    // Mark y as output
    try graph.markOutput(y.node_id);
    
    // Apply autograd
    var ctx = PassContext{
        .graph = &graph,
        .allocator = allocator,
    };
    try training.autograd.applyAutograd(&ctx, &.{a.node_id, b.node_id}, y.node_id);
    
    // Get gradients
    const grad_map = graph.getGradientMap();
    try testing.expect(grad_map != null);
    const grad_a_id = grad_map.?.get(a.node_id);
    const grad_b_id = grad_map.?.get(b.node_id);
    try testing.expect(grad_a_id != null);
    try testing.expect(grad_b_id != null);
    
    // Mark gradients as outputs
    try graph.markOutput(grad_a_id.?);
    try graph.markOutput(grad_b_id.?);
    
    // Compile graph
    const compiled = try compiler.compile.compileCpu(&graph, allocator);
    var compiled_heap = try allocator.create(CompiledGraph);
    compiled_heap.* = compiled;
    defer {
        compiled_heap.deinit(allocator);
        allocator.destroy(compiled_heap);
    }
    
    // Create executor
    var executor = try Executor.init(allocator, compiled_heap, null);
    defer executor.deinit();
    
    // Test at a = 3.0, b = 4.0
    // Expected: df/da = b + 2a = 4 + 6 = 10
    // Expected: df/db = a = 3
    const a_val = [_]f32{3.0};
    const b_val = [_]f32{4.0};
    try executor.setInput(a.node_id, std.mem.sliceAsBytes(&a_val), &.{}, .f32);
    try executor.setInput(b.node_id, std.mem.sliceAsBytes(&b_val), &.{}, .f32);
    
    // Execute
    try executor.run();
    
    // Get gradient values
    const grad_a_view = try executor.getOutput(grad_a_id.?);
    const grad_b_view = try executor.getOutput(grad_b_id.?);
    const grad_a_val = std.mem.bytesAsSlice(f32, grad_a_view.data)[0];
    const grad_b_val = std.mem.bytesAsSlice(f32, grad_b_view.data)[0];
    
    try testing.expectApproxEqAbs(@as(f32, 10.0), grad_a_val, 0.001);
    try testing.expectApproxEqAbs(@as(f32, 3.0), grad_b_val, 0.001);
    
    std.log.info("✓ Gradient of a*b + a^2:", .{});
    std.log.info("  df/da at (3,4): {d:.4f} (expected: 10.0)", .{grad_a_val});
    std.log.info("  df/db at (3,4): {d:.4f} (expected: 3.0)", .{grad_b_val});
}

test "transpose gradient values" {
    // Test transpose gradient computation with actual values
    var arena = std.heap.ArenaAllocator.init(testing.allocator);
    defer arena.deinit();
    const allocator = arena.allocator();
    
    var graph = try Graph.init(allocator);
    
    // Create A[2,3] as parameter
    const A = try tensor.placeholder(&graph, &.{2, 3}, .f32);
    try graph.markParameter(A.node_id);
    
    // Compute loss = sum(transpose(A))
    const A_T = try A.transpose(&.{1, 0}); // [2,3] -> [3,2]
    const loss = try A_T.sumAll();
    
    // Mark loss as output
    try graph.markOutput(loss.node_id);
    
    // Apply autograd
    var ctx = PassContext{
        .graph = &graph,
        .allocator = allocator,
    };
    try training.autograd.applyAutograd(&ctx, &.{A.node_id}, loss.node_id);
    
    // Get gradient
    const grad_map = graph.getGradientMap();
    try testing.expect(grad_map != null);
    const grad_A_id = grad_map.?.get(A.node_id);
    try testing.expect(grad_A_id != null);
    
    // Mark gradient as output
    try graph.markOutput(grad_A_id.?);
    
    // Compile graph
    const compiled = try compiler.compile.compileCpu(&graph, allocator);
    var compiled_heap = try allocator.create(CompiledGraph);
    compiled_heap.* = compiled;
    defer {
        compiled_heap.deinit(allocator);
        allocator.destroy(compiled_heap);
    }
    
    // Create executor
    var executor = try Executor.init(allocator, compiled_heap, null);
    defer executor.deinit();
    
    // Set A = [[1, 2, 3], [4, 5, 6]]
    const A_val = [_]f32{1, 2, 3, 4, 5, 6};
    try executor.setInput(A.node_id, std.mem.sliceAsBytes(&A_val), &.{2, 3}, .f32);
    
    // Execute
    try executor.run();
    
    // Get gradient - should be all ones
    const grad_view = try executor.getOutput(grad_A_id.?);
    const grad_vals = std.mem.bytesAsSlice(f32, grad_view.data);
    
    // Verify gradient is all ones (derivative of sum)
    for (grad_vals) |val| {
        try testing.expectApproxEqAbs(@as(f32, 1.0), val, 0.001);
    }
    
    std.log.info("✓ Transpose gradient: all elements = 1.0 (correct)", .{});
}

test "matmul gradient values" {
    // Test matrix multiplication gradient with PyTorch reference
    var arena = std.heap.ArenaAllocator.init(testing.allocator);
    defer arena.deinit();
    const allocator = arena.allocator();
    
    var graph = try Graph.init(allocator);
    
    // Create A[2,3] and B[3,2] as parameters
    const A = try tensor.placeholder(&graph, &.{2, 3}, .f32);
    const B = try tensor.placeholder(&graph, &.{3, 2}, .f32);
    try graph.markParameter(A.node_id);
    try graph.markParameter(B.node_id);
    
    // C = A @ B
    const C = try A.matmul(B);
    const loss = try C.sumAll();
    
    // Mark loss as output
    try graph.markOutput(loss.node_id);
    
    // Apply autograd
    var ctx = PassContext{
        .graph = &graph,
        .allocator = allocator,
    };
    try training.autograd.applyAutograd(&ctx, &.{A.node_id, B.node_id}, loss.node_id);
    
    // Get gradients
    const grad_map = graph.getGradientMap();
    try testing.expect(grad_map != null);
    const grad_A_id = grad_map.?.get(A.node_id);
    const grad_B_id = grad_map.?.get(B.node_id);
    try testing.expect(grad_A_id != null);
    try testing.expect(grad_B_id != null);
    
    // Mark gradients as outputs
    try graph.markOutput(grad_A_id.?);
    try graph.markOutput(grad_B_id.?);
    
    // Compile graph
    const compiled = try compiler.compile.compileCpu(&graph, allocator);
    var compiled_heap = try allocator.create(CompiledGraph);
    compiled_heap.* = compiled;
    defer {
        compiled_heap.deinit(allocator);
        allocator.destroy(compiled_heap);
    }
    
    // Create executor
    var executor = try Executor.init(allocator, compiled_heap, null);
    defer executor.deinit();
    
    // Use same values as PyTorch test
    const A_val = [_]f32{1.0, 2.0, 3.0, 4.0, 5.0, 6.0};
    const B_val = [_]f32{7.0, 8.0, 9.0, 10.0, 11.0, 12.0};
    try executor.setInput(A.node_id, std.mem.sliceAsBytes(&A_val), &.{2, 3}, .f32);
    try executor.setInput(B.node_id, std.mem.sliceAsBytes(&B_val), &.{3, 2}, .f32);
    
    // Execute
    try executor.run();
    
    // Get computed gradients
    const grad_A_view = try executor.getOutput(grad_A_id.?);
    const grad_B_view = try executor.getOutput(grad_B_id.?);
    const grad_A_vals = std.mem.bytesAsSlice(f32, grad_A_view.data);
    const grad_B_vals = std.mem.bytesAsSlice(f32, grad_B_view.data);
    
    // PyTorch reference: grad_A = ones @ B.T = [[15, 19, 23], [15, 19, 23]]
    const expected_grad_A = [_]f32{15.0, 19.0, 23.0, 15.0, 19.0, 23.0};
    // PyTorch reference: grad_B = A.T @ ones = [[5, 5], [7, 7], [9, 9]]
    const expected_grad_B = [_]f32{5.0, 5.0, 7.0, 7.0, 9.0, 9.0};
    
    std.log.info("Matmul gradients:", .{});
    std.log.info("  grad_A: {d:.2}", .{grad_A_vals});
    std.log.info("  expected: {d:.2}", .{expected_grad_A});
    std.log.info("  grad_B: {d:.2}", .{grad_B_vals});
    std.log.info("  expected: {d:.2}", .{expected_grad_B});
    
    // Verify gradients match PyTorch
    for (grad_A_vals, expected_grad_A) |actual, expected| {
        try testing.expectApproxEqAbs(expected, actual, 0.001);
    }
    for (grad_B_vals, expected_grad_B) |actual, expected| {
        try testing.expectApproxEqAbs(expected, actual, 0.001);
    }
    
    std.log.info("✓ Matmul gradients match PyTorch reference!", .{});
}

test "broadcast gradient values" {
    // Test broadcasting gradient: x[2,3] + b[3]
    var arena = std.heap.ArenaAllocator.init(testing.allocator);
    defer arena.deinit();
    const allocator = arena.allocator();
    
    var graph = try Graph.init(allocator);
    
    // Create parameters
    const x = try tensor.placeholder(&graph, &.{2, 3}, .f32);
    const b = try tensor.placeholder(&graph, &.{3}, .f32);
    try graph.markParameter(x.node_id);
    try graph.markParameter(b.node_id);
    
    // y = x + b (broadcast)
    const y = try x.add(b);
    const loss = try y.sumAll();
    
    // Mark loss as output
    try graph.markOutput(loss.node_id);
    
    // Apply autograd
    var ctx = PassContext{
        .graph = &graph,
        .allocator = allocator,
    };
    try training.autograd.applyAutograd(&ctx, &.{x.node_id, b.node_id}, loss.node_id);
    
    // Get gradients
    const grad_map = graph.getGradientMap();
    try testing.expect(grad_map != null);
    const grad_x_id = grad_map.?.get(x.node_id);
    const grad_b_id = grad_map.?.get(b.node_id);
    try testing.expect(grad_x_id != null);
    try testing.expect(grad_b_id != null);
    
    // Mark gradients as outputs
    try graph.markOutput(grad_x_id.?);
    try graph.markOutput(grad_b_id.?);
    
    // Compile graph
    const compiled = try compiler.compile.compileCpu(&graph, allocator);
    var compiled_heap = try allocator.create(CompiledGraph);
    compiled_heap.* = compiled;
    defer {
        compiled_heap.deinit(allocator);
        allocator.destroy(compiled_heap);
    }
    
    // Create executor
    var executor = try Executor.init(allocator, compiled_heap, null);
    defer executor.deinit();
    
    // Set values
    const x_val = [_]f32{1, 2, 3, 4, 5, 6};
    const b_val = [_]f32{10, 20, 30};
    try executor.setInput(x.node_id, std.mem.sliceAsBytes(&x_val), &.{2, 3}, .f32);
    try executor.setInput(b.node_id, std.mem.sliceAsBytes(&b_val), &.{3}, .f32);
    
    // Execute
    try executor.run();
    
    // Get computed gradients
    const grad_x_view = try executor.getOutput(grad_x_id.?);
    const grad_b_view = try executor.getOutput(grad_b_id.?);
    const grad_x_vals = std.mem.bytesAsSlice(f32, grad_x_view.data);
    const grad_b_vals = std.mem.bytesAsSlice(f32, grad_b_view.data);
    
    // Expected: grad_x = all ones, grad_b = [2, 2, 2] (summed over batch)
    std.log.info("Broadcast gradients:", .{});
    std.log.info("  grad_x: {d:.2}", .{grad_x_vals});
    std.log.info("  grad_b: {d:.2}", .{grad_b_vals});
    
    // Verify grad_x is all ones
    for (grad_x_vals) |val| {
        try testing.expectApproxEqAbs(@as(f32, 1.0), val, 0.001);
    }
    
    // Verify grad_b is [2, 2, 2]
    for (grad_b_vals) |val| {
        try testing.expectApproxEqAbs(@as(f32, 2.0), val, 0.001);
    }
    
    std.log.info("✓ Broadcast gradients correct!", .{});
}

test "autodiff as compiler pass integration" {
    // Test autograd as compiler pass
    var arena = std.heap.ArenaAllocator.init(testing.allocator);
    defer arena.deinit();
    const allocator = arena.allocator();
    
    var graph = try Graph.init(allocator);
    
    // Create simple computation
    const x = try tensor.placeholder(&graph, &.{}, .f32);
    try graph.markParameter(x.node_id);
    const y = try x.mul(x);
    
    // Mark output
    try graph.markOutput(y.node_id);
    
    // Apply autograd as a compiler pass
    var ctx = PassContext{
        .graph = &graph,
        .allocator = allocator,
    };
    
    // Simulate compiler passes
    std.log.info("Graph before autograd: {} nodes", .{graph.nodes.items.len});
    
    try training.autograd.applyAutograd(&ctx, &.{x.node_id}, y.node_id);
    
    std.log.info("Graph after autograd: {} nodes", .{graph.nodes.items.len});
    
    // Verify gradient nodes were added
    const grad_map = graph.getGradientMap();
    try testing.expect(grad_map != null);
    try testing.expect(grad_map.?.count() == 1);
    
    std.log.info("✓ Autograd pass successfully added {} gradient mappings", .{grad_map.?.count()});
}

test "gradient verification against PyTorch ground truth (disabled)" {
    // Test gradients against PyTorch ground truth data
    var arena = std.heap.ArenaAllocator.init(testing.allocator);
    defer arena.deinit();
    const allocator = arena.allocator();
    
    // Load PyTorch ground truth data
    // TODO: Load PyTorch gradient data from a proper location
    // For now, skip this test
    return error.SkipZigTest;
    defer parsed.deinit();
    
    const test_cases = parsed.value.array.items;
    
    // Test each operation from ground truth
    for (test_cases) |test_case| {
        const obj = test_case.object;
        const op_name = obj.get("operation").?.string;
        const x_val: f32 = @floatCast(obj.get("x").?.float);
        const expected_grad: f32 = @floatCast(obj.get("expected").?.float);
        
        var graph = try Graph.init(allocator);
        
        // Create parameter
        const x = try tensor.placeholder(&graph, &.{}, .f32);
        try graph.markParameter(x.node_id);
        
        // Apply operation based on test case
        const y = if (std.mem.eql(u8, op_name, "add")) blk: {
            const y_val: f32 = @floatCast(obj.get("y").?.float);
            const y_const = try tensor.constant(&graph, y_val, .f32);
            break :blk try x.add(y_const);
        } else if (std.mem.eql(u8, op_name, "mul")) blk: {
            const y_val: f32 = @floatCast(obj.get("y").?.float);
            const y_const = try tensor.constant(&graph, y_val, .f32);
            break :blk try x.mul(y_const);
        } else if (std.mem.eql(u8, op_name, "recip")) blk: {
            break :blk try x.recip();
        } else if (std.mem.eql(u8, op_name, "sqrt")) blk: {
            break :blk try x.sqrt();
        } else if (std.mem.eql(u8, op_name, "sin")) blk: {
            break :blk try x.sin();
        } else if (std.mem.eql(u8, op_name, "exp2")) blk: {
            break :blk try x.exp2();
        } else if (std.mem.eql(u8, op_name, "log2")) blk: {
            break :blk try x.log2();
        } else {
            std.log.warn("Skipping unsupported operation: {s}", .{op_name});
            continue;
        };
        
        // Mark output
        try graph.markOutput(y.node_id);
        
        // Apply autograd
        var ctx = PassContext{
            .graph = &graph,
            .allocator = allocator,
        };
        try training.autograd.applyAutograd(&ctx, &.{x.node_id}, y.node_id);
        
        // Get gradient
        const grad_map = graph.getGradientMap();
        const grad_x_id = grad_map.?.get(x.node_id) orelse {
            std.log.err("No gradient found for parameter in {s} operation", .{op_name});
            return error.GradientNotFound;
        };
        
        // Mark gradient as output
        try graph.markOutput(grad_x_id);
        
        // Compile and execute
        const compiled = try compiler.compile.compileCpu(&graph, allocator);
        var compiled_heap = try allocator.create(CompiledGraph);
        compiled_heap.* = compiled;
        defer {
            compiled_heap.deinit(allocator);
            allocator.destroy(compiled_heap);
        }
        
        var executor = try Executor.init(allocator, compiled_heap, null);
        defer executor.deinit();
        
        // Set input value
        const x_input = [_]f32{x_val};
        try executor.setInput(x.node_id, std.mem.sliceAsBytes(&x_input), &.{}, .f32);
        
        // Execute
        try executor.run();
        
        // Get gradient
        const grad_view = try executor.getOutput(grad_x_id);
        const computed_grad = std.mem.bytesAsSlice(f32, grad_view.data)[0];
        
        // Compare with ground truth
        const tolerance: f32 = 0.0001;
        const passed = @abs(computed_grad - expected_grad) < tolerance;
        
        if (passed) {
            std.log.info("✓ {s}({d:.4f}): grad={d:.6f} (expected: {d:.6f})", .{ op_name, x_val, computed_grad, expected_grad });
        } else {
            std.log.err("✗ {s}({d:.4f}): grad={d:.6f} (expected: {d:.6f}, diff: {d:.6f})", .{ op_name, x_val, computed_grad, expected_grad, @abs(computed_grad - expected_grad) });
            return error.GradientMismatch;
        }
    }
    
    std.log.info("✓ All gradient tests match PyTorch ground truth!", .{});
}