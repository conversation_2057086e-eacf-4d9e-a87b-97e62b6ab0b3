/// Integration test for training gradients
/// Verifies gradient computation against ground truth values

const std = @import("std");
const testing = std.testing;

const Graph = @import("graph").Graph;
const tensor = @import("tensor");
const TensorHandle = tensor.TensorHandle;
const session = @import("session");
const Session = session.Session;
const training = @import("training");
const compiler = @import("compiler");
const PassContext = compiler.PassContext;
const execution = @import("execution");
const backends = @import("backends");
const storage = @import("storage");

test "gradient of x squared" {
    // Test simple gradient computation: f(x) = x^2, df/dx = 2x
    var arena = std.heap.ArenaAllocator.init(testing.allocator);
    defer arena.deinit();
    const allocator = arena.allocator();
    
    // Create computation graph
    var graph = try Graph.init(allocator);
    
    // Create x as parameter
    const x = try tensor.placeholder(&graph, &.{}, .f32);
    try graph.markParameter(x.node_id);
    
    // Compute y = x * x
    const y = try x.mul(x);
    
    // Apply autograd
    var ctx = PassContext{
        .graph = &graph,
        .allocator = allocator,
    };
    try training.autograd.applyAutograd(&ctx, &.{x.node_id}, y.node_id);
    
    // Get gradient computation
    const grad_map = graph.getGradientMap();
    try testing.expect(grad_map != null);
    const grad_x_id = grad_map.?.get(x.node_id);
    try testing.expect(grad_x_id != null);
    
    // Create executor
    var data_store = try storage.DataStorage.init(allocator, .cpu, 1024 * 1024); // 1MB buffer
    defer data_store.deinit();
    
    const cpu_backend = try backends.getCpuBackend(allocator);
    var exec_engine = try execution.ExecutionEngine.init(allocator, cpu_backend);
    defer exec_engine.deinit();
    
    // Compile graph
    try compiler.compileGraph(&graph, &exec_engine);
    
    // Test at x = 3.0, expected gradient = 2 * 3 = 6.0
    const x_val = [_]f32{3.0};
    try data_store.setBuffer(x.node_id, std.mem.sliceAsBytes(&x_val));
    
    // Execute to compute gradient
    try exec_engine.executeGraph(&graph, &data_store);
    
    // Get gradient value
    const grad_buffer = data_store.getBuffer(grad_x_id.?) orelse return error.GradientNotComputed;
    const grad_val = std.mem.bytesAsSlice(f32, grad_buffer)[0];
    
    // Check gradient: df/dx = 2x = 2*3 = 6
    try testing.expectApproxEqAbs(@as(f32, 6.0), grad_val, 0.001);
    
    std.log.info("✓ Gradient of x^2 at x=3: {d:.4f} (expected: 6.0)", .{grad_val});
}

test "gradient with chain rule" {
    // Test chain rule: f(x) = (x + 1)^2, df/dx = 2(x + 1)
    var arena = std.heap.ArenaAllocator.init(testing.allocator);
    defer arena.deinit();
    const allocator = arena.allocator();
    
    var graph = try Graph.init(allocator);
    
    // Create x as parameter
    const x = try tensor.placeholder(&graph, &.{}, .f32);
    try graph.markParameter(x.node_id);
    
    // Compute y = (x + 1)^2
    const one = try tensor.constant(&graph, 1.0, .f32);
    const x_plus_one = try x.add(one);
    const y = try x_plus_one.mul(x_plus_one);
    
    // Apply autograd
    var ctx = PassContext{
        .graph = &graph,
        .allocator = allocator,
    };
    try training.autograd.applyAutograd(&ctx, &.{x.node_id}, y.node_id);
    
    // Get gradient
    const grad_map = graph.getGradientMap();
    try testing.expect(grad_map != null);
    const grad_x_id = grad_map.?.get(x.node_id);
    try testing.expect(grad_x_id != null);
    
    // Create executor
    var data_store = try storage.DataStorage.init(allocator, .cpu, 1024 * 1024); // 1MB buffer
    defer data_store.deinit();
    
    const cpu_backend = try backends.getCpuBackend(allocator);
    var exec_engine = try execution.ExecutionEngine.init(allocator, cpu_backend);
    defer exec_engine.deinit();
    
    // Compile and execute
    try compiler.compileGraph(&graph, &exec_engine);
    
    // Test at x = 2.0, expected gradient = 2*(2+1) = 6.0
    const x_val = [_]f32{2.0};
    try data_store.setBuffer(x.node_id, std.mem.sliceAsBytes(&x_val));
    try exec_engine.executeGraph(&graph, &data_store);
    
    const grad_buffer = data_store.getBuffer(grad_x_id.?) orelse return error.GradientNotComputed;
    const grad_val = std.mem.bytesAsSlice(f32, grad_buffer)[0];
    
    try testing.expectApproxEqAbs(@as(f32, 6.0), grad_val, 0.001);
    
    std.log.info("✓ Gradient of (x+1)^2 at x=2: {d:.4f} (expected: 6.0)", .{grad_val});
}

test "gradient with multiple parameters" {
    // Test multiple parameters: f(a,b) = a*b + a^2, df/da = b + 2a, df/db = a
    var arena = std.heap.ArenaAllocator.init(testing.allocator);
    defer arena.deinit();
    const allocator = arena.allocator();
    
    var graph = try Graph.init(allocator);
    
    // Create parameters
    const a = try tensor.placeholder(&graph, &.{}, .f32);
    const b = try tensor.placeholder(&graph, &.{}, .f32);
    try graph.markParameter(a.node_id);
    try graph.markParameter(b.node_id);
    
    // Compute y = a*b + a^2
    const ab = try a.mul(b);
    const a_squared = try a.mul(a);
    const y = try ab.add(a_squared);
    
    // Apply autograd
    var ctx = PassContext{
        .graph = &graph,
        .allocator = allocator,
    };
    try training.autograd.applyAutograd(&ctx, &.{a.node_id, b.node_id}, y.node_id);
    
    // Get gradients
    const grad_map = graph.getGradientMap();
    try testing.expect(grad_map != null);
    const grad_a_id = grad_map.?.get(a.node_id);
    const grad_b_id = grad_map.?.get(b.node_id);
    try testing.expect(grad_a_id != null);
    try testing.expect(grad_b_id != null);
    
    // Create executor
    var data_store = try storage.DataStorage.init(allocator, .cpu, 1024 * 1024); // 1MB buffer
    defer data_store.deinit();
    
    const cpu_backend = try backends.getCpuBackend(allocator);
    var exec_engine = try execution.ExecutionEngine.init(allocator, cpu_backend);
    defer exec_engine.deinit();
    
    // Compile and execute
    try compiler.compileGraph(&graph, &exec_engine);
    
    // Test at a = 3.0, b = 4.0
    // Expected: df/da = b + 2a = 4 + 6 = 10
    // Expected: df/db = a = 3
    const a_val = [_]f32{3.0};
    const b_val = [_]f32{4.0};
    try data_store.setBuffer(a.node_id, std.mem.sliceAsBytes(&a_val));
    try data_store.setBuffer(b.node_id, std.mem.sliceAsBytes(&b_val));
    try exec_engine.executeGraph(&graph, &data_store);
    
    const grad_a_buffer = data_store.getBuffer(grad_a_id.?) orelse return error.GradientNotComputed;
    const grad_b_buffer = data_store.getBuffer(grad_b_id.?) orelse return error.GradientNotComputed;
    const grad_a_val = std.mem.bytesAsSlice(f32, grad_a_buffer)[0];
    const grad_b_val = std.mem.bytesAsSlice(f32, grad_b_buffer)[0];
    
    try testing.expectApproxEqAbs(@as(f32, 10.0), grad_a_val, 0.001);
    try testing.expectApproxEqAbs(@as(f32, 3.0), grad_b_val, 0.001);
    
    std.log.info("✓ Gradient of a*b + a^2:", .{});
    std.log.info("  df/da at (3,4): {d:.4f} (expected: 10.0)", .{grad_a_val});
    std.log.info("  df/db at (3,4): {d:.4f} (expected: 3.0)", .{grad_b_val});
}

test "MSE loss gradient" {
    // Test MSE loss gradient
    var arena = std.heap.ArenaAllocator.init(testing.allocator);
    defer arena.deinit();
    const allocator = arena.allocator();
    
    var graph = try Graph.init(allocator);
    
    // Create prediction and target
    const pred = try tensor.placeholder(&graph, &.{2}, .f32);
    const target = try tensor.placeholder(&graph, &.{2}, .f32);
    try graph.markParameter(pred.node_id);
    
    // Compute MSE loss
    const loss = try training.loss.mseLoss(pred, target);
    
    // Apply autograd
    var ctx = PassContext{
        .graph = &graph,
        .allocator = allocator,
    };
    try training.autograd.applyAutograd(&ctx, &.{pred.node_id}, loss.node_id);
    
    // Get gradient
    const grad_map = graph.getGradientMap();
    try testing.expect(grad_map != null);
    const grad_pred_id = grad_map.?.get(pred.node_id);
    try testing.expect(grad_pred_id != null);
    
    std.log.info("✓ MSE loss gradient computation graph created", .{});
    
    // Note: Can't execute reduction gradients yet - not implemented
    // When implemented, gradient should be: 2*(pred - target)/n
}

test "autodiff as compiler pass integration" {
    // Test autograd as compiler pass
    var arena = std.heap.ArenaAllocator.init(testing.allocator);
    defer arena.deinit();
    const allocator = arena.allocator();
    
    var graph = try Graph.init(allocator);
    
    // Create simple computation
    const x = try tensor.placeholder(&graph, &.{}, .f32);
    try graph.markParameter(x.node_id);
    const y = try x.mul(x);
    
    // Mark output
    try graph.markOutput(y.node_id);
    
    // Apply autograd as a compiler pass
    var ctx = PassContext{
        .graph = &graph,
        .allocator = allocator,
    };
    
    // Simulate compiler passes
    std.log.info("Graph before autograd: {} nodes", .{graph.nodes.items.len});
    
    try training.autograd.applyAutograd(&ctx, &.{x.node_id}, y.node_id);
    
    std.log.info("Graph after autograd: {} nodes", .{graph.nodes.items.len});
    
    // Verify gradient nodes were added
    const grad_map = graph.getGradientMap();
    try testing.expect(grad_map != null);
    try testing.expect(grad_map.?.count() == 1);
    
    std.log.info("✓ Autograd pass successfully added {} gradient mappings", .{grad_map.?.count()});
}