/// Integration tests for the new autograd system
/// 
/// These tests demonstrate the correct API where autograd runs as part of
/// the compilation pipeline, not before it.

const std = @import("std");
const testing = std.testing;

// Import core components
const Graph = @import("graph").Graph;
const tensor = @import("tensor");
const backends = @import("backends");
const compileCpu = backends.cpu.compile;
const execution = @import("execution");
const Executor = execution.Executor;

test "autograd integration - simple addition" {
    var graph = try Graph.init(testing.allocator);
    defer graph.deinit();
    
    // Create tensors with gradient requirements
    const x = try tensor.placeholderWithOptions(&graph, &.{2}, .f32, .{ .requires_grad = true });
    const y = try tensor.placeholderWithOptions(&graph, &.{2}, .f32, .{ .requires_grad = true });
    
    // Build computation
    const z = try x.add(y);
    
    // Mark output and set up gradient computation
    try graph.markOutput(z.node_id);
    try graph.setLossOutput(z.node_id);
    
    // Compile - autograd runs automatically as part of the pipeline
    var compiled = try compileCpu(&graph, testing.allocator);
    defer compiled.deinit(testing.allocator);
    
    // Create executor
    var executor = try Executor.init(testing.allocator, &compiled, null);
    defer executor.deinit();
    
    // Set inputs
    const x_data = [_]f32{ 1.0, 2.0 };
    const y_data = [_]f32{ 3.0, 4.0 };
    try executor.setInput(x.node_id, std.mem.sliceAsBytes(&x_data), &.{2}, .f32);
    try executor.setInput(y.node_id, std.mem.sliceAsBytes(&y_data), &.{2}, .f32);
    
    // Run forward and backward pass
    try executor.run();
    
    // Get gradients
    const grad_x = try executor.getGradient(x.node_id);
    const grad_y = try executor.getGradient(y.node_id);
    
    // Verify gradient values
    const grad_x_data = std.mem.bytesAsSlice(f32, grad_x.data);
    const grad_y_data = std.mem.bytesAsSlice(f32, grad_y.data);
    
    // For z = x + y, dz/dx = 1, dz/dy = 1
    try testing.expectApproxEqAbs(@as(f32, 1.0), grad_x_data[0], 0.001);
    try testing.expectApproxEqAbs(@as(f32, 1.0), grad_x_data[1], 0.001);
    try testing.expectApproxEqAbs(@as(f32, 1.0), grad_y_data[0], 0.001);
    try testing.expectApproxEqAbs(@as(f32, 1.0), grad_y_data[1], 0.001);
}

test "autograd integration - multiplication" {
    var graph = try Graph.init(testing.allocator);
    defer graph.deinit();
    
    // Create tensors
    const x = try tensor.placeholderWithOptions(&graph, &.{}, .f32, .{ .requires_grad = true });
    const y = try tensor.placeholderWithOptions(&graph, &.{}, .f32, .{ .requires_grad = true });
    
    // z = x * y
    const z = try x.mul(y);
    
    try graph.markOutput(z.node_id);
    try graph.setLossOutput(z.node_id);
    
    // Compile with autograd
    var compiled = try compileCpu(&graph, testing.allocator);
    defer compiled.deinit(testing.allocator);
    
    var executor = try Executor.init(testing.allocator, &compiled, null);
    defer executor.deinit();
    
    // x = 3, y = 4
    const x_data = [_]f32{3.0};
    const y_data = [_]f32{4.0};
    try executor.setInput(x.node_id, std.mem.sliceAsBytes(&x_data), &.{}, .f32);
    try executor.setInput(y.node_id, std.mem.sliceAsBytes(&y_data), &.{}, .f32);
    
    try executor.run();
    
    // Get gradients
    const grad_x = try executor.getGradient(x.node_id);
    const grad_y = try executor.getGradient(y.node_id);
    
    const grad_x_val = std.mem.bytesAsSlice(f32, grad_x.data)[0];
    const grad_y_val = std.mem.bytesAsSlice(f32, grad_y.data)[0];
    
    // For z = x * y:
    // dz/dx = y = 4
    // dz/dy = x = 3
    try testing.expectApproxEqAbs(@as(f32, 4.0), grad_x_val, 0.001);
    try testing.expectApproxEqAbs(@as(f32, 3.0), grad_y_val, 0.001);
}

test "autograd integration - chain rule" {
    var graph = try Graph.init(testing.allocator);
    defer graph.deinit();
    
    // Create input
    const x = try tensor.placeholderWithOptions(&graph, &.{}, .f32, .{ .requires_grad = true });
    
    // z = x^3 = x * x * x
    const x2 = try x.mul(x);
    const x3 = try x2.mul(x);
    
    try graph.markOutput(x3.node_id);
    try graph.setLossOutput(x3.node_id);
    
    var compiled = try compileCpu(&graph, testing.allocator);
    defer compiled.deinit(testing.allocator);
    
    var executor = try Executor.init(testing.allocator, &compiled, null);
    defer executor.deinit();
    
    // x = 2
    const x_data = [_]f32{2.0};
    try executor.setInput(x.node_id, std.mem.sliceAsBytes(&x_data), &.{}, .f32);
    
    try executor.run();
    
    const grad_x = try executor.getGradient(x.node_id);
    const grad_x_val = std.mem.bytesAsSlice(f32, grad_x.data)[0];
    
    // For z = x^3:
    // dz/dx = 3x^2 = 3 * 4 = 12
    try testing.expectApproxEqAbs(@as(f32, 12.0), grad_x_val, 0.001);
}

test "autograd integration - parameters default to requires_grad" {
    var graph = try Graph.init(testing.allocator);
    defer graph.deinit();
    
    // Parameters default to requires_grad = true
    const w = try tensor.parameter(&graph, &.{2, 3}, 0, .f32);
    const x = try tensor.placeholder(&graph, &.{3}, .f32);  // Input doesn't need gradients
    
    // y = w @ x (matrix-vector multiply)
    // For now use add as a placeholder since matmul VJP isn't implemented
    const y = try w.sumAll();  // Simplified: just sum weights
    
    try graph.markOutput(y.node_id);
    try graph.setLossOutput(y.node_id);
    
    var compiled = try compileCpu(&graph, testing.allocator);
    defer compiled.deinit(testing.allocator);
    
    var executor = try Executor.init(testing.allocator, &compiled, null);
    defer executor.deinit();
    
    // Set parameter values
    const w_data = [_]f32{ 1.0, 2.0, 3.0, 4.0, 5.0, 6.0 };
    const x_data = [_]f32{ 0.1, 0.2, 0.3 };
    try executor.setParameter(0, std.mem.sliceAsBytes(&w_data), &.{2, 3}, .f32);
    try executor.setInput(x.node_id, std.mem.sliceAsBytes(&x_data), &.{3}, .f32);
    
    try executor.run();
    
    // Parameters should have gradients
    const grad_w = try executor.getGradient(w.node_id);
    const grad_w_data = std.mem.bytesAsSlice(f32, grad_w.data);
    
    // For y = sum(w), dy/dw = 1 for all elements
    for (grad_w_data) |grad| {
        try testing.expectApproxEqAbs(@as(f32, 1.0), grad, 0.001);
    }
}

test "autograd integration - inference mode (no gradients)" {
    var graph = try Graph.init(testing.allocator);
    defer graph.deinit();
    
    // Create tensors without gradient requirements (inference mode)
    const x = try tensor.placeholder(&graph, &.{2}, .f32);
    const y = try tensor.placeholder(&graph, &.{2}, .f32);
    const z = try x.add(y);
    
    try graph.markOutput(z.node_id);
    // Don't call setLossOutput - no gradients needed
    
    var compiled = try compileCpu(&graph, testing.allocator);
    defer compiled.deinit(testing.allocator);
    
    // Verify no gradient map was created
    try testing.expect(compiled.gradient_map == null);
    
    var executor = try Executor.init(testing.allocator, &compiled, null);
    defer executor.deinit();
    
    const x_data = [_]f32{ 1.0, 2.0 };
    const y_data = [_]f32{ 3.0, 4.0 };
    try executor.setInput(x.node_id, std.mem.sliceAsBytes(&x_data), &.{2}, .f32);
    try executor.setInput(y.node_id, std.mem.sliceAsBytes(&y_data), &.{2}, .f32);
    
    try executor.run();
    
    // Forward pass works normally
    const z_view = try executor.getOutput(z.node_id);
    const z_data = std.mem.bytesAsSlice(f32, z_view.data);
    try testing.expectApproxEqAbs(@as(f32, 4.0), z_data[0], 0.001);
    try testing.expectApproxEqAbs(@as(f32, 6.0), z_data[1], 0.001);
    
    // Trying to get gradients should fail
    try testing.expectError(error.GradientsNotEnabled, executor.getGradient(x.node_id));
}

test "autograd integration - mixed gradient requirements" {
    var graph = try Graph.init(testing.allocator);
    defer graph.deinit();
    
    // Mix of tensors with and without gradients
    const x = try tensor.placeholderWithOptions(&graph, &.{}, .f32, .{ .requires_grad = true });
    const c = try tensor.constant(&graph, 2.0, .f32);  // Constants don't need gradients
    
    // z = x * c
    const z = try x.mul(c);
    
    try graph.markOutput(z.node_id);
    try graph.setLossOutput(z.node_id);
    
    var compiled = try compileCpu(&graph, testing.allocator);
    defer compiled.deinit(testing.allocator);
    
    var executor = try Executor.init(testing.allocator, &compiled, null);
    defer executor.deinit();
    
    const x_data = [_]f32{3.0};
    try executor.setInput(x.node_id, std.mem.sliceAsBytes(&x_data), &.{}, .f32);
    
    try executor.run();
    
    // Only x should have gradient
    const grad_x = try executor.getGradient(x.node_id);
    const grad_x_val = std.mem.bytesAsSlice(f32, grad_x.data)[0];
    
    // dz/dx = c = 2
    try testing.expectApproxEqAbs(@as(f32, 2.0), grad_x_val, 0.001);
    
    // Constant should not have gradient
    try testing.expectError(error.NoGradientForNode, executor.getGradient(c.node_id));
}

test "autograd integration - gradient accumulation" {
    var graph = try Graph.init(testing.allocator);
    defer graph.deinit();
    
    // Test that gradients accumulate when a node is used multiple times
    const x = try tensor.placeholderWithOptions(&graph, &.{}, .f32, .{ .requires_grad = true });
    
    // z = x + x + x = 3x
    const x2 = try x.add(x);
    const x3 = try x2.add(x);
    
    try graph.markOutput(x3.node_id);
    try graph.setLossOutput(x3.node_id);
    
    var compiled = try compileCpu(&graph, testing.allocator);
    defer compiled.deinit(testing.allocator);
    
    var executor = try Executor.init(testing.allocator, &compiled, null);
    defer executor.deinit();
    
    const x_data = [_]f32{5.0};
    try executor.setInput(x.node_id, std.mem.sliceAsBytes(&x_data), &.{}, .f32);
    
    try executor.run();
    
    const grad_x = try executor.getGradient(x.node_id);
    const grad_x_val = std.mem.bytesAsSlice(f32, grad_x.data)[0];
    
    // For z = 3x:
    // dz/dx = 3
    try testing.expectApproxEqAbs(@as(f32, 3.0), grad_x_val, 0.001);
}