/// Debug test to see actual gradient values with verbose output

const std = @import("std");
const testing = std.testing;

const Graph = @import("graph").Graph;
const tensor = @import("tensor");
const TensorHandle = tensor.TensorHandle;
const training = @import("training");
const compiler = @import("compiler");
const PassContext = compiler.PassContext;
const execution = @import("execution");
const Executor = execution.Executor;
const CompiledGraph = execution.CompiledGraph;

test "debug gradient output - addition" {
    var arena = std.heap.ArenaAllocator.init(testing.allocator);
    defer arena.deinit();
    const allocator = arena.allocator();
    
    var graph = try Graph.init(allocator);
    
    // x + y where x=5, y=7
    const x = try tensor.placeholder(&graph, &.{}, .f32);
    const y = try tensor.placeholder(&graph, &.{}, .f32);
    try graph.markParameter(x.node_id);
    try graph.markParameter(y.node_id);
    
    const z = try x.add(y);
    try graph.markOutput(z.node_id);
    
    // Apply autograd
    var ctx = PassContext{
        .graph = &graph,
        .allocator = allocator,
    };
    try training.autograd.applyAutograd(&ctx, &.{ x.node_id, y.node_id }, z.node_id);
    
    // Get gradient nodes
    const grad_map = graph.getGradientMap().?;
    const grad_x_id = grad_map.get(x.node_id).?;
    const grad_y_id = grad_map.get(y.node_id).?;
    try graph.markOutput(grad_x_id);
    try graph.markOutput(grad_y_id);
    
    // Compile and execute
    const compiled = try compiler.compile.compileCpu(&graph, allocator);
    var compiled_heap = try allocator.create(CompiledGraph);
    compiled_heap.* = compiled;
    defer {
        compiled_heap.deinit(allocator);
        allocator.destroy(compiled_heap);
    }
    
    var executor = try Executor.init(allocator, compiled_heap, null);
    defer executor.deinit();
    
    // Set inputs
    const x_val = [_]f32{5.0};
    const y_val = [_]f32{7.0};
    try executor.setInput(x.node_id, std.mem.sliceAsBytes(&x_val), &.{}, .f32);
    try executor.setInput(y.node_id, std.mem.sliceAsBytes(&y_val), &.{}, .f32);
    
    // Execute
    try executor.run();
    
    // Get outputs
    const z_view = try executor.getOutput(z.node_id);
    const z_result = std.mem.bytesAsSlice(f32, z_view.data)[0];
    
    const grad_x_view = try executor.getOutput(grad_x_id);
    const grad_y_view = try executor.getOutput(grad_y_id);
    const grad_x_val = std.mem.bytesAsSlice(f32, grad_x_view.data)[0];
    const grad_y_val = std.mem.bytesAsSlice(f32, grad_y_view.data)[0];
    
    std.debug.print("\n=== ADDITION TEST ===\n", .{});
    std.debug.print("Expression: z = x + y\n", .{});
    std.debug.print("Inputs: x = {d:.2}, y = {d:.2}\n", .{x_val[0], y_val[0]});
    std.debug.print("Output: z = {d:.2}\n", .{z_result});
    std.debug.print("Gradients:\n", .{});
    std.debug.print("  dz/dx = {d:.6} (expected: 1.0)\n", .{grad_x_val});
    std.debug.print("  dz/dy = {d:.6} (expected: 1.0)\n", .{grad_y_val});
    std.debug.print("Status: {s}\n", .{if (grad_x_val == 1.0 and grad_y_val == 1.0) "✅ CORRECT" else "❌ WRONG"});
}

test "debug gradient output - multiplication" {
    var arena = std.heap.ArenaAllocator.init(testing.allocator);
    defer arena.deinit();
    const allocator = arena.allocator();
    
    var graph = try Graph.init(allocator);
    
    // x * y where x=3, y=4
    const x = try tensor.placeholder(&graph, &.{}, .f32);
    const y = try tensor.placeholder(&graph, &.{}, .f32);
    try graph.markParameter(x.node_id);
    try graph.markParameter(y.node_id);
    
    const z = try x.mul(y);
    try graph.markOutput(z.node_id);
    
    // Apply autograd
    var ctx = PassContext{
        .graph = &graph,
        .allocator = allocator,
    };
    try training.autograd.applyAutograd(&ctx, &.{ x.node_id, y.node_id }, z.node_id);
    
    // Get gradient nodes
    const grad_map = graph.getGradientMap().?;
    const grad_x_id = grad_map.get(x.node_id).?;
    const grad_y_id = grad_map.get(y.node_id).?;
    try graph.markOutput(grad_x_id);
    try graph.markOutput(grad_y_id);
    
    // Compile and execute
    const compiled = try compiler.compile.compileCpu(&graph, allocator);
    var compiled_heap = try allocator.create(CompiledGraph);
    compiled_heap.* = compiled;
    defer {
        compiled_heap.deinit(allocator);
        allocator.destroy(compiled_heap);
    }
    
    var executor = try Executor.init(allocator, compiled_heap, null);
    defer executor.deinit();
    
    // Set inputs
    const x_val = [_]f32{3.0};
    const y_val = [_]f32{4.0};
    try executor.setInput(x.node_id, std.mem.sliceAsBytes(&x_val), &.{}, .f32);
    try executor.setInput(y.node_id, std.mem.sliceAsBytes(&y_val), &.{}, .f32);
    
    // Execute
    try executor.run();
    
    // Get outputs
    const z_view = try executor.getOutput(z.node_id);
    const z_result = std.mem.bytesAsSlice(f32, z_view.data)[0];
    
    const grad_x_view = try executor.getOutput(grad_x_id);
    const grad_y_view = try executor.getOutput(grad_y_id);
    const grad_x_val = std.mem.bytesAsSlice(f32, grad_x_view.data)[0];
    const grad_y_val = std.mem.bytesAsSlice(f32, grad_y_view.data)[0];
    
    std.debug.print("\n=== MULTIPLICATION TEST ===\n", .{});
    std.debug.print("Expression: z = x * y\n", .{});
    std.debug.print("Inputs: x = {d:.2}, y = {d:.2}\n", .{x_val[0], y_val[0]});
    std.debug.print("Output: z = {d:.2}\n", .{z_result});
    std.debug.print("Gradients:\n", .{});
    std.debug.print("  dz/dx = {d:.6} (expected: y = 4.0)\n", .{grad_x_val});
    std.debug.print("  dz/dy = {d:.6} (expected: x = 3.0)\n", .{grad_y_val});
    std.debug.print("Status: {s}\n", .{if (@abs(grad_x_val - 4.0) < 0.001 and @abs(grad_y_val - 3.0) < 0.001) "✅ CORRECT" else "❌ WRONG"});
}

test "debug gradient output - chain rule" {
    var arena = std.heap.ArenaAllocator.init(testing.allocator);
    defer arena.deinit();
    const allocator = arena.allocator();
    
    var graph = try Graph.init(allocator);
    
    // z = x^3 = x * x * x where x=2
    const x = try tensor.placeholder(&graph, &.{}, .f32);
    try graph.markParameter(x.node_id);
    
    const x_squared = try x.mul(x);
    const x_cubed = try x_squared.mul(x);
    try graph.markOutput(x_cubed.node_id);
    
    // Apply autograd
    var ctx = PassContext{
        .graph = &graph,
        .allocator = allocator,
    };
    try training.autograd.applyAutograd(&ctx, &.{x.node_id}, x_cubed.node_id);
    
    // Get gradient node
    const grad_map = graph.getGradientMap().?;
    const grad_x_id = grad_map.get(x.node_id).?;
    try graph.markOutput(grad_x_id);
    
    // Compile and execute
    const compiled = try compiler.compile.compileCpu(&graph, allocator);
    var compiled_heap = try allocator.create(CompiledGraph);
    compiled_heap.* = compiled;
    defer {
        compiled_heap.deinit(allocator);
        allocator.destroy(compiled_heap);
    }
    
    var executor = try Executor.init(allocator, compiled_heap, null);
    defer executor.deinit();
    
    // Set input x = 2.0
    const x_val = [_]f32{2.0};
    try executor.setInput(x.node_id, std.mem.sliceAsBytes(&x_val), &.{}, .f32);
    
    // Execute
    try executor.run();
    
    // Get outputs
    const z_view = try executor.getOutput(x_cubed.node_id);
    const z_result = std.mem.bytesAsSlice(f32, z_view.data)[0];
    
    const grad_x_view = try executor.getOutput(grad_x_id);
    const grad_x_val = std.mem.bytesAsSlice(f32, grad_x_view.data)[0];
    
    std.debug.print("\n=== CHAIN RULE TEST ===\n", .{});
    std.debug.print("Expression: z = x^3\n", .{});
    std.debug.print("Input: x = {d:.2}\n", .{x_val[0]});
    std.debug.print("Output: z = {d:.2} (expected: 8.0)\n", .{z_result});
    std.debug.print("Gradient:\n", .{});
    std.debug.print("  dz/dx = {d:.6} (expected: 3x^2 = 3*4 = 12.0)\n", .{grad_x_val});
    std.debug.print("Status: {s}\n", .{if (@abs(grad_x_val - 12.0) < 0.001) "✅ CORRECT" else "❌ WRONG"});
}

test "debug gradient output - square root" {
    var arena = std.heap.ArenaAllocator.init(testing.allocator);
    defer arena.deinit();
    const allocator = arena.allocator();
    
    var graph = try Graph.init(allocator);
    
    // z = sqrt(x) where x=4
    const x = try tensor.placeholder(&graph, &.{}, .f32);
    try graph.markParameter(x.node_id);
    
    const z = try x.sqrt();
    try graph.markOutput(z.node_id);
    
    // Apply autograd
    var ctx = PassContext{
        .graph = &graph,
        .allocator = allocator,
    };
    try training.autograd.applyAutograd(&ctx, &.{x.node_id}, z.node_id);
    
    // Get gradient node
    const grad_map = graph.getGradientMap().?;
    const grad_x_id = grad_map.get(x.node_id).?;
    try graph.markOutput(grad_x_id);
    
    // Compile and execute
    const compiled = try compiler.compile.compileCpu(&graph, allocator);
    var compiled_heap = try allocator.create(CompiledGraph);
    compiled_heap.* = compiled;
    defer {
        compiled_heap.deinit(allocator);
        allocator.destroy(compiled_heap);
    }
    
    var executor = try Executor.init(allocator, compiled_heap, null);
    defer executor.deinit();
    
    // Set input x = 4.0
    const x_val = [_]f32{4.0};
    try executor.setInput(x.node_id, std.mem.sliceAsBytes(&x_val), &.{}, .f32);
    
    // Execute
    try executor.run();
    
    // Get outputs
    const z_view = try executor.getOutput(z.node_id);
    const z_result = std.mem.bytesAsSlice(f32, z_view.data)[0];
    
    const grad_x_view = try executor.getOutput(grad_x_id);
    const grad_x_val = std.mem.bytesAsSlice(f32, grad_x_view.data)[0];
    
    std.debug.print("\n=== SQUARE ROOT TEST ===\n", .{});
    std.debug.print("Expression: z = sqrt(x)\n", .{});
    std.debug.print("Input: x = {d:.2}\n", .{x_val[0]});
    std.debug.print("Output: z = {d:.2} (expected: sqrt(4) = 2.0)\n", .{z_result});
    std.debug.print("Gradient:\n", .{});
    std.debug.print("  dz/dx = {d:.6} (expected: 1/(2*sqrt(x)) = 1/4 = 0.25)\n", .{grad_x_val});
    std.debug.print("Status: {s}\n", .{if (@abs(grad_x_val - 0.25) < 0.001) "✅ CORRECT" else "❌ WRONG"});
}

test "debug gradient output - reciprocal" {
    var arena = std.heap.ArenaAllocator.init(testing.allocator);
    defer arena.deinit();
    const allocator = arena.allocator();
    
    var graph = try Graph.init(allocator);
    
    // z = 1/x where x=2
    const x = try tensor.placeholder(&graph, &.{}, .f32);
    try graph.markParameter(x.node_id);
    
    const z = try x.recip();
    try graph.markOutput(z.node_id);
    
    // Apply autograd
    var ctx = PassContext{
        .graph = &graph,
        .allocator = allocator,
    };
    try training.autograd.applyAutograd(&ctx, &.{x.node_id}, z.node_id);
    
    // Get gradient node
    const grad_map = graph.getGradientMap().?;
    const grad_x_id = grad_map.get(x.node_id).?;
    try graph.markOutput(grad_x_id);
    
    // Compile and execute
    const compiled = try compiler.compile.compileCpu(&graph, allocator);
    var compiled_heap = try allocator.create(CompiledGraph);
    compiled_heap.* = compiled;
    defer {
        compiled_heap.deinit(allocator);
        allocator.destroy(compiled_heap);
    }
    
    var executor = try Executor.init(allocator, compiled_heap, null);
    defer executor.deinit();
    
    // Set input x = 2.0
    const x_val = [_]f32{2.0};
    try executor.setInput(x.node_id, std.mem.sliceAsBytes(&x_val), &.{}, .f32);
    
    // Execute
    try executor.run();
    
    // Get outputs
    const z_view = try executor.getOutput(z.node_id);
    const z_result = std.mem.bytesAsSlice(f32, z_view.data)[0];
    
    const grad_x_view = try executor.getOutput(grad_x_id);
    const grad_x_val = std.mem.bytesAsSlice(f32, grad_x_view.data)[0];
    
    std.debug.print("\n=== RECIPROCAL TEST ===\n", .{});
    std.debug.print("Expression: z = 1/x\n", .{});
    std.debug.print("Input: x = {d:.2}\n", .{x_val[0]});
    std.debug.print("Output: z = {d:.2} (expected: 1/2 = 0.5)\n", .{z_result});
    std.debug.print("Gradient:\n", .{});
    std.debug.print("  dz/dx = {d:.6} (expected: -1/x^2 = -1/4 = -0.25)\n", .{grad_x_val});
    std.debug.print("Status: {s}\n", .{if (@abs(grad_x_val + 0.25) < 0.001) "✅ CORRECT" else "❌ WRONG"});
}

test "debug gradient output - broadcast reduction" {
    var arena = std.heap.ArenaAllocator.init(testing.allocator);
    defer arena.deinit();
    const allocator = arena.allocator();
    
    var graph = try Graph.init(allocator);
    
    // Test broadcasting: x[2,3] + b[3] -> sum
    const x = try tensor.placeholder(&graph, &.{2, 3}, .f32);
    const b = try tensor.placeholder(&graph, &.{3}, .f32);
    try graph.markParameter(x.node_id);
    try graph.markParameter(b.node_id);
    
    // Add with broadcasting: x[2,3] + b[3] -> [2,3]
    const y = try x.add(b);  // This should broadcast b to [2,3]
    const z = try y.sumAll();  // Sum all elements
    try graph.markOutput(z.node_id);
    
    // Apply autograd
    var ctx = PassContext{
        .graph = &graph,
        .allocator = allocator,
    };
    try training.autograd.applyAutograd(&ctx, &.{x.node_id, b.node_id}, z.node_id);
    
    // Get gradient nodes
    const grad_map = graph.getGradientMap().?;
    const grad_x_id = grad_map.get(x.node_id).?;
    const grad_b_id = grad_map.get(b.node_id).?;
    try graph.markOutput(grad_x_id);
    try graph.markOutput(grad_b_id);
    
    // Compile and execute
    const compiled = try compiler.compile.compileCpu(&graph, allocator);
    var compiled_heap = try allocator.create(CompiledGraph);
    compiled_heap.* = compiled;
    defer {
        compiled_heap.deinit(allocator);
        allocator.destroy(compiled_heap);
    }
    
    var executor = try Executor.init(allocator, compiled_heap, null);
    defer executor.deinit();
    
    // Set inputs
    const x_val = [_]f32{1.0, 2.0, 3.0, 4.0, 5.0, 6.0};  // [2,3]
    const b_val = [_]f32{10.0, 20.0, 30.0};  // [3]
    try executor.setInput(x.node_id, std.mem.sliceAsBytes(&x_val), &.{2, 3}, .f32);
    try executor.setInput(b.node_id, std.mem.sliceAsBytes(&b_val), &.{3}, .f32);
    
    // Execute
    try executor.run();
    
    // Get outputs
    const z_view = try executor.getOutput(z.node_id);
    const z_result = std.mem.bytesAsSlice(f32, z_view.data)[0];
    
    const grad_x_view = try executor.getOutput(grad_x_id);
    const grad_b_view = try executor.getOutput(grad_b_id);
    const grad_x_vals = std.mem.bytesAsSlice(f32, grad_x_view.data);
    const grad_b_vals = std.mem.bytesAsSlice(f32, grad_b_view.data);
    
    std.debug.print("\n=== BROADCAST REDUCTION TEST ===\n", .{});
    std.debug.print("Expression: z = sum(x + b)  where x[2,3], b[3]\n", .{});
    std.debug.print("Input x[2,3]: [{d:.1}, {d:.1}, {d:.1}, {d:.1}, {d:.1}, {d:.1}]\n", .{x_val[0], x_val[1], x_val[2], x_val[3], x_val[4], x_val[5]});
    std.debug.print("Input b[3]: [{d:.1}, {d:.1}, {d:.1}]\n", .{b_val[0], b_val[1], b_val[2]});
    std.debug.print("Output: z = {d:.1}\n", .{z_result});
    std.debug.print("Expected: (1+10)+(2+20)+(3+30)+(4+10)+(5+20)+(6+30) = 11+22+33+14+25+36 = 141\n", .{});
    std.debug.print("Gradients for x (should all be 1.0):\n", .{});
    for (grad_x_vals, 0..) |val, i| {
        std.debug.print("  dx[{}] = {d:.6}\n", .{i, val});
    }
    std.debug.print("Gradients for b (should be [2.0, 2.0, 2.0] - sum over broadcast dim):\n", .{});
    for (grad_b_vals, 0..) |val, i| {
        std.debug.print("  db[{}] = {d:.6}\n", .{i, val});
    }
    
    var x_correct = true;
    var b_correct = true;
    for (grad_x_vals) |val| {
        if (@abs(val - 1.0) > 0.001) x_correct = false;
    }
    for (grad_b_vals) |val| {
        if (@abs(val - 2.0) > 0.001) b_correct = false;
    }
    
    std.debug.print("Status: x gradients {s}, b gradients {s}\n", .{
        if (x_correct) "✅ CORRECT" else "❌ WRONG",
        if (b_correct) "✅ CORRECT" else "❌ WRONG"
    });
}

test "debug gradient output - complex polynomial" {
    var arena = std.heap.ArenaAllocator.init(testing.allocator);
    defer arena.deinit();
    const allocator = arena.allocator();
    
    var graph = try Graph.init(allocator);
    
    // Complex polynomial: z = 3x^2 + 2xy + y^2 where x=2, y=3
    const x = try tensor.placeholder(&graph, &.{}, .f32);
    const y = try tensor.placeholder(&graph, &.{}, .f32);
    try graph.markParameter(x.node_id);
    try graph.markParameter(y.node_id);
    
    const three = try tensor.constant(&graph, 3.0, .f32);
    const two = try tensor.constant(&graph, 2.0, .f32);
    
    // 3x^2
    const x_squared = try x.mul(x);
    const three_x_squared = try three.mul(x_squared);
    
    // 2xy
    const xy = try x.mul(y);
    const two_xy = try two.mul(xy);
    
    // y^2
    const y_squared = try y.mul(y);
    
    // 3x^2 + 2xy + y^2
    const term1 = try three_x_squared.add(two_xy);
    const z = try term1.add(y_squared);
    
    try graph.markOutput(z.node_id);
    
    // Apply autograd
    var ctx = PassContext{
        .graph = &graph,
        .allocator = allocator,
    };
    try training.autograd.applyAutograd(&ctx, &.{x.node_id, y.node_id}, z.node_id);
    
    // Get gradient nodes
    const grad_map = graph.getGradientMap().?;
    const grad_x_id = grad_map.get(x.node_id).?;
    const grad_y_id = grad_map.get(y.node_id).?;
    try graph.markOutput(grad_x_id);
    try graph.markOutput(grad_y_id);
    
    // Compile and execute
    const compiled = try compiler.compile.compileCpu(&graph, allocator);
    var compiled_heap = try allocator.create(CompiledGraph);
    compiled_heap.* = compiled;
    defer {
        compiled_heap.deinit(allocator);
        allocator.destroy(compiled_heap);
    }
    
    var executor = try Executor.init(allocator, compiled_heap, null);
    defer executor.deinit();
    
    // Set inputs x=2, y=3
    const x_val = [_]f32{2.0};
    const y_val = [_]f32{3.0};
    try executor.setInput(x.node_id, std.mem.sliceAsBytes(&x_val), &.{}, .f32);
    try executor.setInput(y.node_id, std.mem.sliceAsBytes(&y_val), &.{}, .f32);
    
    // Execute
    try executor.run();
    
    // Get outputs
    const z_view = try executor.getOutput(z.node_id);
    const z_result = std.mem.bytesAsSlice(f32, z_view.data)[0];
    
    const grad_x_view = try executor.getOutput(grad_x_id);
    const grad_y_view = try executor.getOutput(grad_y_id);
    const grad_x_val = std.mem.bytesAsSlice(f32, grad_x_view.data)[0];
    const grad_y_val = std.mem.bytesAsSlice(f32, grad_y_view.data)[0];
    
    // Expected calculations:
    // z = 3*4 + 2*2*3 + 9 = 12 + 12 + 9 = 33
    // dz/dx = 6x + 2y = 12 + 6 = 18
    // dz/dy = 2x + 2y = 4 + 6 = 10
    
    std.debug.print("\n=== COMPLEX POLYNOMIAL TEST ===\n", .{});
    std.debug.print("Expression: z = 3x^2 + 2xy + y^2\n", .{});
    std.debug.print("Inputs: x = {d:.2}, y = {d:.2}\n", .{x_val[0], y_val[0]});
    std.debug.print("Output: z = {d:.2} (expected: 3*4 + 2*2*3 + 9 = 33)\n", .{z_result});
    std.debug.print("Gradients:\n", .{});
    std.debug.print("  dz/dx = {d:.6} (expected: 6x + 2y = 12 + 6 = 18.0)\n", .{grad_x_val});
    std.debug.print("  dz/dy = {d:.6} (expected: 2x + 2y = 4 + 6 = 10.0)\n", .{grad_y_val});
    std.debug.print("Status: {s}\n", .{
        if (@abs(grad_x_val - 18.0) < 0.001 and @abs(grad_y_val - 10.0) < 0.001) 
            "✅ CORRECT" 
        else 
            "❌ WRONG"
    });
}

test "debug gradient output - nested operations" {
    var arena = std.heap.ArenaAllocator.init(testing.allocator);
    defer arena.deinit();
    const allocator = arena.allocator();
    
    var graph = try Graph.init(allocator);
    
    // Nested: z = sqrt(x^2 + y^2) where x=3, y=4 (distance formula)
    const x = try tensor.placeholder(&graph, &.{}, .f32);
    const y = try tensor.placeholder(&graph, &.{}, .f32);
    try graph.markParameter(x.node_id);
    try graph.markParameter(y.node_id);
    
    const x_squared = try x.mul(x);
    const y_squared = try y.mul(y);
    const sum_squares = try x_squared.add(y_squared);
    const z = try sum_squares.sqrt();
    
    try graph.markOutput(z.node_id);
    
    // Apply autograd
    var ctx = PassContext{
        .graph = &graph,
        .allocator = allocator,
    };
    try training.autograd.applyAutograd(&ctx, &.{x.node_id, y.node_id}, z.node_id);
    
    // Get gradient nodes
    const grad_map = graph.getGradientMap().?;
    const grad_x_id = grad_map.get(x.node_id).?;
    const grad_y_id = grad_map.get(y.node_id).?;
    try graph.markOutput(grad_x_id);
    try graph.markOutput(grad_y_id);
    
    // Compile and execute
    const compiled = try compiler.compile.compileCpu(&graph, allocator);
    var compiled_heap = try allocator.create(CompiledGraph);
    compiled_heap.* = compiled;
    defer {
        compiled_heap.deinit(allocator);
        allocator.destroy(compiled_heap);
    }
    
    var executor = try Executor.init(allocator, compiled_heap, null);
    defer executor.deinit();
    
    // Set inputs x=3, y=4
    const x_val = [_]f32{3.0};
    const y_val = [_]f32{4.0};
    try executor.setInput(x.node_id, std.mem.sliceAsBytes(&x_val), &.{}, .f32);
    try executor.setInput(y.node_id, std.mem.sliceAsBytes(&y_val), &.{}, .f32);
    
    // Execute
    try executor.run();
    
    // Get outputs
    const z_view = try executor.getOutput(z.node_id);
    const z_result = std.mem.bytesAsSlice(f32, z_view.data)[0];
    
    const grad_x_view = try executor.getOutput(grad_x_id);
    const grad_y_view = try executor.getOutput(grad_y_id);
    const grad_x_val = std.mem.bytesAsSlice(f32, grad_x_view.data)[0];
    const grad_y_val = std.mem.bytesAsSlice(f32, grad_y_view.data)[0];
    
    // Expected calculations:
    // z = sqrt(9 + 16) = sqrt(25) = 5
    // dz/dx = x/sqrt(x^2 + y^2) = 3/5 = 0.6
    // dz/dy = y/sqrt(x^2 + y^2) = 4/5 = 0.8
    
    std.debug.print("\n=== NESTED OPERATIONS TEST ===\n", .{});
    std.debug.print("Expression: z = sqrt(x^2 + y^2)  (distance formula)\n", .{});
    std.debug.print("Inputs: x = {d:.2}, y = {d:.2}\n", .{x_val[0], y_val[0]});
    std.debug.print("Output: z = {d:.2} (expected: sqrt(9+16) = 5.0)\n", .{z_result});
    std.debug.print("Gradients:\n", .{});
    std.debug.print("  dz/dx = {d:.6} (expected: x/z = 3/5 = 0.6)\n", .{grad_x_val});
    std.debug.print("  dz/dy = {d:.6} (expected: y/z = 4/5 = 0.8)\n", .{grad_y_val});
    std.debug.print("Status: {s}\n", .{
        if (@abs(grad_x_val - 0.6) < 0.001 and @abs(grad_y_val - 0.8) < 0.001) 
            "✅ CORRECT" 
        else 
            "❌ WRONG"
    });
}

test "summary" {
    std.debug.print("\n" ++ "=" ** 60 ++ "\n", .{});
    std.debug.print("COMPREHENSIVE GRADIENT VERIFICATION COMPLETE\n", .{});
    std.debug.print("=" ** 60 ++ "\n", .{});
    std.debug.print("\nTested operations:\n", .{});
    std.debug.print("  ✓ Basic arithmetic: +, *, x^3\n", .{});
    std.debug.print("  ✓ Unary operations: sqrt(x), 1/x\n", .{});
    std.debug.print("  ✓ Broadcasting and reduction\n", .{});
    std.debug.print("  ✓ Complex polynomials\n", .{});
    std.debug.print("  ✓ Nested operations with chain rule\n", .{});
    std.debug.print("\nEach test shows ACTUAL computed gradient values\n", .{});
    std.debug.print("compared against hand-calculated expected values.\n", .{});
    std.debug.print("If all tests show ✅ CORRECT, autograd works!\n", .{});
    std.debug.print("=" ** 60 ++ "\n", .{});
}