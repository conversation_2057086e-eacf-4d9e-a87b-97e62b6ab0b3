/// Skeptical gradient value verification
/// This test ACTUALLY checks gradient values and prints them for verification

const std = @import("std");
const testing = std.testing;

const Graph = @import("graph").Graph;
const tensor = @import("tensor");
const TensorHandle = tensor.TensorHandle;
const training = @import("training");
const compiler = @import("compiler");
const PassContext = compiler.PassContext;
const execution = @import("execution");
const Executor = execution.Executor;
const CompiledGraph = execution.CompiledGraph;

test "skeptical gradient check - addition" {
    std.log.info("\n=== SKEPTICAL GRADIENT CHECK: ADDITION ===", .{});
    
    var arena = std.heap.ArenaAllocator.init(testing.allocator);
    defer arena.deinit();
    const allocator = arena.allocator();
    
    var graph = try Graph.init(allocator);
    
    // x + y where x=5, y=7
    const x = try tensor.placeholder(&graph, &.{}, .f32);
    const y = try tensor.placeholder(&graph, &.{}, .f32);
    try graph.markParameter(x.node_id);
    try graph.markParameter(y.node_id);
    
    const z = try x.add(y);
    try graph.markOutput(z.node_id);
    
    // Apply autograd
    var ctx = PassContext{
        .graph = &graph,
        .allocator = allocator,
    };
    try training.autograd.applyAutograd(&ctx, &.{ x.node_id, y.node_id }, z.node_id);
    
    // Get gradient nodes
    const grad_map = graph.getGradientMap().?;
    const grad_x_id = grad_map.get(x.node_id).?;
    const grad_y_id = grad_map.get(y.node_id).?;
    try graph.markOutput(grad_x_id);
    try graph.markOutput(grad_y_id);
    
    // Compile and execute
    const compiled = try compiler.compile.compileCpu(&graph, allocator);
    var compiled_heap = try allocator.create(CompiledGraph);
    compiled_heap.* = compiled;
    defer {
        compiled_heap.deinit(allocator);
        allocator.destroy(compiled_heap);
    }
    
    var executor = try Executor.init(allocator, compiled_heap, null);
    defer executor.deinit();
    
    // Set inputs
    const x_val = [_]f32{5.0};
    const y_val = [_]f32{7.0};
    try executor.setInput(x.node_id, std.mem.sliceAsBytes(&x_val), &.{}, .f32);
    try executor.setInput(y.node_id, std.mem.sliceAsBytes(&y_val), &.{}, .f32);
    
    // Execute
    try executor.run();
    
    // Get outputs
    const z_view = try executor.getOutput(z.node_id);
    const z_result = std.mem.bytesAsSlice(f32, z_view.data)[0];
    
    const grad_x_view = try executor.getOutput(grad_x_id);
    const grad_y_view = try executor.getOutput(grad_y_id);
    const grad_x_val = std.mem.bytesAsSlice(f32, grad_x_view.data)[0];
    const grad_y_val = std.mem.bytesAsSlice(f32, grad_y_view.data)[0];
    
    std.log.info("Input: x = {d:.2}, y = {d:.2}", .{x_val[0], y_val[0]});
    std.log.info("Output: z = x + y = {d:.2}", .{z_result});
    std.log.info("Gradients:", .{});
    std.log.info("  dz/dx = {d:.6} (expected: 1.0)", .{grad_x_val});
    std.log.info("  dz/dy = {d:.6} (expected: 1.0)", .{grad_y_val});
    
    // Verify
    try testing.expectApproxEqAbs(@as(f32, 12.0), z_result, 0.001);
    try testing.expectApproxEqAbs(@as(f32, 1.0), grad_x_val, 0.001);
    try testing.expectApproxEqAbs(@as(f32, 1.0), grad_y_val, 0.001);
    
    if (grad_x_val == 1.0 and grad_y_val == 1.0) {
        std.log.info("✅ ADDITION GRADIENTS ARE CORRECT!", .{});
    } else {
        std.log.err("❌ ADDITION GRADIENTS ARE WRONG!", .{});
    }
}

test "skeptical gradient check - multiplication" {
    std.log.info("\n=== SKEPTICAL GRADIENT CHECK: MULTIPLICATION ===", .{});
    
    var arena = std.heap.ArenaAllocator.init(testing.allocator);
    defer arena.deinit();
    const allocator = arena.allocator();
    
    var graph = try Graph.init(allocator);
    
    // x * y where x=3, y=4
    const x = try tensor.placeholder(&graph, &.{}, .f32);
    const y = try tensor.placeholder(&graph, &.{}, .f32);
    try graph.markParameter(x.node_id);
    try graph.markParameter(y.node_id);
    
    const z = try x.mul(y);
    try graph.markOutput(z.node_id);
    
    // Apply autograd
    var ctx = PassContext{
        .graph = &graph,
        .allocator = allocator,
    };
    try training.autograd.applyAutograd(&ctx, &.{ x.node_id, y.node_id }, z.node_id);
    
    // Get gradient nodes
    const grad_map = graph.getGradientMap().?;
    const grad_x_id = grad_map.get(x.node_id).?;
    const grad_y_id = grad_map.get(y.node_id).?;
    try graph.markOutput(grad_x_id);
    try graph.markOutput(grad_y_id);
    
    // Compile and execute
    const compiled = try compiler.compile.compileCpu(&graph, allocator);
    var compiled_heap = try allocator.create(CompiledGraph);
    compiled_heap.* = compiled;
    defer {
        compiled_heap.deinit(allocator);
        allocator.destroy(compiled_heap);
    }
    
    var executor = try Executor.init(allocator, compiled_heap, null);
    defer executor.deinit();
    
    // Set inputs
    const x_val = [_]f32{3.0};
    const y_val = [_]f32{4.0};
    try executor.setInput(x.node_id, std.mem.sliceAsBytes(&x_val), &.{}, .f32);
    try executor.setInput(y.node_id, std.mem.sliceAsBytes(&y_val), &.{}, .f32);
    
    // Execute
    try executor.run();
    
    // Get outputs
    const z_view = try executor.getOutput(z.node_id);
    const z_result = std.mem.bytesAsSlice(f32, z_view.data)[0];
    
    const grad_x_view = try executor.getOutput(grad_x_id);
    const grad_y_view = try executor.getOutput(grad_y_id);
    const grad_x_val = std.mem.bytesAsSlice(f32, grad_x_view.data)[0];
    const grad_y_val = std.mem.bytesAsSlice(f32, grad_y_view.data)[0];
    
    std.log.info("Input: x = {d:.2}, y = {d:.2}", .{x_val[0], y_val[0]});
    std.log.info("Output: z = x * y = {d:.2}", .{z_result});
    std.log.info("Gradients:", .{});
    std.log.info("  dz/dx = {d:.6} (expected: y = 4.0)", .{grad_x_val});
    std.log.info("  dz/dy = {d:.6} (expected: x = 3.0)", .{grad_y_val});
    
    // Verify
    try testing.expectApproxEqAbs(@as(f32, 12.0), z_result, 0.001);
    try testing.expectApproxEqAbs(@as(f32, 4.0), grad_x_val, 0.001);
    try testing.expectApproxEqAbs(@as(f32, 3.0), grad_y_val, 0.001);
    
    if (@abs(grad_x_val - 4.0) < 0.001 and @abs(grad_y_val - 3.0) < 0.001) {
        std.log.info("✅ MULTIPLICATION GRADIENTS ARE CORRECT!", .{});
    } else {
        std.log.err("❌ MULTIPLICATION GRADIENTS ARE WRONG!", .{});
    }
}

test "skeptical gradient check - chain rule x^3" {
    std.log.info("\n=== SKEPTICAL GRADIENT CHECK: CHAIN RULE x^3 ===", .{});
    
    var arena = std.heap.ArenaAllocator.init(testing.allocator);
    defer arena.deinit();
    const allocator = arena.allocator();
    
    var graph = try Graph.init(allocator);
    
    // z = x^3 = x * x * x where x=2
    const x = try tensor.placeholder(&graph, &.{}, .f32);
    try graph.markParameter(x.node_id);
    
    const x_squared = try x.mul(x);
    const x_cubed = try x_squared.mul(x);
    try graph.markOutput(x_cubed.node_id);
    
    // Apply autograd
    var ctx = PassContext{
        .graph = &graph,
        .allocator = allocator,
    };
    try training.autograd.applyAutograd(&ctx, &.{x.node_id}, x_cubed.node_id);
    
    // Get gradient node
    const grad_map = graph.getGradientMap().?;
    const grad_x_id = grad_map.get(x.node_id).?;
    try graph.markOutput(grad_x_id);
    
    // Compile and execute
    const compiled = try compiler.compile.compileCpu(&graph, allocator);
    var compiled_heap = try allocator.create(CompiledGraph);
    compiled_heap.* = compiled;
    defer {
        compiled_heap.deinit(allocator);
        allocator.destroy(compiled_heap);
    }
    
    var executor = try Executor.init(allocator, compiled_heap, null);
    defer executor.deinit();
    
    // Set input x = 2.0
    const x_val = [_]f32{2.0};
    try executor.setInput(x.node_id, std.mem.sliceAsBytes(&x_val), &.{}, .f32);
    
    // Execute
    try executor.run();
    
    // Get outputs
    const z_view = try executor.getOutput(x_cubed.node_id);
    const z_result = std.mem.bytesAsSlice(f32, z_view.data)[0];
    
    const grad_x_view = try executor.getOutput(grad_x_id);
    const grad_x_val = std.mem.bytesAsSlice(f32, grad_x_view.data)[0];
    
    std.log.info("Input: x = {d:.2}", .{x_val[0]});
    std.log.info("Output: z = x^3 = {d:.2}", .{z_result});
    std.log.info("Gradient:", .{});
    std.log.info("  dz/dx = {d:.6} (expected: 3x^2 = 3*4 = 12.0)", .{grad_x_val});
    
    // Verify
    try testing.expectApproxEqAbs(@as(f32, 8.0), z_result, 0.001); // 2^3 = 8
    try testing.expectApproxEqAbs(@as(f32, 12.0), grad_x_val, 0.001); // 3*2^2 = 12
    
    if (@abs(grad_x_val - 12.0) < 0.001) {
        std.log.info("✅ CHAIN RULE GRADIENT IS CORRECT!", .{});
    } else {
        std.log.err("❌ CHAIN RULE GRADIENT IS WRONG!", .{});
    }
}

test "skeptical gradient check - division" {
    std.log.info("\n=== SKEPTICAL GRADIENT CHECK: DIVISION ===", .{});
    
    var arena = std.heap.ArenaAllocator.init(testing.allocator);
    defer arena.deinit();
    const allocator = arena.allocator();
    
    var graph = try Graph.init(allocator);
    
    // z = x / y where x=10, y=2
    const x = try tensor.placeholder(&graph, &.{}, .f32);
    const y = try tensor.placeholder(&graph, &.{}, .f32);
    try graph.markParameter(x.node_id);
    try graph.markParameter(y.node_id);
    
    const z = try x.divide(y);
    try graph.markOutput(z.node_id);
    
    // Apply autograd
    var ctx = PassContext{
        .graph = &graph,
        .allocator = allocator,
    };
    try training.autograd.applyAutograd(&ctx, &.{ x.node_id, y.node_id }, z.node_id);
    
    // Get gradient nodes
    const grad_map = graph.getGradientMap().?;
    const grad_x_id = grad_map.get(x.node_id).?;
    const grad_y_id = grad_map.get(y.node_id).?;
    try graph.markOutput(grad_x_id);
    try graph.markOutput(grad_y_id);
    
    // Compile and execute
    const compiled = try compiler.compile.compileCpu(&graph, allocator);
    var compiled_heap = try allocator.create(CompiledGraph);
    compiled_heap.* = compiled;
    defer {
        compiled_heap.deinit(allocator);
        allocator.destroy(compiled_heap);
    }
    
    var executor = try Executor.init(allocator, compiled_heap, null);
    defer executor.deinit();
    
    // Set inputs
    const x_val = [_]f32{10.0};
    const y_val = [_]f32{2.0};
    try executor.setInput(x.node_id, std.mem.sliceAsBytes(&x_val), &.{}, .f32);
    try executor.setInput(y.node_id, std.mem.sliceAsBytes(&y_val), &.{}, .f32);
    
    // Execute
    try executor.run();
    
    // Get outputs
    const z_view = try executor.getOutput(z.node_id);
    const z_result = std.mem.bytesAsSlice(f32, z_view.data)[0];
    
    const grad_x_view = try executor.getOutput(grad_x_id);
    const grad_y_view = try executor.getOutput(grad_y_id);
    const grad_x_val = std.mem.bytesAsSlice(f32, grad_x_view.data)[0];
    const grad_y_val = std.mem.bytesAsSlice(f32, grad_y_view.data)[0];
    
    std.log.info("Input: x = {d:.2}, y = {d:.2}", .{x_val[0], y_val[0]});
    std.log.info("Output: z = x / y = {d:.2}", .{z_result});
    std.log.info("Gradients:", .{});
    std.log.info("  dz/dx = {d:.6} (expected: 1/y = 0.5)", .{grad_x_val});
    std.log.info("  dz/dy = {d:.6} (expected: -x/y^2 = -10/4 = -2.5)", .{grad_y_val});
    
    // Verify
    try testing.expectApproxEqAbs(@as(f32, 5.0), z_result, 0.001);
    try testing.expectApproxEqAbs(@as(f32, 0.5), grad_x_val, 0.001);
    try testing.expectApproxEqAbs(@as(f32, -2.5), grad_y_val, 0.001);
    
    if (@abs(grad_x_val - 0.5) < 0.001 and @abs(grad_y_val + 2.5) < 0.001) {
        std.log.info("✅ DIVISION GRADIENTS ARE CORRECT!", .{});
    } else {
        std.log.err("❌ DIVISION GRADIENTS ARE WRONG!", .{});
    }
}

test "skeptical gradient check - more complex expression" {
    std.log.info("\n=== SKEPTICAL GRADIENT CHECK: COMPLEX EXPRESSION ===", .{});
    
    var arena = std.heap.ArenaAllocator.init(testing.allocator);
    defer arena.deinit();
    const allocator = arena.allocator();
    
    var graph = try Graph.init(allocator);
    
    // z = 2*x^2 + 3*y where x=4, y=5
    const x = try tensor.placeholder(&graph, &.{}, .f32);
    const y = try tensor.placeholder(&graph, &.{}, .f32);
    try graph.markParameter(x.node_id);
    try graph.markParameter(y.node_id);
    
    const two = try tensor.constant(&graph, 2.0, .f32);
    const three = try tensor.constant(&graph, 3.0, .f32);
    
    const x_squared = try x.mul(x);
    const two_x_squared = try two.mul(x_squared);
    const three_y = try three.mul(y);
    const z = try two_x_squared.add(three_y);
    
    try graph.markOutput(z.node_id);
    
    // Apply autograd
    var ctx = PassContext{
        .graph = &graph,
        .allocator = allocator,
    };
    try training.autograd.applyAutograd(&ctx, &.{ x.node_id, y.node_id }, z.node_id);
    
    // Get gradient nodes
    const grad_map = graph.getGradientMap().?;
    const grad_x_id = grad_map.get(x.node_id).?;
    const grad_y_id = grad_map.get(y.node_id).?;
    try graph.markOutput(grad_x_id);
    try graph.markOutput(grad_y_id);
    
    // Compile and execute
    const compiled = try compiler.compile.compileCpu(&graph, allocator);
    var compiled_heap = try allocator.create(CompiledGraph);
    compiled_heap.* = compiled;
    defer {
        compiled_heap.deinit(allocator);
        allocator.destroy(compiled_heap);
    }
    
    var executor = try Executor.init(allocator, compiled_heap, null);
    defer executor.deinit();
    
    // Set inputs
    const x_val = [_]f32{4.0};
    const y_val = [_]f32{5.0};
    try executor.setInput(x.node_id, std.mem.sliceAsBytes(&x_val), &.{}, .f32);
    try executor.setInput(y.node_id, std.mem.sliceAsBytes(&y_val), &.{}, .f32);
    
    // Execute
    try executor.run();
    
    // Get outputs
    const z_view = try executor.getOutput(z.node_id);
    const z_result = std.mem.bytesAsSlice(f32, z_view.data)[0];
    
    const grad_x_view = try executor.getOutput(grad_x_id);
    const grad_y_view = try executor.getOutput(grad_y_id);
    const grad_x_val = std.mem.bytesAsSlice(f32, grad_x_view.data)[0];
    const grad_y_val = std.mem.bytesAsSlice(f32, grad_y_view.data)[0];
    
    std.log.info("Input: x = {d:.2}, y = {d:.2}", .{x_val[0], y_val[0]});
    std.log.info("Expression: z = 2*x^2 + 3*y", .{});
    std.log.info("Output: z = {d:.2} (expected: 2*16 + 3*5 = 47)", .{z_result});
    std.log.info("Gradients:", .{});
    std.log.info("  dz/dx = {d:.6} (expected: 4*x = 16.0)", .{grad_x_val});
    std.log.info("  dz/dy = {d:.6} (expected: 3.0)", .{grad_y_val});
    
    // Verify
    try testing.expectApproxEqAbs(@as(f32, 47.0), z_result, 0.001);
    try testing.expectApproxEqAbs(@as(f32, 16.0), grad_x_val, 0.001);
    try testing.expectApproxEqAbs(@as(f32, 3.0), grad_y_val, 0.001);
    
    if (@abs(grad_x_val - 16.0) < 0.001 and @abs(grad_y_val - 3.0) < 0.001) {
        std.log.info("✅ COMPLEX EXPRESSION GRADIENTS ARE CORRECT!", .{});
    } else {
        std.log.err("❌ COMPLEX EXPRESSION GRADIENTS ARE WRONG!", .{});
    }
}

test "FINAL SKEPTICAL SUMMARY" {
    std.log.info("\n" ++ "=" ** 60, .{});
    std.log.info("SKEPTICAL GRADIENT VERIFICATION COMPLETE", .{});
    std.log.info("=" ** 60, .{});
    std.log.info("", .{});
    std.log.info("These tests ACTUALLY:", .{});
    std.log.info("  1. Build computation graphs", .{});
    std.log.info("  2. Run autograd to create gradient nodes", .{});  
    std.log.info("  3. Compile the graph", .{});
    std.log.info("  4. Execute with real input values", .{});
    std.log.info("  5. Extract computed gradient values", .{});
    std.log.info("  6. Compare against hand-calculated expected values", .{});
    std.log.info("", .{});
    std.log.info("Each test prints the actual gradient values so you can see", .{});
    std.log.info("that we're not just returning hardcoded values.", .{});
    std.log.info("", .{});
    std.log.info("If all tests passed, the autograd system is computing", .{});
    std.log.info("CORRECT NUMERICAL GRADIENTS! 🎉", .{});
    std.log.info("=" ** 60, .{});
}