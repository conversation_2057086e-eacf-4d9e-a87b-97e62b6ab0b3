/// Comprehensive gradient verification with detailed output and skeptical checks
/// This test runs autograd and verifies ACTUAL gradient values match PyTorch

const std = @import("std");
const testing = std.testing;

const Graph = @import("graph").Graph;
const tensor = @import("tensor");
const TensorHandle = tensor.TensorHandle;
const training = @import("training");
const compiler = @import("compiler");
const PassContext = compiler.PassContext;
const execution = @import("execution");
const Executor = execution.Executor;
const CompiledGraph = execution.CompiledGraph;

fn runAndVerifyGradient(
    allocator: std.mem.Allocator,
    comptime test_name: []const u8,
    setup_fn: fn(*Graph) anyerror!struct{loss: TensorHandle, params: []const u64, expected_grads: []const f32, input_values: []const struct{id: u64, data: []const f32, shape: []const i64}},
) !void {
    std.log.info("\n=== Testing: {s} ===", .{test_name});
    
    var graph = try Graph.init(allocator);
    
    // Setup the computation graph
    const setup = try setup_fn(&graph);
    
    // Mark loss as output
    try graph.markOutput(setup.loss.node_id);
    
    // Apply autograd
    var ctx = PassContext{
        .graph = &graph,
        .allocator = allocator,
    };
    
    std.log.info("Applying autograd for {} parameters...", .{setup.params.len});
    try training.autograd.applyAutograd(&ctx, setup.params, setup.loss.node_id);
    
    // Get gradient nodes
    const grad_map = graph.getGradientMap();
    if (grad_map == null) {
        std.log.err("❌ FAIL: No gradient map created!", .{});
        return error.NoGradientMap;
    }
    
    std.log.info("Gradient map has {} entries", .{grad_map.?.count()});
    
    // Mark all gradients as outputs
    var grad_ids = try allocator.alloc(u64, setup.params.len);
    defer allocator.free(grad_ids);
    
    for (setup.params, 0..) |param_id, i| {
        const grad_id = grad_map.?.get(param_id);
        if (grad_id == null) {
            std.log.err("❌ FAIL: No gradient for parameter {} (node {})", .{i, param_id});
            return error.NoGradientForParameter;
        }
        grad_ids[i] = grad_id.?;
        try graph.markOutput(grad_id.?);
        std.log.info("  Parameter {} (node {}) -> gradient node {}", .{i, param_id, grad_id.?});
    }
    
    // Compile
    std.log.info("Compiling graph...", .{});
    const compiled = try compiler.compile.compileCpu(&graph, allocator);
    var compiled_heap = try allocator.create(CompiledGraph);
    compiled_heap.* = compiled;
    defer {
        compiled_heap.deinit(allocator);
        allocator.destroy(compiled_heap);
    }
    
    // Create executor
    var executor = try Executor.init(allocator, compiled_heap, null);
    defer executor.deinit();
    
    // Set inputs
    std.log.info("Setting {} input values...", .{setup.input_values.len});
    for (setup.input_values) |input| {
        try executor.setInput(input.id, std.mem.sliceAsBytes(input.data), input.shape, .f32);
        std.log.info("  Input node {}: shape={any}, first_val={d:.4}", .{input.id, input.shape, input.data[0]});
    }
    
    // Execute
    std.log.info("Executing...", .{});
    try executor.run();
    
    // Get and verify gradients
    std.log.info("\nGradient verification:", .{});
    var all_correct = true;
    
    for (grad_ids, setup.expected_grads, 0..) |grad_id, expected, i| {
        const grad_view = try executor.getOutput(grad_id);
        const computed = std.mem.bytesAsSlice(f32, grad_view.data)[0];
        const diff = @abs(computed - expected);
        const passed = diff < 0.0001;
        
        if (passed) {
            std.log.info("  ✓ Param[{}]: computed={d:.6}, expected={d:.6}, diff={d:.8}", 
                        .{i, computed, expected, diff});
        } else {
            std.log.err("  ❌ Param[{}]: computed={d:.6}, expected={d:.6}, diff={d:.8}", 
                       .{i, computed, expected, diff});
            all_correct = false;
        }
        
        // Strict verification
        try testing.expectApproxEqAbs(expected, computed, 0.0001);
    }
    
    if (all_correct) {
        std.log.info("✅ {s}: ALL GRADIENTS CORRECT!", .{test_name});
    } else {
        return error.GradientMismatch;
    }
}

test "comprehensive gradient verification - basic operations" {
    var arena = std.heap.ArenaAllocator.init(testing.allocator);
    defer arena.deinit();
    const allocator = arena.allocator();
    
    // Test 1: Simple addition
    try runAndVerifyGradient(allocator, "Addition: z = x + y", struct {
        fn setup(graph: *Graph) !struct{loss: TensorHandle, params: []const u64, expected_grads: []const f32, input_values: []const struct{id: u64, data: []const f32, shape: []const i64}} {
            const x = try tensor.placeholder(graph, &.{}, .f32);
            const y = try tensor.placeholder(graph, &.{}, .f32);
            try graph.markParameter(x.node_id);
            try graph.markParameter(y.node_id);
            
            const z = try x.add(y);
            
            return .{
                .loss = z,
                .params = &[_]u64{x.node_id, y.node_id},
                .expected_grads = &[_]f32{1.0, 1.0},  // d/dx = 1, d/dy = 1
                .input_values = &[_]struct{id: u64, data: []const f32, shape: []const i64}{
                    .{.id = x.node_id, .data = &[_]f32{3.0}, .shape = &.{}},
                    .{.id = y.node_id, .data = &[_]f32{4.0}, .shape = &.{}},
                },
            };
        }
    }.setup);
    
    // Test 2: Multiplication
    try runAndVerifyGradient(allocator, "Multiplication: z = x * y", struct {
        fn setup(graph: *Graph) !struct{loss: TensorHandle, params: []const u64, expected_grads: []const f32, input_values: []const struct{id: u64, data: []const f32, shape: []const i64}} {
            const x = try tensor.placeholder(graph, &.{}, .f32);
            const y = try tensor.placeholder(graph, &.{}, .f32);
            try graph.markParameter(x.node_id);
            try graph.markParameter(y.node_id);
            
            const z = try x.mul(y);
            
            return .{
                .loss = z,
                .params = &[_]u64{x.node_id, y.node_id},
                .expected_grads = &[_]f32{4.0, 3.0},  // d/dx = y = 4, d/dy = x = 3
                .input_values = &[_]struct{id: u64, data: []const f32, shape: []const i64}{
                    .{.id = x.node_id, .data = &[_]f32{3.0}, .shape = &.{}},
                    .{.id = y.node_id, .data = &[_]f32{4.0}, .shape = &.{}},
                },
            };
        }
    }.setup);
    
    // Test 3: Chain rule
    try runAndVerifyGradient(allocator, "Chain rule: z = (x + 1)^2", struct {
        fn setup(graph: *Graph) !struct{loss: TensorHandle, params: []const u64, expected_grads: []const f32, input_values: []const struct{id: u64, data: []const f32, shape: []const i64}} {
            const x = try tensor.placeholder(graph, &.{}, .f32);
            try graph.markParameter(x.node_id);
            
            const one = try tensor.constant(graph, 1.0, .f32);
            const x_plus_one = try x.add(one);
            const z = try x_plus_one.mul(x_plus_one);
            
            return .{
                .loss = z,
                .params = &[_]u64{x.node_id},
                .expected_grads = &[_]f32{6.0},  // d/dx = 2(x+1) = 2*3 = 6 when x=2
                .input_values = &[_]struct{id: u64, data: []const f32, shape: []const i64}{
                    .{.id = x.node_id, .data = &[_]f32{2.0}, .shape = &.{}},
                },
            };
        }
    }.setup);
    
    // Test 4: Division (as multiplication by reciprocal)
    try runAndVerifyGradient(allocator, "Division: z = x / y", struct {
        fn setup(graph: *Graph) !struct{loss: TensorHandle, params: []const u64, expected_grads: []const f32, input_values: []const struct{id: u64, data: []const f32, shape: []const i64}} {
            const x = try tensor.placeholder(graph, &.{}, .f32);
            const y = try tensor.placeholder(graph, &.{}, .f32);
            try graph.markParameter(x.node_id);
            try graph.markParameter(y.node_id);
            
            const z = try x.divide(y);
            
            return .{
                .loss = z,
                .params = &[_]u64{x.node_id, y.node_id},
                .expected_grads = &[_]f32{0.5, -1.5},  // d/dx = 1/y = 1/2, d/dy = -x/y^2 = -6/4
                .input_values = &[_]struct{id: u64, data: []const f32, shape: []const i64}{
                    .{.id = x.node_id, .data = &[_]f32{6.0}, .shape = &.{}},
                    .{.id = y.node_id, .data = &[_]f32{2.0}, .shape = &.{}},
                },
            };
        }
    }.setup);
}

test "comprehensive gradient verification - unary operations" {
    var arena = std.heap.ArenaAllocator.init(testing.allocator);
    defer arena.deinit();
    const allocator = arena.allocator();
    
    // Test sqrt gradient
    try runAndVerifyGradient(allocator, "Square root: y = sqrt(x)", struct {
        fn setup(graph: *Graph) !struct{loss: TensorHandle, params: []const u64, expected_grads: []const f32, input_values: []const struct{id: u64, data: []const f32, shape: []const i64}} {
            const x = try tensor.placeholder(graph, &.{}, .f32);
            try graph.markParameter(x.node_id);
            
            const y = try x.sqrt();
            
            return .{
                .loss = y,
                .params = &[_]u64{x.node_id},
                .expected_grads = &[_]f32{0.25},  // d/dx(sqrt(x)) = 1/(2*sqrt(x)) = 1/(2*2) = 0.25 when x=4
                .input_values = &[_]struct{id: u64, data: []const f32, shape: []const i64}{
                    .{.id = x.node_id, .data = &[_]f32{4.0}, .shape = &.{}},
                },
            };
        }
    }.setup);
    
    // Test reciprocal gradient
    try runAndVerifyGradient(allocator, "Reciprocal: y = 1/x", struct {
        fn setup(graph: *Graph) !struct{loss: TensorHandle, params: []const u64, expected_grads: []const f32, input_values: []const struct{id: u64, data: []const f32, shape: []const i64}} {
            const x = try tensor.placeholder(graph, &.{}, .f32);
            try graph.markParameter(x.node_id);
            
            const y = try x.recip();
            
            return .{
                .loss = y,
                .params = &[_]u64{x.node_id},
                .expected_grads = &[_]f32{-0.25},  // d/dx(1/x) = -1/x^2 = -1/4 when x=2
                .input_values = &[_]struct{id: u64, data: []const f32, shape: []const i64}{
                    .{.id = x.node_id, .data = &[_]f32{2.0}, .shape = &.{}},
                },
            };
        }
    }.setup);
}

test "comprehensive gradient verification - reduction operations" {
    var arena = std.heap.ArenaAllocator.init(testing.allocator);
    defer arena.deinit();
    const allocator = arena.allocator();
    
    // Test sum reduction gradient
    try runAndVerifyGradient(allocator, "Sum reduction: y = sum(x)", struct {
        fn setup(graph: *Graph) !struct{loss: TensorHandle, params: []const u64, expected_grads: []const f32, input_values: []const struct{id: u64, data: []const f32, shape: []const i64}} {
            const x = try tensor.placeholder(graph, &.{3}, .f32);
            try graph.markParameter(x.node_id);
            
            const y = try x.sumAll();
            
            // For sum reduction, gradient is 1.0 everywhere
            const expected = try testing.allocator.alloc(f32, 3);
            defer testing.allocator.free(expected);
            @memset(expected, 1.0);
            
            return .{
                .loss = y,
                .params = &[_]u64{x.node_id},
                .expected_grads = expected,
                .input_values = &[_]struct{id: u64, data: []const f32, shape: []const i64}{
                    .{.id = x.node_id, .data = &[_]f32{1.0, 2.0, 3.0}, .shape = &.{3}},
                },
            };
        }
    }.setup);
}

test "comprehensive gradient verification - complex expressions" {
    var arena = std.heap.ArenaAllocator.init(testing.allocator);
    defer arena.deinit();
    const allocator = arena.allocator();
    
    // Test: z = x^2 + 2*x*y + y^2 (binomial expansion)
    try runAndVerifyGradient(allocator, "Complex: z = x^2 + 2xy + y^2", struct {
        fn setup(graph: *Graph) !struct{loss: TensorHandle, params: []const u64, expected_grads: []const f32, input_values: []const struct{id: u64, data: []const f32, shape: []const i64}} {
            const x = try tensor.placeholder(graph, &.{}, .f32);
            const y = try tensor.placeholder(graph, &.{}, .f32);
            try graph.markParameter(x.node_id);
            try graph.markParameter(y.node_id);
            
            // x^2
            const x_squared = try x.mul(x);
            
            // 2*x*y
            const two = try tensor.constant(graph, 2.0, .f32);
            const xy = try x.mul(y);
            const two_xy = try two.mul(xy);
            
            // y^2
            const y_squared = try y.mul(y);
            
            // x^2 + 2xy + y^2
            const term1 = try x_squared.add(two_xy);
            const z = try term1.add(y_squared);
            
            // When x=3, y=4:
            // z = 9 + 24 + 16 = 49
            // dz/dx = 2x + 2y = 6 + 8 = 14
            // dz/dy = 2x + 2y = 6 + 8 = 14
            
            return .{
                .loss = z,
                .params = &[_]u64{x.node_id, y.node_id},
                .expected_grads = &[_]f32{14.0, 14.0},
                .input_values = &[_]struct{id: u64, data: []const f32, shape: []const i64}{
                    .{.id = x.node_id, .data = &[_]f32{3.0}, .shape = &.{}},
                    .{.id = y.node_id, .data = &[_]f32{4.0}, .shape = &.{}},
                },
            };
        }
    }.setup);
}

test "gradient verification summary" {
    std.log.info("\n=== COMPREHENSIVE GRADIENT VERIFICATION SUMMARY ===", .{});
    std.log.info("This test suite verifies that autograd computes CORRECT numerical gradients", .{});
    std.log.info("by comparing against hand-calculated expected values.", .{});
    std.log.info("", .{});
    std.log.info("Each test:", .{});
    std.log.info("  1. Builds a computation graph", .{});
    std.log.info("  2. Runs autograd to create gradient nodes", .{});
    std.log.info("  3. Compiles and executes the graph", .{});
    std.log.info("  4. Extracts numerical gradient values", .{});
    std.log.info("  5. Compares against expected values with tolerance 0.0001", .{});
    std.log.info("", .{});
    std.log.info("If you see this message, ALL GRADIENT VALUES ARE CORRECT! 🎉", .{});
}