/// TensorHandle - Core tensor type definition
/// 
/// This module defines the TensorHandle struct which is the primary interface
/// for tensor operations in Zing. It carries shape information and references
/// a node in the computation graph.
///
/// ARCHITECTURAL DECISION: Tensor component is the SINGLE SOURCE OF TRUTH for shape information.
/// TensorHandle carries ShapeTracker while Graph nodes contain NO shape data.

const std = @import("std");

// Import types
const types = @import("types");
const DataType = types.DataType;
const NodeId = types.NodeId;

// Import core components
const Graph = @import("graph").Graph;
const shape_mod = @import("shape");
const ShapeTracker = shape_mod.ShapeTracker;
const SymbolicDim = shape_mod.SymbolicDim;

/// TensorHandle - lightweight view into the graph with shape information
pub const TensorHandle = struct {
    graph: *Graph,          // Reference to the graph
    node_id: NodeId,        // Reference to the graph node
    shape: ShapeTracker,    // Embedded shape tracker (moved from Node)
    dtype: DataType,        // Data type of the tensor

    // Import operation implementations
    const view_ops = @import("ops/view.zig");
    const primitive_ops = @import("ops/primitive.zig");
    const arithmetic_ops = @import("ops/arithmetic.zig");
    const comparison_ops = @import("ops/comparison.zig");
    const reduction_ops = @import("ops/reduction.zig");
    const activation_ops = @import("ops/activation.zig");
    const math_ops = @import("ops/math.zig");
    const logical_ops = @import("ops/logical.zig");
    const normalization_ops = @import("ops/normalization.zig");
    const linalg_ops = @import("ops/linalg.zig");
    const indexing_ops = @import("ops/indexing.zig");

    // View operations
    pub const reshape = view_ops.reshape;
    pub const transpose = view_ops.transpose;
    pub const transposeContiguous = view_ops.transposeContiguous;
    pub const slice = view_ops.slice;
    pub const expand = view_ops.expand;
    pub const expandAxis = view_ops.expandAxis;
    pub const expandDim = view_ops.expandDim;
    pub const expandToShape = view_ops.expandToShape;
    pub const makeContiguous = view_ops.makeContiguous;
    pub const squeeze = view_ops.squeeze;
    pub const unsqueeze = view_ops.unsqueeze;
    pub const flatten = view_ops.flatten;
    pub const flattenDims = view_ops.flattenDims;
    pub const broadcast = view_ops.broadcast;

    // Primitive operations
    pub const add = primitive_ops.add;
    pub const mul = primitive_ops.mul;
    pub const mod = primitive_ops.mod;
    pub const lessThan = primitive_ops.lessThan;
    pub const recip = primitive_ops.recip;
    pub const sqrt = primitive_ops.sqrt;
    pub const sin = primitive_ops.sin;
    pub const exp2 = primitive_ops.exp2;
    pub const log2 = primitive_ops.log2;
    pub const sumReduce = primitive_ops.sumReduce;
    pub const maxReduce = primitive_ops.maxReduce;

    // Arithmetic operations
    pub const subtract = arithmetic_ops.subtract;
    pub const divide = arithmetic_ops.divide;
    pub const pow = arithmetic_ops.pow;
    pub const neg = arithmetic_ops.neg;
    pub const abs = arithmetic_ops.abs;
    pub const square = arithmetic_ops.square;
    pub const sign = arithmetic_ops.sign;
    pub const floorDiv = arithmetic_ops.floorDiv;
    pub const fma = arithmetic_ops.fma;
    pub const clamp = arithmetic_ops.clamp;
    pub const cube = arithmetic_ops.cube;
    pub const maximum_along_axis = arithmetic_ops.maximum_along_axis;

    // Comparison operations
    pub const equal = comparison_ops.equal;
    pub const notEqual = comparison_ops.notEqual;
    pub const lessThanOrEqual = comparison_ops.lessThanOrEqual;
    pub const greaterThan = comparison_ops.greaterThan;
    pub const greaterThanOrEqual = comparison_ops.greaterThanOrEqual;
    pub const maximum = comparison_ops.maximum;
    pub const minimum = comparison_ops.minimum;

    // Reduction operations
    pub const sum = reduction_ops.sum;
    pub const max = reduction_ops.max;
    pub const min = reduction_ops.min;
    pub const mean = reduction_ops.mean;
    pub const prod = reduction_ops.prod;
    pub const variance = reduction_ops.variance;
    pub const stddev = reduction_ops.stddev;
    pub const sumAll = reduction_ops.sumAll;
    pub const maxAll = reduction_ops.maxAll;
    pub const minAll = reduction_ops.minAll;
    pub const minReduce = reduction_ops.minReduce;
    pub const meanAll = reduction_ops.meanAll;
    pub const prodAll = reduction_ops.prodAll;
    pub const norm1 = reduction_ops.norm1;
    pub const norm2 = reduction_ops.norm2;
    pub const normFro = reduction_ops.normFro;
    pub const countNonzero = reduction_ops.countNonzero;
    pub const argmax = reduction_ops.argmax;
    pub const argmin = reduction_ops.argmin;
    pub const cumsumLastDim = reduction_ops.cumsumLastDim;

    // Activation functions
    pub const relu = activation_ops.relu;
    pub const sigmoid = activation_ops.sigmoid;
    pub const tanh = activation_ops.tanh;
    pub const gelu = activation_ops.gelu;
    pub const softplus = activation_ops.softplus;
    pub const leakyRelu = activation_ops.leakyRelu;
    pub const swish = activation_ops.swish;
    pub const silu = activation_ops.silu;
    pub const softmax = activation_ops.softmax;
    pub const logSoftmax = activation_ops.logSoftmax;

    // Math functions
    pub const exp = math_ops.exp;
    pub const log = math_ops.log;
    pub const ln = math_ops.ln;
    pub const cos = math_ops.cos;
    pub const tan = math_ops.tan;
    pub const sinh = math_ops.sinh;
    pub const cosh = math_ops.cosh;

    // Logical operations
    pub const logicalNot = logical_ops.logicalNot;
    pub const logicalAnd = logical_ops.logicalAnd;
    pub const logicalOr = logical_ops.logicalOr;
    pub const select = logical_ops.select;

    // Normalization operations
    pub const layerNorm = normalization_ops.layerNorm;
    pub const normalizeByRange = normalization_ops.normalizeByRange;
    pub const batchNorm = normalization_ops.batchNorm;
    pub const instanceNorm = normalization_ops.instanceNorm;
    pub const groupNorm = normalization_ops.groupNorm;
    
    // Linear algebra operations
    pub const matmul = linalg_ops.matmul;
    
    // Indexing operations
    pub const gather = indexing_ops.gather;

    // Helper methods
    /// Get the current node ID, following any substitutions from pattern recognition
    pub fn getCurrentNodeId(self: TensorHandle) NodeId {
        return self.graph.resolveCurrentNodeId(self.node_id);
    }

    // Helper method to get rank
    pub fn rank(self: TensorHandle) usize {
        return self.shape.dims.len;
    }

    /// Create a TensorHandle from an existing node in the graph
    /// This is useful when working with gradients or other nodes created outside the tensor API
    pub fn fromNode(graph: *Graph, node_id: NodeId) !TensorHandle {
        const node = graph.getNode(node_id) orelse {
            std.log.err("fromNode: node {} not found in graph", .{node_id});
            return error.NodeNotFound;
        };
        
        // Get dtype from node outputs
        const dtype = if (node.outputs.len > 0) 
            node.outputs[0].dtype
        else {
            std.log.err("fromNode: node {} has no outputs", .{node_id});
            return error.NoOutputs;
        };
        
        // Get shape from metadata if available, otherwise infer scalar shape
        const shape = if (node.metadata) |meta| blk: {
            if (meta.output_shape) |s| {
                break :blk s;
            } else {
                // Metadata exists but no output shape - assume scalar
                std.log.warn("fromNode: node {} has metadata but no output_shape, assuming scalar", .{node_id});
                const empty_dims = [_]SymbolicDim{};
                break :blk try ShapeTracker.fromDims(&empty_dims, graph.arena.allocator(), &graph.symbolic_pool);
            }
        } else blk: {
            // No metadata at all - this happens with low-level graph API
            // For data nodes (placeholder/constant), assume scalar
            // For compute nodes, try to infer from operation type
            switch (node.spec) {
                .data => {
                    std.log.warn("fromNode: data node {} has no metadata, assuming scalar", .{node_id});
                    const empty_dims = [_]SymbolicDim{};
                    break :blk try ShapeTracker.fromDims(&empty_dims, graph.arena.allocator(), &graph.symbolic_pool);
                },
                .compute => |op| {
                    // For compute nodes without metadata, we can't safely infer shape
                    // This is an error condition that should be fixed
                    std.log.err("fromNode: compute node {} (op={s}) has no metadata - cannot infer shape", .{node_id, @tagName(op)});
                    return error.MissingMetadata;
                },
            }
        };
        
        return TensorHandle{
            .graph = graph,
            .node_id = node_id,
            .shape = shape,
            .dtype = dtype,
        };
    }
};

/// Tensor-specific error types
pub const TensorError = error{
    InvalidShape,
    InvalidDimension,
    IncompatibleShapes,
    InvalidAxis,
    DimensionMismatch,
    InvalidBroadcast,
    InvalidMatmulDimensions,
    InvalidStep,
    InvalidRange,
    InvalidSqueeze,
    InvalidPermutation,
    DuplicateDimension,
    InvalidRepeat,
    InvalidPadding,
    DynamicDimensionNotSupported,
    InvalidReshape,
    InvalidSlice,
    DTypeMismatch,
};

// ===== Tests =====

test "TensorHandle.fromNode" {
    const testing = std.testing;
    const creation = @import("creation.zig");
    
    var graph = try Graph.init(testing.allocator);
    defer graph.deinit();
    
    // Create a tensor through the normal API
    const tensor = try creation.constant(&graph, 3.14, .f32);
    
    // Get the handle using fromNode
    const handle = try TensorHandle.fromNode(&graph, tensor.node_id);
    
    // Verify the handle has the right properties
    try testing.expectEqual(tensor.node_id, handle.node_id);
    try testing.expectEqual(tensor.dtype, handle.dtype);
    try testing.expectEqual(tensor.shape.dims.len, handle.shape.dims.len);
    
    // Test error cases
    try testing.expectError(error.NodeNotFound, TensorHandle.fromNode(&graph, 999));
}

test "TensorHandle.fromNode with metadata" {
    const testing = std.testing;
    const creation = @import("creation.zig");
    
    var graph = try Graph.init(testing.allocator);
    defer graph.deinit();
    
    // Create a tensor with shape
    const tensor = try creation.placeholder(&graph, &.{2, 3}, .f32);
    
    // Get handle using fromNode
    const handle = try TensorHandle.fromNode(&graph, tensor.node_id);
    
    // Verify shape was preserved
    try testing.expectEqual(@as(usize, 2), handle.shape.dims.len);
    try testing.expectEqual(@as(i64, 2), handle.shape.dims[0].concrete);
    try testing.expectEqual(@as(i64, 3), handle.shape.dims[1].concrete);
    try testing.expectEqual(DataType.f32, handle.dtype);
}

test "TensorHandle.fromNode without metadata" {
    const testing = std.testing;
    
    var graph = try Graph.init(testing.allocator);
    defer graph.deinit();
    
    // Create a node using low-level API (no metadata)
    const node_id = try graph.addPlaceholder(.f32);
    
    // fromNode should handle this gracefully for data nodes
    const handle = try TensorHandle.fromNode(&graph, node_id);
    
    // Should assume scalar shape
    try testing.expectEqual(@as(usize, 0), handle.shape.dims.len);
    try testing.expectEqual(DataType.f32, handle.dtype);
    
    // Create a compute node without metadata
    const x = try graph.addPlaceholder(.f32);
    const y = try graph.addPlaceholder(.f32);
    const compute_id = try graph.addNode(.add, &.{x, y}, .f32);
    
    // fromNode should fail for compute nodes without metadata
    try testing.expectError(error.MissingMetadata, TensorHandle.fromNode(&graph, compute_id));
}