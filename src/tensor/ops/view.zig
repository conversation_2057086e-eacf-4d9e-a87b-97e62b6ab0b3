const std = @import("std");
const types = @import("types");
const DataType = types.DataType;
const NodeId = types.NodeId;
const NodeSpec = types.NodeSpec;
const shape_mod = @import("shape");
const ShapeTracker = shape_mod.ShapeTracker;
const SymbolicDim = shape_mod.SymbolicDim;

// Import TensorHandle type reference
const TensorHandle = @import("../handle.zig").TensorHandle;

/// Reshape tensor to new shape, creating a new view
pub fn reshape(self: TensorHandle, new_shape: []const i64) !TensorHandle {
    // Reshape always requires contiguous memory layout
    // This is because reshape fundamentally changes the interpretation of memory
    // and can't be done with just stride manipulation for non-contiguous tensors
    const contiguous_self = try self.makeContiguous();
    
    // Calculate current total elements (only for concrete shapes for now)
    var current_elements: i64 = 1;
    for (contiguous_self.shape.dims) |dim| {
        switch (dim) {
            .concrete => |val| current_elements *= val,
            .dynamic => {
                std.log.err("reshape: dynamic dimensions not supported in current shape", .{});
                return error.DynamicDimensionNotSupported;
            },
        }
    }

    var new_symbolic_dims = try self.graph.arena.allocator().alloc(SymbolicDim, new_shape.len);
    var inferred_dim_idx: ?usize = null;
    var specified_elements: i64 = 1;

    for (new_shape, 0..) |dim, i| {
        if (dim > 0) {
            specified_elements *= dim;
            new_symbolic_dims[i] = SymbolicDim{ .concrete = dim };
        } else if (dim == -1) {
            if (inferred_dim_idx != null) {
                std.log.err("reshape: multiple -1 dimensions not allowed in shape", .{});
                return error.InvalidShape;
            }
            inferred_dim_idx = i;
        } else {
            std.log.err("reshape: invalid dimension {} at index {} (must be positive or -1)", .{ dim, i });
            return error.InvalidShape;
        }
    }

    var final_elements: i64 = specified_elements;
    if (inferred_dim_idx) |idx| {
        if (@mod(current_elements, specified_elements) != 0) {
            std.log.err("reshape: cannot evenly divide {} elements by {} specified elements", 
                       .{ current_elements, specified_elements });
            return error.InvalidReshape;
        }
        const inferred_val = @divExact(current_elements, specified_elements);
        new_symbolic_dims[idx] = SymbolicDim{ .concrete = inferred_val };
        final_elements *= inferred_val;
    }

    if (final_elements != current_elements) {
        std.log.err("reshape: element count mismatch - current: {}, target: {}", 
                   .{ current_elements, final_elements });
        return error.InvalidReshape;
    }

    // Create the new ShapeTracker with the target dimensions
    const new_shape_tracker = try ShapeTracker.fromDims(new_symbolic_dims, contiguous_self.graph.arena.allocator(), &contiguous_self.graph.symbolic_pool);

    // Return NEW handle with SAME node but NEW shape view
    return TensorHandle{
        .graph = contiguous_self.graph,
        .node_id = contiguous_self.node_id, // SAME data node (now contiguous)
        .shape = new_shape_tracker, // Target shape view
        .dtype = contiguous_self.dtype,
    };
}

/// Transpose tensor dimensions
pub fn transpose(self: TensorHandle, axes: ?[]const usize) !TensorHandle {
    const allocator = self.graph.arena.allocator();
    const axes_array = axes orelse blk: {
        // Default transpose: reverse all dimensions
        const default_axes = try allocator.alloc(usize, self.shape.dims.len);
        for (0..self.shape.dims.len) |i| {
            default_axes[i] = self.shape.dims.len - 1 - i;
        }
        break :blk default_axes;
    };

    // Create DEEP COPY of ShapeTracker to avoid corrupting shared data
    var new_shape_tracker = try self.shape.clone(allocator);
    try new_shape_tracker.transpose(axes_array, allocator);

    // Create handle with transposed view
    const transposed_handle = TensorHandle{
        .graph = self.graph,
        .node_id = self.node_id, // SAME data node
        .shape = new_shape_tracker, // Transposed shape view
        .dtype = self.dtype,
    };
    
    // Return the transposed view WITHOUT materializing
    // This keeps it as a shape transformation, like Luminal's permute
    return transposed_handle;
}

/// Transpose tensor dimensions with materialization
/// Use this when you need the transposed data to be contiguous in memory
pub fn transposeContiguous(self: TensorHandle, axes: ?[]const usize) !TensorHandle {
    const transposed = try transpose(self, axes);
    return transposed.makeContiguous();
}

/// Slice a tensor
pub fn slice(self: TensorHandle, starts: []const i64, ends: []const i64) !TensorHandle {
    // Check if we need to make contiguous (like Luminal does)
    // This is needed when there's existing padding on dimensions being sliced
    var need_contiguous = false;
    for (starts, ends, 0..) |start, end, i| {
        if ((start != 0 or end != self.shape.dims[i].concrete) and 
            self.shape.padding != null and
            (self.shape.padding.?[i][0].concrete != 0 or self.shape.padding.?[i][1].concrete != 0)) {
            need_contiguous = true;
            break;
        }
    }
    
    const handle_to_slice = if (need_contiguous) try self.makeContiguous() else self;
    
    // Create DEEP COPY of ShapeTracker to avoid corrupting shared data
    const allocator = handle_to_slice.graph.arena.allocator();
    var new_shape_tracker = try handle_to_slice.shape.clone(allocator);
    
    // Convert i64 slices to SymbolicDim
    const sym_starts = try allocator.alloc(SymbolicDim, starts.len);
    const sym_ends = try allocator.alloc(SymbolicDim, ends.len);
    for (starts, 0..) |s, i| {
        sym_starts[i] = SymbolicDim{ .concrete = s };
    }
    for (ends, 0..) |e, i| {
        sym_ends[i] = SymbolicDim{ .concrete = e };
    }
    
    try new_shape_tracker.slice(sym_starts, sym_ends, allocator, &handle_to_slice.graph.symbolic_pool);

    // Debug: Check if mask was created
    if (@import("build_options").enable_debug_logs) {
        if (new_shape_tracker.mask) |mask| {
            std.debug.print("slice: mask created with {} dimensions\n", .{mask.len});
            for (mask, 0..) |bounds, i| {
                std.debug.print("  Dim {}: [{}, {})\n", .{i, bounds[0].concrete, bounds[1].concrete});
            }
        } else {
            std.debug.print("slice: ERROR - no mask created!\n", .{});
        }
    }

    // Create handle with sliced view
    const sliced_handle = TensorHandle{
        .graph = handle_to_slice.graph,
        .node_id = handle_to_slice.node_id, // SAME data node
        .shape = new_shape_tracker, // Sliced shape view
        .dtype = handle_to_slice.dtype,
    };
    
    // Materialize the view by making it contiguous
    // This ensures the sliced data is actually computed
    return sliced_handle.makeContiguous();
}

/// Luminal-style expand: add a new dimension at the specified axis
pub fn expandAxis(self: TensorHandle, axis: usize, size: i64) !TensorHandle {
    // Create DEEP COPY of ShapeTracker to avoid corrupting shared data
    const allocator = self.graph.arena.allocator();
    var new_shape_tracker = try self.shape.clone(allocator);
    
    // Use ShapeTracker's expand method to add dimension
    try new_shape_tracker.expand(axis, SymbolicDim{ .concrete = size }, allocator);
    
    // Return NEW handle with SAME node but modified shape view
    return TensorHandle{
        .graph = self.graph,
        .node_id = self.node_id, // SAME data node
        .shape = new_shape_tracker, // NEW shape view with added dimension
        .dtype = self.dtype,
    };
}

/// Expand dimensions (broadcasting) - original function for same-rank broadcasting
pub fn expand(self: TensorHandle, new_shape: []const i64) !TensorHandle {
    // Validate that broadcasting is valid
    if (new_shape.len != self.shape.dims.len) {
        std.log.err("expand: shape rank mismatch - current: {}, target: {}", 
                   .{ self.shape.dims.len, new_shape.len });
        return error.InvalidBroadcast;
    }
    
    // Check each dimension can be broadcast
    for (self.shape.dims, new_shape, 0..) |current_dim, target_size, i| {
        const current_size = switch (current_dim) {
            .concrete => |v| v,
            .dynamic => {
                std.log.err("expand: dynamic dimension not supported at index {}", .{i});
                return error.DynamicDimensionNotSupported;
            },
        };
        
        // Broadcasting rule: current must be 1 or equal to target
        if (current_size != 1 and current_size != target_size) {
            std.log.err("expand: cannot broadcast dimension {} from {} to {}", 
                       .{ i, current_size, target_size });
            return error.InvalidBroadcast;
        }
    }
    
    // Create DEEP COPY of ShapeTracker to avoid corrupting shared data
    const allocator = self.graph.arena.allocator();
    var new_shape_tracker = try self.shape.clone(allocator);
    
    // Convert target shape to SymbolicDim
    const new_dims = try allocator.alloc(SymbolicDim, new_shape.len);
    const new_strides = try allocator.alloc(SymbolicDim, new_shape.len);
    const new_fake = try allocator.alloc(bool, new_shape.len);
    
    // Copy existing data and update for broadcasting
    for (self.shape.dims, self.shape.strides, self.shape.fake, new_shape, 0..) |current_dim, current_stride, current_fake, target_size, i| {
        const current_size = switch (current_dim) {
            .concrete => |v| v,
            .dynamic => unreachable, // Already checked above
        };
        
        new_dims[i] = SymbolicDim{ .concrete = target_size };
        
        if (current_size == 1 and target_size != 1) {
            // This dimension is being broadcast
            new_strides[i] = SymbolicDim{ .concrete = 0 }; // Broadcast stride
            new_fake[i] = true; // Mark as broadcast dimension
        } else {
            // Keep existing stride and fake flag
            new_strides[i] = current_stride;
            new_fake[i] = current_fake;
        }
    }
    
    // Free old arrays before updating (since we cloned)
    allocator.free(new_shape_tracker.dims);
    allocator.free(new_shape_tracker.strides);
    allocator.free(new_shape_tracker.fake);
    
    // Update shape tracker with new arrays
    new_shape_tracker.dims = new_dims;
    new_shape_tracker.strides = new_strides;
    new_shape_tracker.fake = new_fake;
    
    // Create handle with expanded view
    const expanded_handle = TensorHandle{
        .graph = self.graph,
        .node_id = self.node_id, // SAME data node
        .shape = new_shape_tracker, // NEW shape view with broadcasting
        .dtype = self.dtype,
    };
    
    // Check if any actual broadcasting occurred
    var did_broadcast = false;
    for (self.shape.dims, new_shape) |current_dim, target_size| {
        const current_size = switch (current_dim) {
            .concrete => |v| v,
            .dynamic => unreachable, // Already checked above
        };
        if (current_size == 1 and target_size != 1) {
            did_broadcast = true;
            break;
        }
    }
    
    // Materialize if broadcasting occurred
    if (did_broadcast) {
        return expanded_handle.makeContiguous();
    }
    
    return expanded_handle;
}

/// Make tensor contiguous if needed
pub fn makeContiguous(self: TensorHandle) !TensorHandle {
    // Check if current view requires data copy
    if (!self.shape.isContiguous()) {
        // Need data copy - create NEW node with contiguous layout
        const new_node_id = try self.graph.createNode(.{ .compute = .contiguous }, &.{self.node_id}, self.dtype);
        const allocator = self.graph.arena.allocator();
        
        // Compute effective dimensions after applying mask (for slicing)
        var effective_dims = try allocator.alloc(SymbolicDim, self.shape.dims.len);
        if (self.shape.mask) |mask| {
            // If there's a mask (from slicing), compute the actual output dimensions
            for (mask, 0..) |bounds, i| {
                const start = switch (bounds[0]) {
                    .concrete => |v| v,
                    else => 0, // Default to 0 for symbolic
                };
                const end = switch (bounds[1]) {
                    .concrete => |v| v,
                    else => switch (self.shape.dims[i]) {
                        .concrete => |v| v,
                        else => return error.SymbolicSliceNotSupported,
                    },
                };
                effective_dims[i] = SymbolicDim{ .concrete = end - start };
            }
        } else {
            // No mask, use original dimensions
            for (self.shape.dims, 0..) |dim, i| {
                effective_dims[i] = dim;
            }
        }
        
        // Create a fresh shape tracker for the contiguous tensor with effective dimensions
        var contiguous_shape = try ShapeTracker.fromDims(effective_dims, allocator, &self.graph.symbolic_pool);
        
        // IMPORTANT: Preserve transpose information for gradient computation
        // Copy the indexes array from the original shape tracker to preserve transpose metadata
        if (self.shape.indexes.len > 0) {
            // Only preserve if the ranks match (same dimensionality)
            if (self.shape.indexes.len == contiguous_shape.dims.len) {
                allocator.free(contiguous_shape.indexes);
                contiguous_shape.indexes = try allocator.dupe(u8, self.shape.indexes);
            }
        }
        
        // Set shape metadata for compilation
        try self.graph.setNodeShapeMetadata(new_node_id, &.{self.shape}, contiguous_shape);
        
        return TensorHandle{
            .graph = self.graph,
            .node_id = new_node_id, // NEW data node
            .shape = contiguous_shape, // Contiguous shape view
            .dtype = self.dtype, // Same data type
        };
    }
    // Already contiguous - return same handle
    return self;
}

/// Remove dimensions of size 1
pub fn squeeze(self: TensorHandle, axis: ?usize) !TensorHandle {
    // Squeeze needs contiguous because it calls reshape internally
    if (axis) |ax| {
        // Squeeze specific axis
        if (ax >= self.shape.dims.len) {
            std.log.err("squeeze: axis {} out of bounds for tensor with {} dimensions", 
                       .{ ax, self.shape.dims.len });
            return error.InvalidAxis;
        }
        
        const dim_size = switch (self.shape.dims[ax]) {
            .concrete => |v| v,
            else => {
                std.log.err("squeeze: dynamic dimension not supported at axis {}", .{ax});
                return error.DynamicDimensionNotSupported;
            },
        };
        
        if (dim_size != 1) {
            std.log.err("squeeze: cannot squeeze axis {} with size {} (must be 1)", 
                       .{ ax, dim_size });
            return error.InvalidSqueeze;
        }
        
        // Create new shape without the squeezed dimension
        const new_shape = try self.graph.arena.allocator().alloc(i64, self.shape.dims.len - 1);
        var j: usize = 0;
        for (self.shape.dims, 0..) |dim, i| {
            if (i != ax) {
                new_shape[j] = switch (dim) {
                    .concrete => |v| v,
                    else => {
                        std.log.err("squeeze: dynamic dimension not supported at index {}", .{i});
                        return error.DynamicDimensionNotSupported;
                    },
                };
                j += 1;
            }
        }
        
        return self.reshape(new_shape);
    } else {
        // Squeeze all dimensions of size 1
        var num_squeezed: usize = 0;
        for (self.shape.dims, 0..) |dim, i| {
            const size = switch (dim) {
                .concrete => |v| v,
                else => {
                    std.log.err("squeeze: dynamic dimension not supported at index {} when squeezing all", .{i});
                    return error.DynamicDimensionNotSupported;
                },
            };
            if (size == 1) num_squeezed += 1;
        }
        
        if (num_squeezed == 0) return self; // No dimensions to squeeze
        
        const new_shape = try self.graph.arena.allocator().alloc(i64, self.shape.dims.len - num_squeezed);
        var j: usize = 0;
        for (self.shape.dims, 0..) |dim, i| {
            const size = switch (dim) {
                .concrete => |v| v,
                else => {
                    std.log.err("squeeze: dynamic dimension not supported at index {} when building new shape", .{i});
                    return error.DynamicDimensionNotSupported;
                },
            };
            if (size != 1) {
                new_shape[j] = size;
                j += 1;
            }
        }
        
        return self.reshape(new_shape);
    }
}

/// Add dimension of size 1
pub fn unsqueeze(self: TensorHandle, axis: usize) !TensorHandle {
    // Unsqueeze needs contiguous (like Luminal)
    const contiguous_self = try self.makeContiguous();
    if (axis > contiguous_self.shape.dims.len) {
        std.log.err("unsqueeze: axis {} out of bounds for tensor with {} dimensions", 
                   .{ axis, contiguous_self.shape.dims.len });
        return error.InvalidAxis;
    }
    
    const new_shape = try contiguous_self.graph.arena.allocator().alloc(i64, contiguous_self.shape.dims.len + 1);
    
    // Copy dimensions, inserting 1 at the specified axis
    var j: usize = 0;
    for (0..new_shape.len) |i| {
        if (i == axis) {
            new_shape[i] = 1;
        } else {
            new_shape[i] = switch (contiguous_self.shape.dims[j]) {
                .concrete => |v| v,
                else => {
                    std.log.err("unsqueeze: dynamic dimension not supported at index {}", .{j});
                    return error.DynamicDimensionNotSupported;
                },
            };
            j += 1;
        }
    }
    
    // We already made it contiguous, so use contiguous_self
    return contiguous_self.reshape(new_shape);
}

/// Flatten tensor to 1D
pub fn flatten(self: TensorHandle) !TensorHandle {
    return self.reshape(&.{-1});
}

/// Flatten tensor starting from a specific dimension
pub fn flattenDims(self: TensorHandle, start_dim: usize, end_dim: usize) !TensorHandle {
    if (start_dim >= self.shape.dims.len or end_dim >= self.shape.dims.len) {
        std.log.err("flattenDims: axis out of bounds - start: {}, end: {}, dims: {}", 
                   .{ start_dim, end_dim, self.shape.dims.len });
        return error.InvalidAxis;
    }
    if (start_dim > end_dim) {
        std.log.err("flattenDims: invalid range - start ({}) > end ({})", 
                   .{ start_dim, end_dim });
        return error.InvalidRange;
    }
    
    // Calculate the new shape
    const num_dims_to_flatten = end_dim - start_dim + 1;
    const new_rank = self.shape.dims.len - num_dims_to_flatten + 1;
    const new_shape = try self.graph.arena.allocator().alloc(i64, new_rank);
    
    // Copy dimensions before start_dim
    for (0..start_dim) |i| {
        new_shape[i] = switch (self.shape.dims[i]) {
            .concrete => |v| v,
            else => {
                std.log.err("flattenDims: dynamic dimension not supported at index {} (before flatten)", .{i});
                return error.DynamicDimensionNotSupported;
            },
        };
    }
    
    // Calculate flattened dimension size
    var flattened_size: i64 = 1;
    for (start_dim..end_dim + 1) |i| {
        const dim_size = switch (self.shape.dims[i]) {
            .concrete => |v| v,
            else => {
                std.log.err("flattenDims: dynamic dimension not supported at index {} (in flatten range)", .{i});
                return error.DynamicDimensionNotSupported;
            },
        };
        flattened_size *= dim_size;
    }
    new_shape[start_dim] = flattened_size;
    
    // Copy dimensions after end_dim
    for (end_dim + 1..self.shape.dims.len) |i| {
        new_shape[start_dim + 1 + (i - end_dim - 1)] = switch (self.shape.dims[i]) {
            .concrete => |v| v,
            else => {
                std.log.err("flattenDims: dynamic dimension not supported at index {} (after flatten)", .{i});
                return error.DynamicDimensionNotSupported;
            },
        };
    }
    
    return self.reshape(new_shape);
}

/// Add a new dimension with specified size (Luminal-style expand_dim)
/// This is more flexible than unsqueeze as it can create dimensions with size > 1
/// The dimension is "fake" (broadcasted) - no data is copied
pub fn expandDim(self: TensorHandle, axis: usize, size: i64) !TensorHandle {
    if (axis > self.shape.dims.len) {
        std.log.err("expandDim: axis {} out of bounds for tensor with {} dimensions", 
                   .{ axis, self.shape.dims.len });
        return error.InvalidAxis;
    }
    
    if (size < 1) {
        std.log.err("expandDim: size must be >= 1, got {}", .{size});
        return error.InvalidSize;
    }
    
    // If size is 1, this is just unsqueeze
    if (size == 1) {
        return self.unsqueeze(axis);
    }
    
    // For size > 1, we need to:
    // 1. First unsqueeze to add dimension of size 1
    // 2. Then expand that dimension to the desired size
    const unsqueezed = try self.unsqueeze(axis);
    
    // Build new shape with expanded dimension
    const new_shape = try self.graph.arena.allocator().alloc(i64, unsqueezed.shape.dims.len);
    for (unsqueezed.shape.dims, 0..) |dim, i| {
        if (i == axis) {
            new_shape[i] = size;
        } else {
            new_shape[i] = switch (dim) {
                .concrete => |v| v,
                else => {
                    std.log.err("expandDim: dynamic dimension not supported", .{});
                    return error.DynamicDimensionNotSupported;
                },
            };
        }
    }
    
    return unsqueezed.expand(new_shape);
}

/// Broadcast tensor to a target shape
/// This is a convenience function that automatically expands dimensions to match the target shape
pub fn broadcast(self: TensorHandle, target_shape: []const i64) !TensorHandle {
    const current_dims = self.shape.dims;
    const allocator = self.graph.arena.allocator();
    
    // Validate that broadcasting is possible
    if (target_shape.len < current_dims.len) {
        std.log.err("broadcast: target shape rank {} is less than current rank {}", 
                   .{ target_shape.len, current_dims.len });
        return error.InvalidBroadcast;
    }
    
    // Start with the current tensor
    var result = self;
    
    // First, add leading dimensions of size 1 if needed
    const rank_diff = target_shape.len - current_dims.len;
    for (0..rank_diff) |_| {
        result = try result.unsqueeze(0);
    }
    
    // Allocate new_shape once outside the loop
    var new_shape = try allocator.alloc(i64, result.shape.dims.len);
    defer allocator.free(new_shape);
    
    // Now expand dimensions that are size 1 to match target
    for (0..target_shape.len) |i| {
        // Handle both concrete and symbolic dimensions safely
        const dim_info = if (i >= rank_diff) result.shape.dims[i] else shape_mod.SymbolicDim{ .concrete = 1 };
        
        switch (dim_info) {
            .concrete => |current_dim| {
                if (current_dim == 1 and target_shape[i] != 1) {
                    // Need to expand this dimension
                    for (0..result.shape.dims.len) |j| {
                        new_shape[j] = switch (result.shape.dims[j]) {
                            .concrete => |v| v,
                            .dynamic => {
                                std.log.err("broadcast: cannot expand with symbolic dimension at index {}", .{j});
                                return error.SymbolicDimensionInBroadcast;
                            },
                        };
                    }
                    new_shape[i] = target_shape[i];
                    result = try result.expand(new_shape);
                } else if (current_dim != target_shape[i]) {
                    std.log.err("broadcast: dimension {} has size {} but target expects {}", 
                               .{ i, current_dim, target_shape[i] });
                    return error.InvalidBroadcast;
                }
            },
            .dynamic => {
                // For symbolic dimensions, we can't validate at compile time
                // We'll need runtime validation when the dimension is bound
                std.log.warn("broadcast: symbolic dimension at index {} will be validated at runtime", .{i});
                // For now, optimistically continue - proper runtime validation would be added later
            },
        }
    }
    
    // If any dimensions were actually broadcast, we need to materialize the view
    // Check if the result shape differs from the original shape
    var needs_materialization = false;
    if (result.shape.dims.len != self.shape.dims.len) {
        needs_materialization = true;
    } else {
        for (result.shape.dims, self.shape.dims) |new_dim, old_dim| {
            const new_size = switch (new_dim) {
                .concrete => |v| v,
                .dynamic => continue, // Skip dynamic for now
            };
            const old_size = switch (old_dim) {
                .concrete => |v| v,
                .dynamic => continue, // Skip dynamic for now
            };
            if (new_size != old_size) {
                needs_materialization = true;
                break;
            }
        }
    }
    
    // Materialize if broadcasting occurred
    if (needs_materialization) {
        result = try result.makeContiguous();
    }
    
    return result;
}

/// Expand a tensor to match a target shape
/// If the tensor is scalar, it expands to the full target shape
/// Otherwise returns the tensor as-is (broadcasting happens automatically in operations)
pub fn expandToShape(self: TensorHandle, target_shape: ShapeTracker) !TensorHandle {
    const current_shape = self.shape;
    
    // If already correct shape, return as-is
    if (current_shape.dims.len == target_shape.dims.len) {
        var all_match = true;
        for (current_shape.dims, target_shape.dims) |curr, target| {
            if (curr != .concrete or target != .concrete) continue;
            if (curr.concrete != target.concrete) {
                all_match = false;
                break;
            }
        }
        if (all_match) return self;
    }
    
    // If scalar, expand to target shape
    if (current_shape.rank() == 0) {
        // Convert SymbolicDim array to i64 array for expand
        var dims: [8]i64 = undefined;
        for (target_shape.dims, 0..) |dim, i| {
            dims[i] = switch (dim) {
                .concrete => |v| v,
                .dynamic => return error.DynamicDimensionNotSupported,
            };
        }
        return try self.expand(dims[0..target_shape.dims.len]);
    }
    
    // Otherwise, return as-is (broadcasting happens automatically in operations)
    return self;
}

// Tests
test "reshape operation" {
    const testing = std.testing;
    const Graph = @import("graph").Graph;
    const placeholder = @import("../creation.zig").placeholder;
    
    var graph = try Graph.init(testing.allocator);
    defer graph.deinit();
    
    const x = try placeholder(&graph, &.{2, 3, 4}, .f32);
    
    // Test basic reshape
    const reshaped = try reshape(x, &.{6, 4});
    try testing.expectEqual(x.node_id, reshaped.node_id); // Same data node
    try testing.expectEqual(@as(usize, 2), reshaped.shape.dims.len);
    
    // Test reshape with -1
    const auto_reshaped = try reshape(x, &.{-1, 6});
    try testing.expectEqual(x.node_id, auto_reshaped.node_id);
    try testing.expectEqual(@as(i64, 4), auto_reshaped.shape.dims[0].concrete);
}

test "transpose operation" {
    const testing = std.testing;
    const Graph = @import("graph").Graph;
    const placeholder = @import("../creation.zig").placeholder;
    
    var graph = try Graph.init(testing.allocator);
    defer graph.deinit();
    
    const x = try placeholder(&graph, &.{2, 3, 4}, .f32);
    
    // Test explicit transpose
    const transposed = try transpose(x, &.{2, 0, 1});
    // Transpose should NOT materialize - it's just a view transformation
    try testing.expect(x.node_id == transposed.node_id); // Same node, different view
    try testing.expectEqual(@as(i64, 4), transposed.shape.dims[0].concrete);
    try testing.expectEqual(@as(i64, 2), transposed.shape.dims[1].concrete);
    try testing.expectEqual(@as(i64, 3), transposed.shape.dims[2].concrete);
    
    // Test default transpose (reverse all dims)
    const default_transposed = try transpose(x, null);
    try testing.expect(x.node_id == default_transposed.node_id); // Same node, different view
    
    // Test transposeContiguous - this SHOULD create a new node
    const transposed_contiguous = try transposeContiguous(x, &.{2, 0, 1});
    try testing.expect(x.node_id != transposed_contiguous.node_id); // Different nodes due to materialization
}

test "expand operation" {
    const testing = std.testing;
    const Graph = @import("graph").Graph;
    const placeholder = @import("../creation.zig").placeholder;
    
    var graph = try Graph.init(testing.allocator);
    defer graph.deinit();
    
    // Test broadcasting from [1, 3] to [5, 3]
    const x = try placeholder(&graph, &.{1, 3}, .f32);
    const expanded = try expand(x, &.{5, 3});
    
    // Expand now materializes when broadcasting occurs, so it creates a new node
    try testing.expect(x.node_id != expanded.node_id); // Different nodes due to materialization
    try testing.expectEqual(@as(i64, 5), expanded.shape.dims[0].concrete);
    try testing.expectEqual(@as(i64, 3), expanded.shape.dims[1].concrete);
    
    // After materialization, the shape is contiguous, so no fake dimensions
    // The broadcasting has been materialized into actual data
    
    // Test invalid broadcast
    try testing.expectError(error.InvalidBroadcast, expand(x, &.{5, 4}));
}

test "squeeze operation" {
    const testing = std.testing;
    const Graph = @import("graph").Graph;
    const placeholder = @import("../creation.zig").placeholder;

    var graph = try Graph.init(testing.allocator);
    defer graph.deinit();

    // Create test tensor
    const x = try placeholder(&graph, &.{ 2, 1, 3, 1, 4 }, .f32);

    // Test squeeze all
    const squeezed = try squeeze(x, null);
    try testing.expectEqual(@as(usize, 3), squeezed.shape.dims.len);
    try testing.expectEqual(@as(i64, 2), squeezed.shape.dims[0].concrete);
    try testing.expectEqual(@as(i64, 3), squeezed.shape.dims[1].concrete);
    try testing.expectEqual(@as(i64, 4), squeezed.shape.dims[2].concrete);

    // Test squeeze specific axis
    const squeezed_axis = try squeeze(x, 1);
    try testing.expectEqual(@as(usize, 4), squeezed_axis.shape.dims.len);
    try testing.expectEqual(@as(i64, 2), squeezed_axis.shape.dims[0].concrete);
    try testing.expectEqual(@as(i64, 3), squeezed_axis.shape.dims[1].concrete);
    try testing.expectEqual(@as(i64, 1), squeezed_axis.shape.dims[2].concrete);
    try testing.expectEqual(@as(i64, 4), squeezed_axis.shape.dims[3].concrete);
}

test "unsqueeze operation" {
    const testing = std.testing;
    const Graph = @import("graph").Graph;
    const placeholder = @import("../creation.zig").placeholder;

    var graph = try Graph.init(testing.allocator);
    defer graph.deinit();

    const x = try placeholder(&graph, &.{ 2, 3 }, .f32);

    // Test unsqueeze at different positions
    const unsqueezed_0 = try unsqueeze(x, 0);
    try testing.expectEqual(@as(usize, 3), unsqueezed_0.shape.dims.len);
    try testing.expectEqual(@as(i64, 1), unsqueezed_0.shape.dims[0].concrete);
    try testing.expectEqual(@as(i64, 2), unsqueezed_0.shape.dims[1].concrete);
    try testing.expectEqual(@as(i64, 3), unsqueezed_0.shape.dims[2].concrete);

    const unsqueezed_2 = try unsqueeze(x, 2);
    try testing.expectEqual(@as(usize, 3), unsqueezed_2.shape.dims.len);
    try testing.expectEqual(@as(i64, 2), unsqueezed_2.shape.dims[0].concrete);
    try testing.expectEqual(@as(i64, 3), unsqueezed_2.shape.dims[1].concrete);
    try testing.expectEqual(@as(i64, 1), unsqueezed_2.shape.dims[2].concrete);
}

test "flatten operations" {
    const testing = std.testing;
    const Graph = @import("graph").Graph;
    const placeholder = @import("../creation.zig").placeholder;

    var graph = try Graph.init(testing.allocator);
    defer graph.deinit();

    const x = try placeholder(&graph, &.{ 2, 3, 4 }, .f32);

    // Test complete flatten
    const flat = try flatten(x);
    try testing.expectEqual(@as(usize, 1), flat.shape.dims.len);
    try testing.expectEqual(@as(i64, 24), flat.shape.dims[0].concrete);

    // Test flatten range
    const flat_range = try flattenDims(x, 1, 2);
    try testing.expectEqual(@as(usize, 2), flat_range.shape.dims.len);
    try testing.expectEqual(@as(i64, 2), flat_range.shape.dims[0].concrete);
    try testing.expectEqual(@as(i64, 12), flat_range.shape.dims[1].concrete);
}

test "slice operation" {
    const testing = std.testing;
    const Graph = @import("graph").Graph;
    const placeholder = @import("../creation.zig").placeholder;
    
    var graph = try Graph.init(testing.allocator);
    defer graph.deinit();
    
    const x = try placeholder(&graph, &.{4, 5}, .f32);
    
    // Test slice
    const sliced = try slice(x, &.{1, 1}, &.{3, 4});
    // Slice now materializes, so it creates a new node
    try testing.expect(x.node_id != sliced.node_id); // Different nodes due to materialization
    try testing.expectEqual(@as(usize, 2), sliced.shape.dims.len);
    // After materialization, the shape is contiguous with effective dimensions [2, 3]
    // Slice [1:3, 1:4] from [4,5] results in [2,3]
    try testing.expectEqual(@as(i64, 2), sliced.shape.dims[0].concrete);
    try testing.expectEqual(@as(i64, 3), sliced.shape.dims[1].concrete);
}
