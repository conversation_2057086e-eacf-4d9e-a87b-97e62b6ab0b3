const std = @import("std");
const types = @import("types");
const DataType = types.DataType;
const creation = @import("../creation.zig");
const constant = creation.constant;

// Import TensorHandle type reference
const TensorHandle = @import("../handle.zig").TensorHandle;

/// Logical NOT: !x = 1 - x (for boolean tensors)
pub fn logicalNot(self: TensorHandle) !TensorHandle {
    const one = try constant(self.graph, 1.0, self.dtype);
    return one.subtract(self);
}

/// Logical AND: x && y = x * y (for boolean tensors)
pub fn logicalAnd(self: TensorHandle, other: TensorHandle) !TensorHandle {
    return self.mul(other);
}

/// Logical OR: x || y = 1 - (1-x)*(1-y) = x + y - x*y
pub fn logicalOr(self: TensorHandle, other: TensorHandle) !TensorHandle {
    const or_sum = try self.add(other);
    const or_prod = try self.mul(other);
    return or_sum.subtract(or_prod);
}

/// Select operation: where(condition, x, y) = condition * x + (1 - condition) * y
/// Condition should be float tensor with 1.0 for true, 0.0 for false
pub fn select(condition: TensorHandle, x: TensorHandle, y: TensorHandle) !TensorHandle {
    const one = try constant(condition.graph, 1.0, condition.dtype);
    const inv_condition = try one.subtract(condition);
    const x_term = try condition.mul(x);
    const y_term = try inv_condition.mul(y);
    return x_term.add(y_term);
}

// Tests
test "logical operations" {
    const testing = std.testing;
    const Graph = @import("graph").Graph;
    const placeholder = @import("../creation.zig").placeholder;
    
    var graph = try Graph.init(testing.allocator);
    defer graph.deinit();
    
    // Create boolean tensors (using 0 and 1 values)
    const a = try placeholder(&graph, &.{2, 3}, .f32);
    const b = try placeholder(&graph, &.{2, 3}, .f32);
    
    // Test logical NOT
    const not_a = try logicalNot(a);
    try testing.expect(not_a.node_id != a.node_id);
    
    // Test logical AND
    const and_ab = try logicalAnd(a, b);
    try testing.expect(and_ab.node_id != a.node_id);
    
    // Test logical OR
    const or_ab = try logicalOr(a, b);
    try testing.expect(or_ab.node_id != a.node_id);
}

test "select operation" {
    const testing = std.testing;
    const Graph = @import("graph").Graph;
    const placeholder = @import("../creation.zig").placeholder;
    
    var graph = try Graph.init(testing.allocator);
    defer graph.deinit();
    
    const condition = try placeholder(&graph, &.{3, 3}, .f32);
    const x = try placeholder(&graph, &.{3, 3}, .f32);
    const y = try placeholder(&graph, &.{3, 3}, .f32);
    
    // Test select
    const result = try select(condition, x, y);
    try testing.expect(result.node_id != condition.node_id);
    try testing.expectEqual(x.shape.dims.len, result.shape.dims.len);
}