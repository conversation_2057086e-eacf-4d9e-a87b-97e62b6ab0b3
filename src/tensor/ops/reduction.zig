const std = @import("std");
const types = @import("types");
const DataType = types.DataType;
const creation = @import("../creation.zig");
const constant = creation.constant;

// Import TensorHandle type reference
const TensorHandle = @import("../handle.zig").TensorHandle;

/// Sum reduction with keepdims option (defaults to true)
pub fn sum(self: TensorHandle, axis: usize, keepdims: bool) !TensorHandle {
    return self.sumReduce(axis, keepdims);
}

/// Max reduction with keepdims option (defaults to true)
pub fn max(self: TensorHandle, axis: usize, keepdims: bool) !TensorHandle {
    return self.maxReduce(axis, keepdims);
}

/// Min reduction with keepdims option (defaults to true)
pub fn min(self: TensorHandle, axis: usize, keepdims: bool) !TensorHandle {
    return minReduce(self, axis, keepdims);
}

/// Mean reduction with keepdims option (defaults to true)
pub fn mean(self: TensorHandle, axis: usize, keepdims: bool) !TensorHandle {
    const sum_val = try self.sumReduce(axis, keepdims);
    const axis_size = switch (self.shape.dims[axis]) {
        .concrete => |v| v,
        else => {
            std.log.err("mean: dynamic dimension not supported for axis {}", .{axis});
            return error.DynamicDimensionNotSupported;
        },
    };
    const count = try constant(self.graph, @floatFromInt(axis_size), self.dtype);
    return sum_val.divide(count);
}

/// Product reduction
pub fn prod(self: TensorHandle, axis: usize, keepdims: bool) !TensorHandle {
    // prod(x) = exp(sum(log(x)))
    const log_x = try self.log();
    const sum_log = try log_x.sumReduce(axis, keepdims);
    return sum_log.exp();
}

/// Variance calculation
pub fn variance(self: TensorHandle, axis: usize, keepdims: bool) !TensorHandle {
    // var(x) = mean(x^2) - mean(x)^2
    const mean_x = try mean(self, axis, keepdims);
    const x_squared = try self.square();
    const mean_x_squared = try mean(x_squared, axis, keepdims);
    const mean_squared = try mean_x.square();
    return mean_x_squared.subtract(mean_squared);
}

/// Standard deviation
pub fn stddev(self: TensorHandle, axis: usize, keepdims: bool) !TensorHandle {
    const variance_val = try variance(self, axis, keepdims);
    return variance_val.sqrt();
}

/// Argmax (returns indices of maximum values)
pub fn argmax(self: TensorHandle, axis: usize) !TensorHandle {
    // TODO: Implement argmax properly - needs special handling
    // For now, return error
    _ = self;
    _ = axis;
    std.log.err("argmax: operation not yet implemented", .{});
    return error.NotImplemented;
}

/// Argmin (returns indices of minimum values)
pub fn argmin(self: TensorHandle, axis: usize) !TensorHandle {
    // TODO: Implement argmin properly - needs special handling
    // For now, return error
    _ = self;
    _ = axis;
    std.log.err("argmin: operation not yet implemented", .{});
    return error.NotImplemented;
}

/// Sum over all elements
pub fn sumAll(self: TensorHandle) !TensorHandle {
    var result = self;
    var axis: usize = self.shape.dims.len;
    while (axis > 0) {
        axis -= 1;
        result = try result.sumReduce(axis, true);
    }
    return result;
}

/// Mean over all elements
pub fn meanAll(self: TensorHandle) !TensorHandle {
    const sum_val = try sumAll(self);
    var total_elements: i64 = 1;
    for (self.shape.dims) |dim| {
        switch (dim) {
            .concrete => |v| total_elements *= v,
            else => {
                std.log.err("meanAll: dynamic dimensions not supported", .{});
                return error.DynamicDimensionNotSupported;
            },
        }
    }
    const count = try constant(self.graph, @floatFromInt(total_elements), self.dtype);
    return sum_val.divide(count);
}

/// Product over all elements
pub fn prodAll(self: TensorHandle) !TensorHandle {
    var result = self;
    var axis: usize = self.shape.dims.len;
    while (axis > 0) {
        axis -= 1;
        result = try prod(result, axis, true);
    }
    return result;
}

/// Max reduction over all elements
pub fn maxAll(self: TensorHandle) !TensorHandle {
    var result = self;
    var axis: usize = self.shape.dims.len;
    while (axis > 0) {
        axis -= 1;
        result = try result.maxReduce(axis, true);
    }
    return result;
}

/// Min reduction over all elements
pub fn minAll(self: TensorHandle) !TensorHandle {
    var result = self;
    var axis: usize = self.shape.dims.len;
    while (axis > 0) {
        axis -= 1;
        result = try minReduce(result, axis, true);
    }
    return result;
}

/// Min reduction along axis with keepdims option
pub fn minReduce(self: TensorHandle, axis: usize, keepdims: bool) !TensorHandle {
    // min(x) = -max(-x)
    const neg_self = try self.neg();
    const max_neg = try neg_self.maxReduce(axis, keepdims);
    return max_neg.neg();
}

/// L1 norm
pub fn norm1(self: TensorHandle, axis: ?usize) !TensorHandle {
    const abs_val = try self.abs();
    if (axis) |a| {
        return abs_val.sumReduce(a, true);
    } else {
        return sumAll(abs_val);
    }
}

/// L2 norm
pub fn norm2(self: TensorHandle, axis: ?usize) !TensorHandle {
    const squared = try self.square();
    const sum_val = if (axis) |a| try squared.sumReduce(a, true) else try sumAll(squared);
    return sum_val.sqrt();
}

/// Frobenius norm (same as L2 norm for matrices)
pub fn normFro(self: TensorHandle) !TensorHandle {
    return norm2(self, null);
}

/// Count non-zero elements
pub fn countNonzero(self: TensorHandle, axis: ?usize) !TensorHandle {
    // count_nonzero(x) = sum(x != 0)
    const zero = try constant(self.graph, 0.0, self.dtype);
    const not_zero = try self.notEqual(zero);
    if (axis) |a| {
        return not_zero.sumReduce(a, true);
    } else {
        return sumAll(not_zero);
    }
}

/// Cumulative sum along the last dimension
/// For now, this is a simplified implementation that creates the cumsum pattern
/// In a full implementation, this would be a primitive operation or use scan
pub fn cumsumLastDim(self: TensorHandle) !TensorHandle {
    // For the initial implementation, we'll create a special node that represents cumsum
    // This will be properly lowered by the backend
    
    // Ensure tensor is contiguous
    const contiguous_self = try self.makeContiguous();
    
    // For now, we'll create a placeholder implementation
    // The actual cumulative sum would be computed during execution
    // This allows arange to work without circular dependencies
    
    std.log.warn("cumsumLastDim: returning input tensor - cumulative sum not yet implemented", .{});
    // TODO: Implement cumsum as a primitive or decompose into scan operations
    return contiguous_self;
}

// Tests
test "basic reductions" {
    const testing = std.testing;
    const Graph = @import("graph").Graph;
    const placeholder = @import("../creation.zig").placeholder;
    
    var graph = try Graph.init(testing.allocator);
    defer graph.deinit();
    
    const x = try placeholder(&graph, &.{3, 4}, .f32);
    
    // Test sum (keeps dimension with size 1)
    const sum_axis1 = try sum(x, 1, true);
    try testing.expect(sum_axis1.node_id != x.node_id);
    try testing.expectEqual(@as(usize, 2), sum_axis1.shape.dims.len);
    try testing.expectEqual(@as(i64, 3), sum_axis1.shape.dims[0].concrete);
    try testing.expectEqual(@as(i64, 1), sum_axis1.shape.dims[1].concrete);
    
    // Test max
    const max_axis0 = try max(x, 0, true);
    try testing.expect(max_axis0.node_id != x.node_id);
    
    // Test min
    const min_axis1 = try min(x, 1, true);
    try testing.expect(min_axis1.node_id != x.node_id);
}

test "statistical reductions" {
    const testing = std.testing;
    const Graph = @import("graph").Graph;
    const placeholder = @import("../creation.zig").placeholder;
    
    var graph = try Graph.init(testing.allocator);
    defer graph.deinit();
    
    const x = try placeholder(&graph, &.{2, 3, 4}, .f32);
    
    // Test mean (keeps dimension with size 1)
    const mean_x = try mean(x, 1, true);
    try testing.expect(mean_x.node_id != x.node_id);
    try testing.expectEqual(@as(usize, 3), mean_x.shape.dims.len);
    try testing.expectEqual(@as(i64, 2), mean_x.shape.dims[0].concrete);
    try testing.expectEqual(@as(i64, 1), mean_x.shape.dims[1].concrete);
    try testing.expectEqual(@as(i64, 4), mean_x.shape.dims[2].concrete);
    
    // Test variance
    const var_x = try variance(x, 2, true);
    try testing.expect(var_x.node_id != x.node_id);
    
    // Test stddev
    const std_x = try stddev(x, 2, true);
    try testing.expect(std_x.node_id != x.node_id);
}

test "global reductions" {
    const testing = std.testing;
    const Graph = @import("graph").Graph;
    const placeholder = @import("../creation.zig").placeholder;
    
    var graph = try Graph.init(testing.allocator);
    defer graph.deinit();
    
    const x = try placeholder(&graph, &.{2, 3}, .f32);
    
    // Test sumAll (reduces all dimensions)
    const sum_all = try sumAll(x);
    try testing.expect(sum_all.node_id != x.node_id);
    try testing.expectEqual(@as(usize, 2), sum_all.shape.dims.len); // Still 2D but [1, 1]
    try testing.expectEqual(@as(i64, 1), sum_all.shape.dims[0].concrete);
    try testing.expectEqual(@as(i64, 1), sum_all.shape.dims[1].concrete);
    
    // Test meanAll
    const mean_all = try meanAll(x);
    try testing.expect(mean_all.node_id != x.node_id);
    
    // Test maxAll
    const max_all = try maxAll(x);
    try testing.expect(max_all.node_id != x.node_id);
}

test "norm operations" {
    const testing = std.testing;
    const Graph = @import("graph").Graph;
    const placeholder = @import("../creation.zig").placeholder;
    
    var graph = try Graph.init(testing.allocator);
    defer graph.deinit();
    
    const x = try placeholder(&graph, &.{3, 3}, .f32);
    
    // Test L1 norm
    const l1 = try norm1(x, null);
    try testing.expect(l1.node_id != x.node_id);
    
    // Test L2 norm
    const l2 = try norm2(x, 0);
    try testing.expect(l2.node_id != x.node_id);
    
    // Test Frobenius norm
    const fro = try normFro(x);
    try testing.expect(fro.node_id != x.node_id);
}