const std = @import("std");
const types = @import("types");
const DataType = types.DataType;
const NodeId = types.NodeId;
const shape_mod = @import("shape");
const inferBroadcastShape = shape_mod.inferBroadcastShape;
const inferReduceShape = shape_mod.inferReduceShape;

// Import TensorHandle type reference
const TensorHandle = @import("../handle.zig").TensorHandle;

/// Element-wise addition
pub fn add(self: TensorHandle, other: TensorHandle) !TensorHandle {
    const result_shape = inferBroadcastShape(&self.shape, &other.shape, self.graph.arena.allocator(), &self.graph.symbolic_pool) catch |err| {
        std.log.err("add: failed to infer broadcast shape between shapes {} and {}: {}", .{self.shape.dims.len, other.shape.dims.len, err});
        return err;
    };
    const result_id = try self.graph.createNode(.{ .compute = .add }, &.{ self.node_id, other.node_id }, self.dtype);
    
    // Set shape metadata for compilation
    try self.graph.setNodeShapeMetadata(result_id, &.{ self.shape, other.shape }, result_shape);
    
    return TensorHandle{
        .graph = self.graph,
        .node_id = result_id,
        .shape = result_shape,
        .dtype = self.dtype,
    };
}

/// Element-wise multiplication
pub fn mul(self: TensorHandle, other: TensorHandle) !TensorHandle {
    const result_shape = inferBroadcastShape(&self.shape, &other.shape, self.graph.arena.allocator(), &self.graph.symbolic_pool) catch |err| {
        std.log.err("mul: failed to infer broadcast shape between shapes {} and {}: {}", .{self.shape.dims.len, other.shape.dims.len, err});
        return err;
    };
    const result_id = try self.graph.createNode(.{ .compute = .mul }, &.{ self.node_id, other.node_id }, self.dtype);
    
    // Set shape metadata for compilation
    try self.graph.setNodeShapeMetadata(result_id, &.{ self.shape, other.shape }, result_shape);
    
    return TensorHandle{
        .graph = self.graph,
        .node_id = result_id,
        .shape = result_shape,
        .dtype = self.dtype,
    };
}

/// Element-wise modulo
pub fn mod(self: TensorHandle, other: TensorHandle) !TensorHandle {
    const result_shape = inferBroadcastShape(&self.shape, &other.shape, self.graph.arena.allocator(), &self.graph.symbolic_pool) catch |err| {
        std.log.err("mod: failed to infer broadcast shape between shapes {} and {}: {}", .{self.shape.dims.len, other.shape.dims.len, err});
        return err;
    };
    const result_id = try self.graph.createNode(.{ .compute = .mod }, &.{ self.node_id, other.node_id }, self.dtype);
    
    // Set shape metadata for compilation
    try self.graph.setNodeShapeMetadata(result_id, &.{ self.shape, other.shape }, result_shape);
    
    return TensorHandle{
        .graph = self.graph,
        .node_id = result_id,
        .shape = result_shape,
        .dtype = self.dtype,
    };
}

/// Element-wise less than comparison
pub fn lessThan(self: TensorHandle, other: TensorHandle) !TensorHandle {
    const result_shape = inferBroadcastShape(&self.shape, &other.shape, self.graph.arena.allocator(), &self.graph.symbolic_pool) catch |err| {
        std.log.err("lessThan: failed to infer broadcast shape between shapes {} and {}: {}", .{self.shape.dims.len, other.shape.dims.len, err});
        return err;
    };
    // Use f32 type to match the kernel output (1.0 or 0.0)
    const result_id = try self.graph.createNode(.{ .compute = .less_than }, &.{ self.node_id, other.node_id }, .f32);
    
    // Set shape metadata for compilation
    try self.graph.setNodeShapeMetadata(result_id, &.{ self.shape, other.shape }, result_shape);
    
    return TensorHandle{
        .graph = self.graph,
        .node_id = result_id,
        .shape = result_shape,
        .dtype = .f32,  // Changed from .bool to .f32
    };
}

/// Reciprocal (1/x)
pub fn recip(self: TensorHandle) !TensorHandle {
    const result_id = try self.graph.createNode(.{ .compute = .recip }, &.{self.node_id}, self.dtype);
    
    // Set shape metadata for compilation
    try self.graph.setNodeShapeMetadata(result_id, &.{self.shape}, self.shape);
    
    return TensorHandle{
        .graph = self.graph,
        .node_id = result_id,
        .shape = self.shape, // Same shape
        .dtype = self.dtype,
    };
}

/// Square root
pub fn sqrt(self: TensorHandle) !TensorHandle {
    const result_id = try self.graph.createNode(.{ .compute = .sqrt }, &.{self.node_id}, self.dtype);
    
    // Set shape metadata for compilation
    try self.graph.setNodeShapeMetadata(result_id, &.{self.shape}, self.shape);
    
    return TensorHandle{
        .graph = self.graph,
        .node_id = result_id,
        .shape = self.shape, // Same shape
        .dtype = self.dtype,
    };
}

/// Sine
pub fn sin(self: TensorHandle) !TensorHandle {
    const result_id = try self.graph.createNode(.{ .compute = .sin }, &.{self.node_id}, self.dtype);
    
    // Set shape metadata for compilation
    try self.graph.setNodeShapeMetadata(result_id, &.{self.shape}, self.shape);
    
    return TensorHandle{
        .graph = self.graph,
        .node_id = result_id,
        .shape = self.shape, // Same shape
        .dtype = self.dtype,
    };
}

/// Base-2 exponential
pub fn exp2(self: TensorHandle) !TensorHandle {
    const result_id = try self.graph.createNode(.{ .compute = .exp2 }, &.{self.node_id}, self.dtype);
    
    // Set shape metadata for compilation
    try self.graph.setNodeShapeMetadata(result_id, &.{self.shape}, self.shape);
    
    return TensorHandle{
        .graph = self.graph,
        .node_id = result_id,
        .shape = self.shape, // Same shape
        .dtype = self.dtype,
    };
}

/// Base-2 logarithm
pub fn log2(self: TensorHandle) !TensorHandle {
    const result_id = try self.graph.createNode(.{ .compute = .log2 }, &.{self.node_id}, self.dtype);
    
    // Set shape metadata for compilation
    try self.graph.setNodeShapeMetadata(result_id, &.{self.shape}, self.shape);
    
    return TensorHandle{
        .graph = self.graph,
        .node_id = result_id,
        .shape = self.shape, // Same shape
        .dtype = self.dtype,
    };
}

/// Sum reduction along axis with keepdims option
pub fn sumReduce(self: TensorHandle, axis: usize, keepdims: bool) !TensorHandle {
    if (axis >= self.shape.dims.len) {
        std.log.err("sumReduce: axis {} out of bounds for tensor with {} dimensions", .{axis, self.shape.dims.len});
        return error.InvalidAxis;
    }
    const result_shape = shape_mod.inferReduceShape(&self.shape, axis, keepdims, self.graph.arena.allocator(), &self.graph.symbolic_pool) catch |err| {
        std.log.err("sumReduce: failed to infer reduction shape for axis {}: {}", .{axis, err});
        return err;
    };
    const result_id = try self.graph.createNode(.{ .compute = .sum_reduce }, &.{self.node_id}, self.dtype);
    
    // Set complete metadata including semantic and shape information
    try self.graph.setReductionAxis(result_id, @intCast(axis));
    try self.graph.setReductionKeepdims(result_id, keepdims);
    
    // Set shape metadata for efficient compilation
    const node = self.graph.getNodeMut(result_id).?;
    if (node.metadata) |metadata| {
        // Reduction axis and keepdims already set, add shape information
        const input_shapes = try self.graph.arena.allocator().alloc(shape_mod.ShapeTracker, 1);
        input_shapes[0] = try self.shape.clone(self.graph.arena.allocator());
        metadata.input_shapes = input_shapes;
        metadata.output_shape = try result_shape.clone(self.graph.arena.allocator());
    }
    
    return TensorHandle{
        .graph = self.graph,
        .node_id = result_id,
        .shape = result_shape,
        .dtype = self.dtype,
    };
}

/// Max reduction along axis with keepdims option
pub fn maxReduce(self: TensorHandle, axis: usize, keepdims: bool) !TensorHandle {
    if (axis >= self.shape.dims.len) {
        std.log.err("maxReduce: axis {} out of bounds for tensor with {} dimensions", .{axis, self.shape.dims.len});
        return error.InvalidAxis;
    }
    const result_shape = shape_mod.inferReduceShape(&self.shape, axis, keepdims, self.graph.arena.allocator(), &self.graph.symbolic_pool) catch |err| {
        std.log.err("maxReduce: failed to infer reduction shape for axis {}: {}", .{axis, err});
        return err;
    };
    const result_id = try self.graph.createNode(.{ .compute = .max_reduce }, &.{self.node_id}, self.dtype);
    
    // Set complete metadata including semantic and shape information
    try self.graph.setReductionAxis(result_id, @intCast(axis));
    try self.graph.setReductionKeepdims(result_id, keepdims);
    
    // Set shape metadata for efficient compilation
    const node = self.graph.getNodeMut(result_id).?;
    if (node.metadata) |metadata| {
        // Reduction axis and keepdims already set, add shape information
        const input_shapes = try self.graph.arena.allocator().alloc(shape_mod.ShapeTracker, 1);
        input_shapes[0] = try self.shape.clone(self.graph.arena.allocator());
        metadata.input_shapes = input_shapes;
        metadata.output_shape = try result_shape.clone(self.graph.arena.allocator());
    }
    
    return TensorHandle{
        .graph = self.graph,
        .node_id = result_id,
        .shape = result_shape,
        .dtype = self.dtype,
    };
}

// Tests
test "primitive binary operations" {
    const testing = std.testing;
    const Graph = @import("graph").Graph;
    const placeholder = @import("../creation.zig").placeholder;
    
    var graph = try Graph.init(testing.allocator);
    defer graph.deinit();
    
    const a = try placeholder(&graph, &.{2, 3}, .f32);
    const b = try placeholder(&graph, &.{2, 3}, .f32);
    
    // Test add
    const sum = try add(a, b);
    try testing.expect(sum.node_id != a.node_id);
    try testing.expectEqual(a.shape.dims.len, sum.shape.dims.len);
    
    // Test mul
    const prod = try mul(a, b);
    try testing.expect(prod.node_id != a.node_id);
    
    // Test mod
    const remainder = try mod(a, b);
    try testing.expect(remainder.node_id != a.node_id);
    
    // Test lessThan
    const lt = try lessThan(a, b);
    try testing.expect(lt.node_id != a.node_id);
    try testing.expectEqual(DataType.f32, lt.dtype); // Returns f32 (0.0 or 1.0) to match kernel
}

test "primitive unary operations" {
    const testing = std.testing;
    const Graph = @import("graph").Graph;
    const placeholder = @import("../creation.zig").placeholder;
    
    var graph = try Graph.init(testing.allocator);
    defer graph.deinit();
    
    const x = try placeholder(&graph, &.{3, 3}, .f32);
    
    // Test recip
    const recip_x = try recip(x);
    try testing.expect(recip_x.node_id != x.node_id);
    
    // Test sqrt
    const sqrt_x = try sqrt(x);
    try testing.expect(sqrt_x.node_id != x.node_id);
    
    // Test sin
    const sin_x = try sin(x);
    try testing.expect(sin_x.node_id != x.node_id);
    
    // Test exp2
    const exp2_x = try exp2(x);
    try testing.expect(exp2_x.node_id != x.node_id);
    
    // Test log2
    const log2_x = try log2(x);
    try testing.expect(log2_x.node_id != x.node_id);
}

test "primitive reduction operations" {
    const testing = std.testing;
    const Graph = @import("graph").Graph;
    const placeholder = @import("../creation.zig").placeholder;
    
    var graph = try Graph.init(testing.allocator);
    defer graph.deinit();
    
    const x = try placeholder(&graph, &.{2, 3, 4}, .f32);
    
    // Test sumReduce with keepdims=true
    const sum_axis1 = try sumReduce(x, 1, true);
    try testing.expect(sum_axis1.node_id != x.node_id);
    try testing.expectEqual(@as(usize, 3), sum_axis1.shape.dims.len);
    try testing.expectEqual(@as(i64, 2), sum_axis1.shape.dims[0].concrete);
    try testing.expectEqual(@as(i64, 1), sum_axis1.shape.dims[1].concrete);
    try testing.expectEqual(@as(i64, 4), sum_axis1.shape.dims[2].concrete);
    
    // Test maxReduce with keepdims=true
    const max_axis2 = try maxReduce(x, 2, true);
    try testing.expect(max_axis2.node_id != x.node_id);
    try testing.expectEqual(@as(usize, 3), max_axis2.shape.dims.len);
    try testing.expectEqual(@as(i64, 2), max_axis2.shape.dims[0].concrete);
    try testing.expectEqual(@as(i64, 3), max_axis2.shape.dims[1].concrete);
    try testing.expectEqual(@as(i64, 1), max_axis2.shape.dims[2].concrete);
}