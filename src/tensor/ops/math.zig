const std = @import("std");
const types = @import("types");
const DataType = types.DataType;
const creation = @import("../creation.zig");
const constant = creation.constant;

// Import TensorHandle type reference
const TensorHandle = @import("../handle.zig").TensorHandle;

/// Natural exponential: e^x = 2^(x * log2(e))
pub fn exp(self: TensorHandle) !TensorHandle {
    const log2_e = try constant(self.graph, 1.44269504089, self.dtype); // log2(e)
    const x_log2_e = try self.mul(log2_e);
    return x_log2_e.exp2();
}

/// Natural logarithm: ln(x) = log2(x) / log2(e)
pub fn log(self: TensorHandle) !TensorHandle {
    const x_log2 = try self.log2();
    const log2_e = try constant(self.graph, 1.44269504089, self.dtype);
    return x_log2.divide(log2_e);
}

/// Alias for natural logarithm
pub fn ln(self: TensorHandle) !TensorHandle {
    return log(self);
}

/// Cosine: cos(x) = sin(π/2 - x)
pub fn cos(self: TensorHandle) !TensorHandle {
    const half_pi = try constant(self.graph, 1.5707963268, self.dtype);
    const shifted = try half_pi.subtract(self);
    return shifted.sin();
}

/// Tangent: tan(x) = sin(x) / cos(x)
pub fn tan(self: TensorHandle) !TensorHandle {
    const sin_x = try self.sin();
    const cos_x = try cos(self);
    return sin_x.divide(cos_x);
}

/// Hyperbolic sine: sinh(x) = (e^x - e^(-x)) / 2
pub fn sinh(self: TensorHandle) !TensorHandle {
    const exp_x = try exp(self);
    const neg_x = try self.neg();
    const exp_neg_x = try exp(neg_x);
    const diff = try exp_x.subtract(exp_neg_x);
    const two = try constant(self.graph, 2.0, self.dtype);
    return diff.divide(two);
}

/// Hyperbolic cosine: cosh(x) = (e^x + e^(-x)) / 2
pub fn cosh(self: TensorHandle) !TensorHandle {
    const exp_x = try exp(self);
    const neg_x = try self.neg();
    const exp_neg_x = try exp(neg_x);
    const exp_sum = try exp_x.add(exp_neg_x);
    const two = try constant(self.graph, 2.0, self.dtype);
    return exp_sum.divide(two);
}

// Tests
test "exponential and logarithm" {
    const testing = std.testing;
    const Graph = @import("graph").Graph;
    const placeholder = @import("../creation.zig").placeholder;
    
    var graph = try Graph.init(testing.allocator);
    defer graph.deinit();
    
    const x = try placeholder(&graph, &.{2, 3}, .f32);
    
    // Test exp
    const exp_x = try exp(x);
    try testing.expect(exp_x.node_id != x.node_id);
    try testing.expectEqual(x.shape.dims.len, exp_x.shape.dims.len);
    
    // Test log/ln
    const log_x = try log(x);
    try testing.expect(log_x.node_id != x.node_id);
    
    const ln_x = try ln(x);
    try testing.expect(ln_x.node_id != x.node_id);
}

test "trigonometric functions" {
    const testing = std.testing;
    const Graph = @import("graph").Graph;
    const placeholder = @import("../creation.zig").placeholder;
    
    var graph = try Graph.init(testing.allocator);
    defer graph.deinit();
    
    const x = try placeholder(&graph, &.{3, 3}, .f32);
    
    // Test cos
    const cos_x = try cos(x);
    try testing.expect(cos_x.node_id != x.node_id);
    
    // Test tan
    const tan_x = try tan(x);
    try testing.expect(tan_x.node_id != x.node_id);
}

test "hyperbolic functions" {
    const testing = std.testing;
    const Graph = @import("graph").Graph;
    const placeholder = @import("../creation.zig").placeholder;
    
    var graph = try Graph.init(testing.allocator);
    defer graph.deinit();
    
    const x = try placeholder(&graph, &.{2, 2}, .f32);
    
    // Test sinh
    const sinh_x = try sinh(x);
    try testing.expect(sinh_x.node_id != x.node_id);
    
    // Test cosh
    const cosh_x = try cosh(x);
    try testing.expect(cosh_x.node_id != x.node_id);
}