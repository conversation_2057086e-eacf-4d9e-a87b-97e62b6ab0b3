const std = @import("std");
const types = @import("types");
const DataType = types.DataType;
const creation = @import("../creation.zig");
const constant = creation.constant;

// Import TensorHandle type reference
const TensorHandle = @import("../handle.zig").TensorHandle;

/// Layer normalization
pub fn layerNorm(self: TensorHandle, axis: usize, epsilon: f32, gain: TensorHandle, bias: TensorHandle) !TensorHandle {
    // computes (x - mean) / sqrt(variance + epsilon) * gain + bias
    const mean_val = try self.mean(axis, true);
    const variance_val = try self.variance(axis, true);

    const epsilon_tensor = try constant(self.graph, epsilon, self.dtype);

    const x_minus_mean = try self.subtract(mean_val);
    const var_plus_eps = try variance_val.add(epsilon_tensor);
    const std_dev = try var_plus_eps.sqrt();
    const std_dev_inv = try std_dev.recip();

    const normalized = try x_minus_mean.mul(std_dev_inv);
    const scaled = try normalized.mul(gain);
    return scaled.add(bias);
}

/// Normalize by range: (x - min) / (max - min)
pub fn normalizeByRange(self: TensorHandle, axis: ?usize) !TensorHandle {
    const min_val = if (axis) |a| try self.minReduce(a, true) else try self.minAll();
    const max_val = if (axis) |a| try self.maxReduce(a, true) else try self.maxAll();
    const range = try max_val.subtract(min_val);
    const shifted = try self.subtract(min_val);
    return shifted.divide(range);
}

/// Batch normalization
pub fn batchNorm(self: TensorHandle, axis: usize, epsilon: f32, gamma: TensorHandle, beta: TensorHandle) !TensorHandle {
    // Same as layer norm but typically applied to batch dimension
    return layerNorm(self, axis, epsilon, gamma, beta);
}

/// Instance normalization (normalize each sample independently)
pub fn instanceNorm(self: TensorHandle, axis: usize, epsilon: f32) !TensorHandle {
    const mean_val = try self.mean(axis, true);
    const variance_val = try self.variance(axis, true);
    const epsilon_tensor = try constant(self.graph, epsilon, self.dtype);
    
    const x_minus_mean = try self.subtract(mean_val);
    const var_plus_eps = try variance_val.add(epsilon_tensor);
    const std_dev = try var_plus_eps.sqrt();
    return x_minus_mean.divide(std_dev);
}

/// Group normalization
pub fn groupNorm(self: TensorHandle, num_groups: usize, epsilon: f32) !TensorHandle {
    // TODO: Implement proper group normalization
    // For now, use instance normalization as placeholder
    _ = num_groups;
    return instanceNorm(self, 1, epsilon);
}

// Tests
test "layer normalization" {
    const testing = std.testing;
    const Graph = @import("graph").Graph;
    const placeholder = @import("../creation.zig").placeholder;
    const parameter = @import("../creation.zig").parameter;
    
    var graph = try Graph.init(testing.allocator);
    defer graph.deinit();
    
    const x = try placeholder(&graph, &.{2, 3, 4}, .f32);
    const gain = try parameter(&graph, &.{4}, 0, .f32);
    const bias = try parameter(&graph, &.{4}, 1, .f32);
    
    // Test layer norm
    const ln = try layerNorm(x, 2, 1e-5, gain, bias);
    try testing.expect(ln.node_id != x.node_id);
    try testing.expectEqual(x.shape.dims.len, ln.shape.dims.len);
}

test "normalization operations" {
    const testing = std.testing;
    const Graph = @import("graph").Graph;
    const placeholder = @import("../creation.zig").placeholder;
    
    var graph = try Graph.init(testing.allocator);
    defer graph.deinit();
    
    const x = try placeholder(&graph, &.{2, 3, 4}, .f32);
    
    // Test normalize by range
    const norm_range = try normalizeByRange(x, 1);
    try testing.expect(norm_range.node_id != x.node_id);
    
    // Test instance norm
    const inst_norm = try instanceNorm(x, 1, 1e-5);
    try testing.expect(inst_norm.node_id != x.node_id);
    
    // Test group norm
    const group_norm = try groupNorm(x, 2, 1e-5);
    try testing.expect(group_norm.node_id != x.node_id);
}