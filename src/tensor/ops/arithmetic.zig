const std = @import("std");
const types = @import("types");
const DataType = types.DataType;
const creation = @import("../creation.zig");
const constant = creation.constant;

// Import TensorHandle type reference
const TensorHandle = @import("../handle.zig").TensorHandle;

/// Subtraction: a - b = a + (-1 * b)
pub fn subtract(self: TensorHandle, other: TensorHandle) !TensorHandle {
    const neg_one = try constant(self.graph, -1.0, self.dtype);
    const neg_other = try other.mul(neg_one);
    return self.add(neg_other);
}

/// Division: a / b = a * (1/b)
pub fn divide(self: TensorHandle, other: TensorHandle) !TensorHandle {
    const recip_other = try other.recip();
    return self.mul(recip_other);
}

/// Power: a^b using exp2 and log2
pub fn pow(self: TensorHandle, other: TensorHandle) !TensorHandle {
    // a^b = 2^(b * log2(a))
    const log_a = try self.log2();
    const b_log_a = try other.mul(log_a);
    return b_log_a.exp2();
}

/// Negation: -x
pub fn neg(self: TensorHandle) !TensorHandle {
    const neg_one = try constant(self.graph, -1.0, self.dtype);
    return self.mul(neg_one);
}

/// Absolute value: |x|
pub fn abs(self: TensorHandle) !TensorHandle {
    // abs(x) = max(x, -x)
    const neg_x = try neg(self);
    return self.maximum(neg_x);
}

/// Square: x^2
pub fn square(self: TensorHandle) !TensorHandle {
    return self.mul(self);
}

/// Sign function: returns -1, 0, or 1
pub fn sign(self: TensorHandle) !TensorHandle {
    // sign(x) = x / (abs(x) + epsilon) where epsilon prevents division by zero
    // For zero values, this will return ~0
    const epsilon = try constant(self.graph, 1e-10, self.dtype);
    const abs_x = try abs(self);
    const abs_x_plus_eps = try abs_x.add(epsilon);
    return self.divide(abs_x_plus_eps);
}

/// Floor division
pub fn floorDiv(self: TensorHandle, other: TensorHandle) !TensorHandle {
    // floor(a/b) - simplified version, proper floor needs more work
    const div_result = try divide(self, other);
    std.log.warn("floorDiv: returning regular division result - proper floor operation not yet implemented", .{});
    // TODO: Implement proper floor operation
    return div_result;
}

/// Fused multiply-add: a * b + c
pub fn fma(a: TensorHandle, b: TensorHandle, c: TensorHandle) !TensorHandle {
    const product = try a.mul(b);
    return product.add(c);
}

/// Clamp values between min and max
pub fn clamp(self: TensorHandle, min_val: f32, max_val: f32) !TensorHandle {
    const min_tensor = try constant(self.graph, min_val, self.dtype);
    const max_tensor = try constant(self.graph, max_val, self.dtype);
    const clamped_low = try self.maximum(min_tensor);
    return clamped_low.minimum(max_tensor);
}

/// Cube: x^3
pub fn cube(self: TensorHandle) !TensorHandle {
    const squared = try self.square();
    return squared.mul(self);
}

/// Maximum reduction along specified axis
pub fn maximum_along_axis(self: TensorHandle, axis: i64, keepdims: bool) !TensorHandle {
    // Use the existing maxReduce operation from primitive_ops
    return self.maxReduce(@intCast(axis), keepdims);
}

// Tests
test "arithmetic operations" {
    const testing = std.testing;
    const Graph = @import("graph").Graph;
    const placeholder = @import("../creation.zig").placeholder;
    
    var graph = try Graph.init(testing.allocator);
    defer graph.deinit();
    
    const a = try placeholder(&graph, &.{2, 3}, .f32);
    const b = try placeholder(&graph, &.{2, 3}, .f32);
    
    // Test subtract
    const diff = try subtract(a, b);
    try testing.expect(diff.node_id != a.node_id);
    try testing.expectEqual(a.shape.dims.len, diff.shape.dims.len);
    
    // Test divide
    const div = try divide(a, b);
    try testing.expect(div.node_id != a.node_id);
    
    // Test neg
    const neg_a = try neg(a);
    try testing.expect(neg_a.node_id != a.node_id);
    
    // Test square
    const sq = try square(a);
    try testing.expect(sq.node_id != a.node_id);
}

test "abs and sign operations" {
    const testing = std.testing;
    const Graph = @import("graph").Graph;
    const placeholder = @import("../creation.zig").placeholder;
    
    var graph = try Graph.init(testing.allocator);
    defer graph.deinit();
    
    const x = try placeholder(&graph, &.{3, 3}, .f32);
    
    // Test abs
    const abs_x = try abs(x);
    try testing.expect(abs_x.node_id != x.node_id);
    try testing.expectEqual(x.shape.dims.len, abs_x.shape.dims.len);
    
    // Test sign
    const sign_x = try sign(x);
    try testing.expect(sign_x.node_id != x.node_id);
}

test "pow and clamp operations" {
    const testing = std.testing;
    const Graph = @import("graph").Graph;
    const placeholder = @import("../creation.zig").placeholder;
    
    var graph = try Graph.init(testing.allocator);
    defer graph.deinit();
    
    const base = try placeholder(&graph, &.{2, 2}, .f32);
    const exp = try placeholder(&graph, &.{2, 2}, .f32);
    
    // Test pow
    const result = try pow(base, exp);
    try testing.expect(result.node_id != base.node_id);
    
    // Test clamp
    const clamped = try clamp(base, -1.0, 1.0);
    try testing.expect(clamped.node_id != base.node_id);
}