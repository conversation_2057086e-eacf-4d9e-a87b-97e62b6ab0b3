const std = @import("std");
const TensorHandle = @import("../handle.zig").TensorHandle;
const DataType = @import("types").DataType;

/// Equal comparison
pub fn equal(self: TensorHandle, other: TensorHandle) !TensorHandle {
    // a == b is !(a < b) && !(b < a)
    const a_lt_b = try self.lessThan(other);
    const b_lt_a = try other.lessThan(self);
    const not_a_lt_b = try a_lt_b.logicalNot();
    const not_b_lt_a = try b_lt_a.logicalNot();
    return not_a_lt_b.logicalAnd(not_b_lt_a);
}

/// Not equal comparison
pub fn notEqual(self: TensorHandle, other: TensorHandle) !TensorHandle {
    const eq = try equal(self, other);
    return eq.logicalNot();
}

/// Less than or equal
pub fn lessThanOrEqual(self: TensorHandle, other: TensorHandle) !TensorHandle {
    // a <= b is !(b < a)
    const b_lt_a = try other.lessThan(self);
    return b_lt_a.logicalNot();
}

/// Greater than
pub fn greaterThan(self: TensorHandle, other: TensorHandle) !TensorHandle {
    // a > b is b < a
    return other.lessThan(self);
}

/// Greater than or equal
pub fn greaterThanOrEqual(self: TensorHandle, other: TensorHandle) !TensorHandle {
    // a >= b is !(a < b)
    const a_lt_b = try self.lessThan(other);
    return a_lt_b.logicalNot();
}

/// Element-wise maximum
pub fn maximum(self: TensorHandle, other: TensorHandle) !TensorHandle {
    // max(a, b) = select(a > b, a, b)
    const condition = try greaterThan(self, other);
    return condition.select(self, other);
}

/// Element-wise minimum
pub fn minimum(self: TensorHandle, other: TensorHandle) !TensorHandle {
    // min(a, b) = select(a < b, a, b)
    const condition = try self.lessThan(other);
    return condition.select(self, other);
}

// Tests
test "comparison operations" {
    const testing = std.testing;
    const Graph = @import("graph").Graph;
    const placeholder = @import("../creation.zig").placeholder;
    
    var graph = try Graph.init(testing.allocator);
    defer graph.deinit();
    
    const a = try placeholder(&graph, &.{2, 3}, .f32);
    const b = try placeholder(&graph, &.{2, 3}, .f32);
    
    // Test equal
    const eq = try equal(a, b);
    try testing.expect(eq.node_id != a.node_id);
    try testing.expectEqual(DataType.f32, eq.dtype); // Returns f32 (0.0 or 1.0) for boolean operations
    
    // Test not equal
    const neq = try notEqual(a, b);
    try testing.expect(neq.node_id != a.node_id);
    try testing.expectEqual(DataType.f32, neq.dtype);
    
    // Test less than or equal
    const lte = try lessThanOrEqual(a, b);
    try testing.expect(lte.node_id != a.node_id);
    
    // Test greater than
    const gt = try greaterThan(a, b);
    try testing.expect(gt.node_id != a.node_id);
    
    // Test greater than or equal
    const gte = try greaterThanOrEqual(a, b);
    try testing.expect(gte.node_id != a.node_id);
}

test "min max operations" {
    const testing = std.testing;
    const Graph = @import("graph").Graph;
    const placeholder = @import("../creation.zig").placeholder;
    
    var graph = try Graph.init(testing.allocator);
    defer graph.deinit();
    
    const x = try placeholder(&graph, &.{3, 4}, .f32);
    const y = try placeholder(&graph, &.{3, 4}, .f32);
    
    // Test maximum
    const max_xy = try maximum(x, y);
    try testing.expect(max_xy.node_id != x.node_id);
    try testing.expectEqual(x.shape.dims.len, max_xy.shape.dims.len);
    
    // Test minimum
    const min_xy = try minimum(x, y);
    try testing.expect(min_xy.node_id != x.node_id);
    try testing.expectEqual(x.shape.dims.len, min_xy.shape.dims.len);
}