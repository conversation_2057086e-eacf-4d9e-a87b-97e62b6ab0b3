const std = @import("std");
const types = @import("types");
const DataType = types.DataType;
const creation = @import("../creation.zig");
const constant = creation.constant;

// Import TensorHandle type reference
const TensorHandle = @import("../handle.zig").TensorHandle;

/// ReLU activation: max(0, x)
pub fn relu(self: TensorHandle) !TensorHandle {
    const zero = try constant(self.graph, 0.0, self.dtype);
    return self.maximum(zero);
}

/// Sigmoid activation: 1 / (1 + e^(-x))
pub fn sigmoid(self: TensorHandle) !TensorHandle {
    const neg_x = try self.neg();
    const exp_neg_x = try neg_x.exp();
    const one = try constant(self.graph, 1.0, self.dtype);
    const one_plus_exp = try one.add(exp_neg_x);
    return one.divide(one_plus_exp);
}

/// Hyperbolic tangent: tanh(x) = (e^(2x) - 1) / (e^(2x) + 1)
pub fn tanh(self: TensorHandle) !TensorHandle {
    const two_x = try self.add(self);
    const exp_2x = try two_x.exp();
    const one = try constant(self.graph, 1.0, self.dtype);
    const numerator = try exp_2x.subtract(one);
    const denominator = try exp_2x.add(one);
    return numerator.divide(denominator);
}

/// GELU activation approximation
pub fn gelu(self: TensorHandle) !TensorHandle {
    // gelu(x) ≈ 0.5 * x * (1 + tanh(√(2/π) * (x + 0.044715 * x^3)))
    const half = try constant(self.graph, 0.5, self.dtype);
    const sqrt_2_over_pi = try constant(self.graph, 0.7978845608, self.dtype);
    const cubic_coeff = try constant(self.graph, 0.044715, self.dtype);
    
    const x_squared = try self.mul(self);
    const x_cubed = try x_squared.mul(self);
    const cubic_term = try cubic_coeff.mul(x_cubed);
    const inner = try self.add(cubic_term);
    const scaled_inner = try sqrt_2_over_pi.mul(inner);
    const tanh_result = try scaled_inner.tanh();
    const one = try constant(self.graph, 1.0, self.dtype);
    const one_plus_tanh = try one.add(tanh_result);
    const half_x = try half.mul(self);
    return half_x.mul(one_plus_tanh);
}

/// Softplus activation: ln(1 + e^x)
pub fn softplus(self: TensorHandle) !TensorHandle {
    const exp_x = try self.exp();
    const one = try constant(self.graph, 1.0, self.dtype);
    const one_plus_exp = try one.add(exp_x);
    return one_plus_exp.log();
}

/// Leaky ReLU activation
pub fn leakyRelu(self: TensorHandle, negative_slope: f32) !TensorHandle {
    // leaky_relu(x) = max(x, negative_slope * x)
    const slope = try constant(self.graph, negative_slope, self.dtype);
    const scaled = try self.mul(slope);
    return self.maximum(scaled);
}

/// Swish activation (also known as SiLU)
pub fn swish(self: TensorHandle) !TensorHandle {
    // swish(x) = x * sigmoid(x)
    const sigmoid_x = try sigmoid(self);
    return self.mul(sigmoid_x);
}

/// Softmax activation
pub fn softmax(self: TensorHandle, axis: usize) !TensorHandle {
    // x - max(x) with keepdims for broadcasting
    const max_val = try self.maxReduce(axis, true);
    const x_centered = try self.subtract(max_val);
    const exp_val = try x_centered.exp();
    const sum_exp = try exp_val.sumReduce(axis, true);
    return exp_val.divide(sum_exp);
}

/// Log-Softmax activation
pub fn logSoftmax(self: TensorHandle, axis: usize) !TensorHandle {
    // log(softmax(x)) = x - log(sum(exp(x)))
    // Numerically stable: x - max(x) - log(sum(exp(x - max(x))))
    const max_val = try self.maxReduce(axis, true);
    const x_centered = try self.subtract(max_val);
    const exp_centered = try x_centered.exp();
    const sum_exp = try exp_centered.sumReduce(axis, true);
    const log_sum_exp = try sum_exp.log();
    return x_centered.subtract(log_sum_exp);
}

/// Alias for swish
pub fn silu(self: TensorHandle) !TensorHandle {
    return swish(self);
}

// Tests
test "activation functions" {
    const testing = std.testing;
    const Graph = @import("graph").Graph;
    const placeholder = @import("../creation.zig").placeholder;
    
    var graph = try Graph.init(testing.allocator);
    defer graph.deinit();
    
    const x = try placeholder(&graph, &.{2, 3}, .f32);
    
    // Test ReLU
    const relu_x = try relu(x);
    try testing.expect(relu_x.node_id != x.node_id);
    try testing.expectEqual(x.shape.dims.len, relu_x.shape.dims.len);
    
    // Test Sigmoid
    const sigmoid_x = try sigmoid(x);
    try testing.expect(sigmoid_x.node_id != x.node_id);
    
    // Test Tanh
    const tanh_x = try tanh(x);
    try testing.expect(tanh_x.node_id != x.node_id);
    
    // Test GELU
    const gelu_x = try gelu(x);
    try testing.expect(gelu_x.node_id != x.node_id);
    
    // Test Softplus
    const softplus_x = try softplus(x);
    try testing.expect(softplus_x.node_id != x.node_id);
    
    // Test Softmax
    const softmax_x = try softmax(x, 1);
    try testing.expect(softmax_x.node_id != x.node_id);
    
    // Test Log-Softmax
    const log_softmax_x = try logSoftmax(x, 1);
    try testing.expect(log_softmax_x.node_id != x.node_id);
}

test "leaky relu and swish" {
    const testing = std.testing;
    const Graph = @import("graph").Graph;
    const placeholder = @import("../creation.zig").placeholder;
    
    var graph = try Graph.init(testing.allocator);
    defer graph.deinit();
    
    const x = try placeholder(&graph, &.{3, 3}, .f32);
    
    // Test Leaky ReLU
    const leaky_x = try leakyRelu(x, 0.01);
    try testing.expect(leaky_x.node_id != x.node_id);
    
    // Test Swish/SiLU
    const swish_x = try swish(x);
    try testing.expect(swish_x.node_id != x.node_id);
    
    const silu_x = try silu(x);
    try testing.expect(silu_x.node_id != x.node_id);
}