const std = @import("std");
const types = @import("types");
const DataType = types.DataType;
const creation = @import("../creation.zig");
const constant = creation.constant;

// Import TensorHandle type reference - use relative import since we're inside tensor module
const TensorHandle = @import("../handle.zig").TensorHandle;

/// Matrix multiplication - supports 2D, 3D, 4D, and 5D tensors
pub fn matmul(self: TensorHandle, other: TensorHandle) !TensorHandle {
    const self_dims = self.shape.dims.len;
    const other_dims = other.shape.dims.len;
    
    // Dispatch based on dimensions
    if (self_dims == 2 and other_dims == 2) {
        // Current 2D implementation
        return matmul2d(self, other);
    } else if (self_dims == 3) {
        if (other_dims == 2) {
            // [B,M,K] @ [K,N] -> [B,M,N]
            return matmul3d_2d(self, other);
        } else if (other_dims == 3) {
            // [B,M,K] @ [B,K,N] -> [B,M,N]
            return matmul3d_3d(self, other);
        }
    } else if (self_dims == 4) {
        if (other_dims == 2) {
            // [A,B,M,K] @ [K,N] -> [A,B,M,N]
            return matmul4d_2d(self, other);
        } else if (other_dims == 4) {
            // [A,B,M,K] @ [A,B,K,N] -> [A,B,M,N]
            return matmul4d_4d(self, other);
        }
    } else if (self_dims == 5 and other_dims == 5) {
        // [A,B,C,M,K] @ [A,B,C,K,N] -> [A,B,C,M,N]
        return matmul5d_5d(self, other);
    }
    
    std.log.err("matmul: unsupported dimensions {}D @ {}D", .{ self_dims, other_dims });
    return error.UnsupportedDimensions;
}

/// 2D matrix multiplication [M,K] @ [K,N] -> [M,N]
fn matmul2d(self: TensorHandle, other: TensorHandle) !TensorHandle {
    // Get concrete dimensions for validation
    const k1 = switch (self.shape.dims[1]) {
        .concrete => |v| v,
        else => {
            std.log.err("matmul: dynamic dimension not supported for first tensor's inner dimension", .{});
            return error.DynamicDimensionNotSupported;
        },
    };
    const k2 = switch (other.shape.dims[0]) {
        .concrete => |v| v,
        else => {
            std.log.err("matmul: dynamic dimension not supported for second tensor's inner dimension", .{});
            return error.DynamicDimensionNotSupported;
        },
    };

    if (k1 != k2) {
        std.log.err("matmul: incompatible inner dimensions - {} and {} (must match)", .{ k1, k2 });
        return error.IncompatibleShapes;
    }

    // Luminal-style matmul decomposition: A[m,k] @ B[k,n] -> [m,n]
    // Following exact Luminal implementation from hl_ops/matmul.rs lines 10-16
    
    // Get dimensions
    const m = switch (self.shape.dims[0]) {
        .concrete => |v| @as(usize, @intCast(v)),
        else => return error.DynamicDimensionNotSupported,
    };
    const n = switch (other.shape.dims[1]) {
        .concrete => |v| @as(usize, @intCast(v)),
        else => return error.DynamicDimensionNotSupported,
    };
    
    const enable_debug = @import("build_options").enable_debug_logs;
    if (enable_debug) {
        std.debug.print("matmul: Starting decomposition for [{}x{}] @ [{}x{}]\n", .{m, k1, k2, n});
    }
    
    // Step 1: self.expand(1, n) - expand A to [m, n, k]
    // A[m,k] → A[m,n,k] by adding dimension at axis 1
    if (enable_debug) {
        std.debug.print("matmul: Step 1 - expanding A from [{}x{}] at axis 1 by {}\n", .{m, k1, n});
        std.debug.print("  self.node_id = {}, graph.nodes.len = {}\n", .{self.node_id, self.graph.nodes.items.len});
    }
    const a_expanded = try self.expandAxis(1, @as(i64, @intCast(n)));
    if (enable_debug) {
        std.debug.print("  After expand: a_expanded.node_id = {}, graph.nodes.len = {}\n", .{a_expanded.node_id, self.graph.nodes.items.len});
        std.debug.print("matmul: a_expanded shape: ", .{});
        for (a_expanded.shape.dims) |d| std.debug.print("{} ", .{d.concrete});
        std.debug.print(", strides: ", .{});
        for (a_expanded.shape.strides) |s| std.debug.print("{} ", .{s.concrete});
        std.debug.print(", fake: ", .{});
        for (a_expanded.shape.fake) |f| std.debug.print("{} ", .{f});
        std.debug.print("\n", .{});
    }
    
    // Step 2: rhs.permute((1, 0)).expand(0, m) - transpose B then expand to [m, n, k] 
    // IMPORTANT: Use transposeContiguous to materialize the transpose for gradient computation
    // This ensures gradients flow correctly without shape mismatches
    const b_transposed = try other.transposeContiguous(&.{1, 0}); // [k,n] -> [n,k] (materialized)
    const b_expanded = try b_transposed.expandAxis(0, @as(i64, @intCast(m))); // [n,k] -> [m,n,k]
    if (enable_debug) {
        std.debug.print("matmul: b_expanded shape: ", .{});
        for (b_expanded.shape.dims) |d| std.debug.print("{} ", .{d.concrete});
        std.debug.print(", strides: ", .{});
        for (b_expanded.shape.strides) |s| std.debug.print("{} ", .{s.concrete});
        std.debug.print(", fake: ", .{});
        for (b_expanded.shape.fake) |f| std.debug.print("{} ", .{f});
        std.debug.print("\n", .{});
    }
    
    // Step 3: Broadcasted multiply
    if (enable_debug) {
        std.debug.print("matmul: Step 3 - multiplying expanded tensors\n", .{});
        std.debug.print("  a_expanded.node_id = {}, b_expanded.node_id = {}\n", .{a_expanded.node_id, b_expanded.node_id});
    }
    const product = try a_expanded.mul(b_expanded);
    if (enable_debug) {
        std.debug.print("  product.node_id = {}, graph.nodes.len = {}\n", .{product.node_id, self.graph.nodes.items.len});
    }
    
    // Step 4: sum_reduce(2) - sum along the k dimension (axis 2)
    // With keepdims=false, this already removes the dimension, so no squeeze needed
    if (enable_debug) {
        std.debug.print("matmul: Step 4 - sum_reduce along axis 2 (keepdims=false)\n", .{});
    }
    const result = try product.sumReduce(2, false);
    if (enable_debug) {
        std.debug.print("  result.node_id = {}, graph.nodes.len = {}\n", .{result.node_id, self.graph.nodes.items.len});
        std.debug.print("matmul: Final result shape: ", .{});
        for (result.shape.dims) |d| std.debug.print("{} ", .{d.concrete});
        std.debug.print("\n", .{});
    }
    
    return result;
}

/// Batch matmul: [B,M,K] @ [K,N] -> [B,M,N]
fn matmul3d_2d(self: TensorHandle, other: TensorHandle) !TensorHandle {
    // Validate dimensions
    const self_shape = self.shape.dims;
    const other_shape = other.shape.dims;
    
    // Extract dimensions
    const b = switch (self_shape[0]) {
        .concrete => |v| v,
        else => return error.DynamicDimensionNotSupported,
    };
    const m = switch (self_shape[1]) {
        .concrete => |v| v,
        else => return error.DynamicDimensionNotSupported,
    };
    const k1 = switch (self_shape[2]) {
        .concrete => |v| v,
        else => return error.DynamicDimensionNotSupported,
    };
    const k2 = switch (other_shape[0]) {
        .concrete => |v| v,
        else => return error.DynamicDimensionNotSupported,
    };
    const n = switch (other_shape[1]) {
        .concrete => |v| v,
        else => return error.DynamicDimensionNotSupported,
    };
    
    if (k1 != k2) {
        std.log.err("matmul3d_2d: incompatible inner dimensions - {} and {}", .{ k1, k2 });
        return error.IncompatibleShapes;
    }
    
    // Simple approach: reshape to 2D, matmul, reshape back
    // [B,M,K] -> [B*M,K]
    const self_2d = try self.reshape(&.{ b * m, k1 });
    
    // Do 2D matmul: [B*M,K] @ [K,N] -> [B*M,N]
    const result_2d = try matmul2d(self_2d, other);
    
    // Reshape back: [B*M,N] -> [B,M,N]
    return result_2d.reshape(&.{ b, m, n });
}

/// Batch matmul: [B,M,K] @ [B,K,N] -> [B,M,N]
fn matmul3d_3d(self: TensorHandle, other: TensorHandle) !TensorHandle {
    // Validate dimensions
    const self_shape = self.shape.dims;
    const other_shape = other.shape.dims;
    
    // Extract and validate dimensions
    const b1 = switch (self_shape[0]) {
        .concrete => |v| v,
        else => return error.DynamicDimensionNotSupported,
    };
    const b2 = switch (other_shape[0]) {
        .concrete => |v| v,
        else => return error.DynamicDimensionNotSupported,
    };
    
    if (b1 != b2) {
        std.log.err("matmul3d_3d: batch dimensions must match - {} and {}", .{ b1, b2 });
        return error.IncompatibleShapes;
    }
    
    const m = switch (self_shape[1]) {
        .concrete => |v| v,
        else => return error.DynamicDimensionNotSupported,
    };
    const k1 = switch (self_shape[2]) {
        .concrete => |v| v,
        else => return error.DynamicDimensionNotSupported,
    };
    const k2 = switch (other_shape[1]) {
        .concrete => |v| v,
        else => return error.DynamicDimensionNotSupported,
    };
    const n = switch (other_shape[2]) {
        .concrete => |v| v,
        else => return error.DynamicDimensionNotSupported,
    };
    
    if (k1 != k2) {
        std.log.err("matmul3d_3d: incompatible inner dimensions - {} and {}", .{ k1, k2 });
        return error.IncompatibleShapes;
    }
    
    // For true batch matmul, we need to handle each batch separately
    // This is a temporary implementation - ideally we'd have a batched kernel
    // For now, use a loop approach or reshape approach
    
    // Approach 1: Loop over batches (simple but not optimal)
    // We'll implement the reshape approach which is more efficient
    
    // Reshape approach:
    // self: [B,M,K] -> [B*M,K]
    // other: [B,K,N] -> [B,K*N] -> transpose -> [B,N*K] -> reshape -> [K,B*N]
    // This doesn't work well...
    
    // Better approach: Process each batch using existing 2D matmul
    // This is simpler and reuses existing optimized code
    
    // For now, let's use the simplest approach that will work:
    // Loop over batches and accumulate results
    // In a real implementation, we'd want a proper batched kernel
    
    // Temporary implementation using slicing and stacking
    // This is not optimal but will work correctly
    
    // Alternative: Use einsum-like decomposition
    // self: [B,M,K] 
    // other: [B,K,N]
    // We want: batch-wise [M,K] @ [K,N] -> [M,N]
    
    // Let's use the Luminal approach correctly:
    // 1. Transpose other: [B,K,N] -> [B,N,K]  
    const other_t = try other.transpose(&.{0, 2, 1});
    
    // 2. Expand dimensions
    // self: [B,M,K] -> [B,M,K,1]
    const self_expanded = try self.unsqueeze(3);
    // other_t: [B,N,K] -> [B,1,N,K]
    const other_expanded = try other_t.unsqueeze(1);
    
    // 3. Broadcast to compatible shapes
    // self: [B,M,K,1] -> [B,M,K,N]
    const self_broadcast = try self_expanded.broadcast(&.{ b1, m, k1, n });
    // other: [B,1,N,K] -> [B,M,N,K]  
    const other_broadcast = try other_expanded.broadcast(&.{ b1, m, n, k1 });
    
    // 4. Transpose to align for multiplication
    // We want [B,M,K,N] * [B,M,K,N]
    // So transpose other from [B,M,N,K] to [B,M,K,N]
    const other_aligned = try other_broadcast.transpose(&.{ 0, 1, 3, 2 });
    
    // 5. Element-wise multiply
    const product = try self_broadcast.mul(other_aligned);
    
    // 6. Sum reduce along K (axis 2)
    // With keepdims=false, this already removes the dimension to get [B,M,N]
    return product.sumReduce(2, false);
}

/// 4D matmul: [A,B,M,K] @ [K,N] -> [A,B,M,N]
fn matmul4d_2d(self: TensorHandle, other: TensorHandle) !TensorHandle {
    // Extract dimensions
    const a = switch (self.shape.dims[0]) {
        .concrete => |v| v,
        else => return error.DynamicDimensionNotSupported,
    };
    const b = switch (self.shape.dims[1]) {
        .concrete => |v| v,
        else => return error.DynamicDimensionNotSupported,
    };
    const m = switch (self.shape.dims[2]) {
        .concrete => |v| v,
        else => return error.DynamicDimensionNotSupported,
    };
    const k1 = switch (self.shape.dims[3]) {
        .concrete => |v| v,
        else => return error.DynamicDimensionNotSupported,
    };
    const k2 = switch (other.shape.dims[0]) {
        .concrete => |v| v,
        else => return error.DynamicDimensionNotSupported,
    };
    const n = switch (other.shape.dims[1]) {
        .concrete => |v| v,
        else => return error.DynamicDimensionNotSupported,
    };
    
    if (k1 != k2) {
        std.log.err("matmul4d_2d: incompatible inner dimensions - {} and {}", .{ k1, k2 });
        return error.IncompatibleShapes;
    }
    
    // Simple approach: reshape to 2D, matmul, reshape back
    // [A,B,M,K] -> [A*B*M,K]
    const self_2d = try self.reshape(&.{ a * b * m, k1 });
    
    // Do 2D matmul: [A*B*M,K] @ [K,N] -> [A*B*M,N]
    const result_2d = try matmul2d(self_2d, other);
    
    // Reshape back: [A*B*M,N] -> [A,B,M,N]
    return result_2d.reshape(&.{ a, b, m, n });
}

/// 4D matmul: [A,B,M,K] @ [A,B,K,N] -> [A,B,M,N]
fn matmul4d_4d(self: TensorHandle, other: TensorHandle) !TensorHandle {
    // Extract and validate dimensions
    const a1 = switch (self.shape.dims[0]) {
        .concrete => |v| v,
        else => return error.DynamicDimensionNotSupported,
    };
    const a2 = switch (other.shape.dims[0]) {
        .concrete => |v| v,
        else => return error.DynamicDimensionNotSupported,
    };
    const b1 = switch (self.shape.dims[1]) {
        .concrete => |v| v,
        else => return error.DynamicDimensionNotSupported,
    };
    const b2 = switch (other.shape.dims[1]) {
        .concrete => |v| v,
        else => return error.DynamicDimensionNotSupported,
    };
    
    if (a1 != a2 or b1 != b2) {
        std.log.err("matmul4d_4d: batch dimensions must match - [{}x{}] vs [{}x{}]", .{ a1, b1, a2, b2 });
        return error.IncompatibleShapes;
    }
    
    const m = switch (self.shape.dims[2]) {
        .concrete => |v| v,
        else => return error.DynamicDimensionNotSupported,
    };
    const k1 = switch (self.shape.dims[3]) {
        .concrete => |v| v,
        else => return error.DynamicDimensionNotSupported,
    };
    const k2 = switch (other.shape.dims[2]) {
        .concrete => |v| v,
        else => return error.DynamicDimensionNotSupported,
    };
    const n = switch (other.shape.dims[3]) {
        .concrete => |v| v,
        else => return error.DynamicDimensionNotSupported,
    };
    
    if (k1 != k2) {
        std.log.err("matmul4d_4d: incompatible inner dimensions - {} and {}", .{ k1, k2 });
        return error.IncompatibleShapes;
    }
    
    // Reshape to 3D and use 3D matmul
    // [A,B,M,K] -> [A*B,M,K]
    const self_3d = try self.reshape(&.{ a1 * b1, m, k1 });
    // [A,B,K,N] -> [A*B,K,N]
    const other_3d = try other.reshape(&.{ a1 * b1, k1, n });
    
    // Do 3D matmul: [A*B,M,K] @ [A*B,K,N] -> [A*B,M,N]
    const result_3d = try matmul3d_3d(self_3d, other_3d);
    
    // Reshape back: [A*B,M,N] -> [A,B,M,N]
    return result_3d.reshape(&.{ a1, b1, m, n });
}

/// 5D matmul: [A,B,C,M,K] @ [A,B,C,K,N] -> [A,B,C,M,N]
fn matmul5d_5d(self: TensorHandle, other: TensorHandle) !TensorHandle {
    // Extract and validate all dimensions
    const dims_self = self.shape.dims;
    const dims_other = other.shape.dims;
    
    // Extract batch dimensions
    const a1 = switch (dims_self[0]) {
        .concrete => |v| v,
        else => return error.DynamicDimensionNotSupported,
    };
    const a2 = switch (dims_other[0]) {
        .concrete => |v| v,
        else => return error.DynamicDimensionNotSupported,
    };
    const b1 = switch (dims_self[1]) {
        .concrete => |v| v,
        else => return error.DynamicDimensionNotSupported,
    };
    const b2 = switch (dims_other[1]) {
        .concrete => |v| v,
        else => return error.DynamicDimensionNotSupported,
    };
    const c1 = switch (dims_self[2]) {
        .concrete => |v| v,
        else => return error.DynamicDimensionNotSupported,
    };
    const c2 = switch (dims_other[2]) {
        .concrete => |v| v,
        else => return error.DynamicDimensionNotSupported,
    };
    
    if (a1 != a2 or b1 != b2 or c1 != c2) {
        std.log.err("matmul5d_5d: batch dimensions must match", .{});
        return error.IncompatibleShapes;
    }
    
    // Extract matrix dimensions
    const m = switch (dims_self[3]) {
        .concrete => |v| v,
        else => return error.DynamicDimensionNotSupported,
    };
    const k1 = switch (dims_self[4]) {
        .concrete => |v| v,
        else => return error.DynamicDimensionNotSupported,
    };
    const k2 = switch (dims_other[3]) {
        .concrete => |v| v,
        else => return error.DynamicDimensionNotSupported,
    };
    const n = switch (dims_other[4]) {
        .concrete => |v| v,
        else => return error.DynamicDimensionNotSupported,
    };
    
    if (k1 != k2) {
        std.log.err("matmul5d_5d: incompatible inner dimensions - {} and {}", .{ k1, k2 });
        return error.IncompatibleShapes;
    }
    
    // Reshape to 3D and use 3D matmul
    // [A,B,C,M,K] -> [A*B*C,M,K]
    const batch_size = a1 * b1 * c1;
    const self_3d = try self.reshape(&.{ batch_size, m, k1 });
    // [A,B,C,K,N] -> [A*B*C,K,N]
    const other_3d = try other.reshape(&.{ batch_size, k1, n });
    
    // Do 3D matmul: [A*B*C,M,K] @ [A*B*C,K,N] -> [A*B*C,M,N]
    const result_3d = try matmul3d_3d(self_3d, other_3d);
    
    // Reshape back: [A*B*C,M,N] -> [A,B,C,M,N]
    return result_3d.reshape(&.{ a1, b1, c1, m, n });
}

/// Batch matrix multiplication (kept for compatibility)
pub fn batchMatmul(a: TensorHandle, b: TensorHandle) !TensorHandle {
    // Now just delegates to the main matmul function
    return a.matmul(b);
}

/// Matrix-vector multiplication
pub fn matvec(matrix: TensorHandle, vector: TensorHandle) !TensorHandle {
    // Validate dimensions
    if (matrix.shape.dims.len != 2) {
        std.log.err("matvec: matrix must be 2D, got {}D", .{matrix.shape.dims.len});
        return error.InvalidDimensions;
    }
    if (vector.shape.dims.len != 1) {
        std.log.err("matvec: vector must be 1D, got {}D", .{vector.shape.dims.len});
        return error.InvalidDimensions;
    }
    
    // Add dimension to vector to make it 2D
    const vector_2d = try vector.reshape(&.{ -1, 1 });
    
    // Perform matrix multiplication
    const result_2d = try matrix.matmul(vector_2d);
    
    // Squeeze result back to 1D
    return result_2d.reshape(&.{-1});
}

/// Dot product of two vectors
pub fn dot(a: TensorHandle, b: TensorHandle) !TensorHandle {
    // Validate both are 1D
    if (a.shape.dims.len != 1 or b.shape.dims.len != 1) {
        std.log.err("dot: both inputs must be 1D vectors, got {}D and {}D", 
                   .{ a.shape.dims.len, b.shape.dims.len });
        return error.InvalidDimensions;
    }
    
    // Luminal-style dot product: (self * rhs).sum_reduce(0)
    // Keep dimension to return [1] shape instead of scalar
    const product = try a.mul(b);
    return product.sumReduce(0, true);
}

/// Outer product of two vectors
pub fn outer(a: TensorHandle, b: TensorHandle) !TensorHandle {
    // Validate both are 1D
    if (a.shape.dims.len != 1 or b.shape.dims.len != 1) {
        std.log.err("outer: both inputs must be 1D vectors, got {}D and {}D", 
                   .{ a.shape.dims.len, b.shape.dims.len });
        return error.InvalidDimensions;
    }
    
    // Reshape a to column vector [n, 1]
    const a_col = try a.reshape(&.{ -1, 1 });
    
    // Reshape b to row vector [1, m]
    const b_row = try b.reshape(&.{ 1, -1 });
    
    // Multiply to get outer product [n, m]
    return a_col.mul(b_row);
}

// Tests
test "matrix multiplication 2D" {
    const testing = std.testing;
    const Graph = @import("graph").Graph;
    const placeholder = @import("../creation.zig").placeholder;

    var graph = try Graph.init(testing.allocator);
    defer graph.deinit();

    // Test 2D matrix multiplication
    const a = try placeholder(&graph, &.{ 2, 3 }, .f32);
    const b = try placeholder(&graph, &.{ 3, 4 }, .f32);
    
    const result = try matmul(a, b);
    try testing.expect(result.node_id != a.node_id);
    try testing.expect(result.node_id != b.node_id);
    // Matmul now properly returns 2D result
    try testing.expectEqual(@as(usize, 2), result.shape.dims.len);
    try testing.expectEqual(@as(i64, 2), result.shape.dims[0].concrete);
    try testing.expectEqual(@as(i64, 4), result.shape.dims[1].concrete);
    
    // Test dimension mismatch
    const c = try placeholder(&graph, &.{ 2, 5 }, .f32);
    const d = try placeholder(&graph, &.{ 3, 4 }, .f32);
    try testing.expectError(error.IncompatibleShapes, matmul(c, d));
}

test "batch matmul 3D @ 2D" {
    const testing = std.testing;
    const Graph = @import("graph").Graph;
    const placeholder = @import("../creation.zig").placeholder;

    var graph = try Graph.init(testing.allocator);
    defer graph.deinit();

    // Test 3D @ 2D: [2,3,4] @ [4,5] -> [2,3,5]
    const a = try placeholder(&graph, &.{ 2, 3, 4 }, .f32);
    const b = try placeholder(&graph, &.{ 4, 5 }, .f32);
    
    const result = try matmul(a, b);
    
    // Verify output shape
    try testing.expectEqual(@as(usize, 3), result.shape.dims.len);
    try testing.expectEqual(@as(i64, 2), result.shape.dims[0].concrete);
    try testing.expectEqual(@as(i64, 3), result.shape.dims[1].concrete);
    try testing.expectEqual(@as(i64, 5), result.shape.dims[2].concrete);
    
    // Test dimension mismatch
    const c = try placeholder(&graph, &.{ 2, 3, 4 }, .f32);
    const d = try placeholder(&graph, &.{ 5, 6 }, .f32);
    try testing.expectError(error.IncompatibleShapes, matmul(c, d));
}

test "batch matmul 3D @ 3D" {
    const testing = std.testing;
    const Graph = @import("graph").Graph;
    const placeholder = @import("../creation.zig").placeholder;

    var graph = try Graph.init(testing.allocator);
    defer graph.deinit();

    // Test 3D @ 3D: [2,3,4] @ [2,4,5] -> [2,3,5]
    const a = try placeholder(&graph, &.{ 2, 3, 4 }, .f32);
    const b = try placeholder(&graph, &.{ 2, 4, 5 }, .f32);
    
    const result = try matmul(a, b);
    
    // Verify output shape
    try testing.expectEqual(@as(usize, 3), result.shape.dims.len);
    try testing.expectEqual(@as(i64, 2), result.shape.dims[0].concrete);
    try testing.expectEqual(@as(i64, 3), result.shape.dims[1].concrete);
    try testing.expectEqual(@as(i64, 5), result.shape.dims[2].concrete);
    
    // Test batch dimension mismatch
    const c = try placeholder(&graph, &.{ 2, 3, 4 }, .f32);
    const d = try placeholder(&graph, &.{ 3, 4, 5 }, .f32);
    try testing.expectError(error.IncompatibleShapes, matmul(c, d));
    
    // Test inner dimension mismatch
    const e = try placeholder(&graph, &.{ 2, 3, 4 }, .f32);
    const f = try placeholder(&graph, &.{ 2, 5, 6 }, .f32);
    try testing.expectError(error.IncompatibleShapes, matmul(e, f));
}

test "transformer attention pattern" {
    const testing = std.testing;
    const Graph = @import("graph").Graph;
    const placeholder = @import("../creation.zig").placeholder;

    var graph = try Graph.init(testing.allocator);
    defer graph.deinit();

    // Simulate transformer attention: Q,K,V all [batch, heads, seq, dim]
    const batch = 2;
    const heads = 8;
    const seq = 16;
    const dim = 64;
    
    const Q = try placeholder(&graph, &.{ batch * heads, seq, dim }, .f32);
    const K = try placeholder(&graph, &.{ batch * heads, seq, dim }, .f32);
    
    // For attention, we need K transposed: [batch*heads, dim, seq]
    const K_t = try K.transpose(&.{0, 2, 1});
    
    // Compute attention scores: Q @ K^T -> [batch*heads, seq, seq]
    const scores = try matmul(Q, K_t);
    
    // Verify output shape
    try testing.expectEqual(@as(usize, 3), scores.shape.dims.len);
    try testing.expectEqual(@as(i64, batch * heads), scores.shape.dims[0].concrete);
    try testing.expectEqual(@as(i64, seq), scores.shape.dims[1].concrete);
    try testing.expectEqual(@as(i64, seq), scores.shape.dims[2].concrete);
}

test "batch matmul 4D @ 2D" {
    const testing = std.testing;
    const Graph = @import("graph").Graph;
    const placeholder = @import("../creation.zig").placeholder;

    var graph = try Graph.init(testing.allocator);
    defer graph.deinit();

    // Test 4D @ 2D: [2,3,4,5] @ [5,6] -> [2,3,4,6]
    const a = try placeholder(&graph, &.{ 2, 3, 4, 5 }, .f32);
    const b = try placeholder(&graph, &.{ 5, 6 }, .f32);
    
    const result = try matmul(a, b);
    
    // Verify output shape
    try testing.expectEqual(@as(usize, 4), result.shape.dims.len);
    try testing.expectEqual(@as(i64, 2), result.shape.dims[0].concrete);
    try testing.expectEqual(@as(i64, 3), result.shape.dims[1].concrete);
    try testing.expectEqual(@as(i64, 4), result.shape.dims[2].concrete);
    try testing.expectEqual(@as(i64, 6), result.shape.dims[3].concrete);
}

test "batch matmul 4D @ 4D" {
    const testing = std.testing;
    const Graph = @import("graph").Graph;
    const placeholder = @import("../creation.zig").placeholder;

    var graph = try Graph.init(testing.allocator);
    defer graph.deinit();

    // Test 4D @ 4D: [2,3,4,5] @ [2,3,5,6] -> [2,3,4,6]
    const a = try placeholder(&graph, &.{ 2, 3, 4, 5 }, .f32);
    const b = try placeholder(&graph, &.{ 2, 3, 5, 6 }, .f32);
    
    const result = try matmul(a, b);
    
    // Verify output shape
    try testing.expectEqual(@as(usize, 4), result.shape.dims.len);
    try testing.expectEqual(@as(i64, 2), result.shape.dims[0].concrete);
    try testing.expectEqual(@as(i64, 3), result.shape.dims[1].concrete);
    try testing.expectEqual(@as(i64, 4), result.shape.dims[2].concrete);
    try testing.expectEqual(@as(i64, 6), result.shape.dims[3].concrete);
}

test "batch matmul 5D @ 5D" {
    const testing = std.testing;
    const Graph = @import("graph").Graph;
    const placeholder = @import("../creation.zig").placeholder;

    var graph = try Graph.init(testing.allocator);
    defer graph.deinit();

    // Test 5D @ 5D: [2,2,2,3,4] @ [2,2,2,4,5] -> [2,2,2,3,5]
    const a = try placeholder(&graph, &.{ 2, 2, 2, 3, 4 }, .f32);
    const b = try placeholder(&graph, &.{ 2, 2, 2, 4, 5 }, .f32);
    
    const result = try matmul(a, b);
    
    // Verify output shape
    try testing.expectEqual(@as(usize, 5), result.shape.dims.len);
    try testing.expectEqual(@as(i64, 2), result.shape.dims[0].concrete);
    try testing.expectEqual(@as(i64, 2), result.shape.dims[1].concrete);
    try testing.expectEqual(@as(i64, 2), result.shape.dims[2].concrete);
    try testing.expectEqual(@as(i64, 3), result.shape.dims[3].concrete);
    try testing.expectEqual(@as(i64, 5), result.shape.dims[4].concrete);
}

test "vector operations" {
    const testing = std.testing;
    const Graph = @import("graph").Graph;
    const placeholder = @import("../creation.zig").placeholder;

    var graph = try Graph.init(testing.allocator);
    defer graph.deinit();

    // Test dot product
    const a = try placeholder(&graph, &.{3}, .f32);
    const b = try placeholder(&graph, &.{3}, .f32);
    
    const dot_result = try dot(a, b);
    // Dot product reduces all dimensions but keeps shape as [1]
    try testing.expectEqual(@as(usize, 1), dot_result.shape.dims.len);
    try testing.expectEqual(@as(i64, 1), dot_result.shape.dims[0].concrete);
    
    // Test outer product
    const c = try placeholder(&graph, &.{2}, .f32);
    const d = try placeholder(&graph, &.{3}, .f32);
    
    const outer_result = try outer(c, d);
    try testing.expectEqual(@as(usize, 2), outer_result.shape.dims.len);
    try testing.expectEqual(@as(i64, 2), outer_result.shape.dims[0].concrete);
    try testing.expectEqual(@as(i64, 3), outer_result.shape.dims[1].concrete);
}