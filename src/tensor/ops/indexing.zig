const std = @import("std");
const types = @import("types");
const DataType = types.DataType;
const NodeId = types.NodeId;
const shape_mod = @import("shape");
const SymbolicDim = shape_mod.SymbolicDim;
const ShapeTracker = shape_mod.ShapeTracker;

// Import TensorHandle type reference
const TensorHandle = @import("../handle.zig").TensorHandle;

// Import needed operations
const comparison = @import("./comparison.zig");
const view = @import("./view.zig");
const reduction = @import("./reduction.zig");
const creation = @import("../creation.zig");

/// Gather operation - select elements from a tensor using indices
/// Similar to embedding lookup in neural networks
/// 
/// Implementation follows Luminal's approach:
/// 1. Create one-hot encoding of indices
/// 2. Expand and multiply with source tensor
/// 3. Sum reduce to select the indexed elements
///
/// # Arguments
/// - self: Source tensor of shape [vocab_size, embedding_dim] or higher dimensions
/// - indices: Index tensor of shape [batch_size] containing indices to gather
///
/// # Returns
/// - Tensor of shape [batch_size, embedding_dim] containing gathered elements
pub fn gather(self: TensorHandle, indices: TensorHandle) !TensorHandle {
    // For simplicity, we'll start with 2D source tensor (matrix) case
    // Can be extended to support higher dimensions later
    
    if (self.shape.dims.len != 2) {
        std.log.err("gather: currently only supports 2D source tensors, got {}D", .{self.shape.dims.len});
        return error.UnsupportedDimension;
    }
    
    if (indices.shape.dims.len != 1) {
        std.log.err("gather: indices must be 1D, got {}D", .{indices.shape.dims.len});
        return error.InvalidIndicesDimension;
    }
    
    // Get dimensions
    const vocab_size = self.shape.dims[0]; // First dimension of source
    const embedding_dim = self.shape.dims[1]; // Second dimension of source
    const batch_size = indices.shape.dims[0]; // Size of indices
    
    // Create arange tensor for one-hot encoding (use same dtype as indices)
    const range = try creation.arange(self.graph, vocab_size.concrete, indices.dtype);
    
    // Expand range to [batch_size, vocab_size]
    const unsqueezed_range = try range.unsqueeze(0);
    const expanded_range = try unsqueezed_range.expand(&.{ batch_size.concrete, vocab_size.concrete });
    
    // Expand indices to [batch_size, vocab_size]
    const unsqueezed_indices = try indices.unsqueeze(1);
    const expanded_indices = try unsqueezed_indices.expand(&.{ batch_size.concrete, vocab_size.concrete });
    
    // Create one-hot encoding: range == indices
    const one_hot = try expanded_range.equal(expanded_indices);
    
    // Expand one_hot to [batch_size, vocab_size, embedding_dim]
    const unsqueezed_one_hot = try one_hot.unsqueeze(2);
    const one_hot_expanded = try unsqueezed_one_hot.expand(&.{ 
        batch_size.concrete, 
        vocab_size.concrete, 
        embedding_dim.concrete 
    });
    
    // Expand source tensor to [batch_size, vocab_size, embedding_dim]
    const unsqueezed_source = try self.unsqueeze(0);
    const source_expanded = try unsqueezed_source.expand(&.{ 
        batch_size.concrete, 
        vocab_size.concrete, 
        embedding_dim.concrete 
    });
    
    // Multiply one-hot with source (element-wise)
    const selected = try one_hot_expanded.mul(source_expanded);
    
    // Sum reduce along vocab dimension (axis=1) to get final result
    const summed = try selected.sum(1, true);
    
    // The sum operation keeps the dimension with size 1, so squeeze it
    return summed.squeeze(1);
}

// ===== Unit Tests =====

test "gather operation" {
    const testing = std.testing;
    const Graph = @import("graph").Graph;
    const placeholder = @import("../creation.zig").placeholder;
    
    var graph = try Graph.init(testing.allocator);
    defer graph.deinit();
    
    // Create a simple embedding matrix [3, 2]
    // Row 0: [1.0, 2.0]
    // Row 1: [3.0, 4.0]  
    // Row 2: [5.0, 6.0]
    const matrix = try placeholder(&graph, &.{3, 2}, .f32);
    
    // Create indices tensor [2] with values [2, 0]
    // This should select row 2 and row 0
    const indices = try placeholder(&graph, &.{2}, .i32);
    
    // Perform gather
    const result = try gather(matrix, indices);
    
    // Check output shape
    try testing.expectEqual(@as(usize, 2), result.shape.dims.len);
    try testing.expectEqual(@as(i64, 2), result.shape.dims[0].concrete); // batch_size
    try testing.expectEqual(@as(i64, 2), result.shape.dims[1].concrete); // embedding_dim
    
    // The result should be:
    // [[5.0, 6.0],  // row 2
    //  [1.0, 2.0]]  // row 0
}

test "gather with dynamic dimensions" {
    const testing = std.testing;
    const Graph = @import("graph").Graph;
    const placeholder = @import("../creation.zig").placeholder;
    
    var graph = try Graph.init(testing.allocator);
    defer graph.deinit();
    
    // Create embedding matrix with known vocab size but dynamic batch
    const vocab_size = 100;
    const embed_dim = 64;
    const embeddings = try placeholder(&graph, &.{vocab_size, embed_dim}, .f32);
    
    // Indices with dynamic batch size (will be determined at runtime)
    const batch_size = 32;
    const indices = try placeholder(&graph, &.{batch_size}, .i32);
    
    // Perform gather
    const output = try gather(embeddings, indices);
    
    // Verify output shape
    try testing.expectEqual(@as(usize, 2), output.shape.dims.len);
    try testing.expectEqual(@as(i64, batch_size), output.shape.dims[0].concrete);
    try testing.expectEqual(@as(i64, embed_dim), output.shape.dims[1].concrete);
}

test "gather error cases" {
    const testing = std.testing;
    const Graph = @import("graph").Graph;
    const placeholder = @import("../creation.zig").placeholder;
    
    var graph = try Graph.init(testing.allocator);
    defer graph.deinit();
    
    // Test with non-2D source tensor
    const tensor_3d = try placeholder(&graph, &.{10, 20, 30}, .f32);
    const indices_1d = try placeholder(&graph, &.{5}, .i32);
    try testing.expectError(error.UnsupportedDimension, gather(tensor_3d, indices_1d));
    
    // Test with non-1D indices
    const tensor_2d = try placeholder(&graph, &.{10, 20}, .f32);
    const indices_2d = try placeholder(&graph, &.{5, 3}, .i32);
    try testing.expectError(error.InvalidIndicesDimension, gather(tensor_2d, indices_2d));
}