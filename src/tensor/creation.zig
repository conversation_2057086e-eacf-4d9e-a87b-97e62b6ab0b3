const std = @import("std");
const Allocator = std.mem.Allocator;

// Import types
const types = @import("types");
const DataType = types.DataType;
const NodeId = types.NodeId;
const GraphError = types.GraphError;

// Import core components
const Graph = @import("graph").Graph;
const shape_mod = @import("shape");
const ShapeTracker = shape_mod.ShapeTracker;
const SymbolicDim = shape_mod.SymbolicDim;

// Import TensorHandle - use relative import since we're inside tensor module
const TensorHandle = @import("handle.zig").TensorHandle;

// Helper to create a TensorHandle from a node and a concrete shape
fn makeTensor(graph: *Graph, node_id: NodeId, shape: []const i64, dtype: DataType) !TensorHandle {
    const allocator = graph.arena.allocator();
    const symbolic_dims = try allocator.alloc(SymbolicDim, shape.len);
    for (shape, 0..) |dim, i| {
        symbolic_dims[i] = SymbolicDim{ .concrete = dim };
    }
    const shape_tracker = try ShapeTracker.fromDims(symbolic_dims, allocator, &graph.symbolic_pool);
    
    // Set output shape metadata for data nodes
    const node = graph.getNodeMut(node_id).?;
    if (node.metadata == null) {
        node.metadata = try allocator.create(@import("graph").NodeMetadata);
        node.metadata.?.* = .{};
    }
    node.metadata.?.output_shape = try shape_tracker.clone(allocator);
    
    return TensorHandle{
        .graph = graph,
        .node_id = node_id,
        .shape = shape_tracker,
        .dtype = dtype,
    };
}

/// Create a tensor filled with zeros
pub fn zeros(graph: *Graph, shape: []const i64, dtype: DataType) !TensorHandle {
    // Create a scalar constant
    const scalar = try constant(graph, 0.0, dtype);
    // Broadcast it to the desired shape
    return scalar.broadcast(shape);
}

/// Create a tensor filled with ones
pub fn ones(graph: *Graph, shape: []const i64, dtype: DataType) !TensorHandle {
    // Create a scalar constant
    const scalar = try constant(graph, 1.0, dtype);
    // Broadcast it to the desired shape
    return scalar.broadcast(shape);
}

/// Create a placeholder tensor (input to the graph)
pub fn placeholder(graph: *Graph, shape: []const i64, dtype: DataType) !TensorHandle {
    // Validate shape dimensions (at TensorHandle level)
    for (shape, 0..) |dim, i| {
        if (dim <= 0) {
            std.log.err("placeholder: invalid dimension {} at index {} (must be positive)", .{ dim, i });
            return error.InvalidDimension;
        }
    }
    const node_id = try graph.addPlaceholder(dtype);
    return makeTensor(graph, node_id, shape, dtype);
}

/// Create a constant scalar tensor
pub fn constant(graph: *Graph, value: f32, dtype: DataType) !TensorHandle {
    const node_id = try graph.addConstant(value);

    // Scalar has empty shape
    const allocator = graph.arena.allocator();
    const symbolic_dims = try allocator.alloc(SymbolicDim, 0);

    const shape_tracker = try ShapeTracker.fromDims(symbolic_dims, allocator, &graph.symbolic_pool);
    
    // Set output shape metadata for the constant node
    const node = graph.getNodeMut(node_id).?;
    if (node.metadata == null) {
        node.metadata = try allocator.create(@import("graph").NodeMetadata);
        node.metadata.?.* = .{};
    }
    node.metadata.?.output_shape = try shape_tracker.clone(allocator);
    
    return TensorHandle{ .graph = graph, .node_id = node_id, .shape = shape_tracker, .dtype = dtype };
}

/// Create a tensor filled with a specific value
pub fn full(graph: *Graph, shape: []const i64, value: f32, dtype: DataType) !TensorHandle {
    // Create a scalar constant
    const scalar = try constant(graph, value, dtype);
    // Broadcast it to the desired shape
    return scalar.broadcast(shape);
}

/// Create a parameter tensor (trainable variable)
/// This links to the ParameterStore by using the same NodeId as StateId
pub fn parameter(graph: *Graph, shape: []const i64, state_id: NodeId, dtype: DataType) !TensorHandle {
    // The parameter node uses a specific ID that matches the StateId in ParameterStore
    const node_id = try graph.addParameter(state_id, dtype);
    return makeTensor(graph, node_id, shape, dtype);
}

/// Create a range tensor from 0 to n-1
/// Similar to numpy.arange or torch.arange
/// 
/// For now, this creates a special arange node that will be handled by the backend
/// In the future, this can be decomposed into cumsum operations
pub fn arange(graph: *Graph, n: i64, dtype: DataType) !TensorHandle {
    if (n <= 0) {
        std.log.err("arange: n must be positive, got {}", .{n});
        return error.InvalidSize;
    }
    
    // Create a special constant node that represents arange
    // The backend will recognize this pattern and generate the appropriate values
    const node_id = try graph.addConstant(0.0); // Base value
    
    // Create tensor with metadata indicating it's an arange operation
    const tensor = try makeTensor(graph, node_id, &.{n}, dtype);
    
    // Mark this node with metadata for the backend
    const node = graph.getNodeMut(node_id).?;
    if (node.metadata == null) {
        const allocator = graph.arena.allocator();
        node.metadata = try allocator.create(@import("graph").NodeMetadata);
        node.metadata.?.* = .{};
    }
    
    // Store arange info in metadata (the backend will use this)
    // For now, we'll just return the tensor with the right shape
    
    return tensor;
}

// ===== Unit Tests =====

test "tensor creation functions" {
    const testing = std.testing;

    var graph = try Graph.init(testing.allocator);
    defer graph.deinit();

    // Test zeros
    const z = try zeros(&graph, &.{ 2, 3 }, .f32);
    try testing.expectEqual(@as(usize, 2), z.shape.dims.len);
    try testing.expectEqual(@as(i64, 2), z.shape.dims[0].concrete);
    try testing.expectEqual(@as(i64, 3), z.shape.dims[1].concrete);
    try testing.expectEqual(DataType.f32, z.dtype);

    // Test ones
    const o = try ones(&graph, &.{ 4, 5 }, .f32);
    try testing.expectEqual(@as(usize, 2), o.shape.dims.len);
    try testing.expectEqual(@as(i64, 4), o.shape.dims[0].concrete);
    try testing.expectEqual(@as(i64, 5), o.shape.dims[1].concrete);

    // Test placeholder
    const p = try placeholder(&graph, &.{10}, .f32);
    try testing.expectEqual(@as(usize, 1), p.shape.dims.len);
    try testing.expectEqual(@as(i64, 10), p.shape.dims[0].concrete);

    // Test invalid placeholder dimension
    try testing.expectError(error.InvalidDimension, placeholder(&graph, &.{ -1, 5 }, .f32));

    // Test constant scalar
    const c = try constant(&graph, 3.14, .f32);
    try testing.expectEqual(@as(usize, 0), c.shape.dims.len); // Scalar

    // Test full
    const f = try full(&graph, &.{ 2, 2 }, 7.0, .f32);
    try testing.expectEqual(@as(usize, 2), f.shape.dims.len);
    try testing.expectEqual(@as(i64, 2), f.shape.dims[0].concrete);
    try testing.expectEqual(@as(i64, 2), f.shape.dims[1].concrete);
}

test "arange creation" {
    const testing = std.testing;

    var graph = try Graph.init(testing.allocator);
    defer graph.deinit();

    // Test arange with n=1
    const a1 = try arange(&graph, 1, .f32);
    try testing.expectEqual(@as(usize, 1), a1.shape.dims.len);
    try testing.expectEqual(@as(i64, 1), a1.shape.dims[0].concrete);

    // Test arange with n=5
    const a5 = try arange(&graph, 5, .f32);
    try testing.expectEqual(@as(usize, 1), a5.shape.dims.len);
    try testing.expectEqual(@as(i64, 5), a5.shape.dims[0].concrete);

    // Test arange with n=10
    const a10 = try arange(&graph, 10, .i32);
    try testing.expectEqual(@as(usize, 1), a10.shape.dims.len);
    try testing.expectEqual(@as(i64, 10), a10.shape.dims[0].concrete);
    try testing.expectEqual(DataType.i32, a10.dtype);

    // Test invalid arange
    try testing.expectError(error.InvalidSize, arange(&graph, 0, .f32));
    try testing.expectError(error.InvalidSize, arange(&graph, -5, .f32));
}

test "parameter creation" {
    const testing = std.testing;

    var graph = try Graph.init(testing.allocator);
    defer graph.deinit();

    // Create parameter with specific state ID
    const state_id: NodeId = 100;
    const param = try parameter(&graph, &.{ 3, 4 }, state_id, .f32);

    try testing.expectEqual(state_id, param.node_id);
    try testing.expectEqual(@as(usize, 2), param.shape.dims.len);
    try testing.expectEqual(@as(i64, 3), param.shape.dims[0].concrete);
    try testing.expectEqual(@as(i64, 4), param.shape.dims[1].concrete);
}
