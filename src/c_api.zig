//! C API for Zing tensor library
//! 
//! This module provides a C-compatible interface to the Zig Session API.
//! Memory management: All tensors are owned by the session and automatically
//! cleaned up when the session is destroyed.

const std = @import("std");
const session_mod = @import("session");
const types = @import("types");

// Type aliases for cleaner code
const Session = session_mod.Session;
const SessionTensor = session_mod.SessionTensor;
const BackendConfig = session_mod.BackendConfig;
const ExecutionMode = session_mod.ExecutionMode;
const DataType = types.DataType;
const Device = types.Device;

// Global allocator for C API
var global_allocator: std.mem.Allocator = undefined;
var allocator_initialized = false;

// Thread-local error state
threadlocal var last_error: c_int = 0;
threadlocal var error_message: [256]u8 = undefined;
threadlocal var error_message_set = false;

// Error codes matching C header
pub const ZING_OK: c_int = 0;
pub const ZING_ERROR_INVALID_SHAPE: c_int = 1;
pub const ZING_ERROR_INCOMPATIBLE_TENSORS: c_int = 2;
pub const ZING_ERROR_BACKEND_NOT_AVAILABLE: c_int = 3;
pub const ZING_ERROR_OUT_OF_MEMORY: c_int = 4;
pub const ZING_ERROR_INVALID_DTYPE: c_int = 5;
pub const ZING_ERROR_SESSION_NULL: c_int = 6;
pub const ZING_ERROR_TENSOR_NULL: c_int = 7;
pub const ZING_ERROR_BUFFER_TOO_SMALL: c_int = 8;

// Data type mapping
fn zigDtypeFromC(c_dtype: c_int) ?DataType {
    return switch (c_dtype) {
        0 => .f32,  // ZING_DTYPE_F32
        1 => .f64,  // ZING_DTYPE_F64
        2 => .i32,  // ZING_DTYPE_I32
        3 => .i64,  // ZING_DTYPE_I64
        else => null,
    };
}

fn cDtypeFromZig(zig_dtype: DataType) c_int {
    return switch (zig_dtype) {
        .f32 => 0,
        .f64 => 1,
        .i32 => 2,
        .i64 => 3,
        else => 0, // Default to f32
    };
}

// Device mapping
fn zigDeviceFromC(c_device: c_int) Device {
    return switch (c_device) {
        0 => .cpu,   // ZING_DEVICE_CPU
        1 => .cuda,  // ZING_DEVICE_CUDA
        2 => .metal, // ZING_DEVICE_METAL
        else => .cpu,
    };
}

// Execution mode mapping
fn zigModeFromC(c_mode: c_int) ExecutionMode {
    return switch (c_mode) {
        0 => .eager, // ZING_MODE_EAGER
        1 => .lazy,  // ZING_MODE_LAZY
        else => .eager,
    };
}

fn cModeFromZig(zig_mode: ExecutionMode) c_int {
    return switch (zig_mode) {
        .eager => 0,
        .lazy => 1,
    };
}

// Error handling utilities
fn setError(code: c_int, message: []const u8) void {
    last_error = code;
    const len = @min(message.len, error_message.len - 1);
    @memcpy(error_message[0..len], message[0..len]);
    error_message[len] = 0;
    error_message_set = true;
}

fn clearError() void {
    last_error = ZING_OK;
    error_message_set = false;
}

// Initialize allocator on first use
fn ensureAllocatorInitialized() void {
    if (!allocator_initialized) {
        global_allocator = std.heap.c_allocator;
        allocator_initialized = true;
    }
}

// Wrapper structures to hold Zig objects
const SessionWrapper = struct {
    session: Session,
    tensors: std.ArrayList(*TensorWrapper),
    
    fn create(allocator: std.mem.Allocator, session: Session) !*SessionWrapper {
        const wrapper = try allocator.create(SessionWrapper);
        wrapper.* = SessionWrapper{
            .session = session,
            .tensors = std.ArrayList(*TensorWrapper).init(allocator),
        };
        return wrapper;
    }
    
    fn destroy(self: *SessionWrapper, allocator: std.mem.Allocator) void {
        // Clean up all tensors
        for (self.tensors.items) |tensor_wrapper| {
            allocator.destroy(tensor_wrapper);
        }
        self.tensors.deinit();
        
        // Clean up session
        self.session.deinit();
        allocator.destroy(self);
    }
    
    fn addTensor(self: *SessionWrapper, allocator: std.mem.Allocator, tensor: SessionTensor) !*TensorWrapper {
        const tensor_wrapper = try allocator.create(TensorWrapper);
        tensor_wrapper.* = TensorWrapper{ 
            .tensor = tensor,
            .session = self,
        };
        try self.tensors.append(tensor_wrapper);
        return tensor_wrapper;
    }
};

const TensorWrapper = struct {
    tensor: SessionTensor,
    session: *SessionWrapper,  // Back-reference to session for adding result tensors
};

// ===== EXPORTED C FUNCTIONS =====

export fn zing_session_create() ?*SessionWrapper {
    ensureAllocatorInitialized();
    clearError();
    
    const session = Session.init(global_allocator) catch {
        setError(ZING_ERROR_OUT_OF_MEMORY, "Failed to create session");
        return null;
    };
    
    const wrapper = SessionWrapper.create(global_allocator, session) catch {
        setError(ZING_ERROR_OUT_OF_MEMORY, "Failed to create session wrapper");
        return null;
    };
    
    return wrapper;
}

export fn zing_session_create_with_backend(device: c_int) ?*SessionWrapper {
    ensureAllocatorInitialized();
    clearError();
    
    const zig_device = zigDeviceFromC(device);
    const backend_config = BackendConfig{ .device = zig_device };
    
    const session = Session.initWithBackend(global_allocator, backend_config) catch {
        setError(ZING_ERROR_BACKEND_NOT_AVAILABLE, "Failed to create session with backend");
        return null;
    };
    
    const wrapper = SessionWrapper.create(global_allocator, session) catch {
        setError(ZING_ERROR_OUT_OF_MEMORY, "Failed to create session wrapper");
        return null;
    };
    
    return wrapper;
}

export fn zing_session_destroy(session_ptr: ?*SessionWrapper) void {
    ensureAllocatorInitialized();
    clearError();
    
    if (session_ptr) |session| {
        session.destroy(global_allocator);
    }
}

export fn zing_session_set_mode(session_ptr: ?*SessionWrapper, mode: c_int) c_int {
    clearError();
    
    const session = session_ptr orelse {
        setError(ZING_ERROR_SESSION_NULL, "Session is null");
        return ZING_ERROR_SESSION_NULL;
    };
    
    const zig_mode = zigModeFromC(mode);
    session.session.setMode(zig_mode);
    return ZING_OK;
}

export fn zing_session_get_mode(session_ptr: ?*SessionWrapper) c_int {
    clearError();
    
    const session = session_ptr orelse {
        setError(ZING_ERROR_SESSION_NULL, "Session is null");
        return -1;
    };
    
    return cModeFromZig(session.session.getMode());
}

export fn zing_session_run(session_ptr: ?*SessionWrapper) c_int {
    clearError();
    
    const session = session_ptr orelse {
        setError(ZING_ERROR_SESSION_NULL, "Session is null");
        return ZING_ERROR_SESSION_NULL;
    };
    
    session.session.run() catch {
        setError(ZING_ERROR_OUT_OF_MEMORY, "Failed to run session");
        return ZING_ERROR_OUT_OF_MEMORY;
    };
    
    return ZING_OK;
}

export fn zing_zeros(session_ptr: ?*SessionWrapper, shape_ptr: [*c]const i64, ndim: usize, dtype: c_int) ?*TensorWrapper {
    clearError();
    
    const session = session_ptr orelse {
        setError(ZING_ERROR_SESSION_NULL, "Session is null");
        return null;
    };
    
    const zig_dtype = zigDtypeFromC(dtype) orelse {
        setError(ZING_ERROR_INVALID_DTYPE, "Invalid data type");
        return null;
    };
    
    const shape = shape_ptr[0..ndim];
    const tensor = session.session.zeros(shape, zig_dtype) catch {
        setError(ZING_ERROR_INVALID_SHAPE, "Failed to create zeros tensor");
        return null;
    };
    
    const wrapper = session.addTensor(global_allocator, tensor) catch {
        setError(ZING_ERROR_OUT_OF_MEMORY, "Failed to add tensor to session");
        return null;
    };
    
    return wrapper;
}

export fn zing_ones(session_ptr: ?*SessionWrapper, shape_ptr: [*c]const i64, ndim: usize, dtype: c_int) ?*TensorWrapper {
    clearError();
    
    const session = session_ptr orelse {
        setError(ZING_ERROR_SESSION_NULL, "Session is null");
        return null;
    };
    
    const zig_dtype = zigDtypeFromC(dtype) orelse {
        setError(ZING_ERROR_INVALID_DTYPE, "Invalid data type");
        return null;
    };
    
    const shape = shape_ptr[0..ndim];
    const tensor = session.session.ones(shape, zig_dtype) catch {
        setError(ZING_ERROR_INVALID_SHAPE, "Failed to create ones tensor");
        return null;
    };
    
    const wrapper = session.addTensor(global_allocator, tensor) catch {
        setError(ZING_ERROR_OUT_OF_MEMORY, "Failed to add tensor to session");
        return null;
    };
    
    return wrapper;
}

export fn zing_placeholder(session_ptr: ?*SessionWrapper, shape_ptr: [*c]const i64, ndim: usize, dtype: c_int) ?*TensorWrapper {
    clearError();
    
    const session = session_ptr orelse {
        setError(ZING_ERROR_SESSION_NULL, "Session is null");
        return null;
    };
    
    const zig_dtype = zigDtypeFromC(dtype) orelse {
        setError(ZING_ERROR_INVALID_DTYPE, "Invalid data type");
        return null;
    };
    
    const shape = shape_ptr[0..ndim];
    const tensor = session.session.placeholder(shape, zig_dtype) catch {
        setError(ZING_ERROR_INVALID_SHAPE, "Failed to create placeholder tensor");
        return null;
    };
    
    const wrapper = session.addTensor(global_allocator, tensor) catch {
        setError(ZING_ERROR_OUT_OF_MEMORY, "Failed to add tensor to session");
        return null;
    };
    
    return wrapper;
}

export fn zing_add(a_ptr: ?*TensorWrapper, b_ptr: ?*TensorWrapper) ?*TensorWrapper {
    clearError();
    
    const a = a_ptr orelse {
        setError(ZING_ERROR_TENSOR_NULL, "Tensor A is null");
        return null;
    };
    
    const b = b_ptr orelse {
        setError(ZING_ERROR_TENSOR_NULL, "Tensor B is null");
        return null;
    };
    
    const result_tensor = a.tensor.add(b.tensor) catch {
        setError(ZING_ERROR_INCOMPATIBLE_TENSORS, "Failed to add tensors");
        return null;
    };
    
    const wrapper = a.session.addTensor(global_allocator, result_tensor) catch {
        setError(ZING_ERROR_OUT_OF_MEMORY, "Failed to add result tensor to session");
        return null;
    };
    
    return wrapper;
}

export fn zing_multiply(a_ptr: ?*TensorWrapper, b_ptr: ?*TensorWrapper) ?*TensorWrapper {
    clearError();
    
    const a = a_ptr orelse {
        setError(ZING_ERROR_TENSOR_NULL, "Tensor A is null");
        return null;
    };
    
    const b = b_ptr orelse {
        setError(ZING_ERROR_TENSOR_NULL, "Tensor B is null");
        return null;
    };
    
    const result_tensor = a.tensor.mul(b.tensor) catch {
        setError(ZING_ERROR_INCOMPATIBLE_TENSORS, "Failed to multiply tensors");
        return null;
    };
    
    const wrapper = a.session.addTensor(global_allocator, result_tensor) catch {
        setError(ZING_ERROR_OUT_OF_MEMORY, "Failed to add result tensor to session");
        return null;
    };
    
    return wrapper;
}

export fn zing_subtract(a_ptr: ?*TensorWrapper, b_ptr: ?*TensorWrapper) ?*TensorWrapper {
    clearError();
    
    const a = a_ptr orelse {
        setError(ZING_ERROR_TENSOR_NULL, "Tensor A is null");
        return null;
    };
    
    const b = b_ptr orelse {
        setError(ZING_ERROR_TENSOR_NULL, "Tensor B is null");
        return null;
    };
    
    const result_tensor = a.tensor.subtract(b.tensor) catch {
        setError(ZING_ERROR_INCOMPATIBLE_TENSORS, "Failed to subtract tensors");
        return null;
    };
    
    const wrapper = a.session.addTensor(global_allocator, result_tensor) catch {
        setError(ZING_ERROR_OUT_OF_MEMORY, "Failed to add result tensor to session");
        return null;
    };
    
    return wrapper;
}

export fn zing_divide(a_ptr: ?*TensorWrapper, b_ptr: ?*TensorWrapper) ?*TensorWrapper {
    clearError();
    
    const a = a_ptr orelse {
        setError(ZING_ERROR_TENSOR_NULL, "Tensor A is null");
        return null;
    };
    
    const b = b_ptr orelse {
        setError(ZING_ERROR_TENSOR_NULL, "Tensor B is null");
        return null;
    };
    
    const result_tensor = a.tensor.divide(b.tensor) catch {
        setError(ZING_ERROR_INCOMPATIBLE_TENSORS, "Failed to divide tensors");
        return null;
    };
    
    const wrapper = a.session.addTensor(global_allocator, result_tensor) catch {
        setError(ZING_ERROR_OUT_OF_MEMORY, "Failed to add result tensor to session");
        return null;
    };
    
    return wrapper;
}

export fn zing_matmul(a_ptr: ?*TensorWrapper, b_ptr: ?*TensorWrapper) ?*TensorWrapper {
    clearError();
    
    const a = a_ptr orelse {
        setError(ZING_ERROR_TENSOR_NULL, "Tensor A is null");
        return null;
    };
    
    const b = b_ptr orelse {
        setError(ZING_ERROR_TENSOR_NULL, "Tensor B is null");
        return null;
    };
    
    const result_tensor = a.tensor.matmul(b.tensor) catch {
        setError(ZING_ERROR_INCOMPATIBLE_TENSORS, "Failed to multiply matrices");
        return null;
    };
    
    const wrapper = a.session.addTensor(global_allocator, result_tensor) catch {
        setError(ZING_ERROR_OUT_OF_MEMORY, "Failed to add result tensor to session");
        return null;
    };
    
    return wrapper;
}

export fn zing_relu(input_ptr: ?*TensorWrapper) ?*TensorWrapper {
    clearError();
    
    const input = input_ptr orelse {
        setError(ZING_ERROR_TENSOR_NULL, "Input tensor is null");
        return null;
    };
    
    const result_tensor = input.tensor.relu() catch {
        setError(ZING_ERROR_OUT_OF_MEMORY, "Failed to apply ReLU");
        return null;
    };
    
    const wrapper = input.session.addTensor(global_allocator, result_tensor) catch {
        setError(ZING_ERROR_OUT_OF_MEMORY, "Failed to add result tensor to session");
        return null;
    };
    
    return wrapper;
}

export fn zing_reshape(input_ptr: ?*TensorWrapper, new_shape_ptr: [*c]const i64, ndim: usize) ?*TensorWrapper {
    clearError();
    
    const input = input_ptr orelse {
        setError(ZING_ERROR_TENSOR_NULL, "Input tensor is null");
        return null;
    };
    
    const new_shape = new_shape_ptr[0..ndim];
    const result_tensor = input.tensor.reshape(new_shape) catch {
        setError(ZING_ERROR_INVALID_SHAPE, "Failed to reshape tensor");
        return null;
    };
    
    const wrapper = input.session.addTensor(global_allocator, result_tensor) catch {
        setError(ZING_ERROR_OUT_OF_MEMORY, "Failed to add result tensor to session");
        return null;
    };
    
    return wrapper;
}

export fn zing_tensor_rank(tensor_ptr: ?*TensorWrapper) usize {
    clearError();
    
    const tensor = tensor_ptr orelse {
        setError(ZING_ERROR_TENSOR_NULL, "Tensor is null");
        return 0;
    };
    
    return tensor.tensor.rank();
}

export fn zing_tensor_size(tensor_ptr: ?*TensorWrapper) i64 {
    clearError();
    
    const tensor = tensor_ptr orelse {
        setError(ZING_ERROR_TENSOR_NULL, "Tensor is null");
        return 0;
    };
    
    return tensor.tensor.size();
}

export fn zing_tensor_shape(tensor_ptr: ?*TensorWrapper, shape_buffer: [*c]i64, buffer_size: usize) c_int {
    clearError();
    
    const tensor = tensor_ptr orelse {
        setError(ZING_ERROR_TENSOR_NULL, "Tensor is null");
        return ZING_ERROR_TENSOR_NULL;
    };
    
    const tensor_shape = tensor.tensor.shape;
    if (tensor_shape.len > buffer_size) {
        setError(ZING_ERROR_BUFFER_TOO_SMALL, "Shape buffer too small");
        return ZING_ERROR_BUFFER_TOO_SMALL;
    }
    
    @memcpy(shape_buffer[0..tensor_shape.len], tensor_shape);
    return ZING_OK;
}

export fn zing_tensor_dtype(tensor_ptr: ?*TensorWrapper) c_int {
    clearError();
    
    const tensor = tensor_ptr orelse {
        setError(ZING_ERROR_TENSOR_NULL, "Tensor is null");
        return -1;
    };
    
    return cDtypeFromZig(tensor.tensor.dtype);
}

export fn zing_tensor_get_data_f32(tensor_ptr: ?*TensorWrapper, buffer: [*c]f32, buffer_size: usize) c_int {
    clearError();
    
    const tensor = tensor_ptr orelse {
        setError(ZING_ERROR_TENSOR_NULL, "Tensor is null");
        return ZING_ERROR_TENSOR_NULL;
    };
    
    const data = tensor.tensor.data(f32) catch {
        setError(ZING_ERROR_OUT_OF_MEMORY, "Failed to get tensor data");
        return ZING_ERROR_OUT_OF_MEMORY;
    };
    
    if (data.len > buffer_size) {
        setError(ZING_ERROR_BUFFER_TOO_SMALL, "Buffer too small for tensor data");
        return ZING_ERROR_BUFFER_TOO_SMALL;
    }
    
    @memcpy(buffer[0..data.len], data);
    return ZING_OK;
}

export fn zing_tensor_get_data_f64(tensor_ptr: ?*TensorWrapper, buffer: [*c]f64, buffer_size: usize) c_int {
    clearError();
    
    const tensor = tensor_ptr orelse {
        setError(ZING_ERROR_TENSOR_NULL, "Tensor is null");
        return ZING_ERROR_TENSOR_NULL;
    };
    
    const data = tensor.tensor.data(f64) catch {
        setError(ZING_ERROR_OUT_OF_MEMORY, "Failed to get tensor data");
        return ZING_ERROR_OUT_OF_MEMORY;
    };
    
    if (data.len > buffer_size) {
        setError(ZING_ERROR_BUFFER_TOO_SMALL, "Buffer too small for tensor data");
        return ZING_ERROR_BUFFER_TOO_SMALL;
    }
    
    @memcpy(buffer[0..data.len], data);
    return ZING_OK;
}

export fn zing_tensor_get_data_i32(tensor_ptr: ?*TensorWrapper, buffer: [*c]i32, buffer_size: usize) c_int {
    clearError();
    
    const tensor = tensor_ptr orelse {
        setError(ZING_ERROR_TENSOR_NULL, "Tensor is null");
        return ZING_ERROR_TENSOR_NULL;
    };
    
    const data = tensor.tensor.data(i32) catch {
        setError(ZING_ERROR_OUT_OF_MEMORY, "Failed to get tensor data");
        return ZING_ERROR_OUT_OF_MEMORY;
    };
    
    if (data.len > buffer_size) {
        setError(ZING_ERROR_BUFFER_TOO_SMALL, "Buffer too small for tensor data");
        return ZING_ERROR_BUFFER_TOO_SMALL;
    }
    
    @memcpy(buffer[0..data.len], data);
    return ZING_OK;
}

export fn zing_tensor_get_data_i64(tensor_ptr: ?*TensorWrapper, buffer: [*c]i64, buffer_size: usize) c_int {
    clearError();
    
    const tensor = tensor_ptr orelse {
        setError(ZING_ERROR_TENSOR_NULL, "Tensor is null");
        return ZING_ERROR_TENSOR_NULL;
    };
    
    const data = tensor.tensor.data(i64) catch {
        setError(ZING_ERROR_OUT_OF_MEMORY, "Failed to get tensor data");
        return ZING_ERROR_OUT_OF_MEMORY;
    };
    
    if (data.len > buffer_size) {
        setError(ZING_ERROR_BUFFER_TOO_SMALL, "Buffer too small for tensor data");
        return ZING_ERROR_BUFFER_TOO_SMALL;
    }
    
    @memcpy(buffer[0..data.len], data);
    return ZING_OK;
}

export fn zing_get_last_error() c_int {
    return last_error;
}

export fn zing_get_error_message() [*c]const u8 {
    if (error_message_set) {
        return @ptrCast(&error_message);
    }
    return "No error";
}

export fn zing_clear_error() void {
    clearError();
}

export fn zing_get_version() [*c]const u8 {
    return "0.2.0";
}

export fn zing_is_backend_available(device: c_int) c_int {
    clearError();
    
    return switch (device) {
        0 => 1, // CPU always available
        1 => 0, // CUDA not implemented yet
        2 => 0, // Metal not implemented yet
        else => 0,
    };
}