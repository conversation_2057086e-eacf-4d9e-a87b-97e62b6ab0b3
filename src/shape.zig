const std = @import("std");

// Import from other components
const core_types = @import("types");
const NodeId = core_types.NodeId;

// ============================================================================
// Shape Module - Root Export File
// ============================================================================

// Re-export all public APIs through a clean interface
pub const types = @import("shape/types.zig");
pub const ShapeTracker = @import("shape/tracker.zig").ShapeTracker;
pub const symbolic = @import("shape/symbolic.zig");
pub const inference = @import("shape/inference.zig");
pub const utils = @import("shape/utils.zig");
pub const index = @import("shape/index.zig");

// Convenience re-exports for commonly used types
pub const MAX_RANK = types.MAX_RANK;
pub const MAX_TENSOR_SIZE = types.MAX_TENSOR_SIZE;
pub const SymbolicDim = types.SymbolicDim;
pub const Axis = types.Axis;
pub const DataLayout = types.DataLayout;
pub const MemoryFormat = types.MemoryFormat;
pub const ShapeError = types.ShapeError;

// Re-export commonly used functions at top level
pub const inferBroadcastShape = inference.inferBroadcastShape;
pub const inferReduceShape = inference.inferReduceShape;
pub const inferMatmulShape = inference.inferMatmulShape;
pub const inferConcatShape = inference.inferConcatShape;

// Compile-time shape utilities
const comptime_shape = @import("shape/comptime_shape.zig");
pub const ComptimeShape = comptime_shape.ComptimeShape;
pub const comptimeStrides = comptime_shape.comptimeStrides;
pub const comptimeNumElements = comptime_shape.comptimeNumElements;
pub const comptimeBroadcastable = comptime_shape.comptimeBroadcastable;
pub const comptimeBroadcastShape = comptime_shape.comptimeBroadcastShape;
pub const comptimeMatmulShape = comptime_shape.comptimeMatmulShape;
pub const comptimeReshape = comptime_shape.comptimeReshape;
pub const comptimeTransposeShape = comptime_shape.comptimeTransposeShape;
pub const comptimeReduceShape = comptime_shape.comptimeReduceShape;

// HandleShapeMap has been deprecated - shape information is now stored in node metadata


// ============================================================================
// Tests - Re-export from modules
// ============================================================================

const testing = std.testing;
const expect = testing.expect;
const expectEqual = testing.expectEqual;
const expectError = testing.expectError;
const SymbolicPool = @import("symbolic").SymbolicPool;

test "SymbolicDim basic operations" {
    const allocator = testing.allocator;
    var pool = try SymbolicPool.init(allocator);
    defer pool.deinit();

    // Test concrete dimension
    const dim1 = SymbolicDim{ .concrete = 10 };
    try expect(dim1.isStatic());
    try expectEqual(@as(?i64, 10), dim1.getValue(&pool));

    // Test dynamic dimension
    const sym1 = try pool.variable("batch");
    const dim2 = SymbolicDim{ .dynamic = sym1 };
    try expect(!dim2.isStatic());
    try expectEqual(@as(?i64, null), dim2.getValue(&pool));

    // Test symbolic arithmetic
    const sum = try symbolic.symbolicAdd(&pool, dim1, dim2);
    try expect(!sum.isStatic());

    const prod = try symbolic.symbolicMul(&pool, dim1, dim1);
    try expect(prod.isStatic());
    try expectEqual(@as(i64, 100), prod.concrete);
}

test "ShapeTracker contiguity" {
    const allocator = testing.allocator;
    var pool = try SymbolicPool.init(allocator);
    defer pool.deinit();

    // Create contiguous shape
    const dims = try allocator.alloc(SymbolicDim, 3);
    defer allocator.free(dims);
    dims[0] = SymbolicDim{ .concrete = 2 };
    dims[1] = SymbolicDim{ .concrete = 3 };
    dims[2] = SymbolicDim{ .concrete = 4 };

    var tracker = try ShapeTracker.fromDims(dims, allocator, &pool);
    defer tracker.deinit(allocator);

    try expect(tracker.isContiguous());
    try expect(!tracker.isReshaped());

    // Test num elements
    const num_static = tracker.numElementsStatic();
    try expectEqual(@as(?i64, 24), num_static);
}

test "ShapeTracker broadcasting" {
    const allocator = testing.allocator;
    var pool = try SymbolicPool.init(allocator);
    defer pool.deinit();

    // Create shape [3, 1]
    const dims_a = [_]SymbolicDim{
        SymbolicDim{ .concrete = 3 },
        SymbolicDim{ .concrete = 1 },
    };
    var a = try ShapeTracker.fromDims(&dims_a, allocator, &pool);
    defer a.deinit(allocator);

    // Create shape [1, 4]
    const dims_b = [_]SymbolicDim{
        SymbolicDim{ .concrete = 1 },
        SymbolicDim{ .concrete = 4 },
    };
    var b = try ShapeTracker.fromDims(&dims_b, allocator, &pool);
    defer b.deinit(allocator);

    // Infer broadcast shape
    const result = try inference.inferBroadcastShape(&a, &b, allocator, &pool);
    defer result.deinit(allocator);

    try expectEqual(@as(usize, 2), result.dims.len);
    try expectEqual(@as(i64, 3), result.dims[0].concrete);
    try expectEqual(@as(i64, 4), result.dims[1].concrete);
}

test "broadcasting utilities" {
    const allocator = testing.allocator;

    // Test areBroadcastable
    try expect(utils.areBroadcastable(&[_]i64{ 3, 1 }, &[_]i64{ 1, 4 }));
    try expect(utils.areBroadcastable(&[_]i64{ 2, 3, 4 }, &[_]i64{4}));
    try expect(!utils.areBroadcastable(&[_]i64{ 3, 2 }, &[_]i64{ 2, 3 }));

    // Test broadcastShape
    const shape1 = try utils.broadcastShape(&[_]i64{ 3, 1 }, &[_]i64{ 1, 4 }, allocator);
    defer allocator.free(shape1);
    try expectEqual(@as(i64, 3), shape1[0]);
    try expectEqual(@as(i64, 4), shape1[1]);
}

test "Axis resolution" {
    const allocator = testing.allocator;

    // Test single axis
    const axis1 = Axis{ .index = 1 };
    const resolved1 = try axis1.resolve(3, allocator);
    defer allocator.free(resolved1);
    try expectEqual(@as(usize, 1), resolved1.len);
    try expectEqual(@as(usize, 1), resolved1[0]);

    // Test all axes
    const axis_all = Axis{ .all = {} };
    const resolved_all = try axis_all.resolve(3, allocator);
    defer allocator.free(resolved_all);
    try expectEqual(@as(usize, 3), resolved_all.len);
    try expectEqual(@as(usize, 0), resolved_all[0]);
    try expectEqual(@as(usize, 1), resolved_all[1]);
    try expectEqual(@as(usize, 2), resolved_all[2]);

    // Test out of bounds
    const axis_bad = Axis{ .index = 5 };
    try expectError(error.AxisOutOfBounds, axis_bad.resolve(3, allocator));
}

test "createStaticShape" {
    const shape = utils.createStaticShape(&[_]i64{ 2, 3, 4 });
    try expectEqual(@as(usize, 3), shape.len);
    try expectEqual(@as(i64, 2), shape[0].concrete);
    try expectEqual(@as(i64, 3), shape[1].concrete);
    try expectEqual(@as(i64, 4), shape[2].concrete);
}

test "error handling - validation" {
    const allocator = testing.allocator;
    var pool = try SymbolicPool.init(allocator);
    defer pool.deinit();

    // Test negative dimension
    const bad_dims = [_]SymbolicDim{
        SymbolicDim{ .concrete = -5 },
    };
    try expectError(error.NegativeDimension, ShapeTracker.fromDims(&bad_dims, allocator, &pool));

    // Test zero dimension
    const zero_dims = [_]SymbolicDim{
        SymbolicDim{ .concrete = 0 },
    };
    try expectError(error.ZeroDimension, ShapeTracker.fromDims(&zero_dims, allocator, &pool));

    // Test rank too high
    const too_many_dims = try allocator.alloc(SymbolicDim, MAX_RANK + 1);
    defer allocator.free(too_many_dims);
    for (too_many_dims) |*d| {
        d.* = SymbolicDim{ .concrete = 2 };
    }
    try expectError(error.RankTooHigh, ShapeTracker.fromDims(too_many_dims, allocator, &pool));
}

test "overflow protection" {
    const allocator = testing.allocator;
    var pool = try SymbolicPool.init(allocator);
    defer pool.deinit();

    // Test overflow during stride computation should be caught
    const large_dims_for_strides = [_]SymbolicDim{
        SymbolicDim{ .concrete = 1 << 30 }, // 1073741824 
        SymbolicDim{ .concrete = 1 << 30 }, // 1073741824
        SymbolicDim{ .concrete = 10 },      // This will cause overflow in stride computation
    };
    try expectError(error.StrideOverflow, ShapeTracker.fromDims(&large_dims_for_strides, allocator, &pool));
}

test "enhanced input validation" {
    const allocator = testing.allocator;
    var pool = try SymbolicPool.init(allocator);
    defer pool.deinit();

    // Test transpose validation with duplicate axes
    const dims = [_]SymbolicDim{
        SymbolicDim{ .concrete = 2 },
        SymbolicDim{ .concrete = 3 },
        SymbolicDim{ .concrete = 4 },
    };
    var tracker = try ShapeTracker.fromDims(&dims, allocator, &pool);
    defer tracker.deinit(allocator);

    // Test duplicate axis error
    const duplicate_axes = [_]usize{ 0, 1, 0 }; // Duplicate axis 0
    try expectError(error.DuplicateAxis, tracker.transpose(&duplicate_axes, allocator));

    // Test axis out of bounds
    const out_of_bounds_axes = [_]usize{ 0, 1, 5 }; // Axis 5 out of bounds for rank 3
    try expectError(error.AxisOutOfBounds, tracker.transpose(&out_of_bounds_axes, allocator));

    // Test wrong number of axes
    const wrong_length_axes = [_]usize{ 0, 1 }; // Only 2 axes for rank 3 tensor
    try expectError(error.InvalidPermutation, tracker.transpose(&wrong_length_axes, allocator));
}

test "performance optimization - stack allocation" {
    const allocator = testing.allocator;
    var pool = try SymbolicPool.init(allocator);
    defer pool.deinit();

    // Test that small shapes use stack allocation (should not allocate)
    const small_dims = [_]SymbolicDim{
        SymbolicDim{ .concrete = 2 },
        SymbolicDim{ .concrete = 3 },
    };
    var tracker = try ShapeTracker.fromDims(&small_dims, allocator, &pool);
    defer tracker.deinit(allocator);

    // logicalDims should use stack allocation for small ranks
    const logical = try tracker.logicalDims(allocator, &pool);
    try expectEqual(@as(usize, 2), logical.len);
    try expectEqual(@as(i64, 2), logical[0].concrete);
    try expectEqual(@as(i64, 3), logical[1].concrete);
    
    // For small ranks, logical should point to stack memory (no need to free)
    // This is validated by the fact that we don't get a memory leak
}

test "memory management - explicit ownership" {
    const allocator = testing.allocator;
    var pool = try SymbolicPool.init(allocator);
    defer pool.deinit();

    const dims = [_]SymbolicDim{
        SymbolicDim{ .concrete = 2 },
        SymbolicDim{ .concrete = 3 },
        SymbolicDim{ .concrete = 4 },
    };
    
    // Test explicit deinit
    {
        var tracker = try ShapeTracker.fromDims(&dims, allocator, &pool);
        defer tracker.deinit(allocator); // ShapeTracker always owns its arrays
        
        try expect(tracker.isContiguous());
        try expectEqual(@as(usize, 3), tracker.rank());
    }
    
    // Test clone functionality
    {
        var original = try ShapeTracker.fromDims(&dims, allocator, &pool);
        defer original.deinit(allocator); // ShapeTracker always owns its arrays
        
        var cloned = try original.clone(allocator);
        defer cloned.deinit(allocator); // ShapeTracker always owns its arrays
        
        // Verify cloned tracker has same properties
        try expectEqual(original.rank(), cloned.rank());
        try expectEqual(original.isContiguous(), cloned.isContiguous());
        
        // Verify independent memory (different pointers)
        try expect(original.dims.ptr != cloned.dims.ptr);
        try expect(original.strides.ptr != cloned.strides.ptr);
        try expect(original.indexes.ptr != cloned.indexes.ptr);
        try expect(original.fake.ptr != cloned.fake.ptr);
    }
}