/// Training infrastructure for Zing
///
/// This module provides the core training functionality including:
/// - Loss functions (MSE, CrossEntropy, etc.)
/// - Optimizers (SG<PERSON>, <PERSON>, etc.)
/// - Learning rate schedulers
/// - Training loops and utilities
///
/// The training module integrates with the Session API to provide
/// a high-level interface for model training.

const std = @import("std");

// Re-export all training components
pub const loss = @import("training/loss.zig");
pub const optimizer = @import("training/optimizer.zig");
pub const scheduler = @import("training/scheduler.zig");
pub const trainer = @import("training/trainer.zig");
pub const autograd = @import("training/autograd.zig");

// Convenience re-exports
pub const Loss = loss.Loss;
pub const Optimizer = optimizer.Optimizer;
pub const Scheduler = scheduler.Scheduler;
pub const Trainer = trainer.Trainer;

// Common training types
pub const TrainingConfig = trainer.TrainingConfig;
pub const TrainingMetrics = trainer.TrainingMetrics;