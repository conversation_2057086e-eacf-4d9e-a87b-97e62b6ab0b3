# Luminal ShapeTracker Analysis

## Overview

Based on my analysis of Luminal's codebase, here's a comprehensive breakdown of how ShapeTracker works and when tensors become non-contiguous or require `contiguous()` calls.

## 1. ShapeTracker Structure

```rust
pub struct ShapeTracker {
    pub dims: ArrayVec<[Expression; 6]>,      // The actual dimensions (physical shape)
    pub indexes: ArrayVec<[usize; 6]>,        // Permutation indices (how dims map to logical dims)
    pub fake: ArrayVec<[bool; 6]>,            // Fake/broadcasted dimensions
    pub mask: ArrayVec<[(Expression, Expression); 6]>,   // Slicing bounds (start, end)
    pub padding: ArrayVec<[(Expression, Expression); 6]>, // Padding (start, end)
}
```

## 2. What Makes a Tensor Non-Contiguous/Reshaped

A tensor is considered "reshaped" (non-contiguous) when `is_reshaped()` returns true:

```rust
pub fn is_reshaped(&self) -> bool {
    !self.is_contiguous() || self.is_sliced() || self.is_padded()
}
```

This happens when ANY of these conditions are true:

### a) Not Contiguous (`!is_contiguous()`)
```rust
pub fn is_contiguous(&self) -> bool {
    self.indexes.iter().enumerate().all(|(a, b)| a == *b) && self.fake.iter().all(|i| !*i)
}
```

A tensor is NOT contiguous when:
- **Permuted**: `indexes` array is not in order (e.g., `[0,1,2]` → `[2,0,1]` after permute)
- **Has fake dimensions**: Any dimension is marked as `fake=true` (broadcasted dimensions)

### b) Sliced (`is_sliced()`)
```rust
pub fn is_sliced(&self) -> bool {
    self.mask.iter().any(|(b, e)| {
        b.to_usize().map(|i| i != 0).unwrap_or(true) ||
        e.to_usize().map(|n| n as i32 != i32::MAX).unwrap_or(true)
    })
}
```

A tensor is sliced when any dimension has non-default mask bounds:
- Start bound != 0
- End bound != i32::MAX (unbounded)

### c) Padded (`is_padded()`)
```rust
pub fn is_padded(&self) -> bool {
    self.padding.iter().any(|(b, e)| {
        b.to_usize().map(|i| i != 0).unwrap_or(true) ||
        e.to_usize().map(|n| n != 0).unwrap_or(true)
    })
}
```

A tensor is padded when any dimension has non-zero padding.

## 3. When `contiguous()` is Called

### Explicit Calls:
1. **During reshape operations**:
```rust
pub fn reshape(mut self, new_shape: impl ToShape) -> GraphTensor {
    // Insert contiguous call
    self = self.contiguous();
    self.shape = ShapeTracker::new(new_shape);
    self
}
```

2. **Manual calls** when needed:
```rust
pub fn contiguous(mut self) -> GraphTensor {
    if !self.shape.is_reshaped() {
        return self;  // No-op if already contiguous
    }
    // Creates a Contiguous op node
    self.id = self.graph()
        .add_op(op::Contiguous)
        .input(self.id, 0, self.shape)
        .finish();
    self.shape = self.shape.contiguous();
    self
}
```

### Implicit Requirements:
Many operations return tensors with `self.shape.contiguous()`, which creates a fresh ShapeTracker:
```rust
// From binary ops like Add
GraphTensor::from_id(new_id, self.shape.contiguous(), self.graph_ref)

// From unary ops like log2, exp2, sqrt, etc.
GraphTensor::from_id(new_id, self.shape.contiguous(), self.graph_ref)
```

## 4. How Reshape Interacts with ShapeTracker

When `reshape()` is called:
1. **Forces contiguous layout first** - This materializes any virtual transformations
2. **Creates new ShapeTracker** - Completely fresh tracker with new dimensions
3. **Resets all tracking state** - No permutations, no fake dims, no slicing, no padding

This is why reshape always calls `contiguous()` first - it needs the data to be in standard row-major order before changing the interpretation of dimensions.

## 5. Physical vs Logical Shapes

### Physical Shape
- Stored in `dims` array
- The actual memory layout dimensions
- Modified by operations like slicing/padding

### Logical Shape
- Computed by `dims()` method
- Includes effects of padding and masking:
```rust
pub fn dims(&self) -> Vec<Expression> {
    self.indexes
        .into_iter()
        .map(|i| pad_mask_dim(self.dims[i], self.padding[i], self.mask[i]))
        .collect()
}
```

### Permutation affects logical ordering:
- `indexes` array maps logical dimension positions to physical positions
- Example: After `permute([2,0,1])`, logical dim 0 maps to physical dim 2

## 6. Index Translation

The key to understanding non-contiguous tensors is the index translation:

```rust
pub fn index_expression(&self) -> Expression {
    // Translates logical index to physical index
    // Accounts for: permutations, strides, masks, padding, fake dims
}

pub fn valid_expression(&self) -> Expression {
    // Returns whether a logical index is valid
    // Checks bounds considering padding and masking
}
```

## 7. Operations That Preserve/Break Contiguity

### Preserve Contiguity:
- Element-wise operations (add, mul, etc.) - return fresh contiguous result
- Reductions - return fresh contiguous result

### Break Contiguity:
- `permute()` - changes index ordering
- `expand()` - adds fake dimensions  
- `slice()` - sets mask bounds
- `pad()` - adds padding

### Force Contiguity:
- `reshape()` - always calls contiguous first
- `contiguous()` - explicit materialization

## Key Insight

Luminal's ShapeTracker is essentially a **view descriptor** that tracks how to map between logical tensor coordinates and physical memory locations. When a tensor becomes "non-contiguous," it means there's a mismatch between the logical view and physical layout that requires translation via index expressions. The `contiguous()` operation materializes these virtual transformations into actual memory reorganization.