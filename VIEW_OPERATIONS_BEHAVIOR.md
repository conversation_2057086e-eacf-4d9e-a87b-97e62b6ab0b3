# View Operations Behavior - Absolute Clarity

## CRITICAL UNDERSTANDING

View operations in Zing (`reshape`, `transpose`, `slice`, `expand`) have fundamentally different behavior from typical ML frameworks:

**THEY MODIFY EXISTING NODES IN-PLACE AND RETURN THE SAME NodeId**

## The Key Difference

### Traditional ML Framework (PyTorch, JAX, TensorFlow):
```python
a = torch.tensor([[1, 2, 3], [4, 5, 6]])    # tensor_id_1
b = a.transpose(0, 1)                        # tensor_id_2 (NEW object)
c = b.reshape(6)                             # tensor_id_3 (NEW object)
```

### Zing's Behavior:
```zig
const a = try graph.addPlaceholder(&.{2, 3}, .f32);  // node_id_1
const b = try graph.transpose(a, &.{1, 0});          // node_id_1 (SAME!)
const c = try graph.reshape(a, &.{6});               // node_id_1 (SAME!)

// ALL variables reference the SAME node
std.debug.assert(a == b);  // true
std.debug.assert(b == c);  // true
std.debug.assert(a == c);  // true
```

## What Actually Happens

### Node Structure:
```zig
pub const Node = struct {
    id: NodeId,
    op_type: OpType,
    inputs: []const NodeId,
    shape_tracker: ShapeTracker,    // EMBEDDED metadata
    // ...
};
```

### View Operations Process:
1. **Get mutable reference** to the existing Node
2. **Update the embedded ShapeTracker** with new metadata
3. **Return the SAME NodeId** 
4. **NO new nodes created** in the graph
5. **NO new edges created** in the graph

## Behavior Verification

```zig
test "view operations return same NodeId" {
    var graph = try Graph.init(allocator);
    defer graph.deinit();
    
    // Create initial node
    const original = try graph.addPlaceholder(&.{2, 3, 4}, .f32);
    const initial_node_count = graph.getNodeCount();
    
    // Apply view operations
    const reshaped = try graph.reshape(original, &.{6, 4});
    const transposed = try graph.transpose(original, &.{2, 0, 1});
    const sliced = try graph.slice(original, &.{0, 0, 0}, &.{1, 3, 4});
    const expanded = try graph.expand(original, &.{2, 3, 4, 1});
    
    // Verify same NodeId
    try expect(reshaped == original);    // SAME NodeId
    try expect(transposed == original);  // SAME NodeId
    try expect(sliced == original);      // SAME NodeId
    try expect(expanded == original);    // SAME NodeId
    
    // Verify no new nodes created
    try expect(graph.getNodeCount() == initial_node_count);
    
    // Only the ShapeTracker has been modified
    const final_node = graph.getNode(original).?;
    try expect(final_node.id == original);  // Same node
    // final_node.shape_tracker reflects the cumulative transformations
}
```

## Exception: contiguous()

**ONLY** the `contiguous()` operation may create a new node:

```zig
const a = try graph.addPlaceholder(&.{2, 3, 4}, .f32);
_ = try graph.transpose(a, &.{2, 0, 1});  // Makes non-contiguous

const contiguous_a = try graph.makeContiguous(a);
if (!isAlreadyContiguous) {
    std.debug.assert(contiguous_a != a);  // NEW NodeId when memory copy needed
} else {
    std.debug.assert(contiguous_a == a);  // SAME NodeId if already contiguous
}
```

## Documentation References

This behavior is now clarified in:
- `/docs/tensor.md` - Section 4.3 and Section 8
- `/docs/graph.md` - Section 5.4 and Section 6  
- `/docs/shape.md` - Section 3.2 and Section 10

## Why This Matters

1. **Performance**: No object allocation for view operations
2. **Memory**: ShapeTracker is embedded metadata, minimal overhead
3. **Graph size**: View operations don't increase node count
4. **Simplicity**: Same NodeId = same storage location, different interpretation
5. **Debugging**: Easier to track tensor identity through transformations

## Mental Model

Think of view operations as **updating the "viewing instructions"** for the same underlying data, not creating new tensors:

```
Original Tensor: [2, 3, 4] data at node_id_1
└─ ShapeTracker: dims=[2,3,4], strides=[12,4,1], offset=0

After reshape(6, 4): SAME data at node_id_1  
└─ ShapeTracker: dims=[6,4], strides=[4,1], offset=0

After transpose(1, 0): SAME data at node_id_1
└─ ShapeTracker: dims=[4,6], strides=[1,4], offset=0  

After slice(...): SAME data at node_id_1
└─ ShapeTracker: dims=[4,6], strides=[1,4], mask=[[0,2],[0,3]]
```

The NodeId never changes - only the "interpretation metadata" changes.