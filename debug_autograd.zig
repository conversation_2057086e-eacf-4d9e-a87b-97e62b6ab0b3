/// Debug script to investigate the broadcast reduction gradient issue

const std = @import("std");

// Import everything we need
const Graph = @import("graph").Graph;
const tensor = @import("tensor");
const TensorHandle = tensor.TensorHandle;
const training = @import("training");
const compiler = @import("compiler");
const PassContext = compiler.PassContext;
const execution = @import("execution");
const Executor = execution.Executor;
const CompiledGraph = execution.CompiledGraph;

pub fn main() !void {
    var arena = std.heap.ArenaAllocator.init(std.heap.page_allocator);
    defer arena.deinit();
    const allocator = arena.allocator();
    
    std.debug.print("=== DEBUGGING BROADCAST REDUCTION GRADIENT ===\n", .{});
    
    var graph = try Graph.init(allocator);
    
    // Test broadcasting: x[2,3] + b[3] -> sum
    const x = try tensor.placeholder(&graph, &.{2, 3}, .f32);
    const b = try tensor.placeholder(&graph, &.{3}, .f32);
    try graph.markParameter(x.node_id);
    try graph.markParameter(b.node_id);
    
    std.debug.print("Created parameters:\n", .{});
    std.debug.print("  x.node_id = {}\n", .{x.node_id});
    std.debug.print("  b.node_id = {}\n", .{b.node_id});
    
    // Add with broadcasting: x[2,3] + b[3] -> [2,3]
    const y = try x.add(b);  // This should broadcast b to [2,3]
    const z = try y.sumAll();  // Sum all elements
    try graph.markOutput(z.node_id);
    
    std.debug.print("Created operations:\n", .{});
    std.debug.print("  y.node_id = {} (broadcast add)\n", .{y.node_id});
    std.debug.print("  z.node_id = {} (sum all)\n", .{z.node_id});
    
    // Apply autograd
    var ctx = PassContext{
        .graph = &graph,
        .allocator = allocator,
    };
    
    std.debug.print("\nApplying autograd...\n", .{});
    try training.autograd.applyAutograd(&ctx, &.{x.node_id, b.node_id}, z.node_id);
    
    // Get gradient nodes
    const grad_map = graph.getGradientMap().?;
    const grad_x_id = grad_map.get(x.node_id).?;
    const grad_b_id = grad_map.get(b.node_id).?;
    try graph.markOutput(grad_x_id);
    try graph.markOutput(grad_b_id);
    
    std.debug.print("\nGradient nodes created:\n", .{});
    std.debug.print("  grad_x_id = {}\n", .{grad_x_id});
    std.debug.print("  grad_b_id = {}\n", .{grad_b_id});
    
    // Check output nodes before compilation
    std.debug.print("\nOutput nodes before compilation:\n", .{});
    for (graph.output_nodes.items, 0..) |output_id, i| {
        std.debug.print("  output_nodes[{}] = {}\n", .{i, output_id});
    }
    
    // Print all nodes in the graph
    std.debug.print("\nAll nodes in graph:\n", .{});
    for (0..20) |i| {
        const node_id = @as(u32, @intCast(i));
        if (graph.getNode(node_id)) |node| {
            std.debug.print("  Node {}: {s}\n", .{node_id, @tagName(node.spec)});
            if (node.spec == .compute) {
                std.debug.print("    Op: {s}\n", .{@tagName(node.spec.compute)});
                std.debug.print("    Inputs: [", .{});
                for (node.inputs, 0..) |input_id, idx| {
                    if (idx > 0) std.debug.print(", ", .{});
                    std.debug.print("{}", .{input_id});
                }
                std.debug.print("]\n", .{});
            }
        }
    }
    
    // Compile and execute
    std.debug.print("\nCompiling graph...\n", .{});
    const compiled = try compiler.compile.compileCpu(&graph, allocator);
    var compiled_heap = try allocator.create(CompiledGraph);
    compiled_heap.* = compiled;
    defer {
        compiled_heap.deinit(allocator);
        allocator.destroy(compiled_heap);
    }
    
    var executor = try Executor.init(allocator, compiled_heap, null);
    defer executor.deinit();
    
    // Set inputs
    const x_val = [_]f32{1.0, 2.0, 3.0, 4.0, 5.0, 6.0};  // [2,3]
    const b_val = [_]f32{10.0, 20.0, 30.0};  // [3]
    try executor.setInput(x.node_id, std.mem.sliceAsBytes(&x_val), &.{2, 3}, .f32);
    try executor.setInput(b.node_id, std.mem.sliceAsBytes(&b_val), &.{3}, .f32);
    
    // Execute
    std.debug.print("\nExecuting...\n", .{});
    try executor.run();
    
    // Try to get outputs one by one to see which fails
    std.debug.print("\nTrying to retrieve outputs:\n", .{});
    
    std.debug.print("  Getting z_result...\n", .{});
    const z_view = try executor.getOutput(z.node_id);
    const z_result = std.mem.bytesAsSlice(f32, z_view.data)[0];
    std.debug.print("  z = {d}\n", .{z_result});
    
    std.debug.print("  Getting grad_x...\n", .{});
    const grad_x_view = executor.getOutput(grad_x_id) catch |err| {
        std.debug.print("  ERROR getting grad_x (node {}): {}\n", .{grad_x_id, err});
        return;
    };
    const grad_x_vals = std.mem.bytesAsSlice(f32, grad_x_view.data);
    std.debug.print("  grad_x retrieved successfully, length: {}\n", .{grad_x_vals.len});
    
    std.debug.print("  Getting grad_b...\n", .{});
    const grad_b_view = executor.getOutput(grad_b_id) catch |err| {
        std.debug.print("  ERROR getting grad_b (node {}): {}\n", .{grad_b_id, err});
        std.debug.print("  This is the node that's missing!\n", .{});
        return;
    };
    const grad_b_vals = std.mem.bytesAsSlice(f32, grad_b_view.data);
    std.debug.print("  grad_b retrieved successfully, length: {}\n", .{grad_b_vals.len});
    
    std.debug.print("\nAll outputs retrieved successfully!\n", .{});
}