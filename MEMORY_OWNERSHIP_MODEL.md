# Zing Memory Ownership Model

This document establishes clear memory ownership rules for the Zing framework to prevent leaks, use-after-free bugs, and ownership confusion.

## Core Principles

1. **Explicit Ownership**: Every allocation has exactly one owner responsible for freeing it
2. **Arena for Graph Construction**: Use arena allocators for all graph-lifetime data
3. **Explicit for Runtime**: Use explicit allocation for data that outlives graph construction
4. **Clear Boundaries**: Document ownership transfer at every API boundary
5. **RAII Pattern**: Pair every `init` with `deinit`, every allocation with deallocation

## Ownership by Component

### Graph (graph.zig)
- **Owns via Arena**: All nodes, edges, consumer lists, metadata
- **Lifetime**: Arena lives as long as the Graph
- **Cleanup**: Single `arena.deinit()` frees everything
- **Special Case**: `cached_topology` uses explicit allocation (survives graph resets)

```zig
pub const Graph = struct {
    arena: ArenaAllocator,         // Owns all graph construction data
    nodes: ArrayListUnmanaged(Node), // Arena-allocated
    // ... all other fields use arena
    
    pub fn deinit(self: *Graph) void {
        // Special case: cached topology
        if (self.cached_topology) |topo| {
            self.allocator.free(topo);
        }
        // Everything else freed by arena
        self.arena.deinit();
    }
};
```

### ShapeTracker (shape/tracker.zig)
- **Always Owns**: dims, strides, indexes, fake, mask, padding arrays
- **Allocation**: Can be arena or explicit, but tracker always owns its arrays
- **Cleanup**: Call `deinit()` for non-arena trackers
- **View Operations**: Must free old arrays before assigning new ones

```zig
/// OWNERSHIP RULES:
/// - ShapeTracker ALWAYS owns all its arrays
/// - For arena-allocated trackers, no explicit cleanup needed
/// - For non-arena trackers, call deinit() to free all arrays
pub const ShapeTracker = struct {
    dims: []const SymbolicDim,    // Owned
    strides: []const SymbolicDim, // Owned
    // ... all arrays are owned
};
```

### TensorHandle (tensor.zig)
- **Uses Arena**: ShapeTracker is arena-allocated with Graph
- **Lifetime**: Lives as long as the Graph
- **No Cleanup**: Arena handles everything

```zig
pub const TensorHandle = struct {
    graph: *Graph,
    node_id: NodeId,
    shape: ShapeTracker, // Arena-allocated, no explicit free needed
    dtype: DataType,
};
```

### DataStorage (storage.zig)
- **Owns Tensors**: Via OwnedTensor with explicit allocation
- **Arena for Buffers**: Uses bump allocation for tensor data
- **Mixed Pattern**: Justified by different lifetimes
- **Cleanup**: Explicit free for metadata, arena for buffers

```zig
pub const DataStorage = struct {
    allocator: Allocator,           // For metadata
    memory_arena: []u8,             // Pre-allocated buffer pool
    tensors: AutoHashMapUnmanaged,  // Maps to OwnedTensor
    
    pub fn deinit(self: *DataStorage) void {
        // Free each OwnedTensor
        var iter = self.tensors.iterator();
        while (iter.next()) |entry| {
            entry.value_ptr.deinit(self.allocator);
        }
        self.tensors.deinit(self.allocator);
        self.allocator.free(self.memory_arena);
    }
};
```

### Execution (execution.zig)
- **Borrows References**: CompiledGraph, DataStorage
- **Owns Temp Data**: inputs_loaded map
- **Arena Pattern**: Could benefit from arena for execution temp data

### Compiler (compiler.zig)
- **Temporary Arena**: For each compilation pass
- **Owns Results**: CompiledGraph owns its arrays
- **Shape Inference**: Uses defer for temporary arrays

## Allocation Patterns

### Pattern 1: Arena for Graph Construction
```zig
// All graph construction uses arena
const node = try graph.arena.allocator().create(Node);
const inputs = try graph.arena.allocator().dupe(NodeId, input_ids);
// No explicit free needed - arena handles it
```

### Pattern 2: Explicit for Independent Lifecycle
```zig
// Data that outlives graph uses explicit allocation
const tensor = try OwnedTensor.init(allocator, shape, dtype);
defer tensor.deinit(allocator); // Must explicitly free
```

### Pattern 3: Defer for Temporaries
```zig
// Temporary arrays in functions
const temp_dims = try allocator.alloc(i64, rank);
defer allocator.free(temp_dims);
// Use temp_dims...
```

### Pattern 4: Error Cleanup with errdefer
```zig
const array1 = try allocator.alloc(T, size);
errdefer allocator.free(array1);

const array2 = try allocator.alloc(T, size);
errdefer allocator.free(array2);

// If we get here, both succeeded
return MyStruct{ .a = array1, .b = array2 };
```

## API Boundaries

### Functions that Transfer Ownership
When a function takes ownership, document it clearly:
```zig
/// Takes ownership of `dims` array. Caller must not free it.
pub fn createFromDims(dims: []SymbolicDim, allocator: Allocator) !ShapeTracker {
    // Function now owns dims and must ensure it's freed
}
```

### Functions that Borrow
When a function borrows data, document lifetime requirements:
```zig
/// Borrows `shape`. Caller must ensure shape outlives the result.
pub fn analyzeShape(shape: *const ShapeTracker) AnalysisResult {
    // Function does not take ownership
}
```

### Functions that Clone
When a function needs its own copy:
```zig
/// Creates a copy of dims. Caller owns both original and result.
pub fn fromDims(dims: []const SymbolicDim, allocator: Allocator) !ShapeTracker {
    const owned_dims = try allocator.dupe(SymbolicDim, dims);
    // ... use owned_dims
}
```

## Common Patterns and Anti-Patterns

### ✅ Good: Clear Arena Boundary
```zig
pub const Graph = struct {
    arena: ArenaAllocator,
    
    pub fn init(allocator: Allocator) !Graph {
        return Graph{
            .arena = ArenaAllocator.init(allocator),
            // All graph data will use arena
        };
    }
};
```

### ❌ Bad: Mixed Allocation in Same Structure
```zig
// DON'T DO THIS - confusing ownership
pub const BadStruct = struct {
    array1: []u8,  // Sometimes arena, sometimes explicit?
    array2: []u8,  // Who owns what?
};
```

### ✅ Good: Consistent Ownership in Operations
```zig
// ShapeTracker view operations now correctly free old arrays
pub fn transpose(self: *ShapeTracker, axes: []const usize, allocator: Allocator) !void {
    const new_dims = try allocator.alloc(...);
    // ... create other new arrays ...
    
    // Free old arrays before updating
    allocator.free(self.dims);
    allocator.free(self.strides);
    // ... free other arrays ...
    
    // Update with new arrays
    self.dims = new_dims;
    // ...
}
```

## Memory Debugging

### Detecting Leaks
Use `std.testing.allocator` in tests - it detects leaks:
```zig
test "no memory leaks" {
    const allocator = std.testing.allocator;
    
    var thing = try Thing.init(allocator);
    defer thing.deinit();
    
    // Test operations...
    // std.testing.allocator will report any leaks
}
```

### Arena vs Explicit Decision Tree
1. **Does the data live only during graph construction?** → Use Arena
2. **Does the data need to survive graph compilation?** → Use Explicit
3. **Is the data temporary within a function?** → Use stack or defer
4. **Does ownership transfer happen?** → Document clearly

## Future Improvements

1. **Execution Arena**: Add arena allocator for execution temporary data
2. **Type-Safe Ownership**: Use phantom types to encode ownership
3. **Compile-Time Checks**: Use comptime to verify ownership patterns
4. **Debug Mode**: Add ownership tracking in debug builds

## Summary

The Zing memory model is based on clear ownership with two primary patterns:
1. **Arena allocation** for graph construction (no explicit cleanup needed)
2. **Explicit allocation** for data with independent lifecycles (requires cleanup)

By following these patterns consistently, we avoid memory leaks and use-after-free bugs while maintaining performance and clarity.