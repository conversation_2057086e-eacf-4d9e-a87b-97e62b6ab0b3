# Autograd Audit Results: Hypothesis Confirmed

## Executive Summary

**The hypothesis is CORRECT.** <PERSON><PERSON>'s autograd implementation has a fundamental architectural flaw in how it handles gradients for view operations (transpose, reshape, expand, slice). The system cannot correctly compute gradients through these operations because it looks for view metadata in the wrong place.

## The Core Problem

### 1. View Operations Are Metadata-Only

When you perform a view operation like `tensor.transpose()`:
- **NO new graph node is created**
- The operation returns a NEW `TensorHandle` with the SAME `node_id`
- Only the `ShapeTracker` metadata is modified to reflect the view

```zig
// From tensor/ops/view.zig
pub fn transpose(self: TensorHandle, axes: ?[]const usize) !TensorHandle {
    // ...
    return TensorHandle{
        .graph = self.graph,
        .node_id = self.node_id,    // SAME data node
        .shape = new_shape_tracker,  // Transposed shape view
        .dtype = self.dtype,
    };
}
```

### 2. View Information Is Stored on Consumers

When a viewed tensor is used in a compute operation, the view information is stored on the CONSUMER node:

```zig
// From graph.zig - NodeMetadata
pub const NodeMetadata = struct {
    // ...
    // Input shapes after view transformations
    input_shapes: ?[]const @import("shape").ShapeTracker = null,
    // ...
};
```

For example, when computing `sum(transpose(A))`:
- `A` has `node_id=0` with original shape `[2,3]`
- `transpose(A)` returns a handle with `node_id=0` but shape `[3,2]`
- `sum()` creates a new node that stores the transposed shape in its `input_shapes`

### 3. Autograd Looks in the Wrong Place

During backpropagation, `addGradToNode` tries to reverse view operations by looking at the TARGET node's shape:

```zig
fn addGradToNode(ctx: *AutogradContext, target_node: NodeId, new_grad: NodeId) !void {
    // Gets the ORIGINAL shape of the target node
    const target_shape = ctx.graph.getNodeShape(target_node) orelse {...};
    
    // Tries to reverse permutations based on target_shape
    if (target_shape.indexes.len > 0) {
        // This code NEVER runs for typical transpose cases!
        // Because target_shape is the ORIGINAL shape, not the viewed shape
    }
}
```

## Proof from Test Traces

### Transpose Test
```
1. Created tensor A[2,3], node_id=0
   A.shape.indexes = [0 1]

2. Transposed A -> A_T[3,2]
   A_T.node_id = 0 (SAME as A.node_id? true)
   A_T.shape.indexes = [1 0] (permuted!)

3. Created loss = sum(A_T), node_id=2
   Sum node has input_shapes metadata:
     input_shapes[0].indexes = [0 1] (transpose info HERE!)

ERROR: reduceGradientToShape: dimension 0 still incompatible after reduction - current: 3, target: 2
```

The error occurs because:
- Autograd computes a gradient with shape `[3,2]` for the transposed view
- `addGradToNode` tries to apply it to node 0 which has original shape `[2,3]`
- It cannot see the transpose was applied, so cannot reverse it

### Matmul Test

Matmul decomposes into:
1. `A.expand(1, n)` → `A_exp[m,n,k]` (view operation)
2. `B.transpose().expand(0, m)` → `B_exp[m,n,k]` (view operations)
3. `A_exp * B_exp` → `prod[m,n,k]` (compute node)
4. `sum_reduce(prod, axis=2)` → `result[m,n]` (compute node)

During backward:
- Gradients flow through compute nodes correctly
- But when gradients reach the original A and B nodes, the view transformations cannot be reversed
- Results in shape mismatches: "incompatible concrete dimensions"

## Why Current Tests "Pass"

All autograd tests only check if gradients **exist**, not if they're **correct**:

```zig
// Typical test pattern
try expect(grad_map.get(x.node_id) != null);  // Only checks existence!
```

The tests report "8/8 passed" despite logging numerous shape errors because:
1. Gradients ARE created (they exist)
2. But they have wrong shapes/values
3. Tests never verify the actual gradient values

## The Solution

The hypothesis correctly identifies that the solution requires making each VJP responsible for reversing views on its inputs:

```zig
// Current (broken) approach
.add => {
    try addGradToNode(ctx, a, output_grad_handle.node_id);  // No view info!
    try addGradToNode(ctx, b, output_grad_handle.node_id);
},

// Proposed (correct) approach
.add => {
    const input_shapes = node.metadata.?.input_shapes orelse return error.MetadataNotFound;
    const shape_a = &input_shapes[0];
    const shape_b = &input_shapes[1];
    
    // Reverse views BEFORE adding gradients
    const grad_a = try undoInputViews(ctx, output_grad_handle, shape_a);
    const grad_b = try undoInputViews(ctx, output_grad_handle, shape_b);
    
    try addGradToNode(ctx, a, grad_a.node_id);
    try addGradToNode(ctx, b, grad_b.node_id);
},
```

This matches successful libraries like Luminal where each operation handles view reversal for its own inputs, using the actual view information stored in the node's metadata.

## Conclusion

The autograd system is fundamentally broken for any computation involving view operations. This affects:
- All matrix multiplications (use transpose and expand internally)
- Any explicit transposes, reshapes, or slices
- Broadcasting operations
- Most real-world neural network computations

The fix requires a significant refactoring to move view reversal logic from the generic `addGradToNode` function into each operation's VJP implementation, where it has access to the actual view transformations that were applied.