use luminal::prelude::*;
use luminal_cpu::CPUCompiler;
use std::time::Instant;

fn benchmark_luminal_matmul(size: usize) -> Result<(f64, f64), Box<dyn std::error::Error>> {
    // Try to catch panics and convert to Result
    std::panic::catch_unwind(|| {
        // Create a new graph for each benchmark to avoid state issues
        let mut cx = Graph::new();
        
        // Create input tensors
        let a = cx.tensor((size, size));
        let b = cx.tensor((size, size));
        
        // Perform matrix multiplication
        let mut c = a.matmul(b).retrieve();
        
        // Generate random test data
        let a_data: Vec<f32> = (0..size * size).map(|_| rand::random::<f32>()).collect();
        let b_data: Vec<f32> = (0..size * size).map(|_| rand::random::<f32>()).collect();
        
        // Set input data
        a.set(a_data);
        b.set(b_data);
        
        // Compile the graph
        cx.compile(CPUCompiler::default(), &mut c);
        
        // Warm up runs
        for _ in 0..10 {
            cx.execute();
            c.drop();
        }
        
        // Benchmark iterations
        let iterations = 100;
        let start = Instant::now();
        
        for _ in 0..iterations {
            cx.execute();
            c.drop();
        }
        
        let elapsed = start.elapsed();
        let avg_time = elapsed.as_secs_f64() / iterations as f64;
        let avg_time_ms = avg_time * 1000.0;
        
        // Calculate GFLOPS (2 * M * N * K for matrix multiplication)
        let flops = 2.0 * size as f64 * size as f64 * size as f64;
        let gflops = flops / (avg_time * 1e9);
        
        (avg_time_ms, gflops)
    })
    .map_err(|_| "Luminal panicked during execution".into())
}

fn main() -> Result<(), Box<dyn std::error::Error>> {
    println!("Luminal Matrix Multiplication Benchmark");
    println!("=====================================");
    println!("Using Luminal Tensor API");
    println!();
    
    // Create CSV writer - save to benchmarks directory
    let mut wtr = csv::Writer::from_path("/Users/<USER>/Library/CloudStorage/<EMAIL>/My Drive/mycode/zing/src/benchmarks/luminal_tensor_results.csv")?;
    wtr.write_record(&["size", "time_ms", "gflops"])?;
    
    // Benchmark sizes
    let sizes = vec![64, 128, 256, 512, 1024];
    
    for size in sizes {
        print!("Benchmarking {}x{}x{} matmul... ", size, size, size);
        std::io::Write::flush(&mut std::io::stdout())?;
        
        match benchmark_luminal_matmul(size) {
            Ok((time_ms, gflops)) => {
                println!("{:.2} GFLOPS ({:.3} ms/iter)", gflops, time_ms);
                
                // Write to CSV
                wtr.write_record(&[
                    &format!("{}", size),
                    &format!("{:.6}", time_ms),
                    &format!("{:.2}", gflops),
                ])?;
                wtr.flush()?;
            }
            Err(e) => {
                println!("ERROR: {}", e);
                // Write N/A values for failed benchmark
                wtr.write_record(&[
                    &format!("{}", size),
                    "N/A",
                    "N/A",
                ])?;
                wtr.flush()?;
            }
        }
    }
    
    println!("\nResults saved to /Users/<USER>/Library/CloudStorage/<EMAIL>/My Drive/mycode/zing/src/benchmarks/luminal_tensor_results.csv");
    
    Ok(())
}