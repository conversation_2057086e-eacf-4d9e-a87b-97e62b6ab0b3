{"rustc": 6560579996391851404, "features": "[\"alloc\", \"default\", \"getrandom\", \"libc\", \"rand_chacha\", \"std\", \"std_rng\"]", "declared_features": "[\"alloc\", \"default\", \"getrandom\", \"libc\", \"log\", \"min_const_gen\", \"nightly\", \"packed_simd\", \"rand_chacha\", \"serde\", \"serde1\", \"simd_support\", \"small_rng\", \"std\", \"std_rng\"]", "target": 8827111241893198906, "profile": 4701875279837757004, "path": 7862779240279848329, "deps": [[1573238666360410412, "rand_chacha", false, 10524221077000320399], [2924422107542798392, "libc", false, 5776043300504734226], [18130209639506977569, "rand_core", false, 9721188213286934764]], "local": [{"CheckDepInfo": {"dep_info": "release/.fingerprint/rand-78da544725a42f0c/dep-lib-rand", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}