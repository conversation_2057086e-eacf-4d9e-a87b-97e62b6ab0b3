{"rustc": 6560579996391851404, "features": "[\"default\", \"perf\", \"perf-backtrack\", \"perf-cache\", \"perf-dfa\", \"perf-inline\", \"perf-literal\", \"perf-onepass\", \"std\", \"unicode\", \"unicode-age\", \"unicode-bool\", \"unicode-case\", \"unicode-gencat\", \"unicode-perl\", \"unicode-script\", \"unicode-segment\"]", "declared_features": "[\"default\", \"logging\", \"pattern\", \"perf\", \"perf-backtrack\", \"perf-cache\", \"perf-dfa\", \"perf-dfa-full\", \"perf-inline\", \"perf-literal\", \"perf-onepass\", \"std\", \"unicode\", \"unicode-age\", \"unicode-bool\", \"unicode-case\", \"unicode-gencat\", \"unicode-perl\", \"unicode-script\", \"unicode-segment\", \"unstable\", \"use_std\"]", "target": 5796931310894148030, "profile": 4701875279837757004, "path": 7573291953204271307, "deps": [[555019317135488525, "regex_automata", false, 14284916240786626521], [2779309023524819297, "aho_corasick", false, 9105372546582318743], [3129130049864710036, "memchr", false, 6579365348321879195], [9408802513701742484, "regex_syntax", false, 14708932661833638025]], "local": [{"CheckDepInfo": {"dep_info": "release/.fingerprint/regex-dd2d4f3cc26fec84/dep-lib-regex", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}