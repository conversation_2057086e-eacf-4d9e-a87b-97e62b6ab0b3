/// Clean benchmark comparing Zing vs Luminal using only tensor APIs
const std = @import("std");

// Import Zing tensor API
const Graph = @import("graph").Graph;
const TensorHandle = @import("tensor").TensorHandle;
const tensor = @import("tensor");
const backends = @import("backends");
const execution = @import("execution");

const BenchmarkResult = struct {
    size: usize,
    time_ms: f64,
    gflops: f64,
};

pub fn main() !void {
    var gpa = std.heap.GeneralPurposeAllocator(.{}){};
    defer _ = gpa.deinit();
    const allocator = gpa.allocator();

    std.debug.print("\n=================================================\n", .{});
    std.debug.print("ZING vs LUMINAL MATMUL COMPARISON\n", .{});
    std.debug.print("=================================================\n", .{});
    std.debug.print("Using tensor APIs only - no direct backend access\n\n", .{});

    // Register backends
    try backends.registerAllBackends();
    
    const sizes = [_]usize{ 64, 128, 256, 512, 1024 };
    const iterations = 100;
    const warmup = 10;
    
    var zing_results = std.ArrayList(BenchmarkResult).init(allocator);
    defer zing_results.deinit();
    
    std.debug.print("ZING RESULTS:\n", .{});
    std.debug.print("Size    | Time (ms) | GFLOPS\n", .{});
    std.debug.print("--------|-----------|--------\n", .{});
    
    for (sizes) |size| {
        const result = try benchmarkZingMatmul(allocator, size, iterations, warmup);
        try zing_results.append(result);
        std.debug.print("{:<8}| {:<9.3}| {:.2}\n", .{ size, result.time_ms, result.gflops });
    }
    
    // Save Zing results
    try saveResultsToCSV("zing_tensor_results.csv", zing_results.items, allocator);
    
    std.debug.print("\n\nLUMINAL RESULTS:\n", .{});
    std.debug.print("To run Luminal benchmark:\n", .{});
    std.debug.print("  cd src/benchmarks/luminal_bench && cargo run --release\n", .{});
    std.debug.print("\nThen compare the CSV files:\n", .{});
    std.debug.print("  python3 src/benchmarks/compare_results.py\n", .{});
}

fn benchmarkZingMatmul(allocator: std.mem.Allocator, size: usize, iterations: usize, warmup_iters: usize) !BenchmarkResult {
    // Create computation graph
    var graph = try Graph.init(allocator);
    defer graph.deinit();
    
    // Create input placeholders using tensor API
    const shape = [_]i64{ @intCast(size), @intCast(size) };
    const a = try tensor.placeholder(&graph, &shape, .f32);
    const b = try tensor.placeholder(&graph, &shape, .f32);
    
    // Create matrix multiplication using tensor API
    const c = try tensor.matmul(a, b);
    
    // Compile the graph
    const output_handles = [_]TensorHandle{c};
    var compiled = try backends.compileGraph(
        &output_handles,
        .{ .backend_name = "cpu" },
        allocator
    );
    defer compiled.deinit(allocator);
    
    // Create executor
    var executor = try execution.Executor.init(allocator, &compiled, null);
    defer executor.deinit();
    
    // Create input data
    const input_size = size * size;
    const a_data = try allocator.alloc(f32, input_size);
    defer allocator.free(a_data);
    const b_data = try allocator.alloc(f32, input_size);
    defer allocator.free(b_data);
    
    // Initialize with random data
    var prng = std.Random.DefaultPrng.init(42);
    const random = prng.random();
    for (0..input_size) |i| {
        a_data[i] = random.float(f32) * 2.0 - 1.0;
        b_data[i] = random.float(f32) * 2.0 - 1.0;
    }
    
    // Set inputs
    const a_bytes = std.mem.sliceAsBytes(a_data);
    const b_bytes = std.mem.sliceAsBytes(b_data);
    try executor.setInput(0, a_bytes, &shape, .f32);
    try executor.setInput(1, b_bytes, &shape, .f32);
    
    // Warmup
    for (0..warmup_iters) |_| {
        try executor.run();
    }
    
    // Benchmark
    var timer = try std.time.Timer.start();
    for (0..iterations) |_| {
        try executor.run();
    }
    const elapsed_ns = timer.read();
    const elapsed_ms = @as(f64, @floatFromInt(elapsed_ns)) / 1_000_000.0;
    const avg_ms = elapsed_ms / @as(f64, @floatFromInt(iterations));
    
    // Calculate GFLOPS
    const flops = 2.0 * @as(f64, @floatFromInt(size * size * size));
    const gflops = flops / (avg_ms * 1_000_000.0);
    
    return BenchmarkResult{
        .size = size,
        .time_ms = avg_ms,
        .gflops = gflops,
    };
}

fn saveResultsToCSV(filename: []const u8, results: []const BenchmarkResult, allocator: std.mem.Allocator) !void {
    const file = try std.fs.cwd().createFile(filename, .{});
    defer file.close();
    
    // Write header
    try file.writeAll("size,time_ms,gflops\n");
    
    // Write data
    for (results) |result| {
        const line = try std.fmt.allocPrint(allocator, "{},{:.6},{:.2}\n", .{
            result.size,
            result.time_ms,
            result.gflops,
        });
        defer allocator.free(line);
        try file.writeAll(line);
    }
}